ALTER PROC dbo.queue_importAuthCIM_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusProcessing int;
	EXEC dbo.queue_getQueueTypeID @queueType='importAuthCIM', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCOF') IS NOT NULL 
		DROP TABLE #tmpAuthCIMCOF;
	CREATE TABLE #tmpAuthCIMCOF (itemID int);

	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusProcessing,
		qi.dateUpdated = GETDATE()
		OUTPUT inserted.itemID INTO #tmpAuthCIMCOF
	FROM dbo.queue_importAuthCIM AS qi
	INNER JOIN (
		SELECT top 1 qi2.itemID
		FROM dbo.queue_importAuthCIM AS qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) AS batch ON batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	select qid.itemID, qid.profileID, qid.memberID, m.membernumber, m.lastname, m.firstname, qid.FirstNameOnCard, 
		qid.LastNameOnCard, qid.CardNumber, qid.Expiration, qid.BillingAddress, qid.BillingCity, qid.BillingState, 
		qid.BillingZIP, qid.BillingCountry, qid.NickName
	from #tmpAuthCIMCOF as qi
	INNER JOIN dbo.queue_importAuthCIM as qid ON qid.itemID = qi.itemID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.memberID;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCOF') IS NOT NULL 
		DROP TABLE #tmpAuthCIMCOF;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
