ALTER PROC dbo.swod_importSeminars_import
@siteID int,
@recordedByMemberID int,
@ovAction char(1),
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @nowDate datetime = GETDATE(), @queueTypeID int, @statusInserting int, @statusReady int, 
		@itemGroupUID uniqueidentifier = NEWID(), @colList varchar(max), @selColList varchar(max), 
		@dynSQL nvarchar(max), @xmlMessage xml;
	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='importSWODPrograms', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='InsertingItems', @queueStatusID=@statusInserting OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	BEGIN TRY
		BEGIN TRAN;
			INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			SELECT itemUID, @statusInserting
			FROM #mc_SWODImport;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueInt
				FROM #mc_SWODImport
				unpivot (columnValueInt for columnname in (MCSeminarID, MCLayoutID, CompleteTime)) u
			) as unPvtInt on unPvtInt.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID
				AND dc.columnname = unPvtInt.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtString.columnValueString
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueString
				FROM #mc_SWODImport
				unpivot (columnValueString for columnname in (ProgramCode, SeminarTitle, SeminarSubtitle)) u
			) as unPvtString on unPvtString.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID
				AND dc.columnname = unPvtString.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDate.columnValueDate
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueDate
				FROM #mc_SWODImport
				unpivot (columnValueDate for columnname in (OrigPublished, StartSale, EndSale)) u
			) as unPvtDate on unPvtDate.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDate.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueBit)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtBit.columnValueBit
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueBit
				FROM #mc_SWODImport
				unpivot (columnValueBit for columnname in (Featured,PlayerQATab,BlankPlayer,[Certificate],SellInCatalog,Credit,AutoCreateTitle)) u
			) as unPvtBit on unPvtBit.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtBit.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueText)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtText.columnValueText
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueText
				FROM #mc_SWODImport
				unpivot (columnValueText for columnname in ([Description], Objective1, Objective2, Objective3, Objective4, Objective5, IntroductoryText, CompletionText, RateName1, RateName2, RateName3, RateName4, RateName5, MCCategoryIDList, MCFormIDList)) u
			) as unPvtText on unPvtText.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtText.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDecimal.columnValueDecimal2
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueDecimal2
				FROM #mc_SWODImport
				unpivot (columnValueDecimal2 for columnname in (RateAmount1, RateAmount2, RateAmount3, RateAmount4, RateAmount5)) u
			) as unPvtDecimal on unPvtDecimal.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDecimal.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, @ovAction
			FROM #mc_SWODImport as tmp
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'ovAction';

			-- resume task
			EXEC membercentral.dbo.sched_resumeTask @name='SWOD Programs Import Queue', @engine='BERLinux';

			-- update queue item groups to show ready to process
			UPDATE qi WITH (UPDLOCK, HOLDLOCK)
			SET qi.queueStatusID = @statusReady,
				qi.dateUpdated = getdate()
			FROM platformQueue.dbo.tblQueueItems as qi
			INNER JOIN #mc_SWODImport as tmp on tmp.itemUID = qi.itemUID;

			-- send message to service broker to create all the individual messages
			SELECT @xmlMessage = isnull((
				SELECT 'importSWODProgramsLoad' as t, cast(@itemGroupUID as varchar(60)) as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');
			EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to queue programs for import.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT membercentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSWODErrors
			order by rowID
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
