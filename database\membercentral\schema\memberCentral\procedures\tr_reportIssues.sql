ALTER PROC dbo.tr_reportIssues
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @SevenDaysAgo datetime = dateadd(d,-7,getdate()), @orgCode varchar(10), @accountingEmail varchar(max), @triggerJob bit = 0;

	SELECT @orgCode = orgCode, @accountingEmail = isnull(accountingEmail,'') 
	FROM dbo.organizations 
	WHERE orgID = @orgID;

	/* clear tables */
	DELETE FROM datatransfer.dbo.tr_reportIssues_openInv WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_nonPostedBatches WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_outOfOrderInv WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_invProfAllocViolations WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_flaggedTransactions WHERE orgID = @orgID;

	IF @accountingEmail = ''
		GOTO on_done;


	/* ****************************** */
	/* open invoices due > 7 days ago */
	/* ****************************** */
	IF OBJECT_ID('tempdb..#tblOpenInv') IS NOT NULL 
		DROP TABLE #tblOpenInv;
	CREATE TABLE #tblOpenInv (invoiceID int PRIMARY KEY, dateDue datetime, invoiceNumber int, payProfileID int, assignedToMemberID int);

	INSERT INTO #tblOpenInv (invoiceID, dateDue, invoiceNumber, payProfileID, assignedToMemberID)
	select invoiceID, dateDue, invoiceNumber, payProfileID, assignedToMemberID
	from dbo.tr_invoices
	where orgID = @orgID
	and statusID = 1
	and dateDue < @SevenDaysAgo;

	INSERT INTO datatransfer.dbo.tr_reportIssues_openInv (orgID, dateDue, invoiceNumber, memberID, firstName, lastName, memberNumber, hasCard, invDue)
	select @orgID, tmp.dateDue, @orgcode + dbo.fn_tr_padInvoiceNumber(tmp.invoiceNumber), m2.memberid, m2.lastname, m2.firstname, m2.membernumber, 
		case when tmp.payProfileID is null then 0 else 1 end as hasCard,
		isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) as InvDue
	from #tblOpenInv as tmp
	inner join dbo.ams_members as m on m.orgid = @orgID and m.memberid = tmp.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.orgid = @orgID and m2.memberid = m.activeMemberID
	left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
	group by tmp.dateDue, tmp.invoiceNumber, m2.memberid, m2.lastname, m2.firstname, m2.membernumber, tmp.payProfileID;

	IF @@ROWCOUNT > 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblOpenInv') IS NOT NULL 
		DROP TABLE #tblOpenInv;


	/* ************************************************ */
	/* non-posted batches with depositdate > 7 days ago */
	/* ************************************************ */
	IF OBJECT_ID('tempdb..#tblOpenBatches') IS NOT NULL 
		DROP TABLE #tblOpenBatches;
	CREATE TABLE #tblOpenBatches (batchID int PRIMARY KEY, payProfileID int, batchName varchar(400), depositDate datetime, batchstatus varchar(25));

	INSERT INTO #tblOpenBatches (batchID, payProfileID, batchName, depositDate, batchstatus)
	select b.batchID, b.payProfileID, b.batchName, b.depositDate, bs.[status]
	from dbo.tr_batches as b
	inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
	where b.orgID = @orgID 
	and b.statusID <> 4
	and isnull(b.batchCode,'') <> 'PENDINGPAYMENTS'
	and b.depositDate < @SevenDaysAgo;

	INSERT INTO datatransfer.dbo.tr_reportIssues_nonPostedBatches (orgID, batchID, [status], batchName, depositDate, profileName)
	select @orgid, tmp.batchID, tmp.batchstatus, tmp.batchName, tmp.depositDate, mp.profileName
	from #tblOpenBatches as tmp
	left outer join dbo.mp_profiles as mp on mp.profileID = tmp.payProfileID;

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblOpenBatches') IS NOT NULL 
		DROP TABLE #tblOpenBatches;


	/* ********************* */
	/* out of order invoices */
	/* ********************* */
	IF OBJECT_ID('tempdb..#tblIClosed') IS NOT NULL 
		DROP TABLE #tblIClosed;
	IF OBJECT_ID('tempdb..#tblIPaid') IS NOT NULL 
		DROP TABLE #tblIPaid;
	CREATE TABLE #tblIClosed (activeMemberID int, invoiceProfileID int, dateDue datetime);
	CREATE TABLE #tblIPaid (invoiceID int, activeMemberID int, invoiceProfileID int);

	INSERT INTO #tblIClosed (activeMemberID, invoiceProfileID, dateDue)
	select distinct mClosed.activeMemberID, ipClosed.profileID, iClosed.dateDue
	from dbo.tr_invoices as iClosed 
	inner join dbo.tr_invoiceProfiles as ipClosed on ipClosed.orgID = @orgID and ipClosed.profileID = iClosed.invoiceProfileID
	inner join dbo.ams_members as mClosed on mClosed.orgID = @orgID and mClosed.memberid = iClosed.assignedToMemberID
	where iClosed.orgID = @orgID
	and iClosed.statusID in (3,5)
	and ipClosed.enforcePayOldest = 1;

	INSERT INTO #tblIPaid (invoiceID, activeMemberID, invoiceProfileID)
	select distinct iPaid.invoiceID, mPaid.activeMemberID, iPaid.invoiceProfileID
	from dbo.tr_invoices as iPaid 
	inner join dbo.ams_members as mPaid on mPaid.orgID = @orgID and mPaid.memberid = iPaid.assignedToMemberID
	inner join #tblIClosed as iClosed on iClosed.activeMemberID = mPaid.activeMemberID
		and iClosed.invoiceProfileID = iPaid.invoiceProfileID
		and iClosed.dateDue < iPaid.dateDue
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = iPaid.invoiceID
	where iPaid.orgID = @orgID
	and iPaid.statusID = 4
	group by iPaid.invoiceID, mPaid.activeMemberID, iPaid.invoiceProfileID
	having sum(it.cache_invoiceAmountAfterAdjustment) > 0;

	INSERT INTO datatransfer.dbo.tr_reportIssues_outOfOrderInv (orgID, memberID, memberName, memberNumber)
	select distinct @orgID, mClosed.activeMemberID, mClosed.lastName + ', ' + mClosed.firstName as memberName, mClosed.memberNumber
	from #tblIClosed as iClosed
	inner join dbo.ams_members as mClosed on mClosed.orgID = @orgID and mClosed.memberid = iClosed.activeMemberID
		intersect
	select distinct @orgID, mPaid.activeMemberID, mPaid.lastName + ', ' + mPaid.firstName as memberName, mPaid.memberNumber
	from #tblIPaid as iPaid
	inner join dbo.ams_members as mPaid on mPaid.orgID = @orgID and mPaid.memberid = iPaid.activeMemberID;

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblIClosed') IS NOT NULL 
		DROP TABLE #tblIClosed;
	IF OBJECT_ID('tempdb..#tblIPaid') IS NOT NULL 
		DROP TABLE #tblIPaid;


	/* ************************************************************ */
	/* allocations that violate Invoice/Payment Profile constraints */
	/* ************************************************************ */
	IF OBJECT_ID('tempdb..#tblProfiles') IS NOT NULL 
		DROP TABLE #tblProfiles;
	IF OBJECT_ID('tempdb..#tblAlloc') IS NOT NULL 
		DROP TABLE #tblAlloc;
	CREATE TABLE #tblProfiles (invoiceProfileID int, merchantProfileID int);
	CREATE TABLE #tblAlloc (recordedByMemberID int, amount decimal(18,2), dateRecorded datetime, payTID int, saleTID int);

	insert into #tblProfiles (invoiceProfileID, merchantProfileID)
	select ip.profileID, mp.profileID
	from dbo.tr_invoiceProfiles as ip
	inner join dbo.tr_invoiceProfilesMerchantProfiles as ipmp on ipmp.invoiceProfileID = ip.profileID
	inner join dbo.mp_profiles as mp on mp.profileID = ipmp.merchantProfileID
		and mp.[status] = 'A'
	where ip.orgID = @orgID
	and ip.[status] = 'A';

	insert into #tblAlloc (recordedByMemberID, amount, dateRecorded, payTID, saleTID)
	select tAlloc.recordedByMemberID, alloc.amount_alloc, tAlloc.dateRecorded, alloc.transactionID_cash as payTID, alloc.transactionID_rev as saleTID
	from dbo.cache_tr_allocations as alloc
	inner join dbo.tr_transactions as tAlloc on tAlloc.ownedByOrgID = @orgID and tAlloc.transactionID = alloc.transactionID_alloc
		and tAlloc.statusID = 1
		and tAlloc.dateRecorded > @SevenDaysAgo
	 where alloc.orgID = @orgID;

	INSERT INTO datatransfer.dbo.tr_reportIssues_invProfAllocViolations (orgID, dateRecorded, memberName, memberNumber, allocAmount, detail, invoiceNumber, invoiceProfileName, payProfileName)
	select distinct @orgID, tmpAlloc.dateRecorded, 
		mActive.lastname + ', ' + mActive.firstname as memberName, mActive.membernumber, 
		tmpAlloc.amount as allocAmount, tPay.detail, 
		@orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, ip.profileName as invoiceProfileName, 
		mp.profileName as payProfileName 
	from #tblAlloc as tmpAlloc
	inner join dbo.tr_transactions as tPay on tPay.ownedByOrgID = @orgID and tPay.transactionID = tmpAlloc.payTID
	inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = tPay.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.ownedByOrgID = @orgID and tSaleTaxAdj.transactionID = tmpAlloc.saleTID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tSaleTaxAdj.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join #tblProfiles as tmp on tmp.invoiceProfileID = i.invoiceProfileID
	inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmpAlloc.recordedByMemberID
	inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
	where not exists (
		select invoiceProfileID from #tblProfiles where invoiceProfileID = i.invoiceProfileID and merchantProfileID = tp.profileID
	);

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblProfiles') IS NOT NULL 
		DROP TABLE #tblProfiles;
	IF OBJECT_ID('tempdb..#tblAlloc') IS NOT NULL 
		DROP TABLE #tblAlloc;


	/* ****************** */
	/* transaction alerts */
	/* ****************** */
	INSERT INTO datatransfer.dbo.tr_reportIssues_flaggedTransactions (orgID, dateRecorded, [message], memberID, memberName)
	select @orgID, t.dateRecorded, ta.[message], m2.memberid, m2.lastname + ', ' + m2.firstname + ' (' + m2.membernumber + ')' as memberName
	from dbo.tr_transactionAlerts as ta
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = ta.transactionID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = t.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
	where ta.orgID = @orgID;

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;


	IF @triggerJob = 1 BEGIN
		DECLARE @statusReady int; 
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='acctIssuesReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

		INSERT INTO platformQueue.dbo.queue_acctIssuesReport (orgID, statusID, dateAdded, dateUpdated)
		VALUES (@orgID, @statusReady, GETDATE(), GETDATE());

		EXEC dbo.sched_resumeTask @name='Accounting Issues Report', @engine='MCLuceeLinux';
	END


	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
