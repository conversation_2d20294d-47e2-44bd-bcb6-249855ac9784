<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.errCode = 0>
		<cfset local.objChargeCard = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM")>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_payTSBalance_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryBalances" resultset="1">
			</cfstoredproc>

			<cfquery name="local.qryGetTSMerchantInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				SELECT authUsername, authTransKey
				FROM dbo.depoTLA
				WHERE [state] = 'TS'
			</cfquery>
			
			<!--- loop per payment --->
			<cfloop query="local.qryBalances">
			
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_payTSBalance as qi
					INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryBalances.itemID#">
					AND qs.queueStatus = 'grabbedForProcessing'
				</cfquery>
				<cfif local.checkItemID.itemCount>
					<cfquery name="local.qryUpdateToProcessingPayment" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @statusProcessing int;
						EXEC dbo.queue_getStatusIDbyType @queueType='PayTSBalance', @queueStatus='processingPayment', @queueStatusID=@statusProcessing OUTPUT;

						UPDATE dbo.queue_payTSBalance
						SET statusID = @statusProcessing,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryBalances.itemID#">;
					</cfquery>
	
					<cfif local.qryBalances.paymentAmount gt 0>
						<cfset local.objChargeCard.chargeCard(CIMUsername=local.qryGetTSMerchantInfo.authUsername, 
							CIMPassword=local.qryGetTSMerchantInfo.authTransKey, payProfileID=local.qryBalances.payProfileID, amount=local.qryBalances.paymentAmount, 
							detail='TrialSmith Account Payment #DateTimeFormat(now(),"yyyymmddHHnnss")#', TransactionDepoMemberDataID=local.qryBalances.depoMemberDataID)>
					</cfif>
		
					<cfquery name="local.qryUpdateToDone" datasource="#application.dsn.platformQueue.dsn#">
						DELETE FROM dbo.queue_payTSBalance
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryBalances.itemID#">;
					</cfquery>
				</cfif>

			</cfloop>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_payTSBalance;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>