<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="300" } ]>
		<cfset local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields)>

		<cfset local.itemCount = getQueueItemCount()>
		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>

		<cfset local.success = false>
		<cftry>
			<cfstoredproc procedure="queue_authCIMCustCheck_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryCustomers" resultset="1">
			</cfstoredproc>

			<cfif application.MCEnvironment EQ "production">
				<cfset local.authAPIURL = "https://api.authorize.net/xml/v1/request.api">
			<cfelse>
				<cfset local.authAPIURL = "https://apitest.authorize.net/xml/v1/request.api">
			</cfif>

			<cfloop query="local.qryCustomers">
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='authCIMCustCheck', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_authCIMCustCheck
					SET statusID = @statusProcessing,
						dateUpdated = GETDATE()
					WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCustomers.itemID#">;
				</cfquery>

				<!--- First, check to see if we have a record of this customer at all -- if not, delete it --->
				<cfquery name="local.qryCustomerPayProfile" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @customerProfileID varchar(50), @gatewayID int;
					SET @customerProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryCustomers.customerProfileID#">;
					SELECT @gatewayID = dbo.fn_mp_getGatewayID('AuthorizeCCCIM');

					SELECT TOP 1 mpp.payProfileID
					FROM dbo.ams_memberPaymentProfiles AS mpp
					INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = mpp.profileID 
						AND mp.gatewayID = @gatewayID
					WHERE mpp.[status] <> 'D'
					AND mpp.customerProfileID = @customerProfileID
						UNION
					SELECT TOP 1 payProfileID
					FROM trialsmith.dbo.ccMemberPaymentProfiles
					WHERE customerProfileID = @customerProfileID;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfif local.qryCustomerPayProfile.recordcount IS 0>
					<cfset populateDeleteQueue(itemID=local.qryCustomers.itemID, profileID=local.qryCustomers.profileID, 
						gatewayUsername=local.qryCustomers.gatewayUsername, gatewayPassword=local.qryCustomers.gatewayPassword, 
						customerProfileID=local.qryCustomers.customerProfileID)>
				<cfelse>
					<cfsavecontent variable="local.apiPayload"><cfoutput>{ "getCustomerProfileRequest": { "merchantAuthentication": { "name": "#local.qryCustomers.gatewayUsername#", "transactionKey": "#local.qryCustomers.gatewayPassword#" }, "customerProfileId": "#local.qryCustomers.customerProfileID#"}}</cfoutput></cfsavecontent>

					<cfhttp url="#local.authAPIURL#" method="post" throwonerror="yes" result="local.APIResult" charset="utf-8" useragent="Authorize.net CIM Cleanup #gettickcount()#">
						<cfhttpparam type="header" name="Content-Type" value="application/json">
						<cfhttpparam type="body" value="#local.apiPayload#">
					</cfhttp>
			
					<cfset local.apiFileContent = local.APIResult.fileContent>
					<cfset local.apiFileContentPOS = find('{',local.apiFileContent)>
					<cfif local.apiFileContentPOS gt 0>
						<cfset local.apiFileContent = RemoveChars(local.apiFileContent,1,local.apiFileContentPOS-1)>
					</cfif>
					<cfset local.strAPIResult = deserializeJSON(local.apiFileContent)>

					<cfif local.strAPIResult.messages.resultCode eq "Ok">
						<cfif NOT structKeyExists(local.strAPIResult.profile,"paymentProfiles") OR NOT arrayLen(local.strAPIResult.profile.paymentProfiles)>
							<cfset populateDeleteQueue(itemID=local.qryCustomers.itemID, profileID=local.qryCustomers.profileID, 
								gatewayUsername=local.qryCustomers.gatewayUsername, gatewayPassword=local.qryCustomers.gatewayPassword, 
								customerProfileID=local.qryCustomers.customerProfileID)>
						<cfelse>
							<cfquery name="local.qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
								DELETE FROM dbo.queue_authCIMCustCheck
								WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCustomers.itemID#">
							</cfquery>
						</cfif>
					<!--- profile not found --->
					<cfelseif findNoCase("E00040",local.apiFileContent)>
						<cfquery name="local.qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
							DELETE FROM dbo.queue_authCIMCustCheck
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCustomers.itemID#">
						</cfquery>
					<cfelse>
						<cfthrow message="Error checking Customer records">
					</cfif>
				</cfif>
			</cfloop>

			<cfset local.success = true>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="populateDeleteQueue" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="gatewayUsername" type="string" required="true">
		<cfargument name="gatewayPassword" type="string" required="true">
		<cfargument name="customerProfileID" type="numeric" required="true">

		<cfset var qryPopulateCustomerDeleteQueue = "">

		<cfquery name="qryPopulateCustomerDeleteQueue" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @profileID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">,
					@gatewayUsername varchar(50) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.gatewayUsername#">,
					@gatewayPassword varchar(75) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.gatewayPassword#">,
					@customerProfileID varchar(50) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerProfileID#">,
					@statusReady int, @itemCount int;
		
				EXEC dbo.queue_getStatusIDbyType @queueType='authCIMCustDelete', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

				BEGIN TRAN;
					IF NOT EXISTS (SELECT 1 FROM dbo.queue_authCIMCustDelete WHERE profileID = @profileID AND customerProfileID = @customerProfileID) BEGIN
						INSERT INTO dbo.queue_authCIMCustDelete (profileID, gatewayUsername, gatewayPassword, customerProfileID, statusID, dateAdded, dateUpdated)
						VALUES (@profileID, @gatewayUsername, @gatewayPassword, @customerProfileID, @statusReady, GETDATE(), GETDATE());

						EXEC membercentral.dbo.sched_resumeTask @name='Process Authorize CIM Customers Delete Queue', @engine='BERLinux';
					END

					DELETE FROM dbo.queue_authCIMCustCheck
					WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) AS itemCount
			FROM dbo.queue_authCIMCustCheck;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>