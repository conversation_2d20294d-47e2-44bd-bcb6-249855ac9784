<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.processResult = populateQueue()>

		<cfif NOT local.processResult.success>
			<cfthrow message="Error running populateQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="populateQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfquery name="local.qryPopulateAuthCIMCustMPQueue" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
			
				DECLARE @statusReady int, @gatewayID int = dbo.fn_mp_getGatewayID('AuthorizeCCCIM'), @itemCount int;
				EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='authCIMCustMP', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

				INSERT INTO platformQueue.dbo.queue_authCIMCustMP (profileID, gatewayUsername, gatewayPassword, statusID, dateAdded, dateUpdated)
				SELECT MIN(mp.profileID), mp.gatewayUsername, mp.gatewayPassword, @statusReady, GETDATE(), GETDATE()
				FROM dbo.mp_profiles AS mp
				LEFT OUTER JOIN platformQueue.dbo.queue_authCIMCustMP AS qid ON qid.profileID = mp.profileID
				WHERE mp.gatewayID = @gatewayID
				AND mp.[status] = 'A'
				AND qid.itemID IS NULL
				GROUP BY mp.gatewayUsername, mp.gatewayPassword;

				SET @itemCount = @@ROWCOUNT;

				-- resume task
				IF @itemCount > 0
					EXEC dbo.sched_resumeTask @name='Process Authorize CIM Customer Merchant Profiles Queue', @engine='BERLinux';

				SELECT @itemCount AS itemCount;
			</cfquery>
			<cfset local.returnStruct.itemCount = local.qryPopulateAuthCIMCustMPQueue.itemCount>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent> 