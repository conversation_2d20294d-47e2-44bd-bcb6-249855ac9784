<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="5" } ]>
		<cfset local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields)>

		<cfset local.itemCount = getQueueItemCount()>
		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>

		<cfset local.success = false>
		<cftry>
			<cfstoredproc procedure="queue_authCIMCustMP_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryProfiles" resultset="1">
			</cfstoredproc>

			<cfif application.MCEnvironment EQ "production">
				<cfset local.authAPIURL = "https://api.authorize.net/xml/v1/request.api">
			<cfelse>
				<cfset local.authAPIURL = "https://apitest.authorize.net/xml/v1/request.api">
			</cfif>

			<cfloop query="local.qryProfiles">
				<cfsavecontent variable="local.apiPayload"><cfoutput>{ "getCustomerProfileIdsRequest": { "merchantAuthentication": { "name": "#local.qryProfiles.gatewayUsername#", "transactionKey": "#local.qryProfiles.gatewayPassword#" }}}</cfoutput></cfsavecontent>

				<cfhttp url="#local.authAPIURL#" method="post" throwonerror="yes" result="local.APIResult" charset="utf-8" useragent="Authorize.net CIM Cleanup #gettickcount()#">
					<cfhttpparam type="header" name="Content-Type" value="application/json">
					<cfhttpparam type="body" value="#local.apiPayload#">
				</cfhttp>

				<cfset local.apiFileContent = local.APIResult.fileContent>
				<cfset local.apiFileContentPOS = find('{',local.apiFileContent)>
				<cfif local.apiFileContentPOS gt 0>
					<cfset local.apiFileContent = RemoveChars(local.apiFileContent,1,local.apiFileContentPOS-1)>
				</cfif>
				<cfset local.strAPIResult = deserializeJSON(local.apiFileContent)>

				<cfif local.strAPIResult.messages.resultCode eq "Ok">
					<cfif arrayLen(local.strAPIResult.ids)>
						<cfquery name="local.qryPopulateCustomersCheckQueue" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;
			
							DECLARE @profileID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryProfiles.profileID#">,
								@gatewayUsername varchar(50) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryProfiles.gatewayUsername#">, 
								@gatewayPassword varchar(75) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryProfiles.gatewayPassword#">, 
								@statusReady int, @itemCount int;
					
							EXEC dbo.queue_getStatusIDbyType @queueType='authCIMCustCheck', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

							<cfloop array="#local.strAPIResult.ids#" item="local.thisID">
								-- not using query param since server supports a maximum of 2100 parameters
								INSERT INTO dbo.queue_authCIMCustCheck (profileID, gatewayUsername, gatewayPassword, customerProfileID, statusID, dateAdded, dateUpdated)
								VALUES (@profileID, @gatewayUsername, @gatewayPassword, #local.thisID#, @statusReady, GETDATE(), GETDATE());
							</cfloop>

							EXEC membercentral.dbo.sched_resumeTask @name='Process Authorize CIM Customers Check Queue', @engine='BERLinux';

							DELETE FROM dbo.queue_authCIMCustMP
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryProfiles.itemID#">
						</cfquery>
					<cfelse>
						<cfquery name="local.qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
							DELETE FROM dbo.queue_authCIMCustMP
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryProfiles.itemID#">
						</cfquery>
					</cfif>
				<cfelseif local.strAPIResult.messages.resultCode neq "Ok">
					<cfthrow message="Error fetching Customer records">
				</cfif>
			</cfloop>

			<cfset local.success = true>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) AS itemCount
			FROM dbo.queue_authCIMCustMP;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>