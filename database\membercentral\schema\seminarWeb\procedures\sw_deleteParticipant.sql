ALTER PROC dbo.sw_deleteParticipant
@orgcode varchar(10),
@pathToFileS varchar(400),
@pathToFileD varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @participantID int, @siteID int, @siteResourceID int, @s3DeleteReadyStatusID int;

	select @participantID = p.participantID, @siteID = mcs.siteID
	from dbo.tblParticipants as p
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	where p.orgcode = @orgcode 
	and isActive = 1;

	IF @participantID is null
		GOTO on_done;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tblCategories') IS NOT NULL
		DROP TABLE #tblCategories;
	IF OBJECT_ID('tempdb..#tblSeminars') IS NOT NULL
		DROP TABLE #tblSeminars;
	IF OBJECT_ID('tempdb..#tblBundles') IS NOT NULL
		DROP TABLE #tblBundles;
	IF OBJECT_ID('tempdb..#tblTitles') IS NOT NULL
		DROP TABLE #tblTitles;
	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL
		DROP TABLE #tmpFiles;
	IF OBJECT_ID('tempdb..#tmpFilesS3') IS NOT NULL
		DROP TABLE #tmpFilesS3;
	CREATE TABLE #tblCategories (categoryID int PRIMARY KEY);
	CREATE TABLE #tblSeminars (seminarID int PRIMARY KEY);
	CREATE TABLE #tblBundles (bundleID int PRIMARY KEY);
	CREATE TABLE #tblTitles (titleID int PRIMARY KEY);
	CREATE TABLE #tmpFiles (fileID int PRIMARY KEY);
	CREATE TABLE #tmpFilesS3 (objectKey varchar(800), fileSize bigint);
	
	insert into #tblCategories (categoryID)
	select categoryID from dbo.tblCategories where participantID = @participantID;

	insert into #tblSeminars (seminarID)
	select seminarID from dbo.tblSeminars where participantID = @participantID;

	insert into #tblBundles (bundleID)
	select bundleID from dbo.tblBundles where participantID = @participantID;

	insert into #tblTitles (titleID)
	select titleID from dbo.tblTitles where participantID = @participantID;

	insert into #tmpFiles (fileID)
	select fileID from dbo.tblFiles where participantID = @participantID;

	-- import from file
	declare @fullSql varchar(max);
	set @fullSql = 'BULK INSERT #tmpFilesS3 FROM ''' + @pathToFileS + ''' WITH (FIELDTERMINATOR = ''|'')';
	EXEC(@fullSql);
	set @fullSql = 'BULK INSERT #tmpFilesS3 FROM ''' + @pathToFileD + ''' WITH (FIELDTERMINATOR = ''|'')';
	EXEC(@fullSql);

	-- also consider video previews
	INSERT INTO #tmpFilesS3 (objectKey)
	select LOWER('swod/' + sP.orgcode + '/' + cast(sP.participantID as varchar(10)) + '/' 
				+ cast(p.baseFileID as varchar(10)) + '_s_'
				+ cast(p.seminarID as varchar(10)) + '_preview.mp4')
	from dbo.tblVideoPreviews as p
	inner join #tmpFiles as tmp on tmp.fileID = p.baseFileID
	inner join dbo.tblSeminars as s on s.seminarID = p.seminarID
	inner join dbo.tblParticipants as sP on sP.participantID = s.participantID
	where p.isOnline = 1;

	BEGIN TRAN;
		delete from dbo.tblParticipantEvents where participantID = @participantID;
		UPDATE dbo.tblSeminarsOptIn SET IsActive = 0 where participantID = @participantID;
		delete from dbo.tblNationalProgramParticipants where participantID = @participantID;
		UPDATE dbo.tblBundlesOptIn SET isActive = 0 WHERE participantID = @participantID;
		UPDATE dbo.tblBundles set [status] = 'D' where participantID = @participantID;
		UPDATE dbo.tblParticipants set isActive = 0, isConf = 0, isSWOD = 0, isSWL = 0 where participantID = @participantID;
		
		-- from delete submissions
		DELETE FROM dbo.tblSWODSubmissionsAndCategories WHERE categoryID in (select categoryID from #tblCategories);
		DELETE FROM dbo.tblSWODSubmissionsAndCategories WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndAuthors WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndObjectives WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndForms WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndCredits WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndRateGroups WHERE rateID in (SELECT rateID FROM dbo.tblSWODSubmissionsAndRates WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID));
		DELETE FROM dbo.tblSWODSubmissionsAndRates WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndLinks WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissions WHERE participantID = @participantID;

		-- from delete category
		DELETE FROM dbo.tblSeminarsAndCategories WHERE categoryID in (select categoryID from #tblCategories);
		DELETE FROM dbo.tblCategories WHERE categoryID in (select categoryID from #tblCategories);		
		
		-- from delete seminar
		UPDATE dbo.tblSeminars SET isDeleted = 1, isPublished = 0 where seminarID in (select seminarID from #tblSeminars);
		DELETE FROM dbo.tblSeminarAndFilesSyncPoints WHERE seminarID in (select seminarID from #tblSeminars);

		-- from delete title
		UPDATE dbo.tblTitles SET isDeleted = 1 where titleID in (select titleID from #tblTitles);
		DELETE FROM dbo.tblSeminarsAndTitles WHERE titleID in (select titleID from #tblTitles);

		-- from delete file
		UPDATE dbo.tblFiles SET isDeleted = 1, formatsAvailable = '<formats/>' where fileID in (select fileID from #tmpFiles);
		DELETE FROM dbo.tblVideoPreviews WHERE baseFileID in (select fileID from #tmpFiles);
		delete from dbo.tblTitlesAndFiles where fileID in (select fileID from #tmpFiles);
		DELETE FROM dbo.tblSeminarAndFilesSyncPoints where fileID in (select fileID from #tmpFiles);
		DELETE FROM dbo.tblSeminarAndFilesSyncPoints where linkedfileID in (select fileID from #tmpFiles);

		-- delete from search tables
		DELETE FROM searchMC.dbo.sw_programs WHERE programType IN ('SWL','SWOD') AND programID IN (select seminarID from #tblSeminars)
		DELETE FROM searchMC.dbo.sw_programs WHERE programType = 'SWTL' AND programID IN (select titleID from #tblTitles)
		DELETE FROM searchMC.dbo.sw_programs WHERE programType = 'SWB' AND programID IN (select bundleID from #tblBundles)
	COMMIT TRAN;

	
	SET @siteResourceID = null;
	SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblSeminars WHERE participantID = @participantID AND isDeleted = 1 AND siteResourceID is not null;
	WHILE @siteResourceID is not null BEGIN
		EXEC membercentral.dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
		SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblSeminars WHERE participantID = @participantID AND isDeleted = 1 AND siteResourceID is not null AND siteResourceID > @siteResourceID;
	END
	
	SET @siteResourceID = null;
	SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblBundles WHERE participantID = @participantID AND status = 'D' AND siteResourceID is not null;
	WHILE @siteResourceID is not null BEGIN
		EXEC membercentral.dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
		SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblBundles WHERE participantID = @participantID AND status = 'D' AND siteResourceID is not null AND siteResourceID > @siteResourceID;
	END

	INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
	SELECT @s3DeleteReadyStatusID, 'seminarweb', objectKey, GETDATE(), GETDATE()
	FROM #tmpFilesS3;

	delete from lyris.trialslyris1.dbo.sw_marketing where orgcode = @orgcode;

	on_done:

	IF OBJECT_ID('tempdb..#tblCategories') IS NOT NULL
		DROP TABLE #tblCategories;
	IF OBJECT_ID('tempdb..#tblSeminars') IS NOT NULL
		DROP TABLE #tblSeminars;
	IF OBJECT_ID('tempdb..#tblBundles') IS NOT NULL
		DROP TABLE #tblBundles;
	IF OBJECT_ID('tempdb..#tblTitles') IS NOT NULL
		DROP TABLE #tblTitles;
	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL
		DROP TABLE #tmpFiles;
	IF OBJECT_ID('tempdb..#tmpFilesS3') IS NOT NULL
		DROP TABLE #tmpFilesS3;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
