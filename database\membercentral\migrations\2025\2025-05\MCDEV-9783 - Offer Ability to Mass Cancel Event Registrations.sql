-- Create Service Broker Queue for Event Registrant Deletion
USE platformQueue
GO

DECLARE @queueTypeID int;

INSERT INTO dbo.tblQueueTypes (queueType, checkProc, runCheck, clearProc, downloadProc, summaryProc)
VALUES ('evRegistrantDelete', 'queue_evRegistrantDelete_check', 1, 'queue_evRegistrantDelete_clear', 'queue_evRegistrantDelete_download', 'queue_evRegistrantDelete_summary');
SELECT @queueTypeID = SCOPE_IDENTITY();

INSERT INTO dbo.tblQueueStatuses (queueTypeID, queueStatus)
VALUES (@queueTypeID, 'readyToProcess'), (@queueTypeID, 'processingItem');
GO

CREATE TABLE [dbo].[queue_evRegistrantDelete](
	[itemID] [int] IDENTITY(1,1) NOT NULL,
	[itemGroupUID] [uniqueidentifier] NOT NULL,
	[siteID] [int] NOT NULL,
	[orgID] [int] NOT NULL,
	[registrantID] [int] NOT NULL,
	[AROption] [varchar](50) NOT NULL,
	[cancellationFee] [decimal](19, 2) NOT NULL,
	[GLAccountID] [int] NOT NULL,
	[deallocUsingPaidAmt] [bit] NOT NULL,
	[statusID] [int] NOT NULL,
	[dateAdded] [datetime] NOT NULL,
	[dateUpdated] [datetime] NOT NULL,
	[recordedByMemberID] [int] NOT NULL,
 CONSTRAINT [PK_queue_evRegistrantDelete] PRIMARY KEY CLUSTERED
(
	[itemID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- Create Service Broker Queue and Service
CREATE QUEUE EvRegistrantDeleteQueue;
GO
CREATE SERVICE [PlatformQueue/EvRegistrantDeleteService] ON QUEUE EvRegistrantDeleteQueue ([PlatformQueue/GeneralXMLContract]);
GO

USE membercentral
GO

CREATE PROC dbo.ev_removeRegistrantFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @orgID int, @queueTypeID int, @statusReady int, @statusProcessing int,
		@itemStatus int, @registrantID int, @AROption varchar(50), @cancellationFee decimal(19,2),
		@GLAccountID int, @deallocUsingPaidAmt bit, @recordedByMemberID int;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'evRegistrantDelete';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusProcessing = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';

	select @itemStatus = statusID, @siteID = siteID, @orgID = orgID, @registrantID = registrantID,
		@AROption = AROption, @cancellationFee = cancellationFee, @GLAccountID = GLAccountID,
		@deallocUsingPaidAmt = deallocUsingPaidAmt, @recordedByMemberID = recordedByMemberID
	from platformQueue.dbo.queue_evRegistrantDelete
	where itemID = @itemID;

	-- if itemID is not readyToProcess, kick out now
	IF @itemStatus <> @statusReady
		RAISERROR('Item not in readyToProcess state',16,1);

	-- update to processingItem
	UPDATE platformQueue.dbo.queue_evRegistrantDelete
	SET statusID = @statusProcessing,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- Call the existing procedure to remove the registrant
	EXEC dbo.ev_removeRegistrant
		@registrantID = @registrantID,
		@recordedOnSiteID = @siteID,
		@recordedByMemberID = @recordedByMemberID,
		@statsSessionID = 0,
		@AROption = @AROption,
		@cancellationFee = @cancellationFee,
		@cancellationFeeGLAccountID = @GLAccountID,
		@deallocateFeeByAllocatedPayments = @deallocUsingPaidAmt;

	-- Delete the queue item after processing
	DELETE FROM platformQueue.dbo.queue_evRegistrantDelete
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

USE platformQueue
GO

CREATE PROC dbo.queue_evRegistrantDelete_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
	@xmldata xml, @itemID int, @ErrorMessage nvarchar(2048);

WHILE 1 = 1
BEGIN TRY

	SELECT @DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL,
		@itemID = NULL, @ErrorMessage = NULL;

	-- initially set to snapshot. this allows us to go in and out throughout this request
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	BEGIN TRANSACTION;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		WAITFOR (
			RECEIVE TOP (1) @DialogHandle = conversation_handle,
							@MessageType = message_type_name,
							@MessageBody = message_body
			FROM dbo.EvRegistrantDeleteQueue
		), TIMEOUT 1000;

		IF @DialogHandle IS NULL BEGIN
			COMMIT TRANSACTION;
			BREAK;
		END

		IF @MessageType = N'PlatformQueue/GeneralXMLRequest' BEGIN
			BEGIN TRY
				SET @xmldata = cast(@MessageBody as xml);

				SELECT @itemID = @xmldata.value('(/mc/@i)[1]','int');
				IF @itemID is not null
					EXEC membercentral.dbo.ev_removeRegistrantFromQueue @itemID=@itemID;

				END CONVERSATION @DialogHandle;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET @ErrorMessage = N'queue_evRegistrantDelete_Activated - ' + ERROR_MESSAGE();
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END CATCH
		END
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @DialogHandle;
			END
			ELSE BEGIN
				IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
					SET @xmldata = cast(@MessageBody as xml);
					with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
						select @ErrorMessage = N'queue_evRegistrantDelete_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
				ELSE BEGIN
					SET @ErrorMessage = N'queue_evRegistrantDelete_Activated - Unexpected message type received: ' + @MessageType;
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
			END
		END

	IF @@trancount > 0
		COMMIT TRANSACTION;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH

IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
GO

-- modify queue to set activation proc
ALTER QUEUE EvRegistrantDeleteQueue WITH
	  STATUS = ON,
	  ACTIVATION (STATUS = ON,
				  PROCEDURE_NAME = dbo.queue_evRegistrantDelete_Activated,
				  MAX_QUEUE_READERS = 1,
				  EXECUTE AS OWNER)
GO

CREATE PROC dbo.queue_evRegistrantDelete_sendMessage
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @DialogHandleFinal uniqueidentifier;
	set @DialogHandleFinal = NEWID();

	BEGIN DIALOG CONVERSATION @DialogHandleFinal
		FROM SERVICE [PlatformQueue/EvRegistrantDeleteService]
		TO SERVICE N'PlatformQueue/EvRegistrantDeleteService'
		ON CONTRACT [PlatformQueue/GeneralXMLContract]
		WITH ENCRYPTION = OFF;
	SEND ON CONVERSATION @DialogHandleFinal
		MESSAGE TYPE [PlatformQueue/GeneralXMLRequest] (@xmlMessage);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.queue_evRegistrantDelete_load
@itemGroupUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @itemAsStr varchar(60), @xmlMessage xml;

	IF OBJECT_ID('tempdb..#tblMsgs') IS NOT NULL
		DROP TABLE #tblMsgs;
	CREATE TABLE #tblMsgs (itemAsStr varchar(60) not null);

	insert into #tblMsgs (itemAsStr)
	select itemID
	from dbo.queue_evRegistrantDelete
	where itemGroupUID = @itemGroupUID;

	select @itemAsStr = min(itemAsStr) from #tblMsgs;
	while @itemAsStr is not null BEGIN
		SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
		EXEC dbo.queue_evRegistrantDelete_sendMessage @xmlMessage=@xmlMessage;
		select @itemAsStr = min(itemAsStr) from #tblMsgs where itemAsStr > @itemAsStr;
	end

	IF OBJECT_ID('tempdb..#tblMsgs') IS NOT NULL
		DROP TABLE #tblMsgs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.queue_evRegistrantDelete_check
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @queueStatusID int, @nextQueueStatusID int,
		@errorSubject varchar(400), @errorTitle varchar(400), @itemAsStr varchar(60), @xmlMessage xml;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'evRegistrantDelete';

	-- evRegistrantDelete / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingItem';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_evRegistrantDelete WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_evRegistrantDelete
		SET statusID = @nextQueueStatusID,
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @queueStatusID
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_evRegistrantDelete_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'evRegistrantDelete Queue Issue';
		SET @errorSubject = 'evRegistrantDelete queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- evRegistrantDelete catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_evRegistrantDelete WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'evRegistrantDelete Queue Issue';
		SET @errorSubject = 'evRegistrantDelete queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.queue_evRegistrantDelete_clear
@status varchar(60)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	delete qi
	from dbo.queue_evRegistrantDelete as qi
	inner join dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID and qs.queueStatus = @status;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.queue_evRegistrantDelete_download
@status varchar(60),
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max) = '';

	SELECT @selectsql = 'SELECT ''evRegistrantDelete'' as queueType, qs.queueStatus, qi.itemID, qi.itemGroupUID, qi.registrantID,
		qi.orgID, qi.siteID, qi.AROption, qi.cancellationFee, qi.GLAccountID, qi.deallocUsingPaidAmt, qi.dateAdded, qi.dateUpdated,qi.recordedByMemberID,
		ROW_NUMBER() OVER(order by qi.dateAdded, qi.itemID) as mcCSVorder
		*FROM* platformQueue.dbo.queue_evRegistrantDelete as qi
		INNER JOIN platformQueue.dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID and qs.queueStatus = ''' + @status + '''';

	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.queue_evRegistrantDelete_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 'evRegistrantDelete', qs.queueStatus, count(qi.itemid), min(qi.dateUpdated), 1
	from dbo.queue_evRegistrantDelete as qi
	inner join dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID
	group by qs.queueStatus;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_DataImport_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
	@xmldata xml, @itemUID uniqueidentifier, @itemID int, @ErrorMessage nvarchar(2048),
	@queueType varchar(30);

WHILE 1 = 1
BEGIN TRY

	SELECT @DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @queueType = NULL,
		@itemUID = NULL, @itemID = NULL, @ErrorMessage = NULL;

	-- initially set to snapshot. this allows us to go in and out throughout this request
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	BEGIN TRANSACTION;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		WAITFOR (
			RECEIVE TOP (1) @DialogHandle = conversation_handle,
							@MessageType = message_type_name,
							@MessageBody = message_body
			FROM dbo.DataImportQueue
		), TIMEOUT 1000;

		IF @DialogHandle IS NULL BEGIN
			COMMIT TRANSACTION;
			BREAK;
		END

		IF @MessageType = N'PlatformQueue/GeneralXMLRequest' BEGIN
			BEGIN TRY
				SET @xmldata = cast(@MessageBody as xml);

				SELECT @queueType = @xmldata.value('(/mc/@t)[1]','varchar(30)');

				IF @queueType = 'memberConditions' BEGIN
					EXEC dbo.queue_MemberConditions_load @xmlMessage=@xmldata;
					GOTO on_done;
				END
				IF @queueType = 'memberImportLoad' BEGIN
					SELECT @itemID = @xmldata.value('(/mc/@u)[1]','int');
					EXEC dbo.queue_MemberUpdate_load @jobID=@itemID;
					GOTO on_done;
				END

				SELECT @itemUID = @xmldata.value('(/mc/@u)[1]','uniqueidentifier');
				IF @queueType = 'batchPostLoad' BEGIN
					EXEC dbo.queue_BatchPost_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'memberDeleteLoad' BEGIN
					EXEC dbo.queue_memberDelete_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'memberMergeMatchLoad' BEGIN
					EXEC dbo.queue_MemberMergeMatch_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'importAcctOpt1Load' BEGIN
					EXEC dbo.queue_AcctOption1Import_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'importAcctOpt2Load' BEGIN
					EXEC dbo.queue_AcctOption2Import_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'importEventsLoad' BEGIN
					EXEC dbo.queue_EventsImport_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'importPagesLoad' BEGIN
					EXEC dbo.queue_importPages_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'importSWODProgramsLoad' BEGIN
					EXEC dbo.queue_importSWODPrograms_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'addSubscribersLoad' BEGIN
					EXEC dbo.queue_SubscriptionAdd_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'ARByDayInvProfLoad' BEGIN
					EXEC dbo.queue_ARByDayInvProf_load;
					GOTO on_done;
				END
				IF @queueType = 'expireSubsLoad' BEGIN
					EXEC dbo.queue_SubscriptionExpire_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'subscriptionBilledLoad' BEGIN
					EXEC dbo.queue_SubscriptionBilled_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'subscriptionForceAddLoad' BEGIN
					EXEC dbo.queue_subscriptionForceAdd_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'subscriptionActivateLoad' BEGIN
					EXEC dbo.queue_SubscriptionActivate_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'subscriptionInactivateLoad' BEGIN
					EXEC dbo.queue_SubscriptionInactivate_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'setSubRecogRangeLoad' BEGIN
					EXEC dbo.queue_SetSubRecogRange_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'subscriptionDeleteLoad' BEGIN
					EXEC dbo.queue_SubscriptionDelete_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'contributionsInstallmentsLoad' BEGIN
					EXEC dbo.queue_ContributionsInstallments_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'contributionsSalesLoad' BEGIN
					EXEC dbo.queue_ContributionsSales_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'contributionsImportLoad' BEGIN
					EXEC dbo.queue_ContributionsImport_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'deferredRecognitionLoad' BEGIN
					EXEC dbo.queue_DeferredRecognition_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'depoDocsToFileShare2Load' BEGIN
					EXEC dbo.queue_DepoDocsToFileShare2_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'fileShare2toDepoDocsLoad' BEGIN
					EXEC dbo.queue_fileShare2toDepoDocs_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'eventSearchTextLoad' BEGIN
					EXEC dbo.queue_eventSearchText_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'lyrisListSyncLoad' BEGIN
					EXEC dbo.queue_lyrisListSync_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'taskDeleteLoad' BEGIN
					EXEC dbo.queue_taskDelete_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'evRegistrantDeleteLoad' BEGIN
					EXEC dbo.queue_evRegistrantDelete_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'taskImportLoad' BEGIN
					EXEC dbo.queue_TaskImport_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'trackGrpMembershipHistoryLoad' BEGIN
					EXEC dbo.queue_trackGrpMembershipHistory_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END
				IF @queueType = 'tsSubscriberDeposLoad' BEGIN
					EXEC dbo.queue_tsSubscriberDepos_load @itemGroupUID=@itemUID;
					GOTO on_done;
				END

				on_done:
				END CONVERSATION @DialogHandle;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET @ErrorMessage = N'queue_DataImport_Activated - ' + ERROR_MESSAGE();
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END CATCH
		END
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @DialogHandle;
			END
			ELSE BEGIN
				IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
					SET @xmldata = cast(@MessageBody as xml);
					with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
						select @ErrorMessage = N'queue_DataImport_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
				ELSE BEGIN
					SET @ErrorMessage = N'queue_DataImport_Activated - Unexpected message type received: ' + @MessageType;
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
			END
		END

	IF @@trancount > 0
		COMMIT TRANSACTION;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH

IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
GO
