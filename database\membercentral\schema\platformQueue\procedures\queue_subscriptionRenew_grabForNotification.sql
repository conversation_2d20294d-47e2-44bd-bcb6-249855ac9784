ALTER PROC dbo.queue_subscriptionRenew_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenew', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	INSERT INTO #tmpNotify (itemGroupUID)
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID = @statusReady
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID <> @statusReady;

	-- mark as grabbed
	UPDATE queueSR
	SET queueSR.statusID = @statusGrabbed,
		queueSR.dateUpdated = getdate()
	FROM dbo.queue_subscriptionRenew as queueSR
	INNER JOIN #tmpNotify as tmp on tmp.itemGroupUID = queueSR.itemGroupUID
	WHERE queueSR.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, qid.recordedByMemberID, qid.orgID, me.email as reportEmail, s.siteName, s.siteCode
	from #tmpNotify as tmpN
	INNER JOIN dbo.queue_subscriptionRenew as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	-- return report information for failures
	select tmpN.itemGroupUID, qid.rescindDate, qid.errorMessage, qid.wddxMessages
	from #tmpNotify as tmpN
	INNER JOIN dbo.queue_subscriptionRenew as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	order by tmpN.itemGroupUID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
