ALTER PROC dbo.sub_expireSubscriberFromQueue
@itemUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusProcessing int, @statusNotify int, @itemStatus int, @siteID int, 
		@subscriberID int, @recordedByMemberID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='expireSubs', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusNotify OUTPUT;

	select @siteID=t.siteID, @subscriberID=qid.subscriberID, @recordedByMemberID=qid.recordedByMemberID, @itemStatus=qid.statusID
	from platformQueue.dbo.queue_subscriptionExpire AS qid
	inner join dbo.sub_subscribers as ss on ss.subscriberID = qid.subscriberID
	inner join dbo.sub_subscriptions as subs on subs.subscriptionID = ss.subscriptionID
	inner join dbo.sub_types as t on t.typeID = subs.typeID
	where qid.itemUID = @itemUID;

	-- if itemUID is not readyToProcess, kick out now
	IF @itemStatus <> @statusReady OR @subscriberID is null
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_subscriptionExpire
	set statusID = @statusProcessing,
		dateUpdated = getdate()
	where itemUID = @itemUID;

	BEGIN TRY
		EXEC dbo.sub_expireSubscriber @subscriberID=@subscriberID, @memberID=null, @siteID=@siteID, 
			@enteredByMemberID=@recordedByMemberID, @statsSessionID=null, @AROption='C', @fReturnQuery=0;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;
		UPDATE platformQueue.dbo.queue_subscriptionExpire set errorMessage = ERROR_MESSAGE() where itemUID = @itemUID;
	END CATCH

	UPDATE platformQueue.dbo.queue_subscriptionExpire
	set statusID = @statusNotify,
		dateUpdated = getdate()
	where itemUID = @itemUID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
