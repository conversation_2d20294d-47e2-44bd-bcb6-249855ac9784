ALTER PROC dbo.ts_addDepoDocumentToPreApproveQueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='processPreApproveDepoDocs', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_processPreApproveDepoDocs WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO platformQueue.dbo.queue_processPreApproveDepoDocs (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process Pre Approve Depo Document Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
