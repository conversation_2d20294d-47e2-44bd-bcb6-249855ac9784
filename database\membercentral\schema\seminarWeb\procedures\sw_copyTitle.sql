ALTER PROC dbo.sw_copyTitle
@titleID int,
@newSeminarID int,
@oldSeminarID int,
@recordedByMemberID int,
@newTitleID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpTitlesAndFiles;
	IF OBJECT_ID('tempdb..#tmpNewTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpNewTitlesAndFiles;
	CREATE TABLE #tmpNewTitlesAndFiles (newFileID int PRIMARY KEY, newTitleID int, fileID int, titleID int, fileOrder int, 
		formatsAvailable xml, isDownloadable int, isDefaultStream int, isDefaultPaper int, isSupportingDoc int, previewPct int);

	DECLARE @orgID int, @orgCode varchar(10), @participantID int, @siteID int, @minNewFileID int, 
		@nowDate datetime = GETDATE(), @siteCode varchar(10), @s3CopyReadyStatusID int, @resourceTypeID int, @siteResourceID int, @seminarSiteResourceID int;;

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @siteCode = mcs.siteCode, @participantID = p.participantID, @orgCode=p.orgCode
	FROM dbo.tblTitles AS t
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = t.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE t.titleID = @titleID;

	BEGIN TRAN;
		-- insert new title record
		SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWTitle');
			
		SELECT @seminarSiteResourceID = s.siteResourceID
		FROM dbo.tblSeminars as s
		WHERE s.seminarID = @newSeminarID;
			
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
			@isVisible=1, @parentSiteResourceID=@seminarSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;

		INSERT INTO dbo.tblTitles (titleName, participantID, dateCreated, siteResourceID)
		SELECT titleName, participantID, @nowDate, @siteResourceID
		FROM dbo.tblTitles
		WHERE titleID = @titleID;

		SELECT @newTitleID = SCOPE_IDENTITY();
		
		INSERT INTO dbo.tblSeminarsAndTitles (titleID, seminarID, titleOrder)
		SELECT @newTitleID, CASE WHEN @newSeminarID > 0 THEN @newSeminarID ELSE seminarID END AS seminarID, titleOrder
		FROM dbo.tblSeminarsAndTitles
		WHERE titleID = @titleID
		AND seminarID = CASE WHEN @oldSeminarID > 0 THEN @oldSeminarID ELSE seminarID END;

		SELECT @newTitleID as newTitleID, f.fileID, tf.titleID, f.fileTypeID, f.participantID, f.[fileName], 
			f.fileTitle, f.fileDesc, f.formatsAvailable, tf.fileOrder, tf.isDownloadable, tf.isDefaultStream, 
			tf.isDefaultPaper, tf.isSupportingDoc, tf.previewPct
		INTO #tmpTitlesAndFiles
		FROM dbo.tblFiles f
		INNER JOIN dbo.tblTitlesAndFiles tf ON tf.fileID = f.fileID
		WHERE tf.titleID = @titleID;
			
		MERGE INTO dbo.tblFiles as f USING #tmpTitlesAndFiles AS tmp on 1 = 0
		WHEN NOT MATCHED THEN
			INSERT (fileTypeID, participantID, [fileName], fileTitle, fileDesc, formatsAvailable)
			VALUES (fileTypeID, participantID, [fileName], fileTitle, fileDesc, formatsAvailable)
				OUTPUT INSERTED.fileID, tmp.newTitleID, tmp.fileID, tmp.titleID, tmp.fileOrder, tmp.formatsAvailable, 
					tmp.isDownloadable, tmp.isDefaultStream, tmp.isDefaultPaper, tmp.isSupportingDoc, tmp.previewPct
				INTO #tmpNewTitlesAndFiles (newFileID, newTitleID, fileID, titleID, fileOrder, formatsAvailable, 
					isDownloadable, isDefaultStream, isDefaultPaper, isSupportingDoc, previewPct);

		INSERT INTO dbo.tblTitlesAndFiles (titleID, fileID, fileOrder, isDownloadable, isDefaultStream, isDefaultPaper, isSupportingDoc, previewPct)
		SELECT newTitleID, newFileID, fileOrder, isDownloadable, isDefaultStream, isDefaultPaper, isSupportingDoc, previewPct
		FROM #tmpNewTitlesAndFiles;

		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;

		WITH fileInfo AS (
			SELECT f.fileID, f.newFileID, 
				FFR.formatType.value('(@accesstype)[1]', 'varchar(2)') AS accesstype,
				FFR.formatType.value('(@ext)[1]', 'varchar(10)') AS ext, cast(null as varchar(100)) AS pg
			FROM #tmpNewTitlesAndFiles f
			CROSS APPLY f.formatsAvailable.nodes('/formats/format[@ext!=''pvr'']') AS FFR(formatType)
				union all
			SELECT f.fileID, f.newFileID,
				FFR.formatType.value('(../@accesstype)[1]', 'varchar(2)') AS accesstype,
				FFR.formatType.value('(../@ext)[1]', 'varchar(10)') AS ext,
				FFR.formatType.value('(@pg)[1]', 'varchar(100)') AS pg
			FROM #tmpNewTitlesAndFiles f
			CROSS APPLY f.formatsAvailable.nodes('/formats/format[@ext=''pvr'']/page') AS FFR(formatType)
		)
		INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID) 
		SELECT 'seminarweb',
			CASE 
			WHEN accesstype = 'D' AND ext = 'pvr' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(fileID as varchar(10)) +'.pvr/'+ pg)
			WHEN accesstype = 'D' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(fileID as varchar(10)) +'.'+ ext) 
			WHEN accesstype = 'S' THEN 
				LOWER('swod/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(fileID as varchar(10)) +'.'+ ext)					
			END,
			'seminarweb',
			CASE 
			WHEN accesstype = 'D' AND ext = 'pvr' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(newFileID as varchar(10)) +'.pvr/'+ pg)
			WHEN accesstype = 'D' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(newFileID as varchar(10)) +'.'+ ext) 
			WHEN accesstype = 'S' THEN 
				LOWER('swod/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(newFileID as varchar(10)) +'.'+ ext)					
			END,
			@nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID 
		FROM fileInfo;

	COMMIT TRAN;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	SELECT '{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWTL-' + cast(titleID as varchar(20)) + ' [' + titleName + '] has been created by copying SWTL-' + cast(@titleID as varchar(20)) + '.'),'"','\"') + '" } }'
	FROM dbo.tblTitles
	WHERE titleID = @newTitleID;

	IF OBJECT_ID('tempdb..#tmpTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpTitlesAndFiles;
	IF OBJECT_ID('tempdb..#tmpNewTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpNewTitlesAndFiles;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
