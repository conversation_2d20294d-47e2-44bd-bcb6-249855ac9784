ALTER PROC dbo.queue_pubSyndIssueDist_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='pubSyndIssueDist', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpIssues') IS NOT NULL
		DROP TABLE #tmpIssues;
	CREATE TABLE #tmpIssues (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpIssues
	FROM dbo.queue_pubSyndIssueDist as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_pubSyndIssueDist
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	-- return issue info without depending much on publication table since they may be altered after queuing and queue items will be orphaned
	SELECT DISTINCT qid.itemID, qid.parentSiteID, qid.parentPublicationID, qid.parentPublicationIssueID,
		qid.childSiteID, qid.childPublicationID, s.siteCode AS childSiteCode, qid.enteredByMemberID
	FROM #tmpIssues AS tmp
	INNER JOIN dbo.queue_pubSyndIssueDist AS qid ON qid.itemID = tmp.itemID
	LEFT OUTER JOIN membercentral.dbo.pub_publications AS p
		INNER JOIN membercentral.dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
		INNER JOIN membercentral.dbo.sites AS s ON s.siteID = ai.siteID
		ON p.publicationID = qid.childPublicationID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpIssues') IS NOT NULL
		DROP TABLE #tmpIssues;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
