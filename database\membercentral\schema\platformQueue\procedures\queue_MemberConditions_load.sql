ALTER PROC dbo.queue_MemberConditions_load
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @itemGroupUID uniqueidentifier = NEWID(), @orgID int, @processType varchar(30), @xmldataFinal xml,
		@dateAdded datetime = getdate(), @triggerLogID bigint, @readyQueueStatusID int, 
		@notReadyQueueStatusID int, @memberConditionsReadyQueueStatusID int;

	SELECT @orgID = @xmlMessage.value('(/mc/@o)[1]','int'), 
		@processType = @xmlMessage.value('(/mc/@pt)[1]','varchar(30)'),
		@triggerLogID = @xmlMessage.value('(/mc/@x)[1]','bigint');

	select @xmldataFinal = isnull((
		select @processType as t, @orgID as o, @itemGroupUID as u, @triggerLogID as x
		FOR XML RAW('mc'), TYPE
	),'<mc/>');		

	IF OBJECT_ID('tempdb..#tmpsbQueueM') IS NOT NULL 
		DROP TABLE #tmpsbQueueM;
	IF OBJECT_ID('tempdb..#tmpsbQueueC') IS NOT NULL 
		DROP TABLE #tmpsbQueueC;
	IF OBJECT_ID('tempdb..#tblMCQRunMCL') IS NOT NULL 
		DROP TABLE #tblMCQRunMCL;
	CREATE TABLE #tmpsbQueueM (memberID int);
	CREATE TABLE #tmpsbQueueC (conditionID int);
	CREATE TABLE #tblMCQRunMCL (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tmpsbQueueM (memberID)
	select M.member.value('.','int')
	from @xmlMessage.nodes('/mc/m') as M(member);

	INSERT INTO #tmpsbQueueC (conditionID)
	select C.condition.value('.','int')
	from @xmlMessage.nodes('/mc/c') as C(condition);

	insert into #tblMCQRunMCL (orgID, memberID, conditionID)
	select o.orgID, nullIf(tmpM.memberid,0), nullIf(tmpC.conditionID,0)
	from (select @orgID as orgID) as o
	outer apply #tmpsbQueueM as tmpM
	outer apply #tmpsbQueueC as tmpC;

	IF @processType = 'ConditionsOnly' OR @processType = 'ConditionsOnlyNonImm' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberConditions', @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;
		
		insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
		select distinct @itemGroupUID, @processType, @triggerLogID, @orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
		from #tblMCQRunMCL;

		EXEC dbo.queue_MemberConditions_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	IF @processType = 'GroupsOnly' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberGroups', @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

		IF EXISTS (select 1 from #tblMCQRunMCL where memberID is null)
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
			from membercentral.dbo.ams_members
			where orgID = @orgID
			and status <> 'D';
		ELSE 
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
			from #tblMCQRunMCL;

		select @xmldataFinal = isnull((
			select @orgID as o, @itemGroupUID as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');	

		EXEC dbo.queue_MemberGroups_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	IF @processType = 'ConditionsAndGroups' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberGroups', @queueStatus='notReady', @queueStatusID=@notReadyQueueStatusID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberConditions', @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;

		insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
		select distinct @itemGroupUID, @processType, @triggerLogID, @orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
		from #tblMCQRunMCL;

		IF EXISTS (select 1 from #tblMCQRunMCL where memberID is null)
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @notReadyQueueStatusID
			from membercentral.dbo.ams_members
			where orgID = @orgID
			and status <> 'D';
		ELSE 
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @notReadyQueueStatusID
			from #tblMCQRunMCL;

		EXEC dbo.queue_MemberConditions_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	IF @processType = 'ConditionsAndGroupsChanged' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberConditions', @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;
		
		insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
		select distinct @itemGroupUID, @processType, @triggerLogID, @orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
		from #tblMCQRunMCL;

		EXEC dbo.queue_MemberConditions_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	on_done:
	IF OBJECT_ID('tempdb..#tmpsbQueueM') IS NOT NULL 
		DROP TABLE #tmpsbQueueM;
	IF OBJECT_ID('tempdb..#tmpsbQueueC') IS NOT NULL 
		DROP TABLE #tmpsbQueueC;
	IF OBJECT_ID('tempdb..#tblMCQRunMCL') IS NOT NULL 
		DROP TABLE #tblMCQRunMCL;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
