ALTER PROC dbo.fs_importDepoDocumentFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @doneStatusID int, @fileShareID int,
		@depoDocumentID int, @depoDocumentDate datetime, @depoDocumentDesc varchar(max), @depoDocumentTitle varchar(200),
		@depoObjectKey varchar(400), @siteID int, @rootSectionID int, @memberID int, @recordedByMemberID int, @s3CopyReadyStatusID int,
		@categoryIDList varchar(max), @fsObjectKeyPrefix varchar(200), @documentResourceTypeID int, 
		@fileName varchar(255), @documentID int, @documentVersionID int, @documentSiteResourceID int, @documentLanguageID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='depoDocsToFileShare2', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;

	-- get info from queue item
	select @fileShareID=fileShareID, @depoDocumentID=depoDocumentID, @depoDocumentDate=depoDocumentDate, 
		@depoDocumentDesc=depoDocumentDesc, @depoDocumentTitle=depoDocumentTitle, @depoObjectKey=depoObjectKey, 
		@siteID=siteID, @rootSectionID=rootSectionID, @memberID=memberID, @recordedByMemberID=recordedByMemberID, 
		@categoryIDList=categoryIDList, @fsObjectKeyPrefix=fsObjectKeyPrefix, 
		@fileName = cast(depoDocumentID as varchar(10)) + '.pdf'
	from platformQueue.dbo.queue_depoDocsToFileShare2
	where itemID = @itemID
	and statusID = @readyToProcessStatusID;

	IF @fileShareID IS NULL
		goto on_done;

	update platformQueue.dbo.queue_depoDocsToFileShare2
	set statusID = @processingStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	select @documentResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedDocument');

	BEGIN TRAN;
		EXEC dbo.cms_createDocument @siteID=@siteID, @resourceTypeID=@documentResourceTypeID, @siteResourceStatusID=1,
			@languageID=1, @sectionID=@rootSectionID, @contributorMemberID=@memberID, @recordedByMemberID=@recordedByMemberID, 
			@isActive=1, @isVisible=1, @docTitle=@depoDocumentTitle, @docDesc=@depoDocumentDesc, @author=null, 
			@publicationDate=@depoDocumentDate, @fileName=@fileName, @fileExt='pdf', @documentID=@documentID OUTPUT, 
			@documentVersionID=@documentVersionID OUTPUT, @documentSiteResourceID=@documentSiteResourceID OUTPUT;

		UPDATE dbo.cms_documents
		SET importedDocumentID = @depoDocumentID
		WHERE documentID = @documentID;

		INSERT INTO dbo.cms_categorySiteResources (categoryID, siteResourceID) 
		select listItem, @documentSiteResourceID
		from dbo.fn_intListToTable(@categoryIDList,',')
		where listitem is not null;

		-- add to the S3 Copy queue because we are going to copy it
		INSERT INTO platformQueue.dbo.queue_S3Copy (statusID, s3bucketName, objectKey, newS3bucketName, newObjectKey, dateAdded, dateUpdated)
		VALUES (@s3CopyReadyStatusID, 'trialsmith-depos',  @depoObjectKey, 'membercentralcdn', 
				@fsObjectKeyPrefix + right('0000' + cast(@documentVersionID % 1000 as varchar(4)),4) + '/' + cast(@documentVersionID as varchar(10)) + '.pdf',
				GETDATE(), GETDATE());

		-- copy the search terms 
		select @documentLanguageID = documentLanguageID from dbo.cms_documentLanguages where documentID = @documentID and languageID = 1;

		INSERT INTO searchMC.dbo.cms_documentVersions (documentVersionID, searchtext, documentLanguageID, documentID)
		select @documentVersionID, dbo.fn_RegExReplace(searchtext, '\*%\*%\*%\*%\*%\*%\*tsdocbeginheader\*%\*%\*%\*%\*%\*%\*.*$', ''), @documentLanguageID, null
		from search.dbo.depoDocuments 
		where documentID = @depoDocumentID;
	COMMIT TRAN;	

	update platformQueue.dbo.queue_depoDocsToFileShare2
	set statusID = @doneStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	delete from platformQueue.dbo.queue_depoDocsToFileShare2
	where itemID = @itemID;
	
	on_done:
	RETURN 0;
	
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
