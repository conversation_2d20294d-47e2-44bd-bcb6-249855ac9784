<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.processQueueResult = processQueue(strTask=arguments.strTask, batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.totalItemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "totalItemCount":0 }>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_importBlogEntryJSON_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryJSONEntries" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.totalItemCount = val(local.qryJSONEntries.totalItemCount)>

			<cfloop query="local.qryJSONEntries">
				<cfset updateQueueStatus(itemIDList=local.qryJSONEntries.itemID, queueStatus="Processing")>
				<cfset local.strJSONData = deserializeJSON(local.qryJSONEntries.blogEntryJSON)>
				<cfset processJSONImport(itemID=local.qryJSONEntries.itemID, strJSONData=local.strJSONData)>
			</cfloop>

			<cfset sendEmailReportsForProcessedEntries(strTask=arguments.strTask)>

			<cfset deleteCompletedQueueEntries()>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="validateAndPrepJSON" access="private" output="false" returntype="array">
		<cfargument name="strJSONData" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.arrEntryErrors = []>
		<cfset local.arrOtherErrors = []>

		<cfif StructIsEmpty(arguments.strJSONData)>
			<cfset arrayAppend(local.arrOtherErrors, "Empty JSON.")>
		<cfelse>
			<!--- we have no use for this fields, but validating anyway --->
			<cfset local.arrRequiredFields = ["version","title"]>
			<cfloop array="#local.arrRequiredFields#" item="local.thisField">
				<cfif not structKeyExists(arguments.strJSONData, local.thisField) or not len(arguments.strJSONData[local.thisField])>
					<cfset arrayAppend(local.arrOtherErrors, "No value found for [#local.thisField#].")>
				</cfif>
			</cfloop>
			<cfif structKeyExists(arguments.strJSONData, "language") and listfirst(arguments.strJSONData.language,"-") neq "en">
				<cfset arrayAppend(local.arrOtherErrors, "LanguageCode ""#arguments.strJSONData.language#"" is not valid. Only ""en"" is accepted.")>
			</cfif>
		</cfif>

		<cfif NOT ArrayLen(local.arrOtherErrors)>
			<cfif not structKeyExists(arguments.strJSONData,"language")>
				<cfset arguments.strJSONData.language = "en">
			</cfif>
			<cfif not structKeyExists(arguments.strJSONData,"items") or not isArray(arguments.strJSONData.items) or arrayIsEmpty(arguments.strJSONData.items)>
				<cfset arrayAppend(local.arrOtherErrors, "Invalid or empty value found for items.")>
			<cfelse>
				<cfloop array="#arguments.strJSONData.items#" item="local.thisEntry">
					<cfif not structKeyExists(local.thisEntry, "title")>
						<cfset arrayAppend(local.arrOtherErrors, "No value found for [title] within items.")>
					<cfelse>
						<cfset local.tmpErrors = []>

						<cfif len(local.thisEntry.title) gt 1000>
							<cfset arrayAppend(local.tmpErrors, "Given [title] ""#local.thisEntry.title#"" has #len(local.thisEntry.title)# characters. Max length is 1000.")>
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "id")>
							<cfset arrayAppend(local.tmpErrors, "No value found for [id].")>
						<cfelseif len(local.thisEntry.id) gt 200>
							<cfset arrayAppend(local.tmpErrors, "Given [id] ""#local.thisEntry.id#"" has #len(local.thisEntry.id)# characters. Max length is 200.")>
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "_quickLink")>
							<cfset local.thisEntry._quickLink = "">
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "_status")>
							<cfset local.thisEntry._status = "Approved">
						<cfelseif len(local.thisEntry._status) gt 20>
							<cfset arrayAppend(local.tmpErrors, "Given [_status] ""#local.thisEntry._status#"" has #len(local.thisEntry._status)# characters. Max length is 20.")>
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "_isSticky")>
							<cfset local.thisEntry._isSticky = false>
						<cfelseif not isBoolean(local.thisEntry._isSticky)>
							<cfset arrayAppend(local.tmpErrors, "Invalid value found for [_isSticky].")>
						</cfif>
						<!--- this is a required field, however if site has only one postType, that will be used --->
						<cfif not structKeyExists(local.thisEntry, "_postType")>
							<cfset local.thisEntry._postType = "">
						<cfelseif len(local.thisEntry._postType) gt 20>
							<cfset arrayAppend(local.tmpErrors, "Given [_postType] ""#local.thisEntry._postType#"" has #len(local.thisEntry._postType)# characters. Max length is 20.")>
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "content_html")>
							<cfset arrayAppend(local.tmpErrors, "No value found for [content_html].")>
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "summary")>
							<cfset local.thisEntry.summary = "">
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "date_published") or not len(local.thisEntry.date_published)>
							<cfset arrayAppend(local.tmpErrors, "No value found for [date_published].")>
						<cfelseif not IsDate(local.thisEntry.date_published)>
							<cfset arrayAppend(local.tmpErrors, "Invalid value found for [date_published].")>
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "_articleDate")>
							<cfset local.thisEntry._articleDate = "">
						<cfelseif len(local.thisEntry._articleDate) and not IsDate(local.thisEntry._articleDate)>
							<cfset arrayAppend(local.tmpErrors, "Invalid value found for [_articleDate].")>
						</cfif>
						<cfif not structKeyExists(local.thisEntry, "_expirationDate")>
							<cfset local.thisEntry._expirationDate = "">
						<cfelseif len(local.thisEntry._expirationDate) and not IsDate(local.thisEntry._expirationDate)>
							<cfset arrayAppend(local.tmpErrors, "Invalid value found for [_expirationDate].")>
						</cfif>

						<cfif not structKeyExists(local.thisEntry,"authors")>
							<cfset local.thisEntry.authors = []>
						<cfelseif not isArray(local.thisEntry.authors)>
							<cfset arrayAppend(local.tmpErrors, "Invalid value found for authors.")>
						<cfelse>
							<cfloop array="#local.thisEntry.authors#" item="local.thisAuthor">
								<cfif not isStruct(local.thisAuthor)>
									<cfset arrayAppend(local.tmpErrors, "Invalid value found within authors.")>
								<cfelseif not structKeyExists(local.thisAuthor, "_membernumber")>
									<cfset arrayAppend(local.tmpErrors, "No value found for [_membernumber] within authors.")>
								</cfif>
							</cfloop>
						</cfif>

						<cfif not structKeyExists(local.thisEntry,"tags")>
							<cfset local.thisEntry.tags = []>
						<cfelseif not isArray(local.thisEntry.tags)>
							<cfset arrayAppend(local.tmpErrors, "Invalid value found for tags.")>
						<cfelse>
							<cfloop array="#local.thisEntry.tags#" item="local.thisTag">
								<cfif not isValid("string", local.thisTag)>
									<cfset arrayAppend(local.tmpErrors, "Invalid value found within tags.")>
								</cfif>
							</cfloop>
						</cfif>

						<cfif not structKeyExists(local.thisEntry,"_tagsAfterExpiration")>
							<cfset local.thisEntry._tagsAfterExpiration = []>
						<cfelseif not isArray(local.thisEntry._tagsAfterExpiration)>
							<cfset arrayAppend(local.tmpErrors, "Invalid value found for _tagsAfterExpiration.")>
						<cfelse>
							<cfloop array="#local.thisEntry.tags#" item="local.thisTag">
								<cfif not isValid("string", local.thisTag)>
									<cfset arrayAppend(local.tmpErrors, "Invalid value found within _tagsAfterExpiration.")>
								</cfif>
							</cfloop>
						</cfif>

						<cfif arrayLen(local.tmpErrors)>
							<cfset arrayAppend(local.arrEntryErrors, {title: local.thisEntry.title, arrErrors: local.tmpErrors})>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>

		<cfset local.arrErrors = []>
		<cfset arrayAppend(local.arrErrors, local.arrOtherErrors, true)>
		<cfset local.errorEntriesCount = arrayLen(local.arrEntryErrors)>
		<cfif local.errorEntriesCount>
			<cfloop array="#local.arrEntryErrors#" item="local.thisEntryErrors">
				<cfset local.thisEntryTitle = local.thisEntryErrors.title>
				<cfloop array="#local.thisEntryErrors.arrErrors#" item="local.thisError">
					<!--- append entry title with error if there are multiple entries with errors --->
					<cfset arrayAppend(local.arrErrors, local.thisError & (local.errorEntriesCount gt 1 ? ' [#local.thisEntryTitle#]' : ''))>
				</cfloop>
			</cfloop>
		</cfif>

		<cfreturn local.arrErrors>
	</cffunction>

	<cffunction name="processJSONImport" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="strJSONData" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.arrErrors = validateAndPrepJSON(strJSONData=arguments.strJSONData)>

		<cfif NOT ArrayLen(local.arrErrors)>
			<cftry>
				<cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##mc_blogEntryJSONImport') IS NOT NULL
							DROP TABLE ##mc_blogEntryJSONImport;
						IF OBJECT_ID('tempdb..##mc_blogEntryJSONImport_authors') IS NOT NULL
							DROP TABLE ##mc_blogEntryJSONImport_authors;
						IF OBJECT_ID('tempdb..##mc_blogEntryJSONImport_categories') IS NOT NULL
							DROP TABLE ##mc_blogEntryJSONImport_categories;

						DECLARE @itemID int, @success bit, @errorMessage varchar(max), @importEntryID int;
						SET @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;

						CREATE TABLE ##mc_blogEntryJSONImport(importEntryID int IDENTITY(1,1), itemID int, externalImportID varchar(200), blogTitle varchar(1000), isSticky bit,
							postTypeName varchar(20), blogContent varchar(max), summaryContent varchar(max), statusName varchar(20), articleDate datetime, postDate datetime,
							expirationDate datetime, quickLink varchar(1000));
						CREATE TABLE ##mc_blogEntryJSONImport_authors(autoID int IDENTITY(1,1), importEntryID int, memberNumber varchar(50));
						CREATE TABLE ##mc_blogEntryJSONImport_categories(autoID int IDENTITY(1,1), importEntryID int, categoryTreeName varchar(50), categoryPath varchar(max),
							isArchiveCategory bit, categoryString varchar(max));

						-- fill holding tables with data from json
						<cfset local.RFC3339DateFormat = "yyyy-mm-dd'T'HH:nn:ss.lllXXX">
						<cfloop array="#arguments.strJSONData.items#" item="local.thisEntry">
							INSERT INTO ##mc_blogEntryJSONImport(itemID, externalImportID, blogTitle, isSticky, postTypeName, blogContent, summaryContent, statusName,
								articleDate, postDate, expirationDate, quickLink)
							VALUES (
								@itemID,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisEntry.id#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisEntry.title#">,
								<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisEntry._isSticky#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisEntry._postType#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisEntry.content_html#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisEntry.summary#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisEntry._status#">,
								<cfif len(local.thisEntry._articleDate)>
									<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#parseDateTime(local.thisEntry._articleDate, local.RFC3339DateFormat)#">,
								<cfelse>
									NULL,
								</cfif>
								<cfif len(local.thisEntry.date_published)>
									<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#parseDateTime(local.thisEntry.date_published, local.RFC3339DateFormat)#">,
								<cfelse>
									NULL,
								</cfif>
								<cfif len(local.thisEntry._expirationDate)>
									<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#parseDateTime(local.thisEntry._expirationDate, local.RFC3339DateFormat)#">,
								<cfelse>
									NULL,
								</cfif>
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisEntry._quickLink#">
							);
							SELECT @importEntryID = SCOPE_IDENTITY();

							<cfloop array="#local.thisEntry.authors#" item="local.thisAuthor">
								INSERT INTO ##mc_blogEntryJSONImport_authors(importEntryID, memberNumber)
								VALUES (@importEntryID, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisAuthor._membernumber#">);
							</cfloop>

							<cfset local.arrTags = []>
							<cfloop array="#local.thisEntry.tags#" item="local.thisCategory">
								<cfset arrayAppend(local.arrTags, { categoryString:local.thisCategory, isArchiveCategory:0 })>
							</cfloop>
							<cfloop array="#local.thisEntry._tagsAfterExpiration#" item="local.thisCategory">
								<cfset arrayAppend(local.arrTags, { categoryString:local.thisCategory, isArchiveCategory:1 })>
							</cfloop>

							<cfloop array="#local.arrTags#" item="local.thisCategory">
								<cfset local.thisCategoryTreeName = "">
								<cfif listLen(local.thisCategory.categoryString, "::") gt 1>
									<cfset local.thisCategoryTreeName = GetToken(local.thisCategory.categoryString, 1, "::")>
									<cfset local.thisCategoryPath = GetToken(local.thisCategory.categoryString, 2 , "::")>
								<cfelse>
									<cfset local.thisCategoryPath = local.thisCategory.categoryString>
								</cfif>
								<cfset local.thisCategoryPath = replace(local.thisCategoryPath, "|", "\", "all")>

								INSERT INTO ##mc_blogEntryJSONImport_categories(importEntryID, categoryTreeName, categoryPath, isArchiveCategory, categoryString)
								VALUES (
									@importEntryID,
									<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(local.thisCategoryTreeName,50)#">,
									<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisCategoryPath#">,
									<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisCategory.isArchiveCategory#">,
									<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisCategory.categoryString#">
								);
							</cfloop>
						</cfloop>

						-- import blog entry from json
						BEGIN TRY
							SET @success = NULL;
							EXEC dbo.bl_importBlogEntryJSON @itemID=@itemID, @success=@success OUTPUT;
						END TRY
						BEGIN CATCH
							SET @success = 0;
							SET @errorMessage = error_message();
						END CATCH

						SELECT @success AS success, @errorMessage AS errorMessage;

						IF OBJECT_ID('tempdb..##mc_blogEntryJSONImport') IS NOT NULL
							DROP TABLE ##mc_blogEntryJSONImport;
						IF OBJECT_ID('tempdb..##mc_blogEntryJSONImport_authors') IS NOT NULL
							DROP TABLE ##mc_blogEntryJSONImport_authors;
						IF OBJECT_ID('tempdb..##mc_blogEntryJSONImport_categories') IS NOT NULL
							DROP TABLE ##mc_blogEntryJSONImport_categories;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfif local.qryImport.recordCount and val(local.qryImport.success) eq 0 and len(local.qryImport.errorMessage)>
					<cfset arrayAppend(local.arrErrors, "There was a problem importing the blog entries JSON:" & local.qryImport.errorMessage)>
				</cfif>
			<cfcatch type="Any">
				<cfset arrayAppend(local.arrErrors, "There was a problem importing the blog entries JSON: " & cfcatch.message & (len(cfcatch.detail) ? " " & cfcatch.detail : ""))>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfif ArrayLen(local.arrErrors)>
			<cfquery name="local.updateErrorMessage" datasource="#application.dsn.platformQueue.dsn#">
				UPDATE dbo.queue_importBlogEntryJSON
				SET errorMessage = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#ArrayToList(local.arrErrors,"^--^")#">
				WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
			</cfquery>
		</cfif>

		<cfset updateQueueStatus(itemIDList=arguments.itemID, queueStatus="ReadyToNotify")>
	</cffunction>

	<cffunction name="sendEmailReportsForProcessedEntries" access="private" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">

		<cfset var local = structNew()>

		<cfstoredproc procedure="queue_importBlogEntryJSON_grabForNotifying" datasource="#application.dsn.platformQueue.dsn#">
			<cfprocresult name="local.qryNotifyEntries" resultset="1">
		</cfstoredproc>

		<cfoutput query="local.qryNotifyEntries" group="itemGroupUID">
			<cfset local.arrImportedEntries = []>
			<cfset local.arrEntryErrors = []>
			<cfset local.arrOtherErrorMsgs = []>
			<cfset local.itemIDList = "">

			<cftry>
				<cfoutput>
					<cfset local.thisJSONFeedTitle = "">
					<cfif isJSON(local.qryNotifyEntries.blogEntryJSON)>
						<cfset local.strJSONData = deserializeJSON(local.qryNotifyEntries.blogEntryJSON)>
						<cfif structKeyExists(local.strJSONData, "title")>
							<cfset local.thisJSONFeedTitle = local.strJSONData.title>
						</cfif>
					</cfif>
					<cfif len(local.qryNotifyEntries.errorMessage)>
						<cfif len(local.thisJSONFeedTitle)>
							<cfset arrayAppend(local.arrEntryErrors, {
								title: local.thisJSONFeedTitle,
								errMsgs: ListToArray(local.qryNotifyEntries.errorMessage,"^--^",false,true)
							})>
						<cfelse>
							<cfset arrayAppend(local.arrOtherErrorMsgs, ListToArray(local.qryNotifyEntries.errorMessage,"^--^",false,true), "true")>
						</cfif>
					<cfelse>
						<!--- get successfully imported entries info for email --->
						<cfset local.strJSONData = deserializeJSON(local.qryNotifyEntries.blogEntryJSON)>
						<cfset local.arrItems = []>
						<cfloop array="#local.strJSONData.items#" item="local.thisEntry">
							<cfset arrayAppend(local.arrItems, local.thisEntry.title)>
						</cfloop>

						<cfset ArrayAppend(local.arrImportedEntries, {JSONFeedTitle:local.strJSONData.title, arrItems:local.arrItems})>
					</cfif>

					<cfset local.itemIDList = listAppend(local.itemIDList, local.qryNotifyEntries.itemID)>
				</cfoutput>

				<cfsavecontent variable="local.notificationMessage">
					<div>
						<p>Hi #local.qryNotifyEntries.memberName#:</p>
						<p>We have completed processing your JSON files for Blog Entries Import.</p>
						<p>Blog Name: <b>#local.qryNotifyEntries.blogName#</b></p>
						<cfif arrayLen(local.arrEntryErrors) or arrayLen(local.arrOtherErrorMsgs)>
							<p>The following errors occurred while attempting to import blog entries from JSON.</p>
							<cfif arrayLen(local.arrEntryErrors)>
								<cfloop array="#local.arrEntryErrors#" item="local.thisErrorInfo">
									<div>Title: <b>#local.thisErrorInfo.title#</b></div>
									<ul style="margin-bottom:16px;">
										<cfloop array="#local.thisErrorInfo.errMsgs#" item="local.thisErrMsg">
											<li>#local.thisErrMsg#</li>
										</cfloop>
									</ul>
								</cfloop>
							</cfif>
							<cfif arrayLen(local.arrOtherErrorMsgs)>
								<cfif arrayLen(local.arrEntryErrors)>
									<div>Errors from files with no title in the JSON:</div>
								</cfif>
								<ul style="margin-bottom:16px;">
									<cfloop array="#local.arrOtherErrorMsgs#" item="local.thisErrMsg">
										<li>#local.thisErrMsg#</li>
									</cfloop>
								</ul>
							</cfif>
						</cfif>
						<cfif arrayLen(local.arrImportedEntries)>
							<p>The following entries were imported successfully.</p>
							<ul>
								<cfloop array="#local.arrImportedEntries#" item="local.thisEntry">
									<cfif arrayLen(local.thisEntry.arrItems) gt 1>
										<li>
											#local.thisEntry.JSONFeedTitle#
											<ul>
												<cfloop array="#local.thisEntry.arrItems#" item="local.thisItemTitle">
													<li>#local.thisItemTitle#</li>
												</cfloop>
											</ul>
										</li>
									<cfelse>
										<li>#ArrayFirst(local.thisEntry.arrItems)#</li>
									</cfif>
								</cfloop>
							</ul>
						</cfif>
					</div>
				</cfsavecontent>

				<cfif len(local.qryNotifyEntries.memberEmail)>
					<cfset local.sitecode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.qryNotifyEntries.siteID)>
					<cfset local.siteinfo = application.objSiteInfo.mc_siteInfo[local.sitecode]>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo['MC'].networkEmailFrom},
						emailto=[{ name=local.qryNotifyEntries.memberName, email=local.qryNotifyEntries.memberEmail }],
						emailreplyto="",
						emailsubject="JSON Blog Entries Import Report for #local.siteinfo.mainHostname# [JobID: #local.qryNotifyEntries.itemGroupUID#]",
						emailtitle="JSON Blog Entries Import Report for #local.siteinfo.mainHostname#",
						emailhtmlcontent=local.notificationMessage,
						siteID=application.objSiteInfo.mc_siteInfo['MC'].siteID,
						memberID=local.qryNotifyEntries.submittedMemberID,
						messageTypeID=arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="SCHEDTASK"),
						sendingSiteResourceID=application.objSiteInfo.mc_siteInfo['MC'].siteSiteResourceID
					)>
				</cfif>

				<cfset updateQueueStatus(itemIDList=local.itemIDList, queueStatus="Done")>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStruct.success = false>
			</cfcatch>
			</cftry>
		</cfoutput>
	</cffunction>

	<cffunction name="updateQueueStatus" access="private" output="false" returntype="void">
		<cfargument name="itemIDList" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @queueStatusID int;
				EXEC dbo.queue_getStatusIDbyType @queueType='importBlogEntryJSON', @queueStatus=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueStatus#">, @queueStatusID=@queueStatusID OUTPUT;

				UPDATE dbo.queue_importBlogEntryJSON
				SET statusID = @queueStatusID,
					dateUpdated = getdate()
				WHERE itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">);

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="deleteCompletedQueueEntries" access="private" output="false" returntype="void">

		<cfset var local = structNew()>

		<cfquery name="local.updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @statusDone int;
				EXEC dbo.queue_getStatusIDbyType @queueType='importBlogEntryJSON', @queueStatus='Done', @queueStatusID=@statusDone OUTPUT;

				WITH completedJobs AS (
					SELECT qi.itemID 
					FROM dbo.queue_importBlogEntryJSON AS qi
					WHERE qi.statusID = @statusDone
					AND NOT EXISTS (
						SELECT 1
						FROM dbo.queue_importBlogEntryJSON AS tmp
						WHERE tmp.itemGroupUID = qi.itemGroupUID
						AND tmp.statusID <> @statusDone
					)
				)
				DELETE qid
				FROM dbo.queue_importBlogEntryJSON AS qid
				INNER JOIN completedJobs AS batch ON batch.itemID = qid.itemID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		</cfquery>
	</cffunction>

</cfcomponent>