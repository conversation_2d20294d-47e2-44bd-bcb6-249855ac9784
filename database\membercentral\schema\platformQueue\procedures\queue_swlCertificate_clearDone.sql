ALTER PROC dbo.queue_swlCertificate_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='swlCertificate', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;
	
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_swlCertificate
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_swlCertificate
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_swlCertificate as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
