ALTER PROC dbo.ams_importMemberFromQueue
@jobMemberID int,
@runImmediately bit,
@actualMemberID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusToProcess int, @statusProcessing int, @itemStatus int, @bypassListEmailUpdate bit,
		@orgID int, @recordTypeIDChanged bit, @recordTypeID int, @memberTypeID int, @isNewMember bit, @jobID int, 
		@prefixChanged bit, @prefix varchar(50), @firstNameChanged bit, @firstName varchar(75), @middleNameChanged bit, 
		@middleName varchar(25), @lastNameChanged bit, @lastName varchar(75), @suffixChanged bit, @suffix varchar(50),
		@membernumber varchar(50), @professionalSuffixChanged bit, @professionalSuffix varchar(100), @companyChanged bit, 
		@company varchar(200), @defaultSiteID int, @emailTypeID int, @email varchar(255), @websiteTypeID int, @website varchar(400), 
		@PLTypeID int, @LicenseNumber varchar(200), @ActiveDate datetime, @PLStatusID int, @addressTypeID int, 
		@attn varchar(100), @address1 varchar(100), @address2 varchar(100), @address3 varchar(100), @city varchar(75), 
		@stateID int, @postalCode varchar(25), @county varchar(50), @countryID int, @attnChanged bit, @address1Changed bit, 
		@address2Changed bit, @address3Changed bit, @cityChanged bit, @stateIDChanged bit, @postalCodeChanged bit, 
		@countyChanged bit, @countryIDChanged bit, @addressID int, @addressTagTypeID int, @addressTagType varchar(60), 
		@jobPhoneID int, @phoneTypeID int, @phone varchar(40), @phoneID int, @memberTypeIDChanged bit, @condCount int,
		@columnID int, @dataTypeCode varchar(20), @allowMultiple bit, @columnName varchar(255), @columnValue varchar(max),
		@columnValueString varchar(255), @columnValueDecimal2 decimal(14,2), @columnValueInteger int, 
		@columnvalueDate date, @columnValueBit bit, @columnValueContent varchar(max), @columnValueSiteResourceID int, 
		@displayTypeCode varchar(20), @isHTML bit, @contentID int, @columnvalueID int, @thisValueString varchar(255), 
		@oldStatus char(1), @statusChanged bit, @newMemberNumber varchar(50), @memberNumberChanged bit, @xmlMessage xml, 
		@orgcode varchar(10), @photoPath varchar(250), @photoPathTh varchar(250), @oldPrimaryEmailAddr varchar(255), 
		@primaryEmailTypeID int, @status char(1), @cmd varchar(8000), @lyrExtMIDChgRdyToProcessStID int, @dataXML xml, 
		@memberAdminSRID int, @minSiteID int, @emailTagTypeID int, @emailTagType varchar(60), @enteredByMemberID int, 
		@emailChanged bit, @professionalLicenseInfoChanged bit;
	declare @tblEmail TABLE (emailTypeID int, email varchar(255));
	declare @tblWebsite TABLE (websiteTypeID int, website varchar(400));
	declare @tblMPL TABLE (PLTypeID int, LicenseNumber varchar(200), ActiveDate datetime, PLStatusID int);
	declare @tblAddress TABLE (addressTypeID int, attn varchar(100), address1 varchar(100), address2 varchar(100),
		address3 varchar(100), city varchar(75), stateID int, postalCode varchar(25), county varchar(50), 
		countryID int, attnChanged bit, address1Changed bit, address2Changed bit, address3Changed bit, 
		cityChanged bit, stateIDChanged bit, postalCodeChanged bit, countyChanged bit, countryIDChanged bit);
	declare @tblAddressTags TABLE (addressTagTypeID int, addressTypeID int, addressTagType varchar(60));
	declare @tblEmailTags TABLE (emailTagTypeID int, emailTypeID int, emailTagType varchar(60));
	declare @tblPhone TABLE (phoneID int, addressTypeID int, phoneTypeID int, phone varchar(40));
	declare @tblValues TABLE (columnID int, columnValueString varchar(255), columnValueDecimal2 decimal(14,2), 
		columnValueInteger int, columnvalueDate date, columnValueBit bit, columnValueContent varchar(max), 
		columnName varchar(255), dataTypeCode varchar(20), displayTypeCode varchar(20), allowMultiple bit, actualValueID int);
	declare @tblMemberRun TABLE (memberID int PRIMARY KEY, orgID int);

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='MemberImport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusToProcess OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='lyrisExtIDChange', @queueStatus='readyToProcess', @queueStatusID=@lyrExtMIDChgRdyToProcessStID OUTPUT;

	-- get member info
	select @itemStatus=imp.queueStatusID, @orgID=imp.orgID, @jobID=imp.jobID, @actualMemberID=imp.actualMemberID, 
		@recordTypeIDChanged=imp.recordTypeIDChanged, @recordTypeID=imp.recordTypeID, @prefixChanged=imp.prefixChanged, 
		@prefix=imp.prefix, @firstNameChanged=imp.firstNameChanged, @firstName=imp.firstName, @middleName=imp.middleName,
		@middleNameChanged=imp.middleNameChanged, @lastNameChanged=imp.lastNameChanged, @lastName=imp.lastname, 
		@suffixChanged=imp.suffixChanged, @suffix=imp.suffix, @membernumber=imp.membernumber, 
		@professionalSuffixChanged=imp.professionalSuffixChanged, @professionalSuffix=imp.professionalSuffix,
		@companyChanged=imp.companyChanged, @company=imp.company, @status=imp.status, @statusChanged=imp.statusChanged,
		@newMemberNumber=imp.newMemberNumber, @memberNumberChanged=imp.memberNumberChanged, @memberTypeID=imp.memberTypeID, 
		@memberTypeIDChanged=imp.memberTypeIDChanged, @enteredByMemberID=mih.runByMemberID, @bypassListEmailUpdate=j.bypassListEmailUpdate
	from platformQueue.dbo.memimport_members as imp
	inner join platformQueue.dbo.memimport_jobs as j on j.jobID = imp.jobID
	inner join platformStatsMC.dbo.ams_memberImportHistory as mih on mih.jobID = j.jobID
	where imp.memberID = @jobMemberID;

	-- if memberID is not readyToProcess, kick out now
	IF @itemStatus <> @statusToProcess OR @orgID is null
		GOTO on_done;

	update platformQueue.dbo.memimport_members
	set queueStatusID = @statusProcessing
	where memberID = @jobMemberID;

	-- if protected member, mark member as done
	IF @actualmemberID IS NOT NULL AND EXISTS (SELECT 1 FROM dbo.ams_members WHERE memberID = @actualmemberID AND isProtected = 1) BEGIN
		EXEC platformQueue.dbo.queue_MemberUpdate_markMemberDone @jobID=@jobID, @jobMemberID=@jobMemberID;
		GOTO on_done;
	END

	-- temp table to store the conditions that should be reprocessed
	IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL 
		DROP TABLE #tmpMCGConditions;
	CREATE TABLE #tmpMCGConditions (conditionID int);

	insert into @tblEmail (emailTypeID, email)
	select emailTypeID, email
	from platformQueue.dbo.memimport_memberEmails
	where memberID = @jobMemberID;

	insert into @tblEmailTags (emailTagTypeID, emailTypeID, emailTagType)
	select emailTagTypeID, emailTypeID, emailTagType
	from platformQueue.dbo.memimport_memberEmailTags
	where memberID = @jobMemberID;

	insert into @tblWebsite (websiteTypeID, website)
	select websiteTypeID, website
	from platformQueue.dbo.memimport_memberWebsites
	where memberID = @jobMemberID;

	insert into @tblMPL (PLTypeID, LicenseNumber, ActiveDate, PLStatusID)
	select PLTypeID, LicenseNumber, ActiveDate, PLStatusID
	from platformQueue.dbo.memimport_memberProfessionalLicenses
	where memberID = @jobMemberID;

	insert into @tblAddress (addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, 
		county, countryID, attnChanged, address1Changed, address2Changed, address3Changed, cityChanged, 
		stateIDChanged, postalCodeChanged, countyChanged, countryIDChanged)
	select addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, 
		county, countryID, attnChanged, address1Changed, address2Changed, address3Changed, cityChanged, 
		stateIDChanged, postalCodeChanged, countyChanged, countryIDChanged
	from platformQueue.dbo.memimport_memberAddresses
	where memberID = @jobMemberID;

	insert into @tblAddressTags (addressTagTypeID, addressTypeID, addressTagType)
	select addressTagTypeID, addressTypeID, addressTagType
	from platformQueue.dbo.memimport_memberAddressTags
	where memberID = @jobMemberID;

	insert into @tblPhone (phoneID, addressTypeID, phoneTypeID, phone)
	select phoneID, addressTypeID, phoneTypeID, phone
	from platformQueue.dbo.memimport_memberPhones
	where memberID = @jobMemberID;

	insert into @tblValues (columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, 
		columnValueBit, columnValueContent, columnName, dataTypeCode, displayTypeCode, allowMultiple, actualValueID)
	select columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, columnValueBit, 
		columnValueContent, columnName, dataTypeCode, displayTypeCode, allowMultiple, actualValueID
	from platformQueue.dbo.memimport_memberDataColumnValues
	where memberID = @jobMemberID;

	select @defaultSiteID = dbo.fn_getDefaultSiteIDFromOrgID(@orgID);
	select @orgcode = orgcode from dbo.organizations where orgID = @orgID;

	SET @emailChanged = CASE WHEN EXISTS(SELECT 1 FROM @tblEmail) THEN 1 ELSE 0 END;
	SET @professionalLicenseInfoChanged = CASE WHEN EXISTS(SELECT 1 FROM @tblMPL) THEN 1 ELSE 0 END;
	
	IF @memberNumberChanged = 1 BEGIN
		select @photoPath = userAssetsPath + LOWER(@orgcode) + '\memberphotos\',
			@photoPathTh = userAssetsPath + LOWER(@orgcode) + '\memberphotosth\'
		from dbo.fn_getServerSettings();
	END

	BEGIN TRAN;
		-- If actualmemberID is 0, then create the account with basic info and let the remaining code update it
		IF @actualMemberID is null BEGIN
			EXEC dbo.ams_createMember @orgID=@orgID, @memberTypeID=@memberTypeID, @recordTypeID=@recordTypeID, @prefix=@prefix, @firstname=@firstName, 
				@middlename=@middlename, @lastname=@lastName, @suffix=@suffix, @professionalsuffix=@professionalSuffix, 
				@company=@company, @memberNumber=@membernumber, @status=@status, @bypassQueue=1, @bypassHook=0,
				@memberID=@actualMemberID OUTPUT, @newMemberNumber=@membernumber OUTPUT;

			-- create defaults on each site in the org
			select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID;
			while @minSiteID is not null begin
				EXEC dbo.ams_createMemberSiteDefault @memberID=@actualMemberID, @siteID=@minSiteID, @defaultUsername=null, @defaultPassword=null;
				select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID and siteID > @minSiteID;
			end

			UPDATE platformQueue.dbo.memimport_members SET newMemberID = @actualMemberID WHERE memberID = @jobMemberID;

			SET @isNewMember = 1;
		END ELSE 
			SET @isNewMember = 0;

		IF @memberNumberChanged = 1 BEGIN
			update dbo.ams_members set membernumber = @newMemberNumber, datelastupdated = getdate() where memberID = @actualMemberID;

			-- update lyris. cant run proc here because of distributed transaction issue.
			INSERT INTO platformQueue.dbo.queue_lyrisExtIDChange (orgcode, oldExternalID, newExternalID, statusID)
			VALUES (@orgcode, @membernumber, @newMemberNumber, @lyrExtMIDChgRdyToProcessStID);
		
			-- update any memberphotos
			IF dbo.fn_FileExists(@photoPath + LOWER(@membernumber) + '.jpg') = 1 BEGIN
				set @cmd = 'move ' + @photoPath + LOWER(@membernumber) + '.jpg ' + @photoPath + LOWER(@newMemberNumber) + '.jpg';
				EXEC master..xp_cmdshell @cmd, NO_OUTPUT;
			END
			IF dbo.fn_FileExists(@photoPathTh + LOWER(@membernumber) + '.jpg') = 1 BEGIN
				set @cmd = 'move ' + @photoPathTh + LOWER(@membernumber) + '.jpg ' + @photoPathTh + LOWER(@newMemberNumber) + '.jpg';
				EXEC master..xp_cmdshell @cmd, NO_OUTPUT;
			END

			insert into #tmpMCGConditions (conditionID)
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode = 'm_membernumber'
				except 
			select conditionID from #tmpMCGConditions;
		END

		IF @recordTypeIDChanged = 1 BEGIN
			update dbo.ams_members set recordTypeID = @recordTypeID, datelastupdated = getdate() where memberID = @actualMemberID;

			insert into #tmpMCGConditions (conditionID)
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode = 'rt_role'
				except 
			select conditionID from #tmpMCGConditions;
		END

		IF @memberTypeIDChanged = 1 BEGIN
			update dbo.ams_members set memberTypeID = @memberTypeID, datelastupdated = getdate() where memberID = @actualMemberID;

			-- if changing to a guest, ensure we have a default login set on each site in the org
			IF @isNewMember = 0 and @memberTypeID = 1 BEGIN
				select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID;
				while @minSiteID is not null begin
					EXEC dbo.ams_createMemberSiteDefault @memberID=@actualMemberID, @siteID=@minSiteID, @defaultUsername=null, @defaultPassword=null;
					select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID and siteID > @minSiteID;
				end
			END
			
			insert into #tmpMCGConditions (conditionID)
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode = 'm_membertypeid'
				except 
			select conditionID from #tmpMCGConditions;
		END

		IF @actualMemberID is not null BEGIN
			IF @prefixChanged = 1 BEGIN
				update dbo.ams_members set prefix = @prefix, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_prefix'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @firstNameChanged = 1 BEGIN
				update dbo.ams_members set firstname = @firstName, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_firstname'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @middleNameChanged = 1 BEGIN
				update dbo.ams_members set middlename = @middleName, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_middlename'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @lastNameChanged = 1 BEGIN
				update dbo.ams_members set lastname = @lastname, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_lastname'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @suffixChanged = 1 BEGIN
				update dbo.ams_members set suffix = @suffix, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_suffix'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @professionalSuffixChanged = 1 BEGIN
				update dbo.ams_members set professionalSuffix = @professionalSuffix, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_professionalsuffix'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @companyChanged = 1 BEGIN
				update dbo.ams_members set company = @company, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_company'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @statusChanged = 1 BEGIN
				select @oldStatus = [status] from dbo.ams_members where memberID = @actualMemberID;

				update dbo.ams_members set [status] = @status, datelastupdated = getdate() where memberID = @actualMemberID;

				if @oldStatus = 'I' BEGIN
					declare @msdefaultID int;

					select top 1 @msdefaultID = defaultID 
					from dbo.ams_memberSiteDefaults as msd
					inner join dbo.sites as s on s.orgID = @orgID and s.siteID = msd.siteID and msd.memberID = @actualMemberID 
					and msd.status='I';
					
					update dbo.ams_memberSiteDefaults
					set status = 'A'
					where defaultID = @msdefaultID;
				END

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_status'
					except 
				select conditionID from #tmpMCGConditions;
			END
		END

		IF @emailChanged = 1 BEGIN
			if @bypassListEmailUpdate = 0 and @isNewMember = 0 
				select top 1 @oldPrimaryEmailAddr = me.email, @primaryEmailTypeID = metag.emailTypeID
				from dbo.ams_memberEmails as me 
				inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
					and metag.memberID = me.memberID
					and metag.emailTypeID = me.emailTypeID
				inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
					and metagt.emailTagTypeID = metag.emailTagTypeID
					and metagt.emailTagType = 'Primary'
				where me.orgID = @orgID
				and me.memberID = @actualmemberID;

			select @emailTypeID = min(emailTypeID) from @tblEmail;
			while @emailTypeID is not null begin
				set @email = null;
				
				select @email=email from @tblEmail where emailTypeID = @emailTypeID;
				EXEC dbo.ams_saveMemberEmail @memberid=@actualMemberID, @emailTypeID=@emailTypeID, @email=@email, @enteredByMemberID=@enteredByMemberID, @byPassQueue=1;

				-- update lyris list email, cant run proc here because of distributed transaction issue.
				if @bypassListEmailUpdate = 0 and @isNewMember = 0 and isnull(@email,'') <> '' and @primaryEmailTypeID = @emailTypeID and isnull(@oldPrimaryEmailAddr,'') <> ''
					INSERT INTO platformQueue.dbo.queue_lyrisListEmailChange (orgcode, memberNumber, oldEmailAddress, newEmailAddress, statusID)
					select @orgcode, case when @memberNumberChanged = 1 then @newMemberNumber else @memberNumber end, @oldPrimaryEmailAddr, @email, qs.queueStatusID
					from platformQueue.dbo.tblQueueStatuses as qs
					inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
					where qt.queueType = 'lyrisListEmailChange'
					and qs.queueStatus = 'readyToProcess';

				select @emailTypeID = min(emailTypeID) from @tblEmail where emailTypeID > @emailTypeID;
			end
		END

		IF EXISTS (SELECT 1 from @tblEmailTags) BEGIN
			select @emailTagTypeID = min(emailTagTypeID) from @tblEmailTags;
			while @emailTagTypeID is not null begin
				select @emailTypeID = null, @emailTagType = null;
				select @emailTypeID=emailTypeID, @emailTagType=emailTagType from @tblEmailTags where emailTagTypeID = @emailTagTypeID;
				EXEC dbo.ams_setMemberEmailTag @memberid=@actualMemberID, @orgID=@orgID, @emailTagType=@emailTagType, @emailTypeID=@emailTypeID, @byPassQueue=1;
				select @emailTagTypeID = min(emailTagTypeID) from @tblEmailTags where emailTagTypeID > @emailTagTypeID;
			end
		END

		IF EXISTS (select 1 from @tblWebsite) BEGIN
			select @websiteTypeID = min(websiteTypeID) from @tblWebsite;
			while @websiteTypeID is not null begin
				set @website = null;
				select @website=website from @tblWebsite where websiteTypeID = @websiteTypeID;
				EXEC dbo.ams_saveMemberWebsite @memberid=@actualMemberID, @websiteTypeID=@websiteTypeID, @website=@website, @byPassQueue=1;
				select @websiteTypeID = min(websiteTypeID) from @tblWebsite where websiteTypeID > @websiteTypeID;
			end
		END

		IF @professionalLicenseInfoChanged = 1 BEGIN
			select @PLTypeID = min(PLTypeID) from @tblMPL;
			while @PLTypeID is not null begin
				select @licenseNumber=null, @activeDate=null, @PLStatusID=null;
				select @licenseNumber=licenseNumber, @activeDate=activeDate, @PLStatusID=PLStatusID from @tblMPL where PLTypeID = @PLTypeID;
				EXEC dbo.ams_saveMemberProfessionalLicense @memberid=@actualMemberID, @PLTypeID=@PLTypeID, @licenseNumber=@licenseNumber, 
					@activeDate=@activeDate, @PLStatusID=@PLStatusID, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
				select @PLTypeID = min(PLTypeID) from @tblMPL where PLTypeID > @PLTypeID;
			end
		END

		IF EXISTS (select 1 from @tblAddress) BEGIN
			select @addressTypeID = min(addressTypeID) from @tblAddress;
			while @addressTypeID is not null begin
				select @attn=null, @address1=null, @address2=null, @address3=null, @city=null, @stateID=null,
					@postalCode=null, @county=null, @countryID=null, @addressID=null;
				select @attn=attn, @address1=address1, @address2=address2, @address3=address3, @city=city, @stateID=stateID, 
					@postalCode=postalCode, @county=county, @countryID=countryID, @attnChanged=attnChanged, 
					@address1Changed=address1Changed, @address2Changed=address2Changed, @address3Changed=address3Changed, 
					@cityChanged=cityChanged, @stateIDChanged=stateIDChanged, @postalCodeChanged=postalCodeChanged, 
					@countyChanged=countyChanged, @countryIDChanged=countryIDChanged
				from @tblAddress
				where addressTypeID=@addressTypeID;

				IF @attnChanged=0 set @attn = null;
				IF @address1Changed=0 set @address1 = null;
				IF @address2Changed=0 set @address2 = null;
				IF @address3Changed=0 set @address3 = null;
				IF @cityChanged=0 set @city = null;
				IF @postalCodeChanged=0 set @postalCode = null;
				IF @countyChanged=0 set @county = null;

				EXEC dbo.ams_saveMemberAddress @memberid=@actualMemberID, @addressTypeID=@addressTypeID, @attn=@attn, @address1=@address1,
					@address2=@address2, @address3=@address3, @city=@city, @stateID=@stateID, @postalCode=@postalCode, @county=@county,
					@countryID=@countryID, @byPassQueue=1, @addressID=@addressID OUTPUT;
				
				select @addressTypeID = min(addressTypeID) from @tblAddress where addressTypeID > @addressTypeID;
			end
		END

		IF EXISTS (SELECT 1 from @tblAddressTags) BEGIN
			select @addressTagTypeID = min(addressTagTypeID) from @tblAddressTags;
			while @addressTagTypeID is not null begin
				select @addressTypeID = null, @addressTagType = null;
				select @addressTypeID=addressTypeID, @addressTagType=addressTagType from @tblAddressTags where addressTagTypeID = @addressTagTypeID;
				EXEC dbo.ams_setMemberAddressTag @memberid=@actualMemberID, @orgID=@orgID, @addressTagType=@addressTagType, @addressTypeID=@addressTypeID, @byPassQueue=1;
				select @addressTagTypeID = min(addressTagTypeID) from @tblAddressTags where addressTagTypeID > @addressTagTypeID;
			end
		END

		IF EXISTS (SELECT 1 from @tblPhone) BEGIN
			select @jobPhoneID = min(phoneID) from @tblPhone;
			while @jobPhoneID is not null begin
				select @addressTypeID = null, @addressID = null, @phoneTypeID = null, @phone = null, @phoneID = null;
				select @addressTypeID = addressTypeID, @phoneTypeID = phoneTypeID, @phone = phone from @tblPhone where phoneID = @jobPhoneID;

				select @addressID = addressID from dbo.ams_memberAddresses where orgID = @orgID and memberID=@actualMemberID and addressTypeID=@addressTypeID;
				IF @addressID is null 
					EXEC dbo.ams_saveMemberAddress @memberid=@actualMemberID, @addressTypeID=@addressTypeID, @attn=null, @address1=null,
						@address2=null, @address3=null, @city=null, @stateID=null, @postalCode=null, @county=null,
						@countryID=null, @byPassQueue=1, @addressID=@addressID OUTPUT;

				EXEC dbo.ams_saveMemberPhone @memberid=@actualMemberID, @addressID=@addressID, @phoneTypeID=@phoneTypeID, @phone=@phone, 
					@byPassQueue=1, @phoneID=@phoneID OUTPUT;

				select @jobPhoneID = min(phoneID) from @tblPhone where phoneID > @jobPhoneID;
			end
		END

		IF EXISTS (SELECT 1 from @tblValues) BEGIN
			select @columnID = min(columnID) from @tblValues;
			while @columnID is not null begin
				select @columnName = null, @dataTypeCode = null, @allowMultiple = null, @columnValueString = null, @columnValueDecimal2 = null, 
					@columnValueInteger = null, @columnvalueDate = null, @columnValueBit = null, @columnValueContent = null, @columnValueSiteResourceID = null;
				select @columnName = columnName, @dataTypeCode = dataTypeCode, @displayTypeCode = displayTypeCode, @allowMultiple = allowMultiple
				from @tblValues 
				where columnID = @columnID;

				IF @dataTypeCode <> 'CONTENTOBJ' BEGIN
					IF @allowMultiple = 0 BEGIN
						select @columnValueString = columnValueString, @columnValueDecimal2 = columnValueDecimal2, 
							@columnValueInteger = columnValueInteger, @columnvalueDate = columnvalueDate, @columnValueBit = columnValueBit
						from @tblValues 
						where columnID = @columnID;

						IF @columnValueString is not null OR @columnValueDecimal2 is not null OR @columnValueInteger is not null 
								OR @columnvalueDate is not null OR @columnValueBit is not null BEGIN
							IF @dataTypeCode = 'STRING'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueString, @byPassQueue=1;
							IF @dataTypeCode = 'DECIMAL2'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueDecimal2, @byPassQueue=1;
							IF @dataTypeCode = 'INTEGER'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueInteger, @byPassQueue=1;
							IF @dataTypeCode = 'DATE'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnvalueDate, @byPassQueue=1;
							IF @dataTypeCode = 'BIT'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueBit, @byPassQueue=1;
						END ELSE
							EXEC dbo.ams_deleteMemberData @memberID=@actualMemberID, @columnID=@columnID, @byPassQueue=1;
					END ELSE BEGIN
						select @columnValue = COALESCE(@columnValue + ',','') + cast(actualValueID as varchar(10)) from @tblValues where columnID = @columnID;
						IF @columnValue is not null
							EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValue, @byPassQueue=1;
						ELSE
							EXEC dbo.ams_deleteMemberData @memberID=@actualMemberID, @columnID=@columnID, @byPassQueue=1;
					END
				END ELSE BEGIN
					SELECT @columnValueContent = isnull(columnValueContent,'')
						from @tblValues
						where columnID = @columnID;

					SELECT @columnValueSiteResourceID = mdcv.columnValueSiteResourceID
						from dbo.ams_memberData as md
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
						inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
						where md.memberid = @actualMemberID
						and mdc.columnID = @columnID;

					SET @isHTML = 0;
					IF @displayTypeCode = 'HTMLCONTENT'
						SET @isHTML = 1;

					IF @columnValueSiteResourceID is not null BEGIN
						SELECT @contentID = contentID FROM dbo.cms_content WHERE siteResourceID = @columnValueSiteResourceID;
						EXEC dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isHTML=@isHTML, @contentTitle='', @contentDesc='', 
							@rawcontent=@columnValueContent, @memberID=@actualMemberID;
					END ELSE BEGIN
						EXEC dbo.cms_createContentField @siteID=@defaultSiteID, @isHTML=@isHTML, @isActive=1, @languageID=1, @contentTitle='', 
							@contentDesc='', @rawContent=@columnValueContent, @contentID=@contentID OUTPUT, @contentSiteResourceID=@columnValueSiteResourceID OUTPUT;
						EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueSiteResourceID, @byPassQueue=1;
					END
				END

				select @columnID = min(columnID) from @tblValues where columnID > @columnID;
			end
		END

		-- populate conditions to run
		-- if we are changing memberType, we need to force run groups so the user can get in the correct system group.
		-- If we need to run groups, and there are no conditions, then we need to run GroupsOnly to ensure they run.
		IF @isNewMember = 1 OR @memberTypeIDChanged = 1 BEGIN
			SELECT @condCount = COUNT(conditionID) FROM #tmpMCGConditions;
			IF @condCount = 0
				INSERT INTO platformQueue.dbo.memimport_conditionsToRun (jobID, orgID, memberID, conditionID, type, runImmediately)
				VALUES (@jobID, @orgID, @actualMemberID, NULL, 'GroupsOnly', @runImmediately);
			ELSE
				INSERT INTO platformQueue.dbo.memimport_conditionsToRun (jobID, orgID, memberID, conditionID, type, runImmediately)
				SELECT DISTINCT @jobID, @orgID, @actualMemberID, conditionID, 'ConditionsAndGroups', @runImmediately
				FROM #tmpMCGConditions;
		END ELSE 
			INSERT INTO platformQueue.dbo.memimport_conditionsToRun (jobID, orgID, memberID, conditionID, type, runImmediately)
			SELECT DISTINCT @jobID, @orgID, @actualMemberID, conditionID, 'ConditionsAndGroupsChanged', @runImmediately
			FROM #tmpMCGConditions;

		IF @isNewMember = 0 BEGIN
			WITH memberChanges as (
				SELECT memberID, replace(replace(dbo.fn_cleanInvalidXMLChars(change),'\','\\'),'"','\"') as change
				FROM platformQueue.dbo.memimport_mongo
				WHERE memberID = @actualMemberID
				AND jobID = @jobID
			)
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"historyEntries_SYS_ADMIN_MEMUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_MEMUPDATE", "ORGID":' + cast(@orgID as varchar(10)) + 
				', "ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + 
				', "RECEIVERMEMBERID":' + cast(@actualMemberID as varchar(10)) + 
				', "MAINMESSAGE":"Member Info Updated", "CHANGES":[], "MESSAGES":[ ' +
				stuff((SELECT ', "' + change + '"'
					FROM memberChanges
					ORDER BY change
					FOR XML PATH(''), TYPE).value('.','varchar(max)')
				,1,1,'') + ' ]'+
				', "SELFUPDATE":' + CASE WHEN @enteredByMemberID = @actualMemberID THEN 'true' ELSE 'false' END + 
				', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
				' } }'
			FROM (
				SELECT DISTINCT memberID
				FROM memberChanges
			) tmp;
		END
	COMMIT TRAN;

	-- run the updateMemberData hook for each site in the org
	IF @isNewMember = 0 BEGIN
		DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));
		DECLARE @tblUpdatedFields TABLE (columnName sysname);
		DECLARE @tblSites TABLE (memberAdminSiteResourceID int);

		INSERT INTO @tblUpdatedFields (columnName)
		SELECT columnName
		FROM platformQueue.dbo.memimport_mongo
		WHERE memberID = @actualMemberID
		AND jobID = @jobID;

		-- pro lic fields
		IF @professionalLicenseInfoChanged = 1
			INSERT INTO @tblUpdatedFields (columnName)
			SELECT DISTINCT c.columnName
			FROM @tblMPL AS tmp
			INNER JOIN dbo.ams_memberProfessionalLicenseTypes AS mlt ON mlt.orgID = @orgID
				AND mlt.PLTypeID = tmp.PLTypeID
			INNER JOIN dbo.cache_org_memberColumns AS c ON c.orgID = @orgID
				AND c.[area] = 'Professional Licenses'
				AND c.columnName IN (mlt.PLName + '_licenseNumber',mlt.PLName + '_activeDate',mlt.PLName + '_status')
				EXCEPT
			SELECT columnName
			FROM @tblUpdatedFields;

		SELECT @dataXML = (
			SELECT @actualMemberID AS memberid, @membernumber AS membernumber, (
				SELECT columnName AS c
				FROM @tblUpdatedFields
				FOR XML PATH(''), ROOT('updated'),TYPE) 
			FOR XML PATH ('data'));
		
		INSERT INTO @tblSites (memberAdminSiteResourceID)
		SELECT memberAdminSiteResourceID
		FROM dbo.sites
		WHERE orgID = @orgID;

		SELECT @memberAdminSRID = min(memberAdminSiteResourceID) FROM @tblSites;
		WHILE @memberAdminSRID IS NOT NULL BEGIN
			INSERT INTO @tblHookListeners (executionType, objectPath)
			EXEC dbo.hooks_runHook @event='updateMemberData', @siteResourceID=@memberAdminSRID, @dataXML=@dataXML;

			SELECT @memberAdminSRID = min(memberAdminSiteResourceID) FROM @tblSites WHERE memberAdminSiteResourceID > @memberAdminSRID;
		END
	END
	
	-- mark member as done
	EXEC platformQueue.dbo.queue_MemberUpdate_markMemberDone @jobID=@jobID, @jobMemberID=@jobMemberID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
