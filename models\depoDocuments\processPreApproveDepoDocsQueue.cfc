<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset local.itemCount = getQueueItemCount()>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.itemCount)>

		<cfif local.itemCount GT 0>
			<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="SUPPORTISSUE")>
			<cfset local.success = processQueue(messageTypeID=local.messageTypeID)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="messageTypeID" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_processPreApproveDepoDocs_grabForProcessing">
				<cfprocresult name="local.qryDepoDocQueueItem" resultset="1">
			</cfstoredproc>

			<cfset local.objAWS = new modules.awscfml.aws(awsKey='********************', awsSecretKey='2nHm+K4B23RGjq8NVx7uxrAKXwipY5q4h1lZ072g', defaultRegion="us-east-1")>

			<cfloop query="local.qryDepoDocQueueItem">
				<cfset local.thisItemID = local.qryDepoDocQueueItem.itemID>
				<cfset local.thisDepoDocumentID = local.qryDepoDocQueueItem.depoDocumentID>

				<cftry>
					<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
						set nocount on;

						declare @queueTypeID int, @statusProcessing int, @statusGrabbedProcessing int, @okToProceed bit = 0, @itemID int;
						EXEC dbo.queue_getQueueTypeID @queueType='processPreApproveDepoDocs', @queueTypeID=@queueTypeID OUTPUT;
						EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@statusProcessing OUTPUT;
						EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbedProcessing OUTPUT;

						set @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;

						UPDATE dbo.queue_processPreApproveDepoDocs
						SET statusID = @statusProcessing,
							dateUpdated = getdate()
						WHERE itemID = @itemID
						AND statusID = @statusGrabbedProcessing;

						IF @@rowcount = 1
							SET @okToProceed = 1;

						-- delete other items in queue for the same document. this counters the issue of multiple messages putting the same doc in the queue.
						DELETE FROM dbo.queue_processPreApproveDepoDocs
						WHERE depoDocumentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisDepoDocumentID#">
						AND itemID <> @itemID;

						select @okToProceed as okToProceed;
					</cfquery>

					<cfif local.updateToProcessing.okToProceed is 1>
						<!--- mark as not having attachments --->
						<cfquery name="local.qryUpdateDepoDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
							UPDATE dbo.depoDocuments
							SET origHasAttachments = 0, origCheckedForAttachments = 0
							WHERE documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisDepoDocumentID#">
						</cfquery>

						<!--- delete subfolder in originals of s3 by deleting the files in the folder in S3 --->
						<cfset local.s3keyMod = numberFormat(local.thisDepoDocumentID mod 1000,"0000")>
						<cfset local.strAttachments = local.objAWS.s3.listBucketV2(Bucket="trialsmith-depos", prefix="depos/original/#local.s3keyMod#/#local.thisDepoDocumentID#/")>
						<cfif local.strAttachments.data.KeyCount gt 0>
							<cfset local.arrAttachments = arrayMap(local.strAttachments.data.Contents, function(item){ return item.Key; })>
							<cfset local.objAWS.s3.deleteMultipleObjects(bucket="trialsmith-depos", ObjectKeys=local.arrAttachments)>
						</cfif>

						<!--- download document to the originals folder on mcfile1 if not there already --->
						<cfif NOT FileExists("#application.paths.docs.originals.path##local.thisDepoDocumentID#.pdf")>
							<cfif  application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey="depos/original/#local.s3keyMod#/#local.thisDepoDocumentID#.pdf", requestType="vhost")>
								<cfset local.urlToPDF = local.objAWS.s3.generatePresignedURL(Bucket="trialsmith-depos", objectKey="depos/original/#local.s3keyMod#/#local.thisDepoDocumentID#.pdf")>
								<cfhttp url="#local.urlToPDF#" method="get" timeout="500" throwonerror="true" result="local.pdfFile" getasbinary="yes" path="#application.paths.docs.originals.path#" file="#local.thisDepoDocumentID#.pdf"></cfhttp>
							</cfif>
						</cfif>

						<!--- add to queue --->
						<cfstoredproc procedure="ts_addDepoDocumentToAttachQueue" datasource="#application.dsn.tlasites_trialsmith.dsn#">
							<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.thisDepoDocumentID#">
						</cfstoredproc>

						<cfset clearQueueItem(itemID=local.thisItemID)>
					</cfif>

				<cfcatch type="any">
					<cfset local.mc_siteInfo = application.objSiteInfo.mc_siteInfo['MC']>

					<!--- get upload source  --->
					<cfquery name="local.qryGetDepoDocumentSourceName" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						select isNULL(us.sourceName, 'undefined') as sourceName
						from depodocuments d
						inner join depoDocumentUploadSources us on us.uploadSourceID = d.uploadSourceID
						where d.documentid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisDepoDocumentID#">
					</cfquery>

					<cfsavecontent variable="local.emailContent">
						<cfoutput>
						<div>
							Processing Pre-Approval Depo Document Queue failed for the following Depo Document.<br/>
							Depo DocumentID: <b>#local.thisDepoDocumentID#</b><br/>
							Depo Upload Source: <b>#local.qryGetDepoDocumentSourceName.sourceName#</b><br/><br/>
							Error Message: <span style="color:red;">#cfcatch.message#<br/>#cfcatch.detail#</span>
						</div>
						</cfoutput>
					</cfsavecontent>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="MemberCentral", email=local.mc_siteInfo.networkEmailFrom},
						emailto=[{ name=local.mc_siteInfo.supportProviderName, email=local.mc_siteInfo.supportProviderEmail }],
						emailreplyto="",
						emailsubject="[#Application.MCEnvironment#] Processing Pre-Approval Depo Document Queue Failed @dev@",
						emailtitle="Processing Pre-Approval Depo Document Failed",
						emailhtmlcontent=local.emailContent,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.mc_siteInfo.sysMemberID,
						messageTypeID=arguments.messageTypeID,
						sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
						)>

					<cfset clearQueueItem(itemID=local.thisItemID)>
				</cfcatch>
				</cftry>
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="clearQueueItem" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var qryDeleteQueueItem = "">

		<cfquery name="qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
			DELETE FROM dbo.queue_processPreApproveDepoDocs
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
		</cfquery>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_processPreApproveDepoDocs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>