ALTER PROC dbo.up_documentVersions_addSearchStrings
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	DECLARE @sdvID int, @queueStatusID int, @batchsize int = 6000, @minItemID int, @xmlMessage xml;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='documentSearchStrings', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

	-- queue items
	INSERT INTO platformQueue.dbo.queue_documentSearchStrings (sdvID, statusID, dateAdded, dateUpdated)
		OUTPUT INSERTED.itemID INTO #tmpQueueItems
	SELECT TOP (@batchsize) sdv.id, @queueStatusID, GETDATE(), GETDATE()
	FROM searchMC.dbo.cms_documentVersions AS sdv
	LEFT OUTER JOIN platformQueue.dbo.queue_documentSearchStrings AS qi ON qi.sdvID = sdv.id
	WHERE sdv.documentID IS NULL
	AND qi.itemID IS NULL
	ORDER BY sdv.id DESC;

	SET @itemCount = @@ROWCOUNT;

	SELECT @minItemID = min(itemID) FROM #tmpQueueItems;
	WHILE @minItemID IS NOT NULL BEGIN
		SET @xmlMessage = '<mc i="' + CAST(@minItemID AS varchar(10)) + '" />';
		EXEC platformQueue.dbo.queue_documentSearchStrings_sendMessage @xmlMessage=@xmlMessage;
		SELECT @minItemID = min(itemID) FROM #tmpQueueItems WHERE itemID > @minItemID;
	END

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
