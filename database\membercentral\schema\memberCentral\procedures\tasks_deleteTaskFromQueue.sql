ALTER PROC dbo.tasks_deleteTaskFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @orgID int, @queueTypeID int, @statusReady int, @statusProcessing int, @statusReadyForConditions int, 
		@itemStatus int, @taskID int, @itemGroupUID uniqueidentifier, @taskSiteResourceID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='taskDelete', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyForConditions', @queueStatusID=@statusReadyForConditions OUTPUT;
	
	select @itemStatus = statusID, @siteID = siteID, @orgID = orgID, @taskID = taskID, @taskSiteResourceID = taskSiteResourceID, @itemGroupUID = itemGroupUID
	from platformQueue.dbo.queue_taskDelete
	where itemID = @itemID;

	-- if itemID is not readyToProcess, kick out now
	IF @itemStatus <> @statusReady
		RAISERROR('Item not in readyToProcess state',16,1);

	-- update to processingItem
	UPDATE platformQueue.dbo.queue_taskDelete
	SET statusID = @statusProcessing,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;
	
	BEGIN TRAN;
		-- clear any existing project/task tag field data
		DELETE fd
		FROM dbo.cf_fieldData as fd 
		INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
		WHERE fd.itemID = @taskID
		AND fd.itemType IN ('TaskProjectCustom','TaskTagCustom');

		EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@taskSiteResourceID;

		-- readyForConditions
		UPDATE platformQueue.dbo.queue_taskDelete
		SET statusID = @statusReadyForConditions,
			dateUpdated = GETDATE()
		WHERE itemID = @itemID;
	COMMIT TRAN;

	-- if there are no more items to process, re-process conditions
	IF EXISTS (
		select distinct itemGroupUID
		from platformQueue.dbo.queue_taskDelete
		where itemGroupUID = @itemGroupUID
		and statusID = @statusReadyForConditions
			except
		select distinct itemGroupUID
		from platformQueue.dbo.queue_taskDelete
		where itemGroupUID = @itemGroupUID
		and statusID <> @statusReadyForConditions
	) BEGIN

		IF OBJECT_ID('tempdb..#tmpTaskAssocMembers') IS NOT NULL 
			DROP TABLE #tmpTaskAssocMembers;
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tmpTaskAssocMembers (orgID int, memberID int);
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tmpTaskAssocMembers (orgID, memberID)
		select @orgID, t.prospectMemberID
		from dbo.tasks_tasks as t
		inner join platformQueue.dbo.queue_taskDelete as qid on qid.itemGroupUID = @itemGroupUID
			and qid.taskID = t.taskID
			union
		select @orgID, ta.memberID
		from dbo.tasks_taskAssignees as ta
		inner join platformQueue.dbo.queue_taskDelete as qid on qid.itemGroupUID = @itemGroupUID
			and qid.taskID = ta.taskID;

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		select distinct c.orgID, tmp.memberID, c.conditionID
		from dbo.ams_virtualGroupConditions as c
		inner join #tmpTaskAssocMembers as tmp on tmp.orgID = c.orgID
		where c.fieldcode = 'task_entry'
		and c.orgID = @orgID;

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		-- delete queue items
		DELETE FROM platformQueue.dbo.queue_taskDelete
		WHERE itemGroupUID = @itemGroupUID;

		IF OBJECT_ID('tempdb..#tmpTaskAssocMembers') IS NOT NULL 
			DROP TABLE #tmpTaskAssocMembers;
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
