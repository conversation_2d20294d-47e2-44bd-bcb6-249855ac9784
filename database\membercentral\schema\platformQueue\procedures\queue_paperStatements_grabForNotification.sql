ALTER PROC dbo.queue_paperStatements_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int, @downloadModeColumnID int;
	EXEC dbo.queue_getQueueTypeID @queueType='PaperStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	select @downloadModeColumnID=columnID
	from dbo.tblQueueTypeDataColumns
	where queueTypeID = @queueTypeID and columnname = 'downloadmode'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, orgID int, siteID int, recordedByMemberID int, downloadMode varchar(50), PRIMARY KEY (siteID, orgID,itemGroupUID,itemUID));

	-- dequeue. 
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID, qid.itemUID, s.orgID, s.siteID, qid.recordedByMemberID, qid.columnValueString
		INTO #tmpNotify (itemGroupUID, itemUID, orgID, siteID, recordedByMemberID, downloadMode)
	FROM (
		select distinct qid2.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid2 
			on qid2.itemUID = qi.itemUID
			and qid2.columnID =  @downloadModeColumnID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid2.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid2 
			on qid2.itemUID = qi.itemUID
			and qid2.columnID =  @downloadModeColumnID
		where qi.queueStatusID <> @statusReady
		) itemGroupUIDs
	INNER JOIN platformQueue.dbo.tblQueueItemData as qid 
		on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		and qid.columnID =  @downloadModeColumnID
	inner join platformQueue.dbo.tblQueueItems as qi
		on qid.itemUID = qi.itemUID
		and qi.queueStatusID = @statusReady
	inner join membercentral.dbo.sites s 
		on s.siteID = qid.siteID;

	-- return itemGroupUIDs that can be marked as done
	select ig.itemGroupUID, me.email as reportEmail, s.siteName, s.siteID, s.siteCode, o.orgCode, mActive.memberID, mActive.firstname, mActive.lastname, mActive.memberNumber, ig.downloadMode
	from #tmpNotify as ig
	INNER JOIN membercentral.dbo.sites s 
		on ig.siteID = s.siteID
	INNER JOIN membercentral.dbo.organizations o 
		on s.orgID = o.orgID
	INNER JOIN membercentral.dbo.ams_members as m 
		on m.orgID in (ig.orgID,1) 
		and m.memberID = ig.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	group by ig.itemGroupUID, me.email, s.siteName, s.siteID, s.siteCode, o.orgCode, mActive.memberID, mActive.firstname, mActive.lastname, mActive.memberNumber, ig.downloadMode
	order by ig.itemGroupUID;


	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO