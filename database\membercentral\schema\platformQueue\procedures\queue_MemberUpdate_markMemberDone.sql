ALTER PROC dbo.queue_MemberUpdate_markMemberDone
@jobID int,
@jobMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @memImportStatusDone int, @type varchar(30), @runImmediately bit;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='MemberImport', @queueStatus='done', @queueStatusID=@memImportStatusDone OUTPUT;
	
	-- mark member as done
	UPDATE dbo.memimport_members
	SET queueStatusID = @memImportStatusDone
	WHERE memberID = @jobMemberID;

	-- if there are no more members to process, job is done and can be removed.
	IF EXISTS (
		select distinct jobID
		from dbo.memimport_members
		where jobID = @jobID
		and queueStatusID = @memImportStatusDone
			except
		select distinct jobID
		from dbo.memimport_members
		where jobID = @jobID
		and queueStatusID <> @memImportStatusDone
	) BEGIN

		-- send to membermergematch queue
		DECLARE @itemGroupUID uniqueidentifier = NEWID(), @memMergeMatchStatusReady int, @xmlMessage xml;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberMergeMatch', @queueStatus='readyToProcess', @queueStatusID=@memMergeMatchStatusReady OUTPUT;

		INSERT INTO dbo.queue_memberMergeMatch (itemGroupUID, orgID, memberID, dateAdded, dateUpdated, statusID)
		SELECT @itemGroupUID, imp.orgID, ISNULL(imp.actualMemberID,imp.newMemberID), GETDATE(), GETDATE(), @memMergeMatchStatusReady
		FROM dbo.memimport_members AS imp
		WHERE imp.jobID = @jobID
		AND (imp.firstNameChanged = 1
			OR imp.lastNameChanged = 1
			OR EXISTS(SELECT 1 FROM dbo.memimport_memberEmails AS me WHERE me.memberID = imp.memberID)
			OR EXISTS(SELECT 1 FROM dbo.memimport_memberProfessionalLicenses AS mpl WHERE mpl.memberID = imp.memberID)
		);

		IF @@ROWCOUNT > 0 BEGIN
			SELECT @xmlMessage = ISNULL((
				SELECT 'memberMergeMatchLoad' AS t, cast(@itemGroupUID as varchar(36)) AS u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');

			EXEC dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		END

		/* ***************************** */
		/* process conditions and groups */
		/* ***************************** */
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		SELECT @type = MIN(type) FROM dbo.memimport_conditionsToRun WHERE jobID = @jobID;
		WHILE @type IS NOT NULL BEGIN
			SET @runImmediately = 0;

			SELECT TOP 1 @runImmediately = runImmediately
			FROM dbo.memimport_conditionsToRun
			WHERE jobID = @jobID
			AND type = @type;

			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT DISTINCT orgID, memberID, conditionID
			FROM dbo.memimport_conditionsToRun
			WHERE jobID = @jobID
			AND type = @type;

			IF @@ROWCOUNT > 0
				EXEC dbo.queue_triggerMCGCache @runImmediately=@runImmediately, @type=@type;

			TRUNCATE TABLE #tblMCQRun;

			SELECT @type = MIN(type) FROM dbo.memimport_conditionsToRun WHERE jobID = @jobID AND type > @type;
		END

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;

		-- populate the log table for logging of these subprocs. we dont know the logID so just put 0.
		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
		CREATE TABLE #tmpPMILogID (logID int PRIMARY KEY);
		INSERT INTO #tmpPMILogID (logID) VALUES (0);
		
		EXEC membercentral.dbo.ams_importPartialMemberData_cancel @jobID=@jobID;

		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
