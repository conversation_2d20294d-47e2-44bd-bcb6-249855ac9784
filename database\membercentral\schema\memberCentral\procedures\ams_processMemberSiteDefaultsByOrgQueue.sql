ALTER PROC dbo.ams_processMemberSiteDefaultsByOrgQueue
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @minOrgID int, @queueTypeID int, @queueStatusID int, @loginFID int;
	SET @itemCount = 1;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberSiteDefByOrg', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;
	set @loginFID = dbo.fn_getResourceFunctionID('Login', dbo.fn_getResourceTypeID('Site'));

	select @minOrgID = min(orgID) from platformQueue.dbo.queue_memberSiteDefByOrg where statusID = @queueStatusID;
	while @minOrgID IS NOT NULL BEGIN
		EXEC dbo.ams_createMemberSiteDefaultsByOrgID @orgID=@minOrgID, @loginFID=@loginFID;

		select @minOrgID = min(orgID) 
		from platformQueue.dbo.queue_memberSiteDefByOrg 
		where statusID = @queueStatusID
		and orgID > @minOrgID;
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
