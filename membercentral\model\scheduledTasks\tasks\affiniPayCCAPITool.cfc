<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.processResult = populateaffiniPayCCTestQueue()>

		<cfif NOT local.processResult.success>
			<cfthrow message="Error running populateaffiniPayCCTestQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processResult.itemCount)>
			<cfset local.success = true>
		</cfif>
	</cffunction>

	<cffunction name="populateaffiniPayCCTestQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>			
			<cfquery name="local.qryPaymentProfiles" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @queueStatusID int, @recordedByMemberID int, @itemCount int, @nowDate datetime = getdate();
					SET @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();
					EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='affiniPayCCTest', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

					INSERT INTO platformQueue.dbo.queue_affiniPayCCTest (profileID, recordedByMemberID, statusID, dateAdded, dateUpdated)
					SELECT mp.profileID, @recordedByMemberID, @queueStatusID, @nowDate, @nowDate
					FROM dbo.mp_profiles as mp
					INNER JOIN dbo.mp_gateways as mg ON mp.gatewayID = mg.gatewayID
					WHERE mg.isActive = 1
					AND mg.gatewayType = 'affiniPayCC'					
					AND mp.status = 'A'
					AND NOT EXISTS (select 1 from platformQueue.dbo.queue_affiniPayCCTest where profileID = mp.profileID);

					SET @itemCount = @@ROWCOUNT;

					IF @itemCount > 0
						EXEC membercentral.dbo.sched_resumeTask @name='API Testing - affiniPayCC', @engine='MCLuceeLinux';

					select @itemCount as itemCount;

					insert into platformStatsMC.dbo.apilog_testing (testDate, testCFC, testName, results)
					values (@nowDate, 'model.system.platform.gateways.affiniPayCC', 'Task Runner', 'Tests completed');

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.itemCount = local.qryPaymentProfiles.itemCount>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>