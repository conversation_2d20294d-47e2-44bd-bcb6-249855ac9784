ALTER PROC dbo.sub_queueMassSetInactive
@siteID int,
@recordedByMemberID int,
@subscriberIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @statusReady int, @itemGroupUID uniqueidentifier = NEWID(), @xmlMessage xml;

	select @orgID = orgID from dbo.sites where siteID = @siteID;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='subscriptionInactivate', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	-- get subscribers to set Active
	IF OBJECT_ID('tempdb..#tmpSetInactiveSubscribers') IS NOT NULL 
		DROP TABLE #tmpSetInactiveSubscribers;
	CREATE TABLE #tmpSetInactiveSubscribers (subscriberID int, memberID int);

	insert into #tmpSetInactiveSubscribers (subscriberID, memberID)
	select distinct s.subscriberID, s.memberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem;

	BEGIN TRAN;
		-- queue items
		insert into platformQueue.dbo.queue_subscriptionInactivate (itemGroupUID, recordedByMemberID, orgID, siteID,
			memberID, subscriberID, statusID, dateAdded, dateUpdated)
		select @itemGroupUID, @recordedByMemberID, @orgID, @siteID, memberID, subscriberID, @statusReady, getdate(), getdate()
		from #tmpSetInactiveSubscribers;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Subscriber Inactivate Queue', @engine='BERLinux';

		-- send message to service broker to create all the individual messages
		select @xmlMessage = isnull((
			select 'subscriptionInactivateLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpSetInactiveSubscribers') IS NOT NULL 
		DROP TABLE #tmpSetInactiveSubscribers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
