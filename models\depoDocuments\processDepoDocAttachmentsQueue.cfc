<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset local.itemCount = getQueueItemCount()>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.itemCount)>

		<cfif local.itemCount GT 0>
			<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="PROCESSDOCATTCH")>
			<cfset local.success = processQueue(messageTypeID=local.messageTypeID)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="messageTypeID" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_depoDocumentsAttach_grabForProcessing">
				<cfprocresult name="local.qryDepoDocQueueItem" resultset="1">
			</cfstoredproc>

			<cfloop query="local.qryDepoDocQueueItem">
				<cfset local.thisItemID = local.qryDepoDocQueueItem.itemID>
				<cfset local.thisDepoDocumentID = local.qryDepoDocQueueItem.depoDocumentID>

				<cftry>
					<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
						set nocount on;

						declare @statusProcessing int;
						EXEC dbo.queue_getStatusIDbyType @queueType='depoDocumentsAttach', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

						UPDATE dbo.queue_depoDocumentsAttach
						SET statusID = @statusProcessing,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;
					</cfquery>

					<cfscript>
						var pdfFilePath = "#application.paths.docs.originals.path##local.thisDepoDocumentID#.pdf";
						if (fileExists(pdfFilePath)) {
							// put original in array of uploads
							local.originalDocS3UploadFilePath = "#application.paths.docs.originals_s3upload.path##local.thisDepoDocumentID#.pdf";
							local.s3keyMod = numberFormat(local.thisDepoDocumentID mod 1000,"0000");
							local.s3objectKey = lcase("depos/original/#local.s3keyMod#/#local.thisDepoDocumentID#.pdf");
							var arrFilesToS3 = [{ "filepath":local.originalDocS3UploadFilePath, "objectKey":local.s3objectKey }];

							// delete directory if exists
							var explodingFolderPath = "#application.paths.docs.originals.path##local.thisDepoDocumentID#_pdf";
							var explodingFolderPathS3 = "#application.paths.docs.originals_s3upload.path##local.thisDepoDocumentID#_pdf";
							var s3objectKeyAttach = lcase("depos/original/#local.s3keyMod#/#local.thisDepoDocumentID#");
							if (DirectoryExists(explodingFolderPath))
								DirectoryDelete(path=explodingFolderPath, recurse=true);
							DirectoryCreate(explodingFolderPath);

							// get attachments list
							cfexecute(name="/usr/bin/pdfdetach", arguments="-list #pdfFilePath#", variable="local.stAttachments", errorVariable="local.errorMessage", timeout="600");

							// parse attachments list (chr(10) delimited) and remove 1st element ("xx embedded files")
							local.arrAttachments = listToArray(local.stAttachments, chr(10));
							local.arrAttachments.deleteAt(1);

							// save out each attachment
							local.arrAttachments.each(function(element,index) { 
								// get extension and remove it from name
								local.attachFileExt = listLast(arguments.element,".");
								local.attachFileName = replace(arguments.element,".#local.attachFileExt#","");

								// replace index from filename because we want to make it sortable so we need to add our own
								// replace . / and \ with - and slugify it
								local.attachFileNameNew = application.objCommon.slugify(local.attachFileName.replace("#arguments.index#: ","").replace("/","-","ALL").replace("\","-","ALL").replace(".","-","ALL"));
								local.attachFileExtNew = application.objCommon.slugify(local.attachFileExt);

								local.attachFileNameFullNew = "#NumberFormat(arguments.index,"000")#_#local.attachFileNameNew#.#lcase(local.attachFileExtNew)#";

								cfexecute(name="/usr/bin/pdfdetach", arguments="-save #arguments.index# -o #explodingFolderPath#/#local.attachFileNameFullNew# #pdfFilePath#", variable="local.detachsaveoutput", errorVariable="local.errorMessage", timeout="600");

								local.originalDocS3UploadFilePath = "#explodingFolderPathS3#/#local.attachFileNameFullNew#";
								local.s3objectKey = lcase("#s3objectKeyAttach#/#local.attachFileNameFullNew#");
								arrFilesToS3.append({ "filepath":local.originalDocS3UploadFilePath, "objectKey":local.s3objectKey });
							}, true);

							if (NOT local.arrAttachments.len())
								DirectoryDelete(path=explodingFolderPath, recurse=true);

							updateDepoDocumentAttachmentCheck(documentID=local.thisDepoDocumentID, hasAttachments=local.arrAttachments.len() gt 0);
							addToS3UploadQueue(arrFilesToS3=arrFilesToS3);
							clearQueueItem(itemID=local.thisItemID);
						} else {
							clearQueueItem(itemID=local.thisItemID);
						}
					</cfscript>
				<cfcatch type="any">
					<cfscript>
						notifySupport(itemID=local.thisItemID, depoDocumentID=local.thisDepoDocumentID, messageTypeID=arguments.messageTypeID, errorMessage="Message:#cfcatch.message#; Detail:#cfcatch.detail#");

						// delete directory if exists
						if (isDefined("explodingFolderPath") AND DirectoryExists(explodingFolderPath))
							DirectoryDelete(path=explodingFolderPath, recurse=true);

						// upload original file as is
						local.originalDocS3UploadFilePath = "#application.paths.docs.originals_s3upload.path##local.thisDepoDocumentID#.pdf";
						local.s3keyMod = numberFormat(local.thisDepoDocumentID mod 1000,"0000");
						local.s3objectKey = lcase("depos/original/#local.s3keyMod#/#local.thisDepoDocumentID#.pdf");
						local.arrFilesToS3 = [{ "filepath":local.originalDocS3UploadFilePath, "objectKey":local.s3objectKey }];
						addToS3UploadQueue(arrFilesToS3=local.arrFilesToS3);

						clearQueueItem(itemID=local.thisItemID);
					</cfscript>
				</cfcatch>
				</cftry>
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="addToS3UploadQueue" access="private" output="false" returntype="void">
		<cfargument name="arrFilesToS3" type="array" required="true">

		<cfset var qryAddFilesToS3UploadQueue = "">

		<cfquery name="qryAddFilesToS3UploadQueue" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @s3bucketName varchar(100), @s3UploadReadyStatusID int, @nowDate datetime = GETDATE();
			DECLARE @tmpFilesToS3 TABLE (filePath varchar(400), objectKey varchar(400));

			SET @s3bucketName = 'trialsmith-depos';

			EXEC dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

			<cfloop array="#arguments.arrFilesToS3#" index="local.thisFile">
				INSERT INTO @tmpFilesToS3 (filePath, objectKey)
				VALUES (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisFile.filepath#">,
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisFile.objectKey#">);
			</cfloop>

			INSERT INTO dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
			SELECT @s3UploadReadyStatusID, @s3bucketName, objectKey, filePath, 1, @nowDate, @nowDate
			FROM (
				SELECT filePath, objectKey
				FROM @tmpFilesToS3
					EXCEPT
				SELECT filePath, objectKey
				FROM dbo.queue_S3Upload
			) tmp;
		</cfquery>
	</cffunction>

	<cffunction name="updateDepoDocumentAttachmentCheck" access="private" output="false" returntype="void">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="hasAttachments" type="boolean" required="true">

		<cfset var qryUpdateDepoDocument = "">

		<cfquery name="qryUpdateDepoDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			UPDATE dbo.depoDocuments
			SET origHasAttachments = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.hasAttachments#">,
				origCheckedForAttachments = 1
			WHERE documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
		</cfquery>
	</cffunction>

	<cffunction name="clearQueueItem" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var qryDeleteQueueItem = "">

		<cfquery name="qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
			DELETE FROM dbo.queue_depoDocumentsAttach
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
		</cfquery>
	</cffunction>

	<cffunction name="notifySupport" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="depoDocumentID" type="numeric" required="true">
		<cfargument name="messageTypeID" type="numeric" required="true">
		<cfargument name="errorMessage" type="string" required="true">

		<cfset var local = structnew()>

		<cfset local.mc_siteInfo = application.objSiteInfo.mc_siteInfo['MC']>

		<cfsavecontent variable="local.emailContent">
			<cfoutput>
			<div>
				Processing Depo Document PDF Attachments failed for the following Depo Document.<br/>
				We will attempt to process the original PDF as is, but we may not be able to extract its attachments to display in TS Admin.<br/><br/>
				Depo DocumentID: <b>#arguments.depoDocumentID#</b><br/><br/>
				Error Message: <span style="color:red;">#arguments.errorMessage#</span><br/>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="MemberCentral", email=local.mc_siteInfo.networkEmailFrom},
			emailto=[{ name=local.mc_siteInfo.supportProviderName, email=local.mc_siteInfo.supportProviderEmail }],
			emailreplyto="",
			emailsubject="[#Application.MCEnvironment#] Processing Depo Document PDF Attachments Failed",
			emailtitle="Processing Depo Document PDF Attachments Failed",
			emailhtmlcontent=local.emailContent,
			siteID=local.mc_siteInfo.siteID,
			memberID=local.mc_siteInfo.sysMemberID,
			messageTypeID=arguments.messageTypeID,
			sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
			)>

		<cfquery name="local.qryUpdateQueueItemToFailed" datasource="#application.dsn.platformQueue.dsn#">
			set nocount on;

			declare @statusFailed int;
			EXEC dbo.queue_getStatusIDbyType @queueType='depoDocumentsAttach', @queueStatus='failed', @queueStatusID=@statusFailed OUTPUT;

			UPDATE dbo.queue_depoDocumentsAttach
			SET statusID = @statusFailed,
				isNotified = 1,
				errorMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.errorMessage#">,
				dateUpdated = GETDATE()
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(qi.itemID) as itemCount
			from dbo.queue_depoDocumentsAttach as qi
			inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID
			where qs.queueStatus <> 'failed';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>