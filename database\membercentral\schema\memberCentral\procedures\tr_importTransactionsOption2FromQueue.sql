ALTER PROC dbo.tr_importTransactionsOption2FromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @itemGroupUID uniqueidentifier, @recordedByMemberID int, @siteID int, @orgID int, 
		@MCMemberID int, @MCInvoiceProfileID int, @MCRevenueGLAID int, @saleDate date, @saleDescription varchar(255), 
		@saleAmount decimal(18,2), @MCCashGLAID int, @MCPayProfileID int, @MCProfileCode varchar(20), @MCPayGatewayID int, 
		@MCCashGLName varchar(200), @invoiceID int, @invoiceNumber varchar(18), @saleTransactionID int, 
		@batchCode varchar(40), @batchName varchar(400), @batchID int, @paymentDescription varchar(255),
		@historyID int, @paymentTransactionID int, @allocationTransactionID int, @statusReady int, @itemStatus int,
		@statusProcessing int, @statusNotify int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='importAcctOpt2', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusNotify OUTPUT;
	
	select @itemGroupUID=itemGroupUID, @recordedByMemberID=recordedByMemberID, @orgID=orgID, @siteID=siteID, 
		@MCMemberID=memberID, @MCInvoiceProfileID=invoiceProfileID, @MCRevenueGLAID=revenueGLAccountID, 
		@MCCashGLAID=cashGLAccountID, @MCPayProfileID=payProfileID, @MCPayGatewayID=payGatewayID,
		@MCProfileCode=payProfileCode, @MCCashGLName=cashGLAccountName, @saleDate=saleDate, 
		@saleDescription=saleDescription, @saleAmount=saleAmount, @itemStatus=statusID
	from platformQueue.dbo.queue_acctOption2Import
	where itemID = @itemID;

	IF @itemStatus <> @statusReady OR @MCMemberID is null
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_acctOption2Import
	set statusID = @statusProcessing,
		dateUpdated = getdate()
	where itemID = @itemID;

	BEGIN TRAN;
		-- invoice		
		EXEC dbo.tr_createInvoice @invoiceProfileID=@MCInvoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
			@assignedToMemberID=@MCMemberID, @dateBilled=@saleDate, @dateDue=@saleDate, 
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

		-- sale
		EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@MCMemberID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=null, @status='Active', @detail=@saleDescription, 
			@parentTransactionID=null, @amount=@saleAmount, @transactionDate=@saleDate, 
			@creditGLAccountID=@MCRevenueGLAID, @invoiceID=@invoiceID, @stateIDForTax=0, @zipForTax='', @taxAmount=null,
			@bypassTax=1, @bypassInvoiceMessage=1, @bypassAccrual=1, @xmlSchedule=null, @transactionID=@saleTransactionID OUTPUT;
	
		EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;

		IF @saleAmount > 0 BEGIN

			-- batch
			set @batchCode = convert(char(8),@saleDate,112) + replace(cast(@itemGroupUID as char(36)),'-','');
			set @batchName = convert(char(8),@saleDate,112) + ' ' + @MCProfileCode + ' ' + @MCCashGLName + ' ' + cast(@itemGroupUID as char(36));
			select @batchID = b.batchID
				from dbo.tr_batches as b
				where b.orgID = @orgID
				and b.batchTypeID = 1
				and b.batchCode = @batchCode
				and b.statusID = 1;
			IF @batchID is null BEGIN
				EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@MCPayProfileID, @batchTypeID=1, 
					@batchCode=@batchCode, @batchName=@batchName, @controlAmt=0, @controlCount=0, 
					@depositDate=@saleDate, @isSystemCreated=0, @createdByMemberID=@recordedByMemberID, @batchID=@batchID OUTPUT;
			END

			-- payment
			set @paymentDescription = 'Payment for ' + @saleDescription;
			INSERT INTO dbo.tr_paymentHistory (paymentInfo, gatewayResponse, datePaid, statsSessionID, gatewayTransactionID, 
				gatewayApprovalCode, payerMemberID, memberPaymentProfileID, isSuccess, gatewayID, profileID, paymentType, 
				responseReasonCode, orgID)
			VALUES (
				'<payment gatewayid="' + cast(@MCPayGatewayID as varchar(10)) + '" profileid="' + cast(@MCPayProfileID as varchar(10)) + '"><args><x_amount>' + cast(@saleAmount as varchar(20)) + '</x_amount><x_description>' + replace(replace(replace(@paymentDescription,'&','&amp;'),'<','&lt;'),'>','&gt;') + '</x_description><fld_19_></fld_19_></args><gateway><x_amount>' + cast(@saleAmount as varchar(20)) + '</x_amount><x_description>' + replace(replace(replace(@paymentDescription,'&','&amp;'),'<','&lt;'),'>','&gt;') + '</x_description><fld_19_></fld_19_></gateway></payment>',
				'<response><rawresponse/><responsecode>1</responsecode><responsereasontext/><responsereasoncode/><transactionid/><approvalcode/><transactiondetail/><status>Active</status><glaccountid>' + cast(@MCCashGLAID as varchar(10)) + '</glaccountid><historyid/></response>',
				@saleDate, null, '', '', @MCMemberID, null, 1, @MCPayGatewayID, @MCPayProfileID, 'payment', null, @orgID
			);
			select @historyID = SCOPE_IDENTITY();

			EXEC dbo.tr_createTransaction_payment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@MCMemberID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=null, @status='Active', @detail=@paymentDescription, 
				@amount=@saleAmount, @transactionDate=@saleDate, @debitGLAccountID=@MCCashGLAID, @profileID=@MCPayProfileID, 
				@historyID=@historyID, @batchid=@batchID, @offeredPaymentFee=0, @isApplePay=0, @isGooglePay=0, 
				@transactionID=@paymentTransactionID OUTPUT;

			-- allocation
			EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
				@statsSessionID=null, @status='Active', @amount=@saleAmount, @transactionDate=@saleDate, 
				@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@saleTransactionID, @ovBatchID=@batchID, 
				@transactionID=@allocationTransactionID OUTPUT;

		END
	COMMIT TRAN;

	UPDATE platformQueue.dbo.queue_acctOption2Import
	set statusID = @statusNotify,
		dateUpdated = getdate()
	where itemID = @itemID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
