ALTER PROC dbo.ams_saveMemberPhoto
@orgID int,
@memberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	declare @queueStatusIDReady int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberPhotoThumb', @queueStatus='readyToProcess', @queueStatusID=@queueStatusIDReady OUTPUT;
	
	-- set flag
	UPDATE dbo.ams_members
	set hasMemberPhoto = 1, hasMemberPhotoThumb = 0, dateLastUpdated = getdate()
	where memberID = @memberID;

	INSERT INTO platformQueue.dbo.queue_memberPhotoThumb (orgcode, memberid, membernumber, width, height, dateAdded, dateUpdated, statusID)
	select o.orgcode, m.memberID, m.memberNumber, o.memberPhotoWidth, o.memberPhotoHeight, getdate(), getdate(), @queueStatusIDReady
	from dbo.ams_members as m 
	inner join dbo.organizations as o on o.orgID = m.orgID 
	where m.memberID = @memberID;

	-- resume task
	EXEC dbo.sched_resumeTask @name='Member Photo Thumbnails Queue', @engine='BERLinux';

	-- process any groups
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
	SELECT distinct @orgID, @memberID, conditionID
	from dbo.ams_virtualGroupConditions
	where orgID = @orgID
	and fieldCode = 'm_hasMemberPhoto';

	EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
