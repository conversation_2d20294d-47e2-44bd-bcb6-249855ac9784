ALTER PROC dbo.queue_subscriptionOffers_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = getdate();;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionOffers', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#subsOffersPriority') IS NOT NULL 
		DROP TABLE #subsOffersPriority;
	CREATE TABLE #subsOffersPriority (itemID int, itemGroupUID uniqueidentifier, totalQueued int, minutesInQueue int, priority int);

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	INSERT INTO #subsOffersPriority (itemID, itemGroupUID, minutesInQueue)
	select itemID, itemGroupUID, minsInQueue = datediff(minute,dateUpdated,@nowDate)
	from dbo.queue_subscriptionOffers
	where statusID in (@statusReady,@statusGrabbed);

	update rp 
	set rp.totalQueued = temp.totalQueued
	from #subsOffersPriority as rp
	inner join (
		select itemGroupUID, count(*) as totalQueued
		from #subsOffersPriority
		group by itemGroupUID
	) as temp on temp.itemGroupUID = rp.itemGroupUID;

	update temp 
	set priority = 
			case 
				when totalQueued = 1 then -100
				when minutesInQueue > 360 then (totalQueued / 50) + 1
				when minutesInQueue > 90 then (totalQueued / 25) - 10
				when minutesInQueue > 30 then (totalQueued / 25) + 2
				when totalQueued < 500 then (totalQueued / 25)
				else (totalQueued / 25) + 10
            end
	from #subsOffersPriority as temp;

	 -- delete rows where the priority hasn't changed
	delete temp
	from #subsOffersPriority as temp
	inner join dbo.queue_subscriptionOffers as qid on temp.itemID = qid.itemID
	where temp.priority = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = temp.priority
	FROM dbo.queue_subscriptionOffers as qid
	INNER JOIN #subsOffersPriority as temp on temp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#subsOffersPriority') IS NOT NULL 
		DROP TABLE #subsOffersPriority;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
