ALTER PROC dbo.queue_importAuthCIM_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='importAuthCIM', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	;WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_importAuthCIM
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_importAuthCIM
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_importAuthCIM as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errResult, m2.activeMemberID as recordedByMemberID, me.email as reportEmail, s.sitecode, s.siteName,
		mActive.lastname + ', ' + mActive.firstname + isnull(' ' + nullif(mActive.middlename,''),'') + ' (' + mActive.membernumber + ')' as memberName,
		mActive.membernumber, mActive.company, qid.profileID, mp.profileCode, 'XXXX' + right(qid.cardNumber,4) as cardNumber
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_importAuthCIM as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.mp_profiles as mp on mp.siteID = s.siteID and mp.profileID = qid.profileID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = qid.memberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID in (s.orgID,1) and m2.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1) and metag.memberID = m2.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1) and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when errResult is null then 0 else 1 end desc, memberName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
