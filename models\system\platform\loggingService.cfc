<cfcomponent output="false" cache="true">
	<cfset variables.logBaseFolder = application.paths.SharedTemp.path & 'loggingservice/' />
	<cfset variables.logBaseFolderFromDB = application.paths.SharedTemp.pathUNC & 'loggingservice\' />
	<cfset variables.logS3Bucket = 'membercentralcdn' />
	<cfset variables.logS3Region = 'us-east-1' />
	<cfset variables.logS3BucketPrefix = 'loggingservice/#lcase(application.MCEnvironment)#/' />
	<cfset variables.logS3ModFactor = 1000 />

	<cfset variables.logPrefix = "log-" />
	<cfset variables.logExtension = ".txt" />
	<cfset variables.crlf = chr(13) & chr(10)/>

	<cffunction name="initialize" access="public" output="false" returntype="boolean">
		<cfargument name="logID" type="string" required="true">
		<cfscript>
			local.success = false;
			if (not directoryExists(variables.logBaseFolder))
				directoryCreate(variables.logBaseFolder)

			local.logFilePath = getLogFilePath(arguments.logID);
			local.logFileS3Key = getLogFileS3Key(arguments.logID);
			local.logFileExists = fileExists(local.logFilePath);
			local.logFileS3KeyExists = application.objS3.s3FileExists(
				bucket=variables.logS3Bucket,
				objectKey=local.logFileS3Key,
				region=variables.logS3Region);

			updateLastModified(arguments.logID);
			updateAmazonUploadQueue(arguments.logID);

			if (local.logFileExists) {
				
			} else if (local.logFileS3KeyExists) {
				local.logFileS3URL = application.objS3.s3Url(
							bucket=variables.logS3Bucket,
							objectKey=local.logFileS3Key,
							region=variables.logS3Region,
							requestType="vhost",
							method="GET",
							protocol="http");
				cfhttp(url=local.logFileS3URL,method="GET",file=local.logFilePath){

				}
			} else {
				fileWrite( local.logFilePath,"",  "UTF-8");
			}
			local.success = true;
			return local.success;
		</cfscript>
	</cffunction>
	<cffunction name="append" access="public" output="false" returntype="boolean">
		<cfargument name="logID" type="string" required="true">
		<cfargument name="text" type="string" required="true">
		

		<cfscript>
			local.success = false;
			
			local.logFilePath = getLogFilePath(arguments.logID);
			local.logExists = fileExists(local.logFilePath);

			if (not local.logExists)
				local.logExists = initialize(arguments.logID);

			if (local.logExists) {
				updateLastModified(arguments.logID);
				updateAmazonUploadQueue(arguments.logID);

				// process merge tags
				local.logtext = fixLineEndings(arguments.text) & variables.crlf;

				if (findNoCase("{merge_ipaddress}", local.logtext)) {
					local.ipaddress = application.objPlatform.getClientIP();
					local.logtext = replaceNoCase(local.logtext, "{merge_ipaddress}", local.ipaddress, "ALL");
				}

				fileAppend(local.logFilePath,local.logtext,  "UTF-8");
				local.success = true;
			}
			return local.success;
		</cfscript>
	</cffunction> 
	<cffunction name="download" access="public" output="false" returntype="boolean">
		<cfargument name="logID" type="string" required="true">
		

		<cfscript>
			local.success = false;
			
			local.logFilePath = getLogFilePath(arguments.logID);
			local.logFileS3Key = getLogFileS3Key(arguments.logID);
			local.logFileExists = fileExists(local.logFilePath);
			local.logFileS3KeyExists = application.objS3.s3FileExists(
				bucket=variables.logS3Bucket,
				objectKey=local.logFileS3Key,
				region=variables.logS3Region);
			if (application.objsiteInfo.isRequestSecure())
				local.s3protocol = "https";
			else
				local.s3protocol = "http";

			if (local.logFileExists) {
				application.objDocDownload.doDownloadDocument(
					sourceFilePath=local.logFilePath,
					displayName="Log#arguments.logID#.txt",
					deleteSourceFile=false,
					forceDownload=false);
				local.success = true;
			} else if (local.logFileS3KeyExists) {

				application.objDocDownload.doDownloadDocument(
					sourceFilePath=local.logFilePath,
					displayName="Log#arguments.logID#.txt",
					deleteSourceFile=false,
					forceDownload=false,
					s3bucket=variables.logS3Bucket,
					s3objectKey=local.logFileS3Key,
					s3expire=1,
					s3requesttype='vhost',
					region=variables.logS3Region,
					protocol=local.s3protocol);
				local.success = true;
			}

			return local.success;
		</cfscript>
	</cffunction> 

	<cffunction name="getLogFilePath" access="private" output="false" returntype="string">
		<cfargument name="logID" type="string" required="true">
		<cfreturn variables.logBaseFolder & arguments.logID & variables.logExtension>	
	</cffunction>
	<cffunction name="getLogFilePathFromDB" access="private" output="false" returntype="string">
		<cfargument name="logID" type="string" required="true">
		<cfreturn variables.logBaseFolderFromDB & arguments.logID & variables.logExtension>	
	</cffunction>
	<cffunction name="getLogFileS3Key" access="private" output="false" returntype="string">
		<cfargument name="logID" type="string" required="true">
		<cfreturn variables.logS3BucketPrefix & numberformat(arguments.logID mod variables.logS3ModFactor,"0000") & '/' & arguments.logID & variables.logExtension>	
	</cffunction>
	<cffunction name="fixLineEndings" access="private" output="false" returntype="string">
		<cfargument name="text" type="string" required="true">
		<cfreturn REReplaceNoCase(arguments.text, '(\r?\n)', variables.crlf, "ALL")>	
	</cffunction>
	<cffunction name="updateLastModified" access="private" output="false" returntype="void">
		<cfargument name="logID" type="string" required="true">
		<cfquery name="local.updateLogDate" datasource="#application.dsn.platformStatsMC.dsn#">
			update dbo.log_logs
			set dateLastUpdated = getdate()
			where logID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.logID#">
		</cfquery>
	</cffunction>
	<cffunction name="updateAmazonUploadQueue" access="private" output="false" returntype="void">
		<cfargument name="logID" type="string" required="true">
		<cfquery name="local.updateLogDate" datasource="#application.dsn.platformQueue.dsn#">
			set nocount on;

			declare @s3bucketName varchar(100), @objectKey varchar(400), @filePath varchar(400), @deleteOnSuccess bit,
					@dateAdded datetime, @nextAttemptDate datetime, @existingFileID int, @s3UploadReadyStatusID int;

			set @s3bucketName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#variables.logS3Bucket#">;
			set @objectKey = <cfqueryparam cfsqltype="cf_sql_varchar" value="#getLogFileS3Key(arguments.logID)#">;
			set @filePath = <cfqueryparam cfsqltype="cf_sql_varchar" value="#getLogFilePathFromDB(arguments.logID)#">;
			set @deleteOnSuccess  = 1;
			set @dateAdded = getdate();
			set @nextAttemptDate = dateadd(minute,15,@dateAdded);

			EXEC dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

			select @existingFileID = fileID from dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey;
			IF @existingFileID is not null
				update dbo.queue_S3Upload 
				set nextAttemptDate = @nextAttemptDate
				where fileID = @existingFileID;
			ELSE
				insert into dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated, nextAttemptDate)
				values (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePath, @deleteOnSuccess, @dateAdded, @dateAdded, @nextAttemptDate);
		</cfquery>
	</cffunction>
</cfcomponent>