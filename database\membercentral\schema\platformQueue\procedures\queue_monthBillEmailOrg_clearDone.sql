ALTER PROC dbo.queue_monthBillEmailOrg_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	declare @tblBillingPeriods TABLE (billingPeriodID int);
	EXEC dbo.queue_getStatusIDbyType @queueType='monthBillEmailOrg', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	INSERT INTO @tblBillingPeriods (billingPeriodID)
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailOrg
	where statusID = @statusDone
		except
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailOrg
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;
		
	BEGIN TRAN;
		delete q
		from platformQueue.dbo.queue_monthBillSW as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;
		
		delete q
		from platformQueue.dbo.queue_monthBillTSRoyalty as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;

		delete q
		from platformQueue.dbo.queue_monthBillTSA as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID
		where q.isOrg = 1;

		delete q
		from platformQueue.dbo.queue_monthBillEmailOrg as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
