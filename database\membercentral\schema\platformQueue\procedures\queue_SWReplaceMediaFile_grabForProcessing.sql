ALTER PROC dbo.queue_SWReplaceMediaFile_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSWQueueItems') IS NOT NULL
		DROP TABLE #tmpSWQueueItems;
	CREATE TABLE #tmpSWQueueItems (itemID int);

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @environmentName varchar(50), @environmentID int;
	EXEC dbo.queue_getQueueTypeID @queueType='SWReplaceMediaFile', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpSWQueueItems
	FROM dbo.queue_SWReplaceMediaFile AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_SWReplaceMediaFile
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT DISTINCT qid.itemID, qid.seminarID, qid.titleID, qid.oldFileID, qid.newFileID
	FROM #tmpSWQueueItems as tmp
	INNER JOIN dbo.queue_SWReplaceMediaFile as qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpSWQueueItems') IS NOT NULL
		DROP TABLE #tmpSWQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO