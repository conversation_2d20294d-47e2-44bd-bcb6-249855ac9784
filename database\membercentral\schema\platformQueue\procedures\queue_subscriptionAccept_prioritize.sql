ALTER PROC dbo.queue_subscriptionAccept_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = getdate();;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionAccept', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#subsAcceptedPriority') IS NOT NULL 
		DROP TABLE #subsAcceptedPriority;
	CREATE TABLE #subsAcceptedPriority (itemID int, itemGroupUID uniqueidentifier, totalQueued int, minutesInQueue int, priority int);

	INSERT INTO #subsAcceptedPriority (itemID, itemGroupUID, minutesInQueue)
	select itemID, itemGroupUID, minsInQueue = datediff(minute,dateUpdated,@nowDate)
	from dbo.queue_subscriptionAccept
	where statusID in (@statusReady,@statusGrabbed);

	update rp 
	set rp.totalQueued = temp.totalQueued
	from #subsAcceptedPriority as rp
	inner join (
		select itemGroupUID, count(*) as totalQueued
		from #subsAcceptedPriority
		group by itemGroupUID
	) as temp on temp.itemGroupUID = rp.itemGroupUID;

	update temp 
	set priority = 
			case 
				when totalQueued = 1 then -100
				when minutesInQueue > 360 then (totalQueued / 50) + 1
				when minutesInQueue > 90 then (totalQueued / 25) - 10
				when minutesInQueue > 30 then (totalQueued / 25) + 2
				when totalQueued < 500 then (totalQueued / 25)
				else (totalQueued / 25) + 10
            end
	from #subsAcceptedPriority as temp;

	 -- delete rows where the priority hasn't changed
	delete temp
	from #subsAcceptedPriority as temp
	inner join dbo.queue_subscriptionAccept as qid on temp.itemID = qid.itemID
	where temp.priority = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = temp.priority
	FROM dbo.queue_subscriptionAccept as qid
	INNER JOIN #subsAcceptedPriority as temp on temp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#subsAcceptedPriority') IS NOT NULL 
		DROP TABLE #subsAcceptedPriority;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
