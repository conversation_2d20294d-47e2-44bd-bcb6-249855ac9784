<cfset local.objGL = CreateObject("component","GLAccounts")>
<cfset local.qryInvoiceProfiles = local.objGL.getInvoiceProfiles(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
<cfset local.qryInvoiceContentObjects = local.objGL.getInvoiceContentObjects(siteID=arguments.event.getValue('mc_siteinfo.siteID'), incContent=0)>
<cfset local.qrySalesTaxProfiles = local.objGL.getSalesTaxProfiles(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
<cfset local.qrySalesTaxCategoriesTaxJar = local.objGL.getSalesTaxCategoriesTaxJar()>
<cfset local.isFlyout = val(arguments.event.getValue('isflyout',0))>

<cfif val(local.strAccount.qryAccount.GLAccountID) is 0 and arguments.event.getValue('glatid') is 1>
	<cfset local.accountType = 'Cash'>
	<cfset local.accountTypeID = 1>
<cfelseif val(local.strAccount.qryAccount.GLAccountID) is 0 and arguments.event.getValue('glatid') is 3>
	<cfset local.accountType = 'Revenue'>
	<cfset local.accountTypeID = 3>
<cfelse>
	<cfset local.accountType = local.strAccount.qryAccount.accountType>
	<cfset local.accountTypeID = val(local.strAccount.qryAccount.accountTypeID)>
</cfif>

<!--- for liability accounts, get the GL Code of the parent account --->
<cfif local.accountTypeID is 5>
	<cfset local.strParentAccount = local.objAccount.getGLAccount(val(local.strAccount.qryAccount.parentGLAccountID),arguments.event.getValue('mc_siteInfo.orgID'))>
</cfif>

<cfsavecontent variable="local.editAcctJS">
	<cfoutput>
	<script type="text/javascript">
		function validatefrmGLAccount() {
			<cfif local.isFlyout>
				let saveGLBtn = top.$('##btnMCModalSave');
			<cfelse>
				let saveGLBtn = $('##btnSaveGLAcct');
			</cfif>

			let arrReq = [];
			mca_hideAlert('err_glform');
			var userAcctName = $('##frmGLAccount ##accountName').val().toLowerCase();

			if (userAcctName == '') 
				arrReq.push('Account Name is required.');
			else if (userAcctName == 'cash accounts' || userAcctName == 'asset accounts' || userAcctName == 'revenue accounts' || userAcctName == 'expense accounts' || userAcctName == 'liability accounts' || userAcctName == 'deferred revenue accounts')
				arrReq.push('That account name is reserved. Select another account name.');

			if ($('##frmGLAccount ##AccountTypeID').val() == 0)
				arrReq.push('Type of Account is required.');

			<cfif local.accountTypeID is 3>
				if ($('##frmGLAccount ##invoiceProfileID').val() == 0)
					arrReq.push('Selecting an Invoice Profile is required.');
			</cfif>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and val(local.strAccount.qryAccount.GLAccountID)>
				if ($.trim($('##frmGLAccount ##glUID').val()) == '')
					arrReq.push('GL Account UID is required.');
			</cfif>

			let acctSysName = $('##acctSysName').val().trim();
			if (acctSysName.length) {
				let arrAcctSysName = acctSysName.split(':');
				arrAcctSysName = arrAcctSysName.map(s => s.trim());
				arrAcctSysName = arrAcctSysName.filter(s => s.length > 0);
				if (! arrAcctSysName.length) {
					arrReq.push('Invalid Quickbooks Account Name.');
				} else {
					acctSysName = arrAcctSysName.join(':');
				}
			}

			let acctSysClass = $('##acctSysClass').val().trim();
			if (acctSysClass.length) {
				let arrAcctSysClass = acctSysClass.split(':');
				arrAcctSysClass = arrAcctSysClass.map(s => s.trim());
				arrAcctSysClass = arrAcctSysClass.filter(s => s.length > 0);
				if (! arrAcctSysClass.length || arrAcctSysClass.length > 5) {
					arrReq.push('Invalid Quickbooks Class value.');
				} else {
					acctSysClass = arrAcctSysClass.join(':');
				}
			}

			if (arrReq.length) {
				mca_showAlert('err_glform', arrReq.join('<br/>'));
				return false;
			}

			var saveGLAccountResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if(typeof fnGLAA_select == 'function') {
						fnGLAA_select(r.glaccountid);
					} else {
						top.fnSaveAccountResult(true,r.glaccountid);
					}
				} else {
					var msg = '<b>There was an error saving the GL Account</b><br/>
						&bull; Account Codes, if specified, must be unique across all GL Accounts.<br/>
						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and val(local.strAccount.qryAccount.GLAccountID)>
							&bull; GL Account UIDs must be unique across all GL Accounts.<br/>
						</cfif>
						&bull; Account Names must be unique across accounts with the same parent account.';
					mca_showAlert('err_glform', msg);
					saveGLBtn.html('Save Changes').prop('disabled',false);
				}
			};

			saveGLBtn.html('Saving...<i class="fa-solid fa-spinner fa-spin"></i>').prop('disabled',true);

			var objParams = { GLAccountID:$('##GLAccountID').val(), AccountTypeID:$('##AccountTypeID').val(),
				accountName:$('##accountName').val(), accountCode:$('##accountCode').val(),
				acctSysName:acctSysName, acctSysClass:acctSysClass,
				parentGLAccountID:$('##parentGLAccountID').val(), invoiceProfileID:$('##invoiceProfileID').val(), 
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and val(local.strAccount.qryAccount.GLAccountID)>uid:$.trim($('##glUID').val()),</cfif>
				invoiceContentID:$('##invoiceContentID').val(), deferredGLAccountID:$('##deferredGLAccountID').val(),
				salesTaxProfileID:$('##salesTaxProfileID').val(), salesTaxTaxJarCategoryID:$('##salesTaxTaxJarCategoryID').val() };
			
			TS_AJX('ADMINGLACCT','saveGLAccount',objParams,saveGLAccountResult,saveGLAccountResult,10000,saveGLAccountResult);
			return true;
		}
		function showSalesTaxCategoriesTaxJar() {
			var arrSTP = $('##frmGLAccount ##salesTaxProfileID').val().split('|');
			if (arrSTP[0] == '0' || arrSTP[1] != 'TaxJar') {
				$("##salesTaxTaxJarCategoryID").val($("##salesTaxTaxJarCategoryID option:first").val());
				$('.tr_salesTaxTaxJarCategoryID').addClass('d-none');
			} else $('.tr_salesTaxTaxJarCategoryID').removeClass('d-none');
		}

		$(function() {
			<cfif val(local.strAccount.qryAccount.GLAccountID) is 0 and (NOT arguments.event.valueExists('glatid') or NOT listFind("1,3",arguments.event.getValue('glatid')))>
				top.closeBox();
			</cfif>
			<cfif local.accountTypeID is 3 and (val(local.strAccount.qryAccount.GLAccountID) is 0 or local.strAccount.qryAccount.isSystemAccount is 0)>
				showSalesTaxCategoriesTaxJar();
			</cfif>

			<cfif local.isFlyout>
				top.$('##MCModalHeader ##MCModalLabel').html('#local.strAccount.qryAccount.GLAccountID gt 0 ? "Edit" : "Add"# #encodeForJavaScript(local.accountType)# Account');
			</cfif>

			$('##accountName,##acctSysName').on('blur', function() {
				let val = $(this).val().trim().replaceAll('"','');
				$(this).val(val);
			});
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.editAcctJS#">

<cfoutput>
<form name="frmGLAccount" id="frmGLAccount" method="POST" class="px-2">
<input type="hidden" id="GLAccountID" name="GLAccountID" value="#val(local.strAccount.qryAccount.GLAccountID)#">
<input type="hidden" name="AccountTypeID"  id="AccountTypeID" value="#local.accountTypeID#">

<cfif local.isFlyout>
	<button id="btnSaveGLAcct" type="button" class="d-none" onclick="return validatefrmGLAccount()"></button>
<cfelse>
	<div class="d-flex mb-3">
		<div class="mr-auto">
			<h4><cfif local.strAccount.qryAccount.GLAccountID gt 0>Edit<cfelse>Add</cfif> #local.accountType# Account</h4>
		</div>
		<div><button id="btnSaveGLAcct" type="button" class="btn btn-sm btn-primary" onclick="return validatefrmGLAccount()">Save<cfif local.strAccount.qryAccount.GLAccountID gt 0> Changes</cfif></button></div>
	</div>
</cfif>

<div id="err_glform" class="alert alert-danger mb-2 d-none"></div>

<div class="card card-box mb-3">
	<div class="card-header py-1 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">
			Control Panel Chart of Accounts Settings for Staff Reference
		</div>
	</div>
	<div class="card-body">
		<cfif local.strAccount.qryAccount.isSystemAccount is not 1 OR (local.accountTypeID is 5 AND listFindNoCase("DEFERREDREVENUE,DEFERREDTAX",local.strParentAccount.qryAccount.GLCode))>
			<div class="form-row">
				<div class="col">
					<div class="form-label-group">
						<input type="text" name="accountName" id="accountName" value="#local.strAccount.qryAccount.accountName#" class="form-control" maxlength="200" autocomplete="off"/>
						<label for="accountName">Account Name <cfif local.strAccount.qryAccount.isSystemAccount is not 1>*</cfif></label>
					</div>
				</div>
			</div>
		<cfelse>
			<div class="form-group row">
				<div class="col">
					<input type="hidden" id="accountName" name="accountName" value="#local.strAccount.qryAccount.accountName#">
					<b>#local.strAccount.qryAccount.thePathExpanded#</b>
					<cfif local.accountTypeID is 5 and local.strParentAccount.qryAccount.GLCode eq "SALESTAX">
						<div class="mb-2 font-italic small">This account name is managed by a Sales Tax Authority. Edit the Sales Tax Authority to change this account name.</div>
					<cfelseif ListFindNoCase("ACCOUNTSRECEIVABLE,PLEDGESRECEIVABLE,WRITEOFF,DEPOSITS",local.strAccount.qryAccount.GLCode)>
						<div class="mb-2 font-italic small">This is a system account. Its name cannot be changed.</div>
					<cfelse>
						<div class="mb-2 font-italic small">This account's name cannot be changed.</div>
					</cfif>
				</div>
			</div>
		</cfif>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<div class="input-group">
						<input type="text" id="accountCode" name="accountCode" value="#local.strAccount.qryAccount.accountCode#" class="form-control" maxlength="200" autocomplete="off">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer" data-target="accountCode">
								<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-html="true" data-trigger="hover" title="" 
									data-original-title="<div class='text-left'>To use Quickbooks Desktop, separate your Account Code by a pipe (|) symbol.<br/>For example, an account with an Account Code of <b>1000123</b> and class of <b>My Class</b><br/>should be saved as <b>1000123|My Class</b>.<br/><br/>The IIF format of the Batch Summary Report will parse this field to populate<br/>the Class column in the export file.</div>"></i>

							</span>
						</div>
						<label for="accountCode" >Account Code</label>
					</div>
				</div>
			</div>
		</div>

		<cfif local.strAccount.qryAccount.isSystemAccount is 1>
			<input type="hidden" id="parentGLAccountID" name="parentGLAccountID" value="#val(local.strAccount.qryAccount.parentGLAccountID)#">
		<cfelse>
			<cfset local.thisPathLength = len('#local.strAccount.qryAccount.thePath#.')>

			<div class="form-row">
				<div class="col">
					<div class="form-label-group">				
						<select name="parentGLAccountID" id="parentGLAccountID" class="form-control">
							<option value="#val(local.strGLAccounts.qryAccounts.parentGLAccountID)#">None - this is a top level account</option>
							<cfloop query="local.strGLAccounts.qryAccounts">
								<cfif local.strGLAccounts.qryAccounts.GLAccountID is not local.strAccount.qryAccount.GLAccountID and local.strGLAccounts.qryAccounts.AccountTypeID is local.accountTypeID>
									<option value="#local.strGLAccounts.qryAccounts.GLAccountID#"<cfif local.strAccount.qryAccount.parentGLAccountID is local.strGLAccounts.qryAccounts.GLAccountID> selected<cfelseif val(local.strAccount.qryAccount.GLAccountID) gt 0 and left(local.strGLAccounts.qryAccounts.thePath,local.thisPathLength) eq '#local.strAccount.qryAccount.thePath#.'> disabled</cfif>><cfloop from="1" to="#listLen(local.strGLAccounts.qryAccounts.thePath,".")-1#" index="local.x">&nbsp;&nbsp;</cfloop>#left(local.strGLAccounts.qryAccounts.accountName,50)#<cfif len(local.strGLAccounts.qryAccounts.accountName) gt 50>...</cfif></option>
								</cfif>
							</cfloop>
						</select>
						<label for="parentGLAccountID">Child Account of *</label>
					</div>
				</div>
			</div>
		</cfif>

	</div>
</div>

<div class="card card-box mb-3">
	<div class="card-header py-1 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">
			Mapping for QuickBooks Online Journal Entries
		</div>
	</div>
	<div class="card-body">
		<div class="form-label-group">
			<input type="text" name="acctSysName" id="acctSysName" value="#local.strAccount.qryAccount.acctSysName#" class="form-control" maxlength="400" autocomplete="off">
			<label for="acctSysName">QuickBooks Online Account Name (separated by colons for sub-accounts)</label>
		</div>
		<div class="form-label-group">
			<input type="text" name="acctSysClass" id="acctSysClass" value="#local.strAccount.qryAccount.acctSysClass#" class="form-control" maxlength="400" autocomplete="off">
			<label for="acctSysClass">QuickBooks Online Class (separated by colons for sub-classes)</label>
		</div>
	</div>
</div>

<div class="card card-box mb-3">
	<div class="card-header py-1 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">
			Other Control Panel Settings
		</div>
	</div>
	<div class="card-body">
		<cfif local.accountTypeID is 3>
			<cfif local.qryInvoiceProfiles.recordcount gt 1>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">				
							<select name="invoiceProfileID" id="invoiceProfileID" class="form-control">
								<option value="0"></option>
								<cfloop query="local.qryInvoiceProfiles">
									<option value="#local.qryInvoiceProfiles.profileID#"<cfif local.qryInvoiceProfiles.profileID eq local.strAccount.qryAccount.invoiceProfileID> selected</cfif>>#local.qryInvoiceProfiles.profileName#</option>
								</cfloop>
							</select>
							<label for="invoiceProfileID">Invoice Profile *</label>
						</div>
					</div>
				</div>
			<cfelse>
				<input type="hidden" id="invoiceProfileID" name="invoiceProfileID" value="#val(local.qryInvoiceProfiles.profileID)#">
			</cfif>
		<cfelse>
			<input type="hidden" id="invoiceProfileID" name="invoiceProfileID" value="0">
		</cfif>

		<cfif local.accountTypeID is 3 and (val(local.strAccount.qryAccount.GLAccountID) is 0 or local.strAccount.qryAccount.isSystemAccount is 0)>
			<div class="form-row">
				<div class="col">
					<div class="form-label-group">				
						<select name="salesTaxProfileID" id="salesTaxProfileID" class="form-control" onchange="showSalesTaxCategoriesTaxJar();">
							<option value="0|None">No Sales Tax Profile</option>
							<cfloop query="local.qrySalesTaxProfiles">
								<option value="#local.qrySalesTaxProfiles.profileID#|#local.qrySalesTaxProfiles.providerName#" <cfif local.qrySalesTaxProfiles.profileID eq local.strAccount.qryAccount.salesTaxProfileID>selected</cfif>>#local.qrySalesTaxProfiles.profileName#</option>
							</cfloop>
						</select>
						<label for="salesTaxProfileID">Sales Tax Profile *</label>
					</div>
				</div>
			</div>
			
			<div class="form-row tr_salesTaxTaxJarCategoryID d-none">
				<div class="col">
					<div class="form-label-group">				
						<select name="salesTaxTaxJarCategoryID" id="salesTaxTaxJarCategoryID" class="form-control">
							<cfloop query="local.qrySalesTaxCategoriesTaxJar">
								<option value="#local.qrySalesTaxCategoriesTaxJar.categoryID#" <cfif local.qrySalesTaxCategoriesTaxJar.categoryID eq local.strAccount.qryAccount.salesTaxTaxJarCategoryID>selected</cfif>><cfif local.qrySalesTaxCategoriesTaxJar.taxCode neq "FULLTAX">[#local.qrySalesTaxCategoriesTaxJar.taxCode#] </cfif>#local.qrySalesTaxCategoriesTaxJar.category#</option>
							</cfloop>
						</select>
						<label for="salesTaxTaxJarCategoryID">TaxJar Tax Category *</label>
					</div>
				</div>
			</div>
			
			<cfif local.qryInvoiceContentObjects.recordcount>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">				
							<select name="invoiceContentID" id="invoiceContentID" class="form-control">
								<option value="0">None</option>
								<cfloop query="local.qryInvoiceContentObjects">
									<option value="#local.qryInvoiceContentObjects.contentID#"<cfif local.qryInvoiceContentObjects.contentID eq local.strAccount.qryAccount.invoiceContentID> selected</cfif>>#local.qryInvoiceContentObjects.contentTitle#</option>
								</cfloop>
							</select>
							<label for="invoiceContentID">End-of-Invoice Message</label>
						</div>
					</div>
				</div>
			<cfelse>
				<input type="hidden" id="invoiceContentID" name="invoiceContentID" value="0">
			</cfif>
		<cfelse>
			<input type="hidden" id="salesTaxProfileID" name="salesTaxProfileID" value="0|None">
			<input type="hidden" id="salesTaxTaxJarCategoryID" name="salesTaxTaxJarCategoryID" value="0">
			<input type="hidden" id="invoiceContentID" name="invoiceContentID" value="0">
		</cfif>

		<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct') and 
			(
				(local.accountTypeID is 3 and (val(local.strAccount.qryAccount.GLAccountID) is 0 or local.strAccount.qryAccount.isSystemAccount is 0))
				or 
				(local.accountTypeID is 5 and local.strParentAccount.qryAccount.GLCode eq "SALESTAX")
			)>
			<cfset local.qryDeferredGLAccounts = local.objGL.getDeferredGLAccounts(orgID=arguments.event.getValue('mc_siteinfo.orgID'), accountTypeID=local.accountTypeID)>
			<cfset local.unrecognizedAmt = local.objGL.getUnrecognizedAmtForRevenueGL(orgID=arguments.event.getValue('mc_siteInfo.orgID'), revenueGLAccountID=val(local.strAccount.qryAccount.GLAccountID))>

			<div class="form-row">
				<div class="col">
					<div class="form-label-group">				
						<select name="deferredGLAccountID" id="deferredGLAccountID" class="form-control">
							<cfloop query="local.qryDeferredGLAccounts">
								<option value="#local.qryDeferredGLAccounts.GLAccountID#"<cfif local.qryDeferredGLAccounts.GLAccountID eq local.strAccount.qryAccount.deferredGLAccountID> selected</cfif>>#local.qryDeferredGLAccounts.accountName#</option>
							</cfloop>
						</select>
						<label for="deferredGLAccountID">Deferred Account</label>
						<div class="alert alert-info mx-2 mt-2">
							<strong>Note:</strong> By changing this selection, revenue for this account may have optional deferral for manually recorded sale entries (you can choose the accrual schedule if applicable), and may automatically accrue subscription related revenue based on subscription date ranges. Changing this selection will ONLY affect future recorded sale items, and will have no affect on PRIOR recorded sale items. Consult with MemberCentral if you have questions about changing this selection.
							<cfif local.unrecognizedAmt gt 0>
								<br/><strong>We show #dollarformat(local.unrecognizedAmt)# still to be recognized.</strong>
							</cfif>
						</div>
					</div>
				</div>
			</div>
		<cfelseif NOT arguments.event.getValue('mc_siteInfo.useAccrualAcct') and 
			(
				(local.accountTypeID is 3 and (val(local.strAccount.qryAccount.GLAccountID) is 0 or local.strAccount.qryAccount.isSystemAccount is 0))
				or 
				(local.accountTypeID is 5 and local.strParentAccount.qryAccount.GLCode eq "SALESTAX")
			)>
			<input type="hidden" id="deferredGLAccountID" name="deferredGLAccountID" value="0">

			<div class="form-group row no-gutters mt-4 mb-1">
				<div class="col-sm-3">Deferred Account</div>
				<div class="col-sm-9 text-grey">This feature defers and accrues revenue. Call MemberCentral to Activate.</div>
			</div>
		<cfelse>
			<input type="hidden" id="deferredGLAccountID" name="deferredGLAccountID" value="0">
		</cfif>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and val(local.strAccount.qryAccount.GLAccountID)>
			<div class="form-row">
				<div class="col">
					<div class="form-label-group">
						<input type="text" name="glUID" id="glUID" value="#local.strAccount.qryAccount.uid#" class="form-control" maxlength="60" autocomplete="off">
						<label for="glUID">API ID *</label>
					</div>
				</div>
			</div>
		<cfelseif val(local.strAccount.qryAccount.GLAccountID)>
			<div class="form-row">
				<div class="col">
					<div class="form-label-group">
						<input type="text" name="glUIDRO" id="glUIDRO" value="#local.strAccount.qryAccount.uid#" class="form-control" maxlength="60" autocomplete="off" readonly="true">
						<label for="glUIDRO">API ID</label>
					</div>
				</div>
			</div>
		</cfif>

		<cfif local.strAccount.qryAccount.isSystemAccount is 0>
			<div class="form-group row no-gutters mt-2">
				<div class="col text-right">* indicates a required field</div>
			</div>
		<cfelseif local.accountTypeID is 5 and local.strParentAccount.qryAccount.GLCode eq "DEFERREDREVENUE">
			<cfset local.qryGLAccountsDef = local.objAccount.getGLAccountsByDeferredGL(orgID=arguments.event.getValue('mc_siteInfo.orgID'), deferredGLAccountID=local.strAccount.qryAccount.GLAccountID)>
			<div class="form-group row mt-2">
				<div class="col">
					This deferred revenue account is linked to #local.qryGLAccountsDef.recordcount# revenue account<cfif local.qryGLAccountsDef.recordcount is not 1>s</cfif>:
					<div class="mt-2">
						<cfloop query="local.qryGLAccountsDef">
							&bull; #local.qryGLAccountsDef.thePathExpanded#<cfif len(local.qryGLAccountsDef.accountCode)> (#local.qryGLAccountsDef.accountCode#)</cfif><br/>
						</cfloop>
					</div>
				</div>
			</div>
		<cfelseif local.accountTypeID is 5 and local.strParentAccount.qryAccount.GLCode eq "DEFERREDTAX">
			<cfset local.qryGLAccountsDef = local.objAccount.getGLAccountsByDeferredGL(orgID=arguments.event.getValue('mc_siteInfo.orgID'), deferredGLAccountID=local.strAccount.qryAccount.GLAccountID)>
			<div class="form-group row mt-2">
				<div class="col">
					This deferred sales tax account is linked to #local.qryGLAccountsDef.recordcount# sales tax account<cfif local.qryGLAccountsDef.recordcount is not 1>s</cfif>:
					<div class="mt-2">
						<cfloop query="local.qryGLAccountsDef">
							&bull; #local.qryGLAccountsDef.accountname#<cfif len(local.qryGLAccountsDef.accountCode)> (#local.qryGLAccountsDef.accountCode#)</cfif><br/>
						</cfloop>
					</div>
				</div>
			</div>
		</cfif>

	</div>
</div>
</form>
</cfoutput>