ALTER PROC dbo.sw_removeSeminarCredit
@seminarCreditID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @orgID int, @siteID int, @seminarID int, @programType varchar(4), @hasForm1 bit, @objectkey varchar(50), @s3DeleteReadyStatusID int,
		@nowdate datetime = getdate(), @msgjson varchar(max);

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID , @seminarID = sac.seminarID, 
		@programType = CASE WHEN swl.seminarID IS NOT NULL THEN 'SWL' WHEN swod.seminarID IS NOT NULL THEN 'SWOD' END,
		@msgjson = 'Credit Authority [' + ca.code + ' - ' + ca.authorityName + ' (' + cs.sponsorName + ')] removed from '
	FROM dbo.tblSeminarsAndCredit AS sac
	INNER JOIN dbo.tblSeminars AS s ON s.seminarID = sac.seminarID
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID
	INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID
	INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
	LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = sac.seminarID
	LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = sac.seminarID
	WHERE sac.seminarCreditID = @seminarCreditID;

	SET @msgjson = @msgjson + @programType + '-' + cast(@seminarID as varchar(10));
	SET @objectkey = 'swodprograms/form1_'+ cast(@seminarCreditID as varchar(10)) +'.pdf';
	SELECT @hasForm1 = hasForm1 FROM dbo.tblSeminarsAndCredit WHERE seminarCreditID = @seminarCreditID;

	IF NOT EXISTS (SELECT 1 FROM dbo.tblEnrollmentsAndCredit WHERE seminarCreditID = @seminarCreditID) BEGIN
		BEGIN TRAN;
			DELETE FROM dbo.tblSeminarsAndCredit WHERE seminarCreditID = @seminarCreditID;
			
			IF @programType = 'SWL' AND NOT EXISTS (SELECT TOP 1 seminarCreditID FROM dbo.tblSeminarsAndCredit WHERE seminarID = @seminarID)
				UPDATE dbo.tblSeminarsSWLive
				SET offerCredit = 0
				WHERE seminarID = @seminarID;

			IF @hasForm1 = 1 BEGIN
				EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;
				
				INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
				VALUES (@s3DeleteReadyStatusID, 'seminarweb', @objectkey, @nowdate, @nowdate);
			END

			IF @programType = 'SWL'
				EXEC dbo.swl_populateSearchText @seminarID = @seminarID;
			IF @programType = 'SWOD'
				EXEC dbo.swod_populateSearchText @seminarID = @seminarID;
		COMMIT TRAN;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES('{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + CAST(@orgID AS varchar(10)) + ',
			"SITEID":' + CAST(@siteID AS varchar(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
			"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
