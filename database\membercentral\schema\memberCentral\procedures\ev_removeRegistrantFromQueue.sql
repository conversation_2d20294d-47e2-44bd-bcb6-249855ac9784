ALTER PROC dbo.ev_removeRegistrantFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @orgID int, @queueTypeID int, @statusReady int, @statusProcessing int,
		@itemStatus int, @registrantID int, @AROption varchar(50), @cancellationFee decimal(19,2),
		@GLAccountID int, @deallocUsingPaidAmt bit, @recordedByMemberID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='evRegistrantDelete', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;
	
	select @itemStatus = statusID, @siteID = siteID, @orgID = orgID, @registrantID = registrantID,
		@AROption = AROption, @cancellationFee = cancellationFee, @GLAccountID = GLAccountID,
		@deallocUsingPaidAmt = deallocUsingPaidAmt, @recordedByMemberID = recordedByMemberID
	from platformQueue.dbo.queue_evRegistrantDelete
	where itemID = @itemID;

	-- if itemID is not readyToProcess, kick out now
	IF @itemStatus <> @statusReady
		RAISERROR('Item not in readyToProcess state',16,1);

	-- update to processingItem
	UPDATE platformQueue.dbo.queue_evRegistrantDelete
	SET statusID = @statusProcessing,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- Call the existing procedure to remove the registrant
	EXEC dbo.ev_removeRegistrant
		@registrantID = @registrantID,
		@recordedOnSiteID = @siteID,
		@recordedByMemberID = @recordedByMemberID,
		@statsSessionID = 0,
		@AROption = @AROption,
		@cancellationFee = @cancellationFee,
		@cancellationFeeGLAccountID = @GLAccountID,
		@deallocateFeeByAllocatedPayments = @deallocUsingPaidAmt;

	-- Delete the queue item after processing
	DELETE FROM platformQueue.dbo.queue_evRegistrantDelete
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO