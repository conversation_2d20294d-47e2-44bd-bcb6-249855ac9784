<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="20" } ];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_goDaddyOrders_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryOrders" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryOrders.recordCount>

			<cfloop query="local.qryOrders">
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='goDaddyOrders', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_goDaddyOrders
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrders.itemID#">;
				</cfquery>

				<cfset local.addOrderResult = addOrder(goDaddyOrderID=local.qryOrders.orderID)>

				<cfif local.addOrderResult.success>
					<cfquery name="local.qryRemoveFromQueue" datasource="#application.dsn.platformQueue.dsn#">
						DELETE FROM dbo.queue_goDaddyOrders
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrders.itemID#">
					</cfquery>
				</cfif>
			</cfloop>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addOrder" access="private" output="false" returntype="struct">
		<cfargument name="goDaddyOrderID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStr = { success: false }>

		<cfset local.getOrderURL = "https://api.godaddy.com/v1/orders/#arguments.goDaddyOrderID#">
		<cfset local.apiAuthorization = "sso-key 2uPXGHs2Qg_GSEewngShxwQxT13SCSrc2:MXUXgHQQg2Nb3ZvLqeiNaG">
					
		<cfhttp method="get" url="#local.getOrderURL#" throwonerror="Yes" charset="utf-8" result="local.APIResult" useragent="MemberCentral.com">
			<cfhttpparam type="header" name="Authorization" value="#local.apiAuthorization#">
		</cfhttp>

		<cfset local.apiJSONResponse = toString(trim(local.APIResult.fileContent))>
		<cfset local.strAPIResponse = deserializeJSON(local.apiJSONResponse)>
		<cfif isArray(local.strAPIResponse.items) AND arrayLen(local.strAPIResponse.items)>
			<cfquery name="local.qryPopulateTable" datasource="#application.dsn.platformStatsMC.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tblOrders') IS NOT NULL
						DROP TABLE ##tblOrders;
					CREATE TABLE ##tblOrders (row int IDENTITY(1,1), GoDaddyOrderID varchar(20), orderDate datetime, domain varchar(200),
						totalPrice decimal(18, 2), orderDetail varchar(600), siteID int, dateLinkedToSite datetime);
					
					<cfloop array="#local.strAPIResponse.items#" index="local.thisItem">
						<!--- The price given is in a format called currency-micro-unit. Divide the price by 10^6 you'll get the correct value --->
						<cfset local.amountInDollars = precisionEvaluate((local.thisItem.pricing.subtotal + local.thisItem.pricing.taxes + local.thisItem.pricing.fees.total) / 1000000)>

						<cfif structKeyExists(local.thisItem,"domains")>
							INSERT INTO ##tblOrders (GoDaddyOrderID, orderDate, domain, totalPrice, orderDetail, siteID, dateLinkedToSite)
	     					VALUES (
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.goDaddyOrderID#">,
								<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.strAPIResponse.createdAt#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.thisItem.domains[1]#">,
								<cfqueryparam cfsqltype="cf_sql_decimal" value="#local.amountInDollars#" scale="2">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisItem.domains[1] & ' ' & local.thisItem.label & ' (' & local.thisItem.period & ' ' & local.thisItem.periodUnit & ')',600)#">,
								NULL,
								NULL
							);
	     				</cfif>
					</cfloop>

					UPDATE o
					SET o.siteID = h.siteID,
						o.dateLinkedToSite = getdate()
					FROM ##tblOrders o
					INNER JOIN membercentral.dbo.siteHostNames h ON h.hostName = o.domain;

					BEGIN TRAN;
						MERGE dbo.site_GoDaddyOrders AS o USING ##tblOrders AS tmp ON o.GoDaddyOrderID = tmp.GoDaddyOrderID
							AND o.orderDate = tmp.orderDate
							AND o.domain = tmp.domain
							AND o.orderDetail = tmp.orderDetail
						WHEN NOT MATCHED THEN
							INSERT (GoDaddyOrderID, orderDate, domain, totalPrice, orderDetail, siteID, dateLinkedToSite)
							VALUES (tmp.GoDaddyOrderID, tmp.orderDate, tmp.domain, tmp.totalPrice, tmp.orderDetail, tmp.siteID, tmp.dateLinkedToSite);
					COMMIT TRAN;

					IF OBJECT_ID('tempdb..##tblOrders') IS NOT NULL
						DROP TABLE ##tblOrders;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStr.success = true>
		</cfif>

		<cfreturn local.returnStr>
	</cffunction>

</cfcomponent>