<cfcomponent>
	<cffunction name="getDocumentDataBySiteResourceID" access="public" output="true" returntype="struct" hint="Get Single Document Data">
		<cfargument name="siteResourceID" type="numeric" required="true"/>

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDocument">
			SELECT d.documentID, dv.documentVersionID
			FROM dbo.cms_documents as d
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID AND dl.languageID = 1
			INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID AND isActive = 1
			WHERE d.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn getDocumentData(documentID=local.qryDocument.documentID, documentVersionID=local.qryDocument.documentVersionID, activeOnly=1, lang='en')>
	</cffunction>

	<cffunction name="getDocumentData" access="public" output="true" returntype="struct" hint="Get Single Document Data">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="documentVersionID" type="numeric" required="false" default="0">
		<cfargument name="activeOnly" type="boolean" required="false" default="true">
		<cfargument name="lang" type="string" required="true" default="#session.mcstruct.languageCode#">

		<cfscript>
		var local = structNew();
		local.data = getDocument(documentID=arguments.documentID, documentVersionID=arguments.documentVersionID, activeOnly=arguments.activeOnly, lang=arguments.lang);
		local.docData = structNew();
		</cfscript>

		<cfloop list="#local.data.columnList#" index="local.thisField">
			<cfset local.docData[local.thisField] = local.data[local.thisField]>
		</cfloop>

		<cfreturn local.docData>
	</cffunction>
	
	<cffunction name="getAdminDocumentData" access="public" output="false" returntype="struct" hint="Get Single Document Data">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="documentVersionID" type="numeric" required="true">
		<cfargument name="activeOnly" type="boolean" required="false" default="true">
		<cfargument name="lang" type="string" required="false" default="#session.mcstruct.languageCode#">
		<cfargument name="activeSiteResource" type="boolean" required="false" default="true">

		<cfscript>
		var local = structNew();
		local.data = getDocument(documentID=arguments.documentID, documentVersionID=arguments.documentVersionID, activeOnly=arguments.activeOnly, lang=arguments.lang, activeSiteResource=arguments.activeSiteResource);
		local.docData = structNew();
		</cfscript>

		<cfloop list="#local.data.columnList#" index="local.thisField">
			<cfset local.docData[local.thisField] = local.data[local.thisField]>
		</cfloop>
		<cfset local.args = arguments>

		<cfreturn local.docData>
	</cffunction>
	
	<cffunction name="getDocument" access="private" output="false" returntype="query">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="documentVersionID" type="numeric" required="false" default="0">
		<cfargument name="activeOnly" type="boolean" required="true">
		<cfargument name="lang" type="string" required="true">
		<cfargument name="activeSiteResource" type="boolean" required="false" default="true">

		<cfset var qryDocument = "">
	
		<cfquery name="qryDocument" datasource="#application.dsn.memberCentral.dsn#">			
		SELECT top 1 
			d.documentID, d.sectionID, d.siteResourceID, 
			o.orgid, o.orgcode,	
			s.siteID, s.sitecode, 
			sr.resourceTypeID,			 
			COALESCE(l.docTitle, lDef.docTitle)  as docTitle,
			COALESCE(l.docDesc, lDef.docDesc)  as docDesc,
			COALESCE(l.documentLanguageID, lDef.documentLanguageID)  as documentLanguageID,
			COALESCE(l.dateCreated, lDef.dateCreated)  as dateCreated,
			COALESCE(l.languageID, lDef.languageID)  as languageID,
			COALESCE(lang.languageCode, langDef.languageCode) as languageCode,
			COALESCE(v.dateModified, vDef.dateModified)  as dateModified,
			COALESCE(v.publicationDate, vDef.publicationDate)  as publicationDate,
			COALESCE(v.author, vDef.author)  as author,
			COALESCE(v.contributorMemberID, vDef.contributorMemberID)  as contributorMemberID,
			COALESCE(v.fileName, vDef.fileName)  as fileName,
			COALESCE(v.fileExt, vDef.fileExt)  as fileExt,				
			COALESCE(v.documentVersionID, vDef.documentVersionID) as documentVersionID,		
			COALESCE(m2.memberid, m4.memberid)  as memberid,
			COALESCE(m2.firstname, m4.firstname)  as firstname,
			COALESCE(m2.middlename, m4.middlename)  as middlename,
			COALESCE(m2.lastname, m4.lastname)  as lastname,				
			sr.siteResourceStatusID,
			isnull(rd.redirectID,'0') as redirectID, rd.redirectName,
			ps.thePathExpanded as expandedPath
		FROM 
			dbo.cms_documents as d
			INNER JOIN dbo.sites AS s ON s.siteID = d.siteID
			INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
			INNER JOIN dbo.cms_pageSections ps ON ps.sectionID = d.sectionID
	
			LEFT OUTER JOIN dbo.cms_documentLanguages as l 
				INNER JOIN dbo.cms_languages as lang on lang.languageID = l.languageID
				INNER JOIN dbo.cms_documentVersions as v on l.documentLanguageID = v.documentLanguageID  
				<cfif arguments.activeOnly or (arguments.documentVersionID eq 0)>
					AND v.isActive = 1 
				</cfif>
				<cfif arguments.documentVersionID neq 0>
					AND v.documentVersionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentVersionID#">
				</cfif>					
				INNER JOIN dbo.ams_members m on m.memberid = v.contributorMemberID
				INNER JOIN dbo.ams_members m2 on m2.memberid = m.activeMemberID
			ON d.documentID = l.documentID  AND l.languageID = isnull(dbo.fn_getLanguageID(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.lang#">),0) 
	
			LEFT OUTER JOIN dbo.cms_documentLanguages as lDef 
				INNER JOIN dbo.cms_languages as langDef on langDef.languageID = lDef.languageID
				INNER JOIN dbo.cms_documentVersions as vDef on vDef.documentLanguageID = lDef.documentLanguageID 
				<cfif arguments.activeOnly or (arguments.documentVersionID eq 0)>
					AND vDef.isActive = 1 
				</cfif>
				<cfif arguments.documentVersionID neq 0>
					AND vDef.documentVersionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentVersionID#">
				</cfif>	
				INNER JOIN dbo.ams_members m3 on m3.memberid = vDef.contributorMemberID
				INNER JOIN dbo.ams_members m4 on m4.memberid = m3.activeMemberID
			ON d.documentID = lDef.documentID  
				AND lDef.languageID = (SELECT s.defaultLanguageID FROM dbo.sites as s INNER JOIN dbo.cms_documents as d2 on d2.siteID = s.siteID WHERE d2.documentID = d.documentID)
	
			INNER JOIN dbo.cms_siteResources AS sr ON d.siteResourceID = sr.siteResourceID
			<cfif arguments.activeSiteResource>
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			</cfif>
			LEFT OUTER JOIN dbo.siteRedirects rd ON d.redirectID = rd.redirectID	
		WHERE 
			d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">			
		</cfquery>

		<cfreturn qryDocument>
	</cffunction>
	
	<cffunction name="getDocLanguages" access="public" output="false" returntype="query">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var qryDocLanguages = "">
		
		<cfif arguments.documentID neq 0>
			<cfquery name="qryDocLanguages" datasource="#application.dsn.memberCentral.dsn#">
				SELECT DISTINCT 
					v.documentVersionID, v.fileName, v.dateCreated, v.author, v.PublicationDate, dl.dateModified, l.languageName, l.languageCode
					, dl.docTitle, dl.docDesc, dl.languageID, dl.documentLanguageID, d.documentID
					,(SELECT COUNT(v.documentVersionID) FROM dbo.cms_documents d 
						INNER JOIN cms_documentLanguages l on d.documentID = l.documentID 
						INNER JOIN cms_documentVersions v on l.documentLanguageID = v.documentLanguageID
		    			WHERE d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
		    			AND l.languageID = dl.languageID) as versionsTotal
				FROM dbo.cms_documents d
				INNER JOIN dbo.cms_documentLanguages dl on d.documentID = dl.documentID 
				INNER JOIN dbo.cms_languages l ON dl.languageID = l.languageID
				INNER JOIN dbo.cms_documentVersions v ON dl.documentLanguageID = v.documentLanguageID 
					AND v.isActive = 1 
				WHERE d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
				ORDER BY dl.languageID
			</cfquery>		
		<cfelse>
			<cfquery name="qryDocLanguages" datasource="#application.dsn.memberCentral.dsn#">
				SELECT '' as documentVersionID, '' as fileName, '' as dateCreated, '' as author, '' as PublicationDate, '' as dateModified
					, l.languageName, l.languageCode, '' as docTitle, '' as docDesc, #session.mcstruct.languageID# as languageID
					, #session.mcstruct.languageID# as documentLanguageID, '' as documentID
					, 0 as versionsTotal
				FROM dbo.cms_languages as l 
				WHERE l.languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.mcstruct.languageID#">
			</cfquery>		
		</cfif>
		
		<cfreturn qryDocLanguages>
	</cffunction>
	
	<cffunction name="updateDocument" access="public" output="false" returntype="numeric" hint="Update Document">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="author" type="string" required="true">
		<cfargument name="docTitle" type="string" required="true">
		<cfargument name="docDesc" type="string" required="true">
		<cfargument name="fileData" type="struct" required="true">
		<cfargument name="sectionID" type="numeric" required="false" default="0">
		<cfargument name="contributorMemberID" type="numeric" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">
		<cfargument name="newFileUploaded" type="boolean" required="true">
		<cfargument name="languageID" type="numeric" required="false" default="#session.mcstruct.languageID#">
		<cfargument name="publicationDate" type="date" required="false">
		<cfargument name="delayInMinutes" type="numeric" required="false" default="0">
		<cfargument name="oldFileExt" type="string" required="false" default="">

		<cfset var local = structNew()>

		<cfif structKeyExists(arguments.fileData,"fileName")>
			<cfset local.fileName = arguments.fileData.fileName>
		<cfelse>
			<cfset local.fileName = arguments.fileData.clientFile>
		</cfif>
		<cfif structKeyExists(arguments.fileData,"fileExt")>
			<cfset local.fileExt = arguments.fileData.fileExt>
		<cfelse>
			<cfset local.fileExt = arguments.fileData.serverFileExt>
		</cfif>
			
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#"  procedure="cms_updateDocument">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contributorMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfif len(arguments.author)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.author#">	
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="Yes">
				</cfif>	
				<cfif isDefined('arguments.publicationDate')>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.publicationDate#">	
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docTitle#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docDesc#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fileName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fileExt#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.newFileUploaded#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.documentVersionID">
			</cfstoredproc>

			<cfif arguments.newFileUploaded>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySite">
					SELECT s.siteCode, o.orgCode
					FROM dbo.sites as s
					INNER JOIN dbo.organizations as o on o.orgID = s.orgID
					WHERE s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				</cfquery>

				<!--- now move doc to proper location, rename the file, and add to s3 Upload Queue --->
				<cfset processUploadedFile(orgcode=local.qrySite.orgcode, sitecode=local.qrySite.sitecode, fileData=arguments.fileData, 
					documentVersionID=local.documentVersionID, delayInMinutes=arguments.delayInMinutes, oldFileExt=arguments.oldFileExt)>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.documentVersionID = 0>
		</cfcatch>
		</cftry>
		
		<cfreturn local.documentVersionID>
	</cffunction>
	
	<cffunction name="insertVersion" access="public" output="false" returntype="numeric" hint="creates a new version of a document">
		<cfargument name="orgcode" type="string" required="true">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="fileData" type="struct" required="true">
		<cfargument name="documentLanguageID" type="numeric" required="true">
		<cfargument name="contributorMemberID" type="numeric" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">
		<cfargument name="author" type="string" required="true">
		<cfargument name="isActive" type="numeric" required="false" default="1">
		<cfargument name="delayInMinutes" type="numeric" required="false" default="0">
		<cfargument name="oldFileExt" type="string" required="false" default="">
		<cfargument name="publicationDate" type="date" required="false">

		<cfset var local = structNew()>

		<cfif structKeyExists(arguments.fileData,"fileName")>
			<cfset local.fileName = arguments.fileData.fileName>
		<cfelse>
			<cfset local.fileName = arguments.fileData.clientFile>
		</cfif>
		<cfif structKeyExists(arguments.fileData,"fileExt")>
			<cfset local.fileExt = arguments.fileData.fileExt>
		<cfelse>
			<cfset local.fileExt = arguments.fileData.clientFileExt>
		</cfif>
				
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_createDocumentVersion">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.documentLanguageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fileName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fileExt#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.author#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contributorMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isActive#">
				<cfif structKeyExists(arguments,"publicationDate")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.publicationDate#">	
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
				</cfif>	
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.documentVersionID">
			</cfstoredproc>

			<!--- now move doc to proper location, rename the file, and add to s3 Upload Queue --->
			<cfset processUploadedFile(orgcode=arguments.orgcode, sitecode=arguments.sitecode, fileData=arguments.fileData, 
				documentVersionID=local.documentVersionID, delayInMinutes=arguments.delayInMinutes, oldFileExt=arguments.oldFileExt)>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.documentVersionID = 0>
		</cfcatch>
		</cftry>
		
		<cfreturn local.documentVersionID>
	</cffunction>
	
	<cffunction name="insertDocument" access="public" output="false" returntype="struct" hint="Insert Document">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="sectionID" type="numeric" required="false" default="0">
		<cfargument name="resourceType" type="string" required="true" default="UserCreatedDocument" hint="ApplicationCreatedDocument, UserCreatedDocument, SystemCreatedDocument">
		<cfargument name="parentSiteResourceID" type="numeric" required="true" default="0">
		<cfargument name="docTitle" type="string" required="true">
		<cfargument name="docDesc" type="string" required="true">
		<cfargument name="author" type="string" required="true">
		<cfargument name="fileData" type="struct" required="true">
		<cfargument name="isActive" type="boolean" required="false" default="True">
		<cfargument name="contributorMemberID" type="numeric" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">
		<cfargument name="publicationDate" type="date" required="false">
		<cfargument name="isVisible" type="boolean" required="false" default="True">
		<cfargument name="delayInMinutes" type="numeric" required="false" default="0">
		<cfargument name="oldFileExt" type="string" required="false" default="">
		
		<cfset var local = structNew()>
											
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryResource">
			SET NOCOUNT ON;

			DECLARE @siteID int, @resourceTypeID int, @sectionID int;
			
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SELECT @resourceTypeID = dbo.fn_getResourceTypeId(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ResourceType#">);

			<cfif arguments.sectionID gt 0>
				SET @sectionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionID#">;
			<cfelse>
				SELECT @sectionID = dbo.fn_getRootSectionID(@siteID);
			</cfif>

			SELECT s.siteCode, o.orgCode, @resourceTypeID as ResourceTypeID, 1 as siteResourceStatusID, @sectionID as sectionID
			FROM dbo.sites as s 
			INNER JOIN dbo.organizations as o on o.orgID = s.orgID
			WHERE s.siteID = @siteID;
		</cfquery>

		<cfif structKeyExists(arguments.fileData,"fileName")>
			<cfset local.fileName = arguments.fileData.fileName>
		<cfelse>
			<cfset local.fileName = arguments.fileData.clientFile>
		</cfif>
		<cfif structKeyExists(arguments.fileData,"fileExt")>
			<cfset local.fileExt = arguments.fileData.fileExt>
		<cfelse>
			<cfset local.fileExt = arguments.fileData.serverFileExt>
		</cfif>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#"  procedure="cms_createDocument">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryResource.ResourceTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryResource.siteResourceStatusID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.mcstruct.languageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryResource.sectionID#">	
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contributorMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isActive#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isVisible#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docTitle#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docDesc#">
				<cfif len(arguments.author)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.author#">	
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="Yes">
				</cfif>	
				<cfif isDefined('arguments.publicationDate')>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.publicationDate#">	
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
				</cfif>	
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fileName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fileExt#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.insertDocument.documentID">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.insertDocument.documentVersionID">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.insertDocument.documentSiteResourceID">
			</cfstoredproc>
			<cfif arguments.parentSiteResourceID>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.siteResourceID">
					UPDATE dbo.cms_siteResources
					SET	parentsiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.parentSiteResourceID#">
					WHERE siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.insertDocument.documentSiteResourceID#">
				</cfquery>
			</cfif>

			<!--- now move doc to proper location, rename the file, and add to s3 Upload Queue --->
			<cfset processUploadedFile(orgcode=local.qryResource.orgcode, sitecode=local.qryResource.sitecode, fileData=arguments.fileData, 
				documentVersionID=local.insertDocument.documentVersionID, delayInMinutes=arguments.delayInMinutes, oldFileExt=arguments.oldFileExt)>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.insertDocument = { documentID=0, documentVersionID=0, documentSiteResourceID=0 }>
		</cfcatch>
		</cftry>

		<cfreturn local.insertDocument>
	</cffunction>	

	<cffunction name="deleteDocument" access="public" output="false" returntype="void" hint="Marks file in database as Deleted">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">

		<cfquery name="qryDeleteDocument" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpDocumentsToDelete') IS NOT NULL
				DROP TABLE ##tmpDocumentsToDelete;
			CREATE TABLE ##tmpDocumentsToDelete (documentID int, siteID int);

			INSERT INTO ##tmpDocumentsToDelete (documentID, siteID)
			VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">);

			EXEC dbo.cms_deleteDocuments;

			IF OBJECT_ID('tempdb..##tmpDocumentsToDelete') IS NOT NULL
				DROP TABLE ##tmpDocumentsToDelete;
		</cfquery>
	</cffunction>
	
	<cffunction name="uploadFile" access="public" output="false" returntype="struct" hint="upload file to server">
		<cfargument name="fileToUpload" type="string" required="true">

		<cfset var local = structNew()/>
			
		<cftry>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='mc')>
			<cffile action="UPLOAD" filefield="#arguments.fileToUpload#" destination="#local.strFolder.folderPath#" nameconflict="MAKEUNIQUE" result="local.fileData">

			<!--- Fix the issue with : being in the file name from Mac Uploads --->
			<cfset local.fileData.serverFileExt = local.fileData.ClientFileExt>
			<cfset local.badClientFileName = local.fileData.clientFileName>			
			<cfset local.fileData.clientFileName = replace(local.fileData.clientFileName, ":", "-", "ALL")>			
			<cfset local.fileData.clientDirectory = replace(local.fileData.clientDirectory, local.badClientFileName, local.fileData.clientFileName, "ALL")>
			<cfset local.fileData.clientFile = replace(local.fileData.clientFile, local.badClientFileName, local.fileData.clientFileName, "ALL")>
			
			<!--- Reset to original file name after removing : because other apps rely on original file name --->
			<cffile action="move" 
						source="#local.filedata.ServerDirectory#/#local.filedata.ServerFile#" 
						destination="#local.filedata.ServerDirectory#/#local.filedata.ServerFileName#.#local.fileData.clientFileExt#">
			
			<cfset local.fileData.serverFile = "#local.filedata.ServerFileName#.#local.fileData.clientFileExt#" />
			<cfset local.fileData.serverFileExt = local.fileData.clientFileExt />
			
			<cfset local.fileData.uploadComplete = TRUE>
			<cfset local.fileData.ReasonText = "">
			<cfcatch type="VirusScan">
				<cfset local.fileData.ReasonText = cfcatch.Message>
				<cfset local.fileData.uploadComplete = FALSE>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.fileData.ReasonText = "Failed to upload the file.">
				<cfset local.fileData.uploadComplete = FALSE>
			</cfcatch>		
		</cftry>				

		<cfreturn local.fileData />
	</cffunction>

	<cffunction name="processUploadedFile" access="private" output="false" returnType="void">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="fileData" type="struct" required="true">
		<cfargument name="documentVersionID" type="numeric" required="true">
		<cfargument name="delayInMinutes" type="numeric" required="true">
		<cfargument name="oldFileExt" type="string" required="true">

		<cfscript>
			var local = structNew();
			try{
				// set the sourceFile to the location of the uploaded file
				local.sourceFile = arguments.fileData.serverDirectory & "/" & arguments.fileData.serverFile;
				// set the destinationFile to the location of where the file is going
				local.destinationFile = application.paths.RAIDSiteDocuments.path & arguments.orgCode & "/" & arguments.siteCode & "/" & arguments.documentVersionID & "." & arguments.fileData.serverFileExt;
				// move doc to proper location and rename the file to the documentVersionID
				fileMove(local.sourceFile, local.destinationFile);
				// if the ext of the newFile is different from the oldFile then we need to delete oldFile
				if( arguments.oldFileExt NEQ arguments.fileData.serverFileExt ){
					// get path of old file that will not be over written due to new fileExt
					local.oldFile = application.paths.RAIDSiteDocuments.path & arguments.orgCode & "/" & arguments.siteCode & "/" & arguments.documentVersionID & "." & arguments.oldFileExt;
					// delete old File if it exists
					if( fileExists(local.oldFile) ){
						fileDelete(local.oldFile);
					}
				}
				addToS3UploadQueue(orgCode=arguments.orgCode, siteCode=arguments.siteCode, documentVersionID=arguments.documentVersionID, 
					fileExt=arguments.fileData.serverFileExt, delayInMinutes=arguments.delayInMinutes);
			} catch(e) {
				application.objError.sendError(cfcatch=cfcatch, objectToDump=local);
				rethrow;
			}
		</cfscript>
	</cffunction>

	<cffunction name="addToS3UploadQueue" access="private" output="false" returntype="void">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="documentVersionID" type="numeric" required="true">
		<cfargument name="fileExt" type="string" required="true">
		<cfargument name="delayInMinutes" type="numeric" required="true">

		<cfset var qryAddDocToS3UploadQueue = "">

		<cfquery name="qryAddDocToS3UploadQueue" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteCode varchar(10), @orgCode varchar(10), @documentVersionID int, @fileExt varchar(20), @s3keyMod varchar(4), 
				@objectKey varchar(400), @s3bucketName varchar(100), @siteDocumentsPath varchar(40), @filePath varchar(400),
				@s3UploadReadyStatusID int, @delayInMinutes int, @dateToUse datetime;

			SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;
			SET @orgCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgCode#">;
			SET @documentVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentVersionID#">;
			SET @fileExt = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileExt#">;
			SET @s3bucketName = 'membercentralcdn';
			SET @delayInMinutes = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.delayInMinutes#">;
			IF @delayInMinutes > 0
				SET @dateToUse = dateadd(n,@delayInMinutes,getdate());
			ELSE
				SET @dateToUse = getdate();

			EXEC dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

			select @siteDocumentsPath = siteDocumentsPath from memberCentral.dbo.fn_getServerSettings();
			SET @filePath = lower(@siteDocumentsPath + @orgCode + '\' + @siteCode + '\' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);

			SET @s3keyMod = FORMAT(@documentVersionID % 1000, '0000');
			SET @objectKey = LOWER('sitedocuments/' + @orgCode + '/' + @siteCode + '/' + @s3keyMod + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);

			IF NOT EXISTS (select 1 from dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
				INSERT INTO dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
				VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePath, 1, @dateToUse, @dateToUse);
		</cfquery>
	</cffunction>

	<cffunction name="forceFileExtentionIfBlank" access="public" output="false" returnType="void">
		<cfargument name="fileData" type="struct" required="true">

		<cfset var local = structNew()>
		<cfif NOT len(trim(arguments.fileData.serverFileExt))>
			<cfset arguments.fileData.clientFileExt = "DAT">
			<cfset arguments.fileData.serverFileExt = arguments.fileData.clientFileExt>
			<cfset arguments.fileData.clientFile &= "." & arguments.fileData.clientFileExt>			
		</cfif>
	</cffunction>

	<cffunction name="copyDocument" access="public" output="false" returntype="struct" hint="Copies a document">
		<cfargument name="sourceDocumentID" type="numeric" required="true">
		<cfargument name="destinationSiteID" type="numeric" required="true">
		<cfargument name="destinationSectionID" type="numeric" required="true">
		<cfargument name="contributorMemberID" type="numeric" required="true"/>
		<cfargument name="recordedByMemberID" type="numeric" required="true"/>
		
		<cfset var local = structNew()/>
		<cfset local.strResult = { "success":false, "documentID":0 }>

		<cftry>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#"  procedure="cms_copyDocument">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceDocumentID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.destinationSiteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.destinationSectionID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contributorMemberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newDocumentID">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newDocumentVersionID">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newDocumentSiteResourceID">
		</cfstoredproc>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySourceDocumentVersions">
			SELECT d.siteID, dv.documentVersionID, dv.fileExt, dl.languageID
			FROM dbo.cms_documents as d
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
			WHERE d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sourceDocumentID#">;
		</cfquery>

		<cfset local.sourceSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.qrySourceDocumentVersions.siteID)>
		<cfset local.strSourceSiteInfo = application.objSiteInfo.getSiteInfo(siteCode=local.sourceSiteCode)>
		<cfset local.destinationSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.destinationSiteID)>
		<cfset local.strDestinationSiteInfo = application.objSiteInfo.getSiteInfo(siteCode=local.destinationSiteCode)>

		<cfset local.arrCopyDocumentInfo = []>

		<cfif local.qrySourceDocumentVersions.recordCount eq 1>
			<cfset arrayAppend(local.arrCopyDocumentInfo, {
				sourceDocVersionID = local.qrySourceDocumentVersions.documentVersionID,
				destinationDocVersionID = local.newDocumentVersionID,
				fileExt = local.qrySourceDocumentVersions.FileExt,
				sourceFile = application.paths.RAIDSiteDocuments.path & local.strSourceSiteInfo.orgCode & "/" & local.strSourceSiteInfo.siteCode & "/" & local.qrySourceDocumentVersions.documentVersionID & "." & local.qrySourceDocumentVersions.FileExt,
				destinationFile = application.paths.RAIDSiteDocuments.path & local.strDestinationSiteInfo.orgCode & "/" & local.strDestinationSiteInfo.siteCode & "/" & local.newDocumentVersionID & "." & local.qrySourceDocumentVersions.FileExt
			})>
		<cfelseif local.qrySourceDocumentVersions.recordCount gt 1> <!--- If there are any additional languages with active versions --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDestinationDocumentVersions">
				SELECT dv.documentVersionID, dl.languageID
				FROM dbo.cms_documents as d
				INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
				WHERE d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.newDocumentID#">;
			</cfquery>

			<cfloop query="local.qrySourceDocumentVersions">
				<cfquery name="local.thisDestinationDocumentVersion" dbtype="query">
					SELECT documentVersionID
					FROM [local].qryDestinationDocumentVersions
					WHERE languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySourceDocumentVersions.languageID#">
				</cfquery>

				<cfset arrayAppend(local.arrCopyDocumentInfo, {
					sourceDocVersionID = local.qrySourceDocumentVersions.documentVersionID,
					destinationDocVersionID = local.thisDestinationDocumentVersion.documentVersionID,
					fileExt = local.qrySourceDocumentVersions.FileExt,
					sourceFile = application.paths.RAIDSiteDocuments.path & local.strSourceSiteInfo.orgCode & "/" & local.strSourceSiteInfo.siteCode & "/" & local.qrySourceDocumentVersions.documentVersionID & "." & local.qrySourceDocumentVersions.FileExt,
					destinationFile = application.paths.RAIDSiteDocuments.path & local.strDestinationSiteInfo.orgCode & "/" & local.strDestinationSiteInfo.siteCode & "/" & local.thisDestinationDocumentVersion.documentVersionID & "." & local.qrySourceDocumentVersions.FileExt
				})>
			</cfloop>
		</cfif>

		<cfloop array="#local.arrCopyDocumentInfo#" index="local.thisCopyDocumentInfo">
			<cfif fileExists(local.thisCopyDocumentInfo.sourceFile)>
				<cfset fileCopy(local.thisCopyDocumentInfo.sourceFile, local.thisCopyDocumentInfo.destinationFile)>
			</cfif>
		</cfloop>

		<cfif application.MCEnvironment eq "production">
			<cfquery name="qryS3CopyQueue" datasource="#application.dsn.platformQueue.dsn#">
				SET NOCOUNT ON;

				DECLARE @sourceDocVersionID int, @destinationDocVersionID int, @fileExt varchar(20), @sourceS3keyMod varchar(4), @destinationS3keyMod varchar(4), 
					@sourceObjectKey varchar(200), @destinationObjectKey varchar(200), @s3bucketName varchar(100),
					@s3CopyReadyStatusID int, @nowDate datetime = getdate();
				SET @s3bucketName = 'membercentralcdn';
				EXEC dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;

				<cfloop array="#local.arrCopyDocumentInfo#" index="local.thisCopyDocumentInfo">
					SET @sourceDocVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCopyDocumentInfo.sourceDocVersionID#">;
					SET @destinationDocVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCopyDocumentInfo.destinationDocVersionID#">;
					SET @fileExt = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisCopyDocumentInfo.fileExt#">;

					SET @sourceS3keyMod = FORMAT(@sourceDocVersionID % 1000, '0000');
					SET @destinationS3keyMod = FORMAT(@destinationDocVersionID % 1000, '0000');
					SET @sourceObjectKey = LOWER('sitedocuments/#local.strSourceSiteInfo.orgCode#/#local.strSourceSiteInfo.siteCode#/' + @sourceS3keyMod + '/' + cast(@sourceDocVersionID as varchar(10)) + '.' + @fileExt);
					SET @destinationObjectKey = LOWER('sitedocuments/#local.strDestinationSiteInfo.orgCode#/#local.strDestinationSiteInfo.siteCode#/' + @destinationS3keyMod + '/' + cast(@destinationDocVersionID as varchar(10)) + '.' + @fileExt);

					INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
					VALUES (@s3bucketName, @sourceObjectKey, @s3bucketName, @destinationObjectKey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
				</cfloop>
			</cfquery>
		</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfset local.strResult.success = true>
		<cfset local.strResult.documentID = local.newDocumentID>
		<cfset local.strResult.documentSiteResourceID = local.newDocumentSiteResourceID>
		<cfreturn local.strResult>
	</cffunction>

</cfcomponent>