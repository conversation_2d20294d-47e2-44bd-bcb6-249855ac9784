<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">

		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.sendOrdersResult = sendOrders(batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.sendOrdersResult.success>
			<cfthrow message="Error running sendOrders()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.sendOrdersResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="sendOrders" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfloop from="1" to="#arguments.batchSize#" index="local.thisItem">
				<cftry>
					<cfstoredproc procedure="queue_taxjarorders_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
						<cfprocresult name="local.qryOrder" resultset="1">
						<cfprocresult name="local.qryOrderDetails" resultset="2">
					</cfstoredproc>

					<cfif local.qryOrder.recordcount and local.qryOrderDetails.recordcount>
						<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;

							declare @statusProcessing int;
							EXEC dbo.queue_getStatusIDbyType @queueType='taxjarorders', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

							UPDATE dbo.queue_taxjarorders
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrder.itemID#">;
						</cfquery>

						<cfset local.arrLineItems = arrayNew(1)>
						<cfloop query="local.qryOrderDetails">
							<cfset local.strTemp = { product_tax_code=local.qryOrderDetails.product_tax_code, 
													 unit_price=local.qryOrderDetails.unit_price, 
													 sales_tax=local.qryOrderDetails.sales_tax }>
							<cfset arrayAppend(local.arrLineItems,local.strTemp)>
						</cfloop>

						<!--- if net amount is positive, issue an order. --->
						<cfif local.qryOrder.amount gte 0>
							<cfset local.strOrder = createOrder(orgID=local.qryOrder.orgID, apiKey=local.qryOrder.apiKey, transaction_id=local.qryOrder.transaction_id, 
																transaction_date=dateformat(local.qryOrder.transaction_date,"YYYY-MM-DD"), to_zip=local.qryOrder.to_zip, 
																to_state=local.qryOrder.to_state, amount=local.qryOrder.amount, sales_tax=local.qryOrder.sales_tax,
																arrLineItems=local.arrLineItems)>

						<!--- if net amount is negative, issue a refund. --->
						<cfelse>
							<cfset local.strOrder = createRefund(orgID=local.qryOrder.orgID, apiKey=local.qryOrder.apiKey, transaction_id=local.qryOrder.transaction_id, 
																transaction_date=dateformat(local.qryOrder.transaction_date,"YYYY-MM-DD"), to_zip=local.qryOrder.to_zip, 
																to_state=local.qryOrder.to_state, amount=local.qryOrder.amount, sales_tax=local.qryOrder.sales_tax,
																arrLineItems=local.arrLineItems)>
						</cfif>

						<cfif local.strOrder.orderCreated>
							<cfquery name="local.qryUpdateStatus" datasource="#application.dsn.platformQueue.dsn#">
								set nocount on;

								declare @statusDone int;
								EXEC dbo.queue_getStatusIDbyType @queueType='taxjarorders', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

								UPDATE dbo.queue_taxjarorders
								SET statusID = @statusDone,
									dateUpdated = getdate()
								WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrder.itemID#">;
							</cfquery>
						</cfif>

						<cfset local.returnStruct.itemCount++>
					<cfelse>
						<cfbreak>
					</cfif>

				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>	
			</cfloop>

			<!--- --------------------------------------------------- --->
			<!--- post processing - delete any itemIDs that are done --->
			<!--- --------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_taxjarorders_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>

			<cfset local.returnStruct.success = true>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>			

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createOrder" access="private" returntype="struct" output="no">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="apiKey" type="string" required="yes">
		<cfargument name="transaction_id" type="string" required="yes" hint="Unique identifier of the given order transaction.">
		<cfargument name="transaction_date" type="string" required="yes" hint="YYYY-MM-DD">
		<cfargument name="to_zip" type="string" required="yes" hint="Postal code where the order shipped to (5-Digit ZIP or ZIP+4).">
		<cfargument name="to_state" type="string" required="yes" hint="Two-letter ISO state code where the order shipped to.">
		<cfargument name="amount" type="numeric" required="yes" hint="Total amount of the order excluding sales tax.">
		<cfargument name="sales_tax" type="numeric" required="yes" hint="Total amount of sales tax collected for the order.">
		<cfargument name="arrLineItems" type="array" required="yes">

		<cfset var local = StructNew()>
		<cfset local.strReturn = { orderCreated=false }>

		<cftry>
			<!--- SerializeJSON doesnt handle numbers well, so construct it manually --->
			<cfset local.lstLineItems = "">
			<cfloop array="#arguments.arrLineItems#" index="local.thisLineItem">
				<cfset local.lstLineItems = listAppend(local.lstLineItems,'{"product_tax_code":"#local.thisLineItem.product_tax_code#","unit_price":#local.thisLineItem.unit_price#,"sales_tax":#local.thisLineItem.sales_tax#}')>
			</cfloop>
			<cfset local.stJSONRequest = '{"transaction_id":"#arguments.transaction_id#","transaction_date":"#arguments.transaction_date#","to_country":"US","to_zip":"#arguments.to_zip#","to_state":"#arguments.to_state#","amount":#arguments.amount#,"shipping":0,"sales_tax":#arguments.sales_tax#,"line_items":[#local.lstLineItems#]}'>

			<cfhttp method="post" url="https://api.taxjar.com/v2/transactions/orders" throwonerror="Yes" charset="utf-8" result="local.APIResult">
				<cfhttpparam type="header" name="Authorization" value="Bearer #arguments.apiKey#">
				<cfhttpparam type="header" name="Content-Type" value="application/json">
				<cfhttpparam type="body" value="#local.stJSONRequest#">
			</cfhttp>

			<cfset local.apiJSONResponse = toString(trim(local.APIResult.fileContent))>
			<cfset local.apiResponseStruct = deserializeJSON(local.apiJSONResponse)>

			<cfif structKeyExists(local.apiResponseStruct,"order") and structKeyExists(local.apiResponseStruct.order,"transaction_id")>
				<cfset local.strReturn.orderCreated = true>
			<cfelseif structKeyExists(local.apiResponseStruct,"error") and structKeyExists(local.apiResponseStruct,"detail")>
				<cfthrow message="#local.apiResponseStruct.error#" detail="#local.apiResponseStruct.detail#">
			<cfelse>
				<cfthrow message="Uncaught issue creating order." detail="Neither order nor error nodes in the response.">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfif local.strReturn.orderCreated>
			<cftry>
				<cfset local.strRequest = {
					"c":"TaxJar",
					"d": {
						"orgid":arguments.orgID,
						"request": {
							"timestamp":now(),
							"method":"POST",
							"endpoint":"https://api.taxjar.com/v2/transactions/orders",
							"authToken":"Bearer #arguments.apiKey#",
							"bodycontent":local.stJSONRequest
						},
						"response": {
							"statuscode":local.APIResult.status_code,
							"bodycontent":local.apiJSONResponse,
							"headers":local.APIResult.responseheader
						}
					}
				}>

				<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.platformQueue.dsn#">
					INSERT INTO dbo.queue_mongo (msgjson) 
					VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="createRefund" access="private" returntype="struct" output="no">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="apiKey" type="string" required="yes">
		<cfargument name="transaction_id" type="string" required="yes" hint="Unique identifier of the given refund transaction.">
		<cfargument name="transaction_date" type="string" required="yes" hint="YYYY-MM-DD">
		<cfargument name="to_zip" type="string" required="yes" hint="Postal code where the order shipped to (5-Digit ZIP or ZIP+4).">
		<cfargument name="to_state" type="string" required="yes" hint="Two-letter ISO state code where the order shipped to.">
		<cfargument name="amount" type="numeric" required="yes" hint="Total amount of the refund excluding sales tax.">
		<cfargument name="sales_tax" type="numeric" required="yes" hint="Total amount of sales tax collected for the refund.">
		<cfargument name="arrLineItems" type="array" required="yes">

		<cfset var local = StructNew()>
		<cfset local.strReturn = { orderCreated=false }>

		<cftry>
			<!--- SerializeJSON doesnt handle numbers well, so construct it manually --->
			<cfset local.lstLineItems = "">
			<cfloop array="#arguments.arrLineItems#" index="local.thisLineItem">
				<cfset local.lstLineItems = listAppend(local.lstLineItems,'{"product_tax_code":"#local.thisLineItem.product_tax_code#","unit_price":#local.thisLineItem.unit_price#,"sales_tax":#local.thisLineItem.sales_tax#}')>
			</cfloop>
			<cfset local.stJSONRequest = '{"transaction_id":"#arguments.transaction_id#","transaction_reference_id":"#CreateUUID()#","transaction_date":"#arguments.transaction_date#","to_country":"US","to_zip":"#arguments.to_zip#","to_state":"#arguments.to_state#","amount":#arguments.amount#,"shipping":0,"sales_tax":#arguments.sales_tax#,"line_items":[#local.lstLineItems#]}'>

			<cfhttp throwonerror="Yes" result="local.APIResult" url="https://api.taxjar.com/v2/transactions/refunds" method="post" charset="utf-8">
				<cfhttpparam type="header" name="Authorization" value="Bearer #arguments.apiKey#">
				<cfhttpparam type="header" name="Content-Type" value="application/json">
				<cfhttpparam type="body" value="#local.stJSONRequest#">
			</cfhttp>

			<cfset local.apiJSONResponse = ToString(local.APIResult.fileContent)>
			<cfset local.apiResponseStruct = deserializeJSON(local.apiJSONResponse)>

			<cfif structKeyExists(local.apiResponseStruct,"refund") and structKeyExists(local.apiResponseStruct.refund,"transaction_id")>
				<cfset local.strReturn.orderCreated = true>
			<cfelseif structKeyExists(local.apiResponseStruct,"error") and structKeyExists(local.apiResponseStruct,"detail")>
				<cfthrow message="#local.apiResponseStruct.error#" detail="#local.apiResponseStruct.detail#">
			<cfelse>
				<cfthrow message="Uncaught issue creating refund." detail="Neither refund nor error nodes in the response.">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfif local.strReturn.orderCreated>
			<cftry>
				<cfset local.strRequest = {
					"c":"TaxJar",
					"d": {
						"orgid":arguments.orgID,
						"request": {
							"timestamp":now(),
							"method":"POST",
							"endpoint":"https://api.taxjar.com/v2/transactions/refunds",
							"authToken":"Bearer #arguments.apiKey#",
							"bodycontent":local.stJSONRequest
						},
						"response": {
							"statuscode":local.APIResult.status_code,
							"bodycontent":local.apiJSONResponse,
							"headers":local.APIResult.responseheader
						}
					}
				}>

				<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.platformQueue.dsn#">
					INSERT INTO dbo.queue_mongo (msgjson) 
					VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>