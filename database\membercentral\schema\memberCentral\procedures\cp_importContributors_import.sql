ALTER PROC dbo.cp_importContributors_import
@siteID int,
@programID int,
@siteResourceID int,
@runByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusInserting int, @statusReady int, @itemGroupUID uniqueidentifier, 
		@colList varchar(max), @selColList varchar(max), @dynSQL nvarchar(max), @xmlMessage xml, @roleFieldUsageID int;

	IF OBJECT_ID('tempdb..#tblCustomCols') IS NOT NULL 
		DROP TABLE #tblCustomCols;
	IF OBJECT_ID('tempdb..#tmpContributionCustomDetails') IS NOT NULL 
		DROP TABLE #tmpContributionCustomDetails;
	CREATE TABLE #tblCustomCols (fieldID int, columnName varchar(128), itemType varchar(30), dataTypeCode varchar(12), displayTypeCode varchar(12));
	CREATE TABLE #tmpContributionCustomDetails (rowID int, fieldID int, columnName varchar(255), fieldValue varchar(max));

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='importContributions', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@statusInserting OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	set @roleFieldUsageID = dbo.fn_cf_getUsageID('ContributionAdmin','Role',NULL);

	-- all imported contributors get the same ItemGroupUID
	set @itemGroupUID = NEWID();

	BEGIN TRY
		-- program and cross program custom fields
		insert into #tblCustomCols (fieldID, columnName, itemType, dataTypeCode, displayTypeCode)
		select f.fieldID, f.fieldReference, 'ContributionProgram', ft.dataTypeCode, ft.displayTypeCode
		from dbo.cf_fields as f 
		inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
		inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID 
		inner join dbo.cp_programs as cp on cp.programID = f.detailID and f.controllingSiteResourceID = cp.siteResourceID
		where cp.programID = @programID
		and f.isActive = 1
		and len(f.fieldReference) > 0
		and ft.displayTypeCode <> 'LABEL'
		and 1 = case 
				when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
				else 1 end
			union all
		select f.fieldID, f.fieldReference, 'ContributionRole', ft.dataTypeCode, ft.displayTypeCode
		from dbo.cf_fields as f
		inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
		where f.controllingSiteResourceID = @siteResourceID
		and f.usageID = @roleFieldUsageID
		and f.isActive = 1
		and len(f.fieldReference) > 0
		and ft.displayTypeCode <> 'LABEL'
		and 1 = case 
				when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
				else 1 end;

		-- string columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblCustomCols
		where dataTypeCode = 'STRING'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #cp_ContributorImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueString
							from (
								select rowID, columnName, valueString
								from #cp_ContributorImport
								unpivot (valueString for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
	
			INSERT INTO #tmpContributionCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- integer columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblCustomCols
		where dataTypeCode = 'INTEGER'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + tbl.listitem + ' varchar(10) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #cp_ContributorImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueInteger
							from (
								select rowID, columnName, valueInteger
								from #cp_ContributorImport
								unpivot (valueInteger for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';

			INSERT INTO #tmpContributionCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- decimal columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblCustomCols
		where dataTypeCode = 'DECIMAL2'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + tbl.listitem + ' varchar(15) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #cp_ContributorImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueDecimal2
							from (
								select rowID, columnName, valueDecimal2
								from #cp_ContributorImport
								unpivot (valueDecimal2 for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';

			INSERT INTO #tmpContributionCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- date columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblCustomCols
		where dataTypeCode = 'DATE'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + tbl.listitem + ' varchar(30) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #cp_ContributorImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueDate
							from (
								select rowID, columnName, valueDate
								from #cp_ContributorImport
								unpivot (valueDate for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';

			INSERT INTO #tmpContributionCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- select, radio, and checkbox custom field columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblCustomCols
		where displayTypeCode in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #cp_ContributorImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tbl.listitem
							from (
								select rowID, columnName, columnValue
								from #cp_ContributorImport
								unpivot (columnValue for columnName in (' + @colList + ')) u
							) tmp
							cross apply dbo.fn_varcharListToTableInline(tmp.columnValue,''|'') as tbl
							inner join #tblCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
			
			INSERT INTO #tmpContributionCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to prepare program fields for import.');

		INSERT INTO #tblImportErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH
	
	BEGIN TRY
		BEGIN TRAN;
			insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			select itemUID, @statusInserting
			from #cp_ContributorImport;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, @programID
			from #cp_ContributorImport as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'programID';

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
			from #cp_ContributorImport as tmp
			inner join (
				select rowID, columnname, columnValueInt
				from #cp_ContributorImport
				unpivot (columnValueInt for columnname in (MCMemberID, campaignID, frequencyID, rateID, CPStatusID)) u
			) as unPvtInt on unPvtInt.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtInt.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDate.columnValueDate
			from #cp_ContributorImport as tmp
			inner join (
				select rowID, columnname, columnValueDate
				from #cp_ContributorImport
				unpivot (columnValueDate for columnname in (startDate, endDate)) u
			) as unPvtDate on unPvtDate.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDate.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueBit)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, tmp.isAnonymous
			from #cp_ContributorImport as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'isAnonymous';

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDec.columnValueDecimal2
			from #cp_ContributorImport as tmp
			inner join (
				select rowID, columnname, columnValueDecimal2
				from #cp_ContributorImport
				unpivot (columnValueDecimal2 for columnname in (installmentAmount, amountAlreadyPaid)) u
			) as unPvtDec on unPvtDec.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDec.columnname;

			-- distributions
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + quoteName(distCode) from dbo.cp_distributions where programID = @programID;
			if @colList is not null BEGIN
				select @dynSQL = '
					select ''' + cast(@itemGroupUID as varchar(60)) + ''', tmp.itemUID, ' + cast(@runByMemberID as varchar(20)) + ', ' + cast(@siteID as varchar(10)) + ', dc.columnID, tmp.rowID, unPvtDec.columnValueDecimal2
					from #cp_ContributorImport as tmp
					inner join (
						select rowID, columnname, columnValueDecimal2
						from #cp_ContributorImport
						unpivot (columnValueDecimal2 for columnname in (' + @colList + ')) u
					) as unPvtDec on unPvtDec.rowID = tmp.rowID
					inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = ' + cast(@queueTypeID as varchar(10)) + ' and dc.columnname = unPvtDec.columnname;';
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
				EXEC(@dynSQL);
			END

			-- custom fields
			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, fd.fieldValue
			from #cp_ContributorImport as tmp
			inner join #tmpContributionCustomDetails as fd on fd.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = fd.columnname;

			-- resume task
			EXEC dbo.sched_resumeTask @name='Contributions Import Queue', @engine='BERLinux';

			-- update queue item groups to show ready to process
			update qi WITH (UPDLOCK, HOLDLOCK)
			set qi.queueStatusID = @statusReady,
				qi.dateUpdated = getdate()
			from platformQueue.dbo.tblQueueItems as qi
			inner join #cp_ContributorImport as tmp on tmp.itemUID = qi.itemUID;

			-- send message to service broker to create all the individual messages
			select @xmlMessage = isnull((
				select 'contributionsImportLoad' as t, cast(@itemGroupUID as varchar(60)) as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');
			EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to queue contributions for import.');

		INSERT INTO #tblImportErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblCustomCols') IS NOT NULL 
		DROP TABLE #tblCustomCols;
	IF OBJECT_ID('tempdb..#tmpContributionCustomDetails') IS NOT NULL 
		DROP TABLE #tmpContributionCustomDetails;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
