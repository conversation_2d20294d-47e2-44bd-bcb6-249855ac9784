ALTER PROC dbo.job_ALLmemberJoinDates
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	DECLARE @readyToProcessStatusID int, @minItemID int, @xmlData xml;
	SET @itemCount = 0;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberJoinDates', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	
	INSERT INTO platformQueue.dbo.queue_memberJoinDates (udid, dateAdded, dateUpdated, statusID)
		OUTPUT INSERTED.itemID INTO #tmpQueueItems (itemID)
	SELECT udid, GETDATE(), GETDATE(), @readyToProcessStatusID
	FROM dbo.schedTask_memberJoinDates 
	WHERE isActive = 1;

	SET @itemCount = @@ROWCOUNT;

	SELECT @minItemID = MIN(itemID) FROM #tmpQueueItems;
	WHILE @minItemID IS NOT NULL BEGIN
		SET @xmlData = NULL;

		SELECT @xmlData = isnull((
			SELECT @minItemID as i
			FOR XML RAW('mc'), TYPE
		),'<mc/>');

		EXEC platformQueue.dbo.queue_memberJoinDates_sendMessage @xmlMessage=@xmlData;

		SELECT @minItemID = MIN(itemID) FROM #tmpQueueItems WHERE itemID > @minItemID;
	END

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
