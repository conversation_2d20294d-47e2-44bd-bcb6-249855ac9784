ALTER PROC dbo.queue_webhooks_grabForProcessing
@batchSize int,
@maxEntriesPerSQSQueueName int = 100

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='webhook', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpWebhooks') IS NOT NULL
		DROP TABLE #tmpWebhooks;
	CREATE TABLE #tmpWebhooks (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries. also limiting the max items grabbed per SQSQueueName
	WITH rankedQueueEntries AS (
		SELECT itemID, ROW_NUMBER() OVER (PARTITION BY SQSQueueName ORDER BY queuePriority, dateAdded, itemID) AS rankedNum
		FROM dbo.queue_webhook
		WHERE statusID = @statusReady
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpWebhooks
	FROM dbo.queue_webhook AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) qw.itemID 
		FROM dbo.queue_webhook as qw
		INNER JOIN rankedQueueEntries as cteR on cteR.itemID = qw.itemID
			AND cteR.rankedNum <= @maxEntriesPerSQSQueueName
		WHERE qw.statusID = @statusReady
		ORDER BY qw.queuePriority, qw.dateAdded, qw.itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM #tmpWebhooks;

	SELECT qid.itemID, s.sitecode, qid.siteID, qid.webhookID, qid.webhookURL, qid.payloadMessage, qid.SQSQueueName, @totalItemCount as totalItemCount
	FROM #tmpWebhooks AS tmp
	INNER JOIN dbo.queue_webhook AS qid ON qid.itemID = tmp.itemID
	inner join membercentral.dbo.sites s on s.siteID = qid.siteID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpWebhooks') IS NOT NULL
		DROP TABLE #tmpWebhooks;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
