<cfcomponent output="false" cache="false">
	<cfset variables.awsapi = {} >

	<cfset variables.defaultRegion = "us-east-1">
	<cfset variables.visibilityTimeout = 300>
	<cfset variables.waitTimeSeconds = 20>
	<cfset variables.maxNumberOfMessages = 10>
	<cfset variables.runCleanMessageTask = false>
	<cfset variables.runListAttachmentTask = false>
	<cfset variables.runListAttachmentExtractedTask = false>
	<cfset variables.runSiteDocumentsExtractedTask = false>
	<cfset variables.runDepoDocumentProcessingResultsTask = false>
	<cfset variables.runDepoDocumentProcessingMessagesTask = false>
	<cfset variables.runDepoConnectMessagesTask = false>
	<cfset variables.runMediaInfoMessagesTask = false>
	<cfset variables.runListservApprovedMessagesTask = false>
	<cfset variables.runListservStatusMessagesTask = false>
	
	<cfset variables.objectID = createUUID()>
	<cfset variables.maxAttempts = 6>
	<cfset variables.loopsToSleep = 150>

	<cffunction name="init" access="public" output="false" returntype="queueManager">
		<cfargument name="awsKey" type="string" required="true">
		<cfargument name="awsSecretKey" type="string" required="true">

		<cfset variables.awsKey = arguments.awsKey>
		<cfset variables.awsSecretKey = arguments.awsSecretKey>
		<cfset variables.simpleDateFormat = createObject("java", "java.text.SimpleDateFormat").init("yyyy-MM-dd'T'HH:mm:ss.SSSZ")>

		<cfset variables.awsapi["us-west-1"] = new modules.awscfml.aws(awsKey = variables.awsKey, awsSecretKey = variables.awsSecretKey, defaultRegion = "us-west-1")>
		<cfset variables.awsapi["us-west-2"] = new modules.awscfml.aws(awsKey = variables.awsKey, awsSecretKey = variables.awsSecretKey, defaultRegion = "us-west-2")>
		<cfset variables.awsapi["us-east-1"] = new modules.awscfml.aws(awsKey = variables.awsKey, awsSecretKey = variables.awsSecretKey, defaultRegion = "us-east-1")>
		<cfset variables.awsapi["us-east-2"] = new modules.awscfml.aws(awsKey = variables.awsKey, awsSecretKey = variables.awsSecretKey, defaultRegion = "us-east-2")>

		<cfset variables.nonAsciiRegex = "[^\x00-\x7F]">
		<cfset variables.objMimeUtility = createObject("java","javax.mail.internet.MimeUtility")>

		<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): queueManager initialized", addNewLine=true, doErrorStream=true)>

		<cfreturn this>
	</cffunction>

	<cffunction name="shutdown" access="public" output="false" returntype="void">
		<cfscript>
			var anythingRunning = (variables.runCleanMessageTask || variables.runListAttachmentTask || variables.runListAttachmentExtractedTask 
									|| variables.runListservApprovedMessagesTask || variables.runListservStatusMessagesTask)
			if (anythingRunning) {
				stopCleanMessageTask();
				stopListAttachmentTask();
				stopListAttachmentExtractedTask();
				stopSiteDocumentsExtractedTask();
				stopDepoDocumentProcessingResultsTask();
				stopDepoDocumentProcessingMessagesTask();
				stopDepoConnectMessagesTask();
				stopMediaInfoMessagesTask();
				stopListservApprovedMessagesTask();
				stopListservStatusMessagesTask();
				systemOutput(obj="SQS QueueManager (#variables.objectID#): sent stop to all tasks", addNewLine=true, doErrorStream=true);
			}
		</cfscript> 
	</cffunction>

	<cffunction name="getMessageArray" access="public" output="false" returntype="array">
		<cfargument name="SQSMessageResult" type="any" required="true">

		<cfscript>
		// Create uniform array based on 0, 1, or more than 1 item retrieved

		var local = {};
		local.messageArray = [];

		if ( NOT isStruct(arguments.SQSMessageResult) AND NOT isArray(arguments.SQSMessageResult) AND arguments.SQSMessageResult EQ ""){
		} else if ( isArray(arguments.SQSMessageResult)){
			local.messageArray = arguments.SQSMessageResult;
		} else {
			arrayAppend(local.messageArray, arguments.SQSMessageResult.Message) ;
		}	
	
		return local.messageArray;
		</cfscript> 
	</cffunction>

	<cffunction name="getQueues" access="public" returntype="void" output="false">
		<cfargument name="region" type="any">
	
		<cfset var local = structnew()>
		<cfset local.response.queuesArray = ArrayNew(1)>
		<cfset local.response.success = true>

		<cftry>
			<cfset local.aws = variables.awsapi["#arguments.region#"]>
			<cfset local.queues = local.aws.sqs.listQueues()>
			<!--- local.queues.data.ListQueuesResult --->
			<cfset local.response.statusCode = local.queues.statusCode>
			<cfif structKeyExists(local.queues, "data")>
				<cfif isArray(local.queues.data.ListQueuesResult)>
					<cfset local.response.queuesArray = local.queues.data.ListQueuesResult>
				<cfelseif local.queues.data.ListQueuesResult EQ "">
				<cfelse>
					<cfset arrayAppend(local.response.queuesArray, local.queues.data.ListQueuesResult) >
				</cfif>	
			<cfelse>
				<cfset local.response.success = false>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.response.success = false>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="startCleanMessageTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runCleanMessageTask = true>
			<cfset local.loopCount = 0>

			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runCleanMessageTask", addNewLine=true, doErrorStream=true) >
			<!--- Valid Queue to check --->
			<cfloop condition="variables.runCleanMessageTask EQ True">
				<cfset processCleanMessageQueue() > 
				<cfset local.loopCount += 1>
				<cfif local.loopCount GTE variables.loopsToSleep>
					<cfsleep  time="1000">
					<cfset local.loopCount = 0>
				</cfif>
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	


	<cffunction name="stopCleanMessageTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runCleanMessageTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runCleanMessageTask", addNewLine=true, doErrorStream=true) >
			</cfif>
			<cfset variables.runCleanMessageTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	
	

	<cffunction name="processCleanMessageQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-west-1'>
		<cfset local.queueName = 'listMessageCleanSQS'>

		<cfset local.maxthreads = 4>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processCleanListMessages(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			<cfset structDelete(local, "messageArray")>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>


	<cffunction name="processCleanListMessages" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processCleanListMessage(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="getHdrByKey" access="private" output="false" returntype="string">
		<cfargument name="arrHeader" type="array">
		<cfargument name="key" type="string">

		<cfset var local = structNew()>
		<cftry>
			<cfloop array="#arguments.arrHeader#" item="local.item">
				<cfif local.item.name EQ arguments.key>
					<cfreturn local.item.value>
				</cfif>
			</cfloop>
		<cfcatch type="Any">
			<cfset local.execptionCustomMessage = 'Error occured in while trying to look up a key in getHdrByKey'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.execptionCustomMessage)>
			<cfreturn "">
		</cfcatch>
		</cftry>
		<cfreturn "">
	</cffunction>	


	<cffunction name="processCleanListMessage" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.queueName = 'listMessageCleanSQS'>
		<cfset local.success = true >
		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>

			<cfif structKeyExists(local.jsonResult, "messageID")>
				<cfset local.messageState = isMessageMissing(messageID=local.jsonResult.messageid)>
				<cfif local.messageState.isMissing>
					<cfset local.addMissing = logMissingMessage(messageID=local.jsonResult.messageid, list=local.jsonResult.list, s3key=local.jsonResult.s3key, originatingQueue=local.queueName)>

					<cfif application.MCEnvironment eq 'production' AND local.addMissing EQ true>
						<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
					</cfif>
					<cfreturn false>							
				</cfif>

				<cfif local.messageState.recordExists>
					<cfset local.itemFromDynamoDB =  variables.awsapi["us-west-1"].dynamodb.getItem('messages', { 'primaryKey': '#local.jsonResult.primaryKey#', 'list': '#local.jsonResult.list#'  } )>
					<cfif structKeyExists(local.itemFromDynamoDB, "data") and  structKeyExists(local.itemFromDynamoDB.data, "Item")>
						<cfquery name="local.qryUpdateCleanMessage" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qryUpdateCleanMessageResult">
							set nocount on;
							declare @messageID int;
							
							set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.jsonResult.messageid#">;
							
							<cfif structKeyExists(local.itemFromDynamoDB.data.Item, "messageHeaders")>
								update messages_
								set hdrMessageID = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(getHdrByKey(local.itemFromDynamoDB.data.Item.messageHeaders,"Message-ID"))#">,''),
									hdrInReplyTo = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(getHdrByKey(local.itemFromDynamoDB.data.Item.messageHeaders,"In-Reply-To"))#">,'')
								where messageid_ = @messageID;
							</cfif>

							update messages_
								set onDynamoDB = 1
							where messageid_ = @messageID;

							update messageSearchText 
							set cleanHTML = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.itemFromDynamoDB.data.Item.cleanedbodyhtml#">,''),
								cleanText = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.itemFromDynamoDB.data.Item.cleanedbodyplain#">,'')
							where messageid_ = @messageID;
						</cfquery>
					<cfelse>
						<cfquery name="local.qryUpdateCleanMessage" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qryUpdateCleanMessageResult">
							set nocount on;
							declare @messageID int;
							
							set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.jsonResult.messageid#">;
							
							update messages_
							set onDynamoDB = 0
							where messageid_ = @messageID;
						</cfquery>
						<cfset local.success = false>
					</cfif>
					<cfif application.MCEnvironment eq 'production'>
						<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
					</cfif>
					<cfset structDelete(local, "itemFromDynamoDB")>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset local.execptionCustomMessage = 'Error occured in while trying to update clean list message'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.execptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.success>		
	</cffunction>


	<cffunction name="startListAttachmentTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runListAttachmentTask = true>
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runListAttachmentTask", addNewLine=true, doErrorStream=true) >			
			<!--- Queue to check --->
			<cfloop condition="variables.runListAttachmentTask EQ True">
				<cfset processListAttachmentQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	


	<cffunction name="stopListAttachmentTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runListAttachmentTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested processCleanMessageQueue", addNewLine=true, doErrorStream=true) >
			</cfif>
			<cfset variables.runListAttachmentTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processListAttachmentQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-west-1'>
		<cfset local.queueName = 'listAttachmentSQS'>

		<cfset local.maxthreads = 4>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processListAttachments(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			<cfset structDelete(local, "messageArray")>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processListAttachments" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processListAttachment(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>


	<cffunction name="processListAttachment" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.queueName = 'listAttachmentSQS'>
		<cfset local.success = true >
		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>

			<cfif structKeyExists(local.jsonResult, "messageID")>
				<cfset local.messageState = isMessageMissing(messageID=local.jsonResult.messageid)>
				<cfif local.messageState.isMissing>
					<cfset local.addMissing = logMissingMessage(messageID=local.jsonResult.messageid, list=local.jsonResult.list, s3key=local.jsonResult.primaryKey, originatingQueue=local.queueName)>

					<cfif application.MCEnvironment eq 'production' AND local.addMissing EQ true>
						<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
					</cfif>
					<cfreturn false>							
				</cfif>

				<cfif local.messageState.recordExists>
					<cfquery name="local.qryUpdateCleanMessage" datasource="#application.dsn.lyrisarchive.dsn#">

						declare @listID int, @messageID int, @saveFileName varchar(255), @attachedMessagePath varchar(50), 
								@attachmentIndex int, @originalFilename varchar(255), @extension varchar(50), @bytes int;
						
						set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.jsonResult.messageid#">;
						set @saveFileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.savedFilename#">;
						set @attachedMessagePath = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.attachedMessagePath#">;
						set @attachmentIndex = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.jsonResult.attachmentIndex#">;
						set @originalFilename = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.originalFilename#">;
						set @extension = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.extension#">;
						set @bytes = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.jsonResult.bytes#">;

						select @listID = listid from messages_ where messageid_ = @messageID;

						SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
						BEGIN TRAN;

						UPDATE dbo.messageAttachments
							SET listID=@listID, attachedMessagePath=@attachedMessagePath, attachmentIndex=@attachmentIndex, originalFilename=@originalFilename, extension=@extension, bytes=@bytes
						WHERE  messageID = @messageID
						AND savedFilename = @saveFileName;

						INSERT dbo.messageAttachments (messageID, savedFilename, listID, attachedMessagePath, attachmentIndex, originalFilename, extension, bytes )
						SELECT @messageID, @saveFileName, @listID, @attachedMessagePath, @attachmentIndex, @originalFilename, @extension, @bytes
						WHERE @@ROWCOUNT=0;

						COMMIT;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfif application.MCEnvironment eq 'production'>
						<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
					</cfif>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset local.execptionCustomMessage = 'Error occured in while trying to update list attachment'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.execptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.success>		
	</cffunction>	



	<cffunction name="startListAttachmentExtractedTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runListAttachmentExtractedTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runListAttachmentExtractedTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runListAttachmentExtractedTask EQ True">
				<cfset processListAttachmentExtractedQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	


	<cffunction name="stopListAttachmentExtractedTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runListAttachmentExtractedTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runListAttachmentExtractedTask", addNewLine=true, doErrorStream=true) >
			</cfif>
			<cfset variables.runListAttachmentExtractedTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processListAttachmentExtractedQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-west-1'>
		<cfset local.queueName = 'listAttachmentSearchTextSQS'>

		<cfset local.maxthreads = 4>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processListAttachmentExtractedTexts(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			<cfset structDelete(local, "messageArray")>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processListAttachmentExtractedTexts" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processListAttachmentExtractedText(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>


	<cffunction name="processListAttachmentExtractedText" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.queueName = 'listAttachmentSearchTextSQS'>
		<cfset local.success = true >
		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>

			<cfif structKeyExists(local.jsonResult, "Records")>
				<cfset local.s3Object = local.jsonResult.Records[1]>
				<cfif local.s3Object.eventName EQ "ObjectCreated:Put">
					<cfset local.keyParts = listToArray(local.s3Object.s3.object.key, "/")>
					<cfset local.list = local.keyParts[2]> 
					<cfset local.mod = local.keyParts[3]>
					<cfset local.savedFileName = local.keyParts[4]>
					<cfset local.fileParts = listToArray(local.savedFileName, "_")>
					<cfset local.messageID = local.fileParts[1]>
					<cfset local.searchStringPrepend = "tslistname#local.list#xxx tsmessageid#local.messageid#xxx">

					<cfif isNumeric(local.messageID)>
						<cfset local.messageState = isMessageMissing(messageID=local.messageid)>
						<cfif local.messageState.isMissing>
							<cfset local.addMissing = logMissingMessage(messageID=local.messageid, list=local.list, s3key=local.s3Object.s3.object.key, originatingQueue=local.queueName)>
		
							<cfif application.MCEnvironment eq 'production' AND local.addMissing EQ true>
								<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
							</cfif>
							<cfreturn false>							
						</cfif>

						<cfif local.messageState.recordExists>
							<cfquery name="local.qryMessageExists" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qryMessageExistsResult">
								set nocount on;
								declare @messageID int, @recordExists int, @recordIsProcessing int;
								
								set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.messageID#">;
			
								select @recordExists = count(messageid_) from messages_ where messageid_ = @messageID;
								select @recordIsProcessing = count(messageid_) from messagesToArchive where messageid_ = @messageID;
								select @recordExists as recordExists,  @recordIsProcessing as recordIsProcessing
							</cfquery>
							<!--- The message does not exist but does on S3 --->
							<cfif local.qryMessageExists.recordExists EQ 0 AND local.qryMessageExists.recordIsProcessing EQ 0>
								<cfquery name="local.qryInsertMessageMissing" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qryInsertMessageMissingResult">
									set nocount on;
									declare @messageID int;
									
									set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.messageID#">;

									insert into sqsMissingMessages(messageID, list, s3key, originatingQueue)
									values(@messageID, 
											<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.list#">, 
											<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.s3Object.s3.object.key#">, 
											'listAttachmentSearchTextSQS');
								</cfquery>
								<cfif application.MCEnvironment eq 'production'>
									<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
								</cfif>
								<cfreturn false>				
							</cfif>
		
							<cfset local.s3File = variables.awsapi["us-west-1"].s3.getObject(Bucket=local.s3Object.s3.bucket.name, ObjectKey=local.s3Object.s3.object.key)>
							<cfif local.s3File.responseHeaders.status_code EQ "200">
								<cfquery name="local.qryUpdateCleanMessage" datasource="#application.dsn.lyrisarchive.dsn#">
									declare @messageID int, @saveFileName varchar(255),  @crlf char(2), @filecontent varchar(max);
									
									set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.messageID#">;
									set @saveFileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.savedFilename#">;
									set @filecontent = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.searchStringPrepend# #local.s3File.rawData#">;
									set @crlf = char(13) + char(10);
		
		
									SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
									BEGIN TRAN;
		
									UPDATE dbo.messageAttachmentSearchText
										SET searchText= dbo.fn_ConsolidateWhiteSpace(dbo.fn_RegExReplace(@filecontent,'(\r\n|\n|\\n|\\t)', @crlf)) 
									WHERE  messageID = @messageID
									AND savedFilename = @saveFileName;
		
									INSERT dbo.messageAttachmentSearchText (messageID, savedFilename, searchText )
									SELECT @messageID, @saveFileName, dbo.fn_ConsolidateWhiteSpace(dbo.fn_RegExReplace(@filecontent,'(\r\n|\n|\\n|\\t)', @crlf)) 
									WHERE @@ROWCOUNT=0;
		
									COMMIT;
		
									SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
								</cfquery>
							</cfif>			
						</cfif>			
					</cfif>
				</cfif>
				<cfset structDelete(local, "s3Object")>
			</cfif>
			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-west-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>	
			</cfif>			
		<cfcatch type="Any">
			<cfset local.execptionCustomMessage = 'Error occured in while trying to update list attachment extracted text'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.execptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	


	<cffunction name="startSiteDocumentsExtractedTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runSiteDocumentsExtractedTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runSiteDocumentsExtractedTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runSiteDocumentsExtractedTask EQ True">
				<cfset processSiteDocumentsExtractedQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	


	<cffunction name="stopSiteDocumentsExtractedTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runSiteDocumentsExtractedTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runSiteDocumentsExtractedTask", addNewLine=true, doErrorStream=true) >
			</cfif>
			<cfset variables.runSiteDocumentsExtractedTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processSiteDocumentsExtractedQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-east-1'>
		<cfset local.queueName = 'siteDocumentExtractedText'>

		<cfset local.maxthreads = 4>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processSiteDocumentsExtractedTexts(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processSiteDocumentsExtractedTexts" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processSiteDocumentsExtractedText(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>


	<cffunction name="processSiteDocumentsExtractedText" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.queueName = 'siteDocumentExtractedText'>
		<cfset local.success = true >
		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>

			<cfif structKeyExists(local.jsonResult, "Records")>
				<cfset local.s3Object = local.jsonResult.Records[1]>

				<cfif local.s3Object.eventName EQ "ObjectCreated:Put">
					<cfset local.keyParts = listToArray(local.s3Object.s3.object.key, "/")>
					<cfset local.orgcode = local.keyParts[2]> 
					<cfset local.sitecode = local.keyParts[3]>
					<cfset local.mod = local.keyParts[4]>
					<cfset local.savedFileName = local.keyParts[5]>
					<cfset local.fileParts = listToArray(local.savedFileName, ".")>
					<cfset local.documentVersionID = local.fileParts[1]>
					
					<cfif isNumeric(local.documentVersionID)>
						<cfset local.s3File = variables.awsapi["us-east-1"].s3.getObject(Bucket=local.s3Object.s3.bucket.name, ObjectKey=local.s3Object.s3.object.key)>
						<cfif local.s3File.responseHeaders.status_code EQ "200">
							<cfquery name="local.qryUpdateSiteDocSearchText" datasource="#application.dsn.searchMC.dsn#">
								declare @documentVersionID int, @filecontent varchar(max);
								
								set @documentVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.documentVersionID#">;
								set @filecontent = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#tostring(local.s3File.rawData,"utf-8")#">;

								SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
								BEGIN TRAN;
	
								UPDATE dbo.cms_documentVersions
									SET searchText = searchMC.dbo.fn_StripHTMLAndCompress(@filecontent),
										documentID = NULL
								WHERE  documentVersionID = @documentVersionID;
									
								INSERT dbo.cms_documentVersions (documentVersionID, searchText )
								SELECT @documentVersionID, searchMC.dbo.fn_StripHTMLAndCompress(@filecontent)
								WHERE @@ROWCOUNT=0;
	
								COMMIT;
	
								SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							</cfquery>
						</cfif>				
					</cfif>
				</cfif>
			</cfif>
			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>	
			</cfif>			
		<cfcatch type="Any">
			<cfset local.execptionCustomMessage = 'Error occured in while trying to update site documents extracted text'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.execptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>


	<cffunction name="startDepoDocumentProcessingResultsTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runDepoDocumentProcessingResultsTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runDepoDocumentProcessingResultsTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runDepoDocumentProcessingResultsTask EQ True">
				<cfset processDepoDocumentProcessingResultsQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="stopDepoDocumentProcessingResultsTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runDepoDocumentProcessingResultsTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runDepoDocumentProcessingResultsTask", addNewLine=true, doErrorStream=true)>
			</cfif>
			<cfset variables.runDepoDocumentProcessingResultsTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processDepoDocumentProcessingResultsQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-east-1'>
		<cfset local.queueName = 'depoDocumentProcessingResults'>

		<cfset local.maxthreads = 2>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processDepoDocumentProcessingResultsRequests(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processDepoDocumentProcessingResultsRequests" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processDepoDocumentProcessingResults(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processDepoDocumentProcessingResults" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset var thisFile = "">
		<cfset local.queueName = 'depoDocumentProcessingResults'>
		<cfset local.success = true>

		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif NOT IsNull(local.jsonResult) AND NOT isArray(local.jsonResult) AND structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>

			<cfset local.isPreApproval = find("depos/approvals/",local.jsonResult[1].outputkey) gt 0>
			<cfset local.isPreApprovalAttachment = local.isPreApproval && listLen(local.jsonResult[1].outputkey,'/') gt 4>
			
			<!--- ignore pre approval attachments --->
			<cfif NOT local.isPreApprovalAttachment>

				<cfif local.isPreApproval>
					<cfset local.documentID = val(ListGetAt(local.jsonResult[1].outputkey,4,'/'))>
					<!--- Clear the processing message since we successfully ran --->
					<cfquery name="local.addMessageToDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						update dbo.depoDocuments set processingMessage = NULL
						WHERE documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
					</cfquery>

					<!--- if the original PDF document was not already checked for attachments, do that now. --->
					<!--- add to attachment queue via the preapproval queue because we dont know if we have the document locally yet. --->
					<cfquery name="local.qryCheckDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						SELECT documentID
						FROM dbo.depoDocuments
						WHERE documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
						AND originalExt = 'pdf'
						AND origCheckedForAttachments = 0
					</cfquery>
					<cfif local.qryCheckDocument.recordcount>
						<cfstoredproc procedure="ts_addDepoDocumentToPreApproveQueue" datasource="#application.dsn.tlasites_trialsmith.dsn#">
							<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
						</cfstoredproc>
					</cfif>
				
					<cfstoredproc procedure="ts_updateDepoDocumentFilesOnline" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
						<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="xodpreapprove">
					</cfstoredproc>

				<cfelse>
					<cfset local.documentID = val(ListGetAt(local.jsonResult[1].outputkey,4,'/'))>
					<!--- Clear the processing message since we successfully ran --->
					<cfquery name="local.addMessageToDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						update dbo.depoDocuments set processingMessage = NULL
						WHERE documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
					</cfquery>

					<cfloop array="#local.jsonResult#" item="thisFile">
						<cfif thisFile.type eq "txt" and find("depos/text/",thisFile.outputkey) gt 0>
							<cfset local.documentID = val(ListGetAt(thisFile.outputkey,4,'/'))>

							<cfset local.s3File = variables.awsapi["us-east-1"].s3.getObject(Bucket=thisFile.bucketname, ObjectKey=thisFile.outputkey)>
							<cfif local.s3File.responseHeaders.status_code EQ "200">
								<cfstoredproc procedure="ts_updateDepoDocumentSearchText" datasource="#application.dsn.tlasites_search.dsn#">
									<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
									<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#tostring(local.s3File.rawData,"utf-8")#">
								</cfstoredproc>
							</cfif>
							<cfset local.s3File = "">

						<cfelseif thisFile.type eq "pdf" and find("depos/pdfs/",thisFile.outputkey) gt 0>
							<cfset local.documentID = val(ListGetAt(thisFile.outputkey,4,'/'))>

							<cfstoredproc procedure="ts_updateDepoDocumentPDF" datasource="#application.dsn.tlasites_trialsmith.dsn#">
								<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
								<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#thisFile.pages#">
							</cfstoredproc>

						<cfelseif thisFile.type eq "xod" and find("depos/xod/",thisFile.outputkey) gt 0>
							<cfset local.documentID = val(ListGetAt(thisFile.outputkey,4,'/'))>

							<cfstoredproc procedure="ts_updateDepoDocumentFilesOnline" datasource="#application.dsn.tlasites_trialsmith.dsn#">
								<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
								<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="xodfull">
							</cfstoredproc>

						<cfelseif thisFile.type eq "xodpreview" and find("depos/xodpreview/",thisFile.outputkey) gt 0>
							<cfset local.documentID = val(ListGetAt(thisFile.outputkey,4,'/'))>

							<cfstoredproc procedure="ts_updateDepoDocumentFilesOnline" datasource="#application.dsn.tlasites_trialsmith.dsn#">
								<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
								<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="xodpreview">
							</cfstoredproc>
						</cfif>
					</cfloop>

				</cfif>

			</cfif>

			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>	
			</cfif>			
		<cfcatch type="Any">
			<cfset local.exceptionCustomMessage = 'Error occured in while trying to process document processing results'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.exceptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>


	<cffunction name="startDepoDocumentProcessingMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runDepoDocumentProcessingMessagesTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runDepoDocumentProcessingMessagesTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runDepoDocumentProcessingMessagesTask EQ True">
				<cfset processDepoDocumentProcessingMessagesQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="stopDepoDocumentProcessingMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runDepoDocumentProcessingMessagesTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runDepoDocumentProcessingMessagesTask", addNewLine=true, doErrorStream=true)>
			</cfif>
			<cfset variables.runDepoDocumentProcessingMessagesTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processDepoDocumentProcessingMessagesQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-east-1'>
		<cfset local.queueName = 'processingMessage'>

		<cfset local.maxthreads = 2>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processDepoDocumentProcessingMessagesRequests(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processDepoDocumentProcessingMessagesRequests" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processDepoDocumentProcessingMessages(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processDepoDocumentProcessingMessages" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset var thisFile = "">
		<cfset local.queueName = 'processingMessage'>
		<cfset local.success = true>

		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif NOT IsNull(local.jsonResult) AND structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>
			<cfset local.isAttachment = listLen(local.jsonResult.outputkey,'/') gt 4>
			<!--- ignore attachment processing --->
			<cfif NOT local.isAttachment>
				<cfif structKeyExists(local.jsonResult, "documentid") AND isNumeric(local.jsonResult.documentID)>
					<cfquery name="local.addMessageToDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						update dbo.depoDocuments set processingMessage = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.processingMessage#">
						WHERE documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.jsonResult.documentID#">
					</cfquery>
				</cfif>
			</cfif>

			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>	
			</cfif>			
		<cfcatch type="Any">
			<cfset local.exceptionCustomMessage = 'Error occured in while trying to process document processing message'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.exceptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>


	<cffunction name="startDepoConnectMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runDepoConnectMessagesTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runDepoConnectMessagesTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runDepoConnectMessagesTask EQ True">
				<cfset processDepoConnectMessagesQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="stopDepoConnectMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runDepoConnectMessagesTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runDepoConnectMessagesTask", addNewLine=true, doErrorStream=true)>
			</cfif>
			<cfset variables.runDepoConnectMessagesTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processDepoConnectMessagesQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-east-1'>
		<cfset local.queueName = 'depoConnectEmail'>

		<cfset local.maxthreads = 2>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processDepoConnectMessagesRequests(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processDepoConnectMessagesRequests" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processDepoConnectMessage(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processDepoConnectMessage" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset var thisFile = "">
		<cfset local.queueName = 'depoConnectEmail'>
		<cfset local.success = true>

		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif NOT IsNull(local.jsonResult) AND structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>
			<cfset local.messageKey = local.jsonResult.messagejsons3key>
			<cfset local.s3File = variables.awsapi["us-east-1"].s3.getObject(Bucket="mail-depoconnect-com", ObjectKey=local.messageKey)>
			<cfif local.s3File.responseHeaders.status_code EQ "200">
				<cfset local.success = doProcessDepoConnectMessageAndSendEmail(rawData=local.s3File.rawData)>
			</cfif>
			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.exceptionCustomMessage = 'Error occured in while trying to process depoconnect message'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.exceptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="doProcessDepoConnectMessageAndSendEmail" access="private" output="false" returntype="boolean">
		<cfargument name="rawData" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.success = false>

		<cftry>
			<cfscript>
				local.expectedDomain = "depoconnect.trialsmith.com";
				local.strMessage = deserializeJSON(arguments.rawData);
				local.fromName = local.strMessage["from"]["name"];
				local.fromEmail = local.strMessage["from"]["address"];
				local.recipientList = "";
				for (local.thisElement in local.strMessage["to"]) {
					local.recipientList = local.recipientList.listAppend(local.thisElement.address);
				}
				if (local.strMessage["cc"].len()) {
					for (local.thisElement in local.strMessage["cc"]) {
						local.recipientList = local.recipientList.listAppend(local.thisElement.address);
					}
				}
				local.toAddressIndex = listContains(local.recipientList,"@#local.expectedDomain#");
				if (local.toAddressIndex) {
					local.toAddress = listGetAt(local.recipientList,local.toAddressIndex);

					local.arrToAddressParts = listToArray(replaceNoCase(local.toAddress,"@#local.expectedDomain#",""),'_');
					local.expertNameSlug = local.arrToAddressParts[1];
					local.inquiryID = local.arrToAddressParts[2];
					local.receiverParticipantID = local.arrToAddressParts[3];
		
					local.headerMessageID = "";
					local.headerInReplyTo = "";
					if (structKeyExists(local.strMessage, "messageHeaders")) {
						if (structKeyExists(local.strMessage.messageHeaders, "message-id")) {
							local.headerMessageID = local.strMessage["messageHeaders"]["message-id"];
						}
						if (structKeyExists(local.strMessage.messageHeaders, "in-reply-to")) {
							local.headerInReplyTo = local.strMessage["messageHeaders"]["in-reply-to"];
						}
					}					
					local.cleanText = local.strMessage["cleanedbodyplain"];
					local.arrAttachments = local.strMessage["attachments"];
				}
			</cfscript>
			<cfif local.toAddressIndex>
				<cfquery name="local.qryProcessIncomingMessage" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @isProcessed bit = 0, @inquiryID int, @conversationID int, @receiverParticipantID int, @senderDelegateRoleID int,
							@receiverRoleCode varchar(20), @fromEmail varchar(400), @approvedByDepoMemberDataID int, @senderParticipantID int,
							@messageID int, @nowDate datetime, @attachmentID int, @documentID int, @uploadSourceID int, @ownerDepoMemberDataID int,
							@ObjectKey varchar(200), @newObjectKey varchar(200), @s3CopyReadyStatusID int, @docState varchar(10), @originalExt varchar(50);

						DECLARE @possibleSenderRoleCodes TABLE (roleCode varchar(20));

						SET @inquiryID = <cfqueryparam value="#local.inquiryID#" cfsqltype="CF_SQL_INTEGER">;
						SET @receiverParticipantID = <cfqueryparam value="#local.receiverParticipantID#" cfsqltype="CF_SQL_INTEGER">;
						SET @fromEmail = <cfqueryparam value="#local.fromEmail#" cfsqltype="CF_SQL_VARCHAR">;
						SET @nowDate = getdate();

						SELECT @uploadSourceID = uploadSourceID
						FROM dbo.depoDocumentUploadSources
						WHERE sourceName = 'Depoconnect';

						EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;

						SELECT @conversationID =  p.conversationID, @receiverRoleCode = r.roleCode,
							@approvedByDepoMemberDataID = p.approvedByDepoMemberDataID
						FROM dbo.expertConnectInquiryConversationParticipants AS p
						INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
						INNER JOIN dbo.expertConnectInquiryConversations AS c ON c.conversationID = p.conversationID
						INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = c.inquiryID
							AND i.inquiryID = @inquiryID
							AND i.emailAddressSlug = <cfqueryparam value="#local.expertNameSlug#" cfsqltype="CF_SQL_VARCHAR">
						WHERE p.participantID = @receiverParticipantID
						AND p.inquiryID = @inquiryID;

						IF @conversationID IS NULL
							GOTO on_done;

						IF @receiverRoleCode IN ('Requestor','RequestorDelegate')
							INSERT INTO @possibleSenderRoleCodes (roleCode)
							VALUES('Respondent'), ('RespondentDelegate');
						ELSE
							INSERT INTO @possibleSenderRoleCodes (roleCode)
							VALUES('Requestor'), ('RequestorDelegate');

						SELECT @senderParticipantID = p.participantID
						FROM dbo.expertConnectInquiryConversationParticipants AS p
						INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
						INNER JOIN @possibleSenderRoleCodes AS tmp ON tmp.roleCode = r.roleCode
						WHERE p.conversationID = @conversationID
						AND email = @fromEmail;

						IF @senderParticipantID IS NULL BEGIN
							SELECT @senderDelegateRoleID = roleID
							FROM dbo.expertConnectInquiryRoles
							WHERE roleCode = CASE
								WHEN @receiverRoleCode IN ('Requestor','RequestorDelegate') THEN 'RespondentDelegate'
								ELSE 'RequestorDelegate'
							END

							INSERT INTO dbo.expertConnectInquiryConversationParticipants (conversationID, inquiryID,
								roleID, firstName, lastName, email, approvedByDepoMemberDataID, dateApproved)
							VALUES (
								@conversationID, @inquiryID, @senderDelegateRoleID,
								<cfqueryparam value="#local.fromName#" cfsqltype="CF_SQL_VARCHAR">, '',
								@fromEmail, @approvedByDepoMemberDataID, @nowDate
							);
							SELECT @senderParticipantID = SCOPE_IDENTITY();
						END

						INSERT INTO dbo.expertConnectConversationMessages (conversationID, dateReceived, dateApprovedForDistribution,
							fromParticipantID, headerMessageID, headerInReplyTo, cleanText, hasAttachments)
						VALUES (
							@conversationID, @nowDate, @nowDate, @senderParticipantID,
							<cfqueryparam value="#local.headerMessageID#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#local.headerInReplyTo#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#local.cleanText#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arrayLen(local.arrAttachments)#" cfsqltype="CF_SQL_BIT">
						);
						SELECT @messageID = SCOPE_IDENTITY();

						<cfloop array="#local.arrAttachments#" item="local.thisAttachment">
							<cfset local.attachmentS3Key = "incoming/attachments/" & listFirst(local.thisAttachment['savedFilename'],'_') & "/" & local.thisAttachment['savedFilename']>
							INSERT INTO dbo.expertConnectConversationMessageAttachments (messageID, inquiryID, conversationID, fileName, extension, isViewable, s3Key)
							VALUES (
								@messageID, @inquiryID, @conversationID,
								<cfqueryparam value="#local.thisAttachment['originalFileName']#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#lCase(listLast(local.thisAttachment['originalFileName'],'.'))#" cfsqltype="CF_SQL_VARCHAR">,
								0,
								<cfqueryparam value="#local.attachmentS3Key#" cfsqltype="CF_SQL_VARCHAR">
							);

							SELECT @attachmentID = SCOPE_IDENTITY();

							SELECT @ownerDepoMemberDataID = isnull(p.depoMemberDataID, p2.depoMemberDataID), @docState = md.TLAMemberState, @originalExt = ema.extension
							FROM dbo.expertConnectConversationMessageAttachments as ema
							inner join dbo.expertConnectConversationMessages as em on em.messageID = ema.messageID
								and em.messageid = @messageID
								and ema.attachmentID = @attachmentID
							inner join dbo.expertConnectInquiryConversationParticipants as p on p.participantID = em.fromParticipantID
							inner join dbo.expertConnectInquiryRoles as r on r.roleID = p.roleID
							left outer join dbo.expertConnectInquiryConversationParticipants as p2
								inner join dbo.expertConnectInquiryRoles as r2 on r2.roleID = p2.roleID
								inner join dbo.depoMemberData as md2 on md2.depoMemberDataID = p2.depoMemberDataID
								on p2.conversationID = p.conversationID
								and r2.roleCode = case when r.roleCode = 'RequestorDelegate' then 'Requestor' else 'Respondent' end
							left outer join dbo.depoMemberData as md on md.depoMemberDataID = isnull(p.depoMemberDataID, p2.depoMemberDataID);

							EXEC dbo.ts_addDepoDocument
								@depomemberdataID=@ownerDepoMemberDataID,
								@docOrgCode=@docState,
								@originalExt=@originalExt,
								@contributeDate=@nowDate,
								@DepoAmazonBucks=0,
								@DepoAmazonBucksFullName=NULL,
								@DepoAmazonBucksEmail=NULL,
								@uploadSourceID=@uploadSourceID,
								@enteredByDepomemberdataID=@ownerDepoMemberDataID,
								@documentID=@documentID OUTPUT;

							SELECT @newObjectKey = 'depos/original/' + FORMAT(@documentID % 1000, '0000') + '/' + cast(@documentID as varchar(10)) + '.' + @originalExt;
							SET @ObjectKey = <cfqueryparam value="#local.attachmentS3Key#" cfsqltype="CF_SQL_VARCHAR">;
							<cfif application.MCEnvironment eq 'production'>
								IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_S3Copy WHERE s3bucketName = 'mail-depoconnect-com' AND objectKey = @objectKey AND newObjectKey = @newObjectKey)
									INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
									VALUES ('mail-depoconnect-com', @ObjectKey, 'trialsmith-depos', @newObjectKey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
							</cfif>
						</cfloop>

						SET @isProcessed = 1;

						on_done:
						SELECT @isProcessed AS isProcessed, ISNULL(@senderParticipantID,0) AS senderParticipantID, ISNULL(@messageID,0) AS messageID;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfif local.qryProcessIncomingMessage.recordCount AND local.qryProcessIncomingMessage.isProcessed eq 1>
					<cfset local.incomingMessageHTML = "">
					<cfif len(trim(local.strMessage["cleanedbodyhtml"]))>
						<cfset local.incomingMessageHTML = local.strMessage["cleanedbodyhtml"]>
					<cfelseif len(trim(local.strMessage["html"]))>
						<cfset local.incomingMessageHTML = local.strMessage["html"]>
					<cfelseif len(trim(local.strMessage["cleanedbodyplain"]))>
						<cfset local.incomingMessageHTML = local.strMessage["cleanedbodyplain"]>
					<cfelseif len(trim(local.strMessage["text"]))>
						<cfset local.incomingMessageHTML = local.strMessage["text"]>
					</cfif>

					<cfset local.emailContent = generateDepoConectMessageHTMLFromData(
						senderParticipantID=local.qryProcessIncomingMessage.senderParticipantID,
						messageID=local.qryProcessIncomingMessage.messageID,
						incomingMessageHTML=local.incomingMessageHTML,
						cleanText=local.strMessage["cleanedbodyplain"]
					)>

					<cfstoredproc procedure="ts_forwardIncomingDepoConnectMessage" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.receiverParticipantID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryProcessIncomingMessage.senderParticipantID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.emailContent#">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.messageID">
					</cfstoredproc>

					<cfset local.success = true>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump={local:local, arguments:arguments})>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="generateDepoConectMessageHTMLFromData" access="private" output="false" returntype="string">
		<cfargument name="senderParticipantID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="incomingMessageHTML" type="string" required="true">
		<cfargument name="cleanText" type="string" required="false" default="">
		

		<cfset var local = structNew()>

		<cfquery name="local.qrySenderInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			DECLARE @senderParticipantID int, @conversationID int, @senderRoleCode varchar(20), @sentOnBehalfOfParticipantID int;

			SET @senderParticipantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.senderParticipantID#">;

			SELECT @conversationID =  p.conversationID, @senderRoleCode = r.roleCode
			FROM dbo.expertConnectInquiryConversationParticipants AS p
			INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			WHERE p.participantID = @senderParticipantID;

			IF @senderRoleCode IN ('Requestor','Respondent')
				SET @sentOnBehalfOfParticipantID = @senderParticipantID;
			ELSE BEGIN
				SELECT @sentOnBehalfOfParticipantID = p.participantID
				FROM dbo.expertConnectInquiryConversationParticipants AS p
				INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
					AND r.roleCode = CASE
						WHEN @senderRoleCode = 'RequestorDelegate' THEN 'Requestor'
						ELSE 'Respondent'
					END
				WHERE p.conversationID = @conversationID;
			END

			SELECT ISNULL(i.expertFirstName,'') + ' ' + ISNULL(i.expertLastName,'') AS expertName,
				ISNULL(p.firstName,'') + ' ' + ISNULL(p.lastName,'') AS sendingLawyerName,
				CASE WHEN r.roleCode = 'Requestor' THEN i.companyName ELSE d.BillingFirm END as firm,
				CASE WHEN r.roleCode = 'Requestor' THEN i.companyAddress1 ELSE d.BillingAddress END as address1,
				CASE WHEN r.roleCode = 'Requestor' THEN i.companyAddress2 ELSE d.BillingAddress2 END as address2,
				CASE WHEN r.roleCode = 'Requestor' THEN i.companyAddress3 ELSE d.BillingAddress3 END as address3,
				CASE WHEN r.roleCode = 'Requestor' THEN i.companyCity ELSE d.BillingCity END as city,
				CASE WHEN r.roleCode = 'Requestor' THEN i.companyState ELSE d.BillingState END as stateCode,
				CASE WHEN r.roleCode = 'Requestor' THEN i.companyZIP ELSE d.BillingZip END as postalCode,
				CASE WHEN r.roleCode = 'Requestor' THEN i.website ELSE '' END as website,
				CASE
					WHEN @sentOnBehalfOfParticipantID <> @senderParticipantID
						THEN (
							SELECT ISNULL(p2.firstName,'') + ' ' + ISNULL(p2.lastName,'')
							FROM dbo.expertConnectInquiryConversationParticipants AS p2
							WHERE p2.participantID = @senderParticipantID
						)
					ELSE ''
					END AS delegateName
			FROM dbo.expertConnectInquiryConversationParticipants AS p
			INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			INNER JOIN dbo.depomemberdata AS d ON d.depoMemberDataID = p.depoMemberDataID
			INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = p.inquiryID
			WHERE p.participantID = @sentOnBehalfOfParticipantID;
		</cfquery>

		<cfquery name="local.qryAttachments" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SELECT conversationID, attachmentUUID, fileName
			FROM dbo.expertConnectConversationMessageAttachments
			WHERE messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messageID#">;
		</cfquery>

		<cfset local.arrAddress = [local.qrySenderInfo.address1,local.qrySenderInfo.address2,local.qrySenderInfo.address3]>
		<cfset local.stateZip = trim(local.qrySenderInfo.stateCode & (len(local.qrySenderInfo.postalCode) ? ' ' & local.qrySenderInfo.postalCode : ''))>
		<cfset local.cityStateZip = trim(local.qrySenderInfo.city & (len(local.qrySenderInfo.city) and len(local.stateZip) ? ', '  : '') & local.stateZip)>
		<cfset local.arrAddress = local.arrAddress.filter(function(thisVal){ return thisVal neq ''; })>

		<cfsavecontent variable="local.emailContent">
			<cfoutput>
			<div>
				<div style="font-size:16px;text-decoration:underline;">TrialSmith&trade; - Expert Inquiry for #local.qrySenderInfo.expertName#</div>
				<div style="font-size:16px;margin-top:1em;">#arguments.incomingMessageHTML#</div>
				<cfif local.qryAttachments.recordCount>
					<cfset local.fileDownloadURL = "#application.objSiteInfo.mc_siteInfo['TS'].scheme#://#application.objSiteInfo.mc_siteInfo['TS'].mainhostname#/?pg=tsDocDownload&da=downloadExpertInquiryAttachment&conversationID=#local.qryAttachments.conversationID#&messageID=#arguments.messageID#&attachmentUUID=">
					<div style="font-size:16px;margin-top:1em;">
						<cfif local.qryAttachments.recordCount gt 1>
							<div><b>Attachments:</b><div>
							<ul style="margin-top:0.5em;">
								<cfloop query="local.qryAttachments">
									<li><a href="#local.fileDownloadURL##local.qryAttachments.attachmentUUID#">#local.qryAttachments.fileName#</a></li>
								</cfloop>
							</ul>
						<cfelse>
							<div><b>Attachment:</b> <a href="#local.fileDownloadURL##local.qryAttachments.attachmentUUID#">#local.qryAttachments.fileName#</a><div>
						</cfif>
					<div>
				</cfif>
				<div style="font-size:14px;margin-top:2em;">
					<div style="text-decoration:underline;">Requested By:</div>
					<cfif len(local.qrySenderInfo.delegateName)>
						#local.qrySenderInfo.delegateName# on behalf of #local.qrySenderInfo.sendingLawyerName#
					<cfelse>
						#local.qrySenderInfo.sendingLawyerName#
					</cfif>
					<div>#local.qrySenderInfo.firm#</div>
					<div>#ArrayToList(local.arrAddress, ", ")#</div>
					<div>#local.cityStateZip#</div>
					<div>#local.qrySenderInfo.website#</div>
				</div>
				<div style="font-size:14px;margin-top:1em;padding:10px;background-color:##efefef;">
					<b>Simply Reply:</b> Your response will be exclusively shared with <b>#local.qrySenderInfo.sendingLawyerName#</b>.<br/><br/>
					<b>Delegate:</b> Ask your team to <a href="mailto:[[replyToEmailAddress]]">click here</a> to email responses to <b>#local.qrySenderInfo.sendingLawyerName#</b>, or email [[replyToEmailAddress]].<br/><br/>
					<i>Any Expert Transcripts sent may be shared with TrialSmith.</i><br/><br/>
					<b>Opt-Out:</b> If you don't want to assist other plaintiff lawyers with expert inquiries via TrialSmith, simply opt-out below.<br/><br/>
					<b>About this Inquiry:</b> This email was sent by #local.qrySenderInfo.sendingLawyerName# using <a target="_blank" href="https://www.trialsmith.com/depoconnect">DepoConnect</a>. Instantly email trial lawyers having direct prior experience with an expert. <a target="_blank" href="https://www.trialsmith.com/depoconnect">Learn More</a>
				</div>
				<div style="margin-top:1em;font-size:12px;text-align:center;">
					This private message was sent from #local.qrySenderInfo.sendingLawyerName# by TrialSmith, Inc., 13359 N Hwy 183 ##406-1220, Austin, Texas 78750<br/><br/>
					<div style="font-family:sans-serif">
						<div style="font-size:12px;color:##555;line-height:1.2;font-family:Arial,'Helvetica Neue',Helvetica,sans-serif">
							<p style="margin:0;font-size:12px;text-align:center">
								<a href="[[emailOptOutURL]]" target="_blank" title="Unsubscribe From DepoConnect National Inquiries" style="text-decoration:underline;color:##0068a5" rel="noopener">
									Unsubscribe from DepoConnect National Inquiries
								</a>
							</p>
						</div>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.basetemplate = application.objEmailWrapper.getBaseTemplateHTML(sitecode="TS",emailTitle="TrialSmith - Expert Inquiry for #local.qrySenderInfo.expertName#")>
		<cfset local.emailMessage = replacenocase(local.basetemplate,"<!--BodyContent-->",local.emailContent,"all")>
		<cfif len(arguments.cleanText)>
			<cfset local.emailMessage = application.objEmailWrapper.injectPreviewText(htmlcontent=local.emailMessage,preheadertext=arguments.cleanText)>
		</cfif>
		<cfreturn local.emailMessage>
	</cffunction>

	<cffunction name="startMediaInfoMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runMediaInfoMessagesTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runMediaInfoMessagesTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runMediaInfoMessagesTask EQ True">
				<cfset processMediaInfoMessagesQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="stopMediaInfoMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runMediaInfoMessagesTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runMediaInfoMessagesTask", addNewLine=true, doErrorStream=true)>
			</cfif>
			<cfset variables.runMediaInfoMessagesTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processMediaInfoMessagesQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-east-1'>
		<cfset local.queueName = 'mediaInfo'>

		<cfset local.maxthreads = 2>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processMediaInfoMessagesRequests(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processMediaInfoMessagesRequests" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processMediaInfoMessages(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processMediaInfoMessages" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset var thisFile = "">
		<cfset local.queueName = 'mediaInfo'>
		<cfset local.success = true>

		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif NOT IsNull(local.jsonResult) AND structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>

			<cfif structKeyExists(local.jsonResult, "fileid") AND isNumeric(local.jsonResult.fileid)>
				<cfquery name="local.updateMediaInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					update dbo.tblFiles 
					set duration = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.jsonResult.duration)#">,
						fileSize = <cfqueryparam cfsqltype="CF_SQL_BIGINT" value="#local.jsonResult.filesize#">
					WHERE fileid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.jsonResult.fileid#">
				</cfquery>
			</cfif>

			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>	
			</cfif>			
		<cfcatch type="Any">
			<cfset local.exceptionCustomMessage = 'Error occured in while trying to process media info message'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.exceptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>



	<cffunction name="startListservApprovedMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runListservApprovedMessagesTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runListservApprovedMessagesTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runListservApprovedMessagesTask EQ True">
				<cfset processListservApprovedMessagesQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="stopListservApprovedMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runListservApprovedMessagesTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runListservApprovedMessagesTask", addNewLine=true, doErrorStream=true)>
			</cfif>
			<cfset variables.runListservApprovedMessagesTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processListservApprovedMessagesQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-east-1'>
		<cfset local.queueName = 'ListservApprovedEmail'>

		<cfset local.maxthreads = 2>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processListservApprovedMessagesRequests(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processListservApprovedMessagesRequests" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processListservApprovedMessage(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processListservApprovedMessage" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset var thisFile = "">
		<cfset local.queueName = 'ListservApprovedEmail'>
		<cfset local.success = true>

		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif NOT IsNull(local.jsonResult) AND structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>
			
			<cfset local.receiptHandle = arguments.thisMessage.ReceiptHandle>
			<cfset local.messageKey = local.jsonResult.messagejsons3key>
			<cfset local.s3File = variables.awsapi["us-east-1"].s3.getObject(Bucket="incoming-listserv-mail", ObjectKey=local.messageKey)>
			<cfif structKeyExists(local.s3File, "responseHeaders") AND structKeyExists(local.s3File.responseHeaders, "status_code") AND local.s3File.responseHeaders.status_code EQ "200">
				<cfset local.jsonMessage = deserializeJSON(local.s3File.rawData)>
				<!--- Need to verify date format --->
				<cfset local.dateHeader = local.jsonMessage.date>
				<cfset local.subject = local.jsonMessage.subject>
				<cfset local.fromName = local.jsonMessage["from"]["name"]>
				<cfscript>
					if (refind(variables.nonAsciiRegex,local.fromName))
						local.fromName = variables.objMimeUtility.encodeText(local.fromName);
					if (refind(variables.nonAsciiRegex,local.subject))
						local.subject = variables.objMimeUtility.encodeText(local.subject);					
				</cfscript>
				<cfset local.fromEmail = local.jsonMessage["from"]["address"]>
				<cfset local.fromHeader = "">
				<cfif local.fromName NEQ "">
					<cfset local.fromHeader = '"#replace(local.fromName,'"','\"','all')#" <#local.fromEmail#>'>
				<cfelse>
					<cfset local.fromHeader = local.fromEmail>
				</cfif>
				
				<cfset local.s3NewEML = variables.awsapi["us-east-1"].s3.getObject(Bucket="incoming-listserv-mail", ObjectKey=local.jsonResult.messageS3key)>
				<cfif structKeyExists(local.s3NewEML, "responseHeaders") AND structKeyExists(local.s3NewEML.responseHeaders, "status_code") AND local.s3NewEML.responseHeaders.status_code EQ "200">
					<cfset local.headerDelimiter = "#chr(13)##chr(10)##chr(13)##chr(10)#">
					<cfset local.headerEnd = find(local.headerDelimiter, local.s3NewEML.rawData)>

					<cfif local.headerEnd NEQ 0>
						<cfset local.header = left(local.s3NewEML.rawData, local.headerEnd - 1)>
						<cfset local.body = mid(local.s3NewEML.rawData, local.headerEnd + len(local.headerDelimiter))>

						<cfloop index="local.thisElement" array="#local.jsonResult.recipients#">
							<cfset local.toEmail = local.thisElement>
							<cfset local.toHeader = local.thisElement>
							<!---
							<cfset local.toEmail = local.thisElement.address>
							<cfset local.toName = local.thisElement.name>
							<cfscript>
								if (refind(variables.nonAsciiRegex,local.toName))
									local.toName = variables.objMimeUtility.encodeText(local.toName);									
							</cfscript>
							<cfset local.toHeader = "">
							<cfif local.toName neq "">
								<cfset local.toHeader = '"#replace(local.toName,'"','\"','all')#" <#local.toEmail#>'>
							<cfelse>
								<cfset local.toHeader = local.toEmail>
							</cfif>
							--->

							<cfstoredproc procedure="lyrisCustom.dbo.lists_insertIncomingMail" datasource="#application.dsn.trialslyris1.dsn#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.dateHeader#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.fromHeader#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.fromEmail#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.toHeader#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.toEmail#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.subject#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.header#">
								<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.body#">
								<cfprocparam variable="local.messageID" cfsqltype="CF_SQL_INTEGER" type="out">
							</cfstoredproc>

							<cfstoredproc procedure="emailTracking.dbo.emailTracking_recordInmailMapping" datasource="#application.dsn.trialslyris1.dsn#">
								<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.sesid#">
								<cfprocparam type="in" cfsqltype="CF_SQL_INT" value="#local.messageID#">
								<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.toEmail#">
								<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.fromEmail#">
								<cfprocresult name="result">
							</cfstoredproc>
						</cfloop>

						<cfstoredproc procedure="emailTracking.dbo.emailTracking_recordIncomingMessageDeliveredStatus" datasource="#application.dsn.trialslyris1.dsn#">
							<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.sesid#">
							<cfprocresult name="result">
						</cfstoredproc>
				
						<cfset local.success = true>

					<cfelse>
						<cfset local.headerEnd = "Unable to parse email - Nothing done">
						<cfset local.success = false>
					</cfif>
				</cfif>
			</cfif>

			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.exceptionCustomMessage = 'Error occured in while trying to process listserv approved message'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.exceptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>


	<cffunction name="startListservStatusMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfset variables.runListservStatusMessagesTask = true>
			
			<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Startup Requested runListservStatusMessagesTask", addNewLine=true, doErrorStream=true) >
			<!--- Queue to check --->
			<cfloop condition="variables.runListservStatusMessagesTask EQ True">
				<cfset processListservStatusMessagesQueue() > 
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="stopListservStatusMessagesTask" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cftry>
			<cfif variables.runListservStatusMessagesTask>
				<cfset systemOutput(obj="SQS QueueManager (#variables.objectID#): Shutdown Requested runListservStatusMessagesTask", addNewLine=true, doErrorStream=true)>
			</cfif>
			<cfset variables.runListservStatusMessagesTask = false>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="processListservStatusMessagesQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.messageArray = ArrayNew(1)>
		<cfset local.success = true>
		<cfset local.region = 'us-east-1'>
		<cfset local.queueName = 'listservMessageProcessingResults'>

		<cfset local.maxthreads = 2>
		
		<cftry>
			<cfset local.aws = variables.awsapi["#local.region#"]>
			<cfset local.sqsmessage = local.aws.sqs.receiveMessage(
				queueName=local.queueName, 
				visibilityTimeout=variables.visibilityTimeout, 
				waitTimeSeconds=variables.waitTimeSeconds, 
				maxNumberOfMessages = variables.maxNumberOfMessages,
				attributeNames = ['SentTimestamp']
			)>

			<cfif structKeyExists(local.sqsmessage, "data")>
				<cfset local.messageArray = getMessageArray(local.sqsmessage.data.ReceiveMessageResult)>
				<cfif arrayLen(local.messageArray)>
					<cfset local.success = processListservStatusMessagesRequests(messageArray=local.messageArray, numthreads=local.maxthreads)>
				</cfif>
			<cfelse>
				<cfset local.response.success = false>
			</cfif>	
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processListservStatusMessagesRequests" access="public" returntype="boolean" output="false">
		<cfargument name="messageArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processCleanListMessage
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.messageArray, 
				function(eachMessage) {return (not processListservStatusMessage(eachMessage));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processListservStatusMessage" access="private" output="false" returntype="boolean">
		<cfargument name="thisMessage" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset var thisFile = "">
		<cfset local.queueName = 'listservMessageProcessingResults'>
		<cfset local.success = true>

		<cftry>
			<cfset local.jsonResult = deserializeJSON(arguments.thisMessage.Body)>
			<cfif structKeyExists(arguments.thisMessage, "Attribute") AND structKeyExists(arguments.thisMessage.Attribute, "Name") AND arguments.thisMessage.Attribute.Name EQ "SentTimestamp">
				<cfset local.jsonResult.SentTimestamp = arguments.thisMessage.Attribute.Value>		
				<cfset local.jsonResult.SentDate = createObject( "java", "java.util.Date" ).init(javaCast( "long", local.jsonResult.SentTimestamp))>
			</cfif>
			
			<!--- This case only occurs when an SQS queue is setup.  AWS will send a test message to the queue which can break message processing --->
			<cfif NOT IsNull(local.jsonResult) AND structKeyExists(local.jsonResult, "Event") AND local.jsonResult.Event EQ "s3:TestEvent">
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
				<cfreturn local.success>
			</cfif>

			<cfstoredproc procedure="emailTracking.dbo.emailTracking_recordIncomingMessageStatus" datasource="#application.dsn.trialslyris1.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.hdrmessageid#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.sesid#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.stepfunctionname#">
				<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#local.jsonResult.SentDate#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.subject#">
				<cfif isStruct(local.jsonResult.from)>
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.from.address#">
				<cfelse>
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.from#">
				</cfif>				
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.disposition#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.disposition#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.status#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.jsonResult.reason#">
				<cfprocparam type="in" cfsqltype="CF_SQL_BIGINT" value="#local.jsonResult.SentTimestamp#">
				<cfprocparam variable="local.incomingMessageID" cfsqltype="CF_SQL_INTEGER" type="out">
			</cfstoredproc>			

			<cfif application.MCEnvironment eq 'production'>
				<cfset variables.awsapi["us-east-1"].sqs.deleteMessage(queueName=local.queueName, receiptHandle=arguments.thisMessage.ReceiptHandle)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.exceptionCustomMessage = 'Error occured in while trying to process listserv approved message'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.exceptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>


	<cffunction name="sendSNSMessage" access="public" output="false" returntype="boolean">
		<cfargument name="jsonMessage" type="string" required="true">
		<cfargument name="TopicArn" type="string" required="true">
		<cfargument name="Subject" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.success = true>

		<cftry>
			<cfset variables.awsapi["us-east-1"].sns.publish(message=arguments.jsonMessage, TopicArn=arguments.TopicArn, Subject=arguments.Subject)>
		<cfcatch type="Any">
			<cfset local.exceptionCustomMessage = 'Error occured attempting to send SNS message.'>
			<cfset application.objError.sendError(cfcatch=cfcatch, customMessage=local.exceptionCustomMessage)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="logMissingMessage" access="private" output="false" returntype="boolean">
		<cfargument name="messageID" type="string" required="true">
		<cfargument name="list" type="string" required="true">
		<cfargument name="s3key" type="string" required="true">
		<cfargument name="originatingQueue" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.success = true >
		<cftry>
			<cfquery name="local.qryInsertMessageMissing" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qryInsertMessageMissingResult">
				set nocount on;
				declare @messageID int, @list varchar(100), @s3key varchar(100), @originatingQueue varchar(50);
				
				set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messageID#">;
				set @list = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.list#">;
				set @s3key = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.s3key#">;
				set @originatingQueue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.originatingQueue#">;

				insert into sqsMissingMessages(messageID, list, s3key, originatingQueue)
				values(@messageID, @list, @s3key, @originatingQueue);
			</cfquery>
		<cfcatch type="Any">
			<cfset local.execptionCustomMessage = 'Error occured in while trying to log a missing message'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.execptionCustomMessage)>
			<cfset local.success = false >
		</cfcatch>
		</cftry>
		<cfreturn local.success>
	</cffunction>

	<cffunction name="isMessageMissing" access="private" output="false" returntype="struct">
		<cfargument  name="messageid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnObj.recordExists = 0 >
		<cfset local.returnObj.recordIsProcessing = 0 >
		<cfset local.returnObj.isMissing = true >
		<cftry>
			<cfquery name="local.qryMessageExists" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qryMessageExistsResult">
				set nocount on;
				declare @messageID int, @recordExists int, @recordIsProcessing int;
				
				set @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messageid#">;
				select @recordExists = count(messageid_) from messages_ where messageid_ = @messageID;
				select @recordIsProcessing = count(messageid_) from messagesToArchive where messageid_ = @messageID;
				select @recordExists as recordExists,  @recordIsProcessing as recordIsProcessing
			</cfquery>

			<cfset local.returnObj.recordExists = local.qryMessageExists.recordExists >
			<cfset local.returnObj.recordIsProcessing = local.qryMessageExists.recordIsProcessing >
			<cfif local.qryMessageExists.recordExists EQ 0 AND local.qryMessageExists.recordIsProcessing EQ 0>
				<cfset local.returnObj.isMissing = true >
			<cfelse>
				<cfset local.returnObj.isMissing = false >
			</cfif>
		<cfcatch type="Any">
			<cfset local.execptionCustomMessage = 'Error occured in while trying to look up a message in isMessageMissing'>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.execptionCustomMessage)>
			<cfset local.returnObj.isMissing = false >
		</cfcatch>
		</cftry>
		<cfreturn local.returnObj>
	</cffunction>

</cfcomponent>