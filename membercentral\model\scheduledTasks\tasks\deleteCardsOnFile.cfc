<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="500">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfscript>
  				local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
  				local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
  			</cfscript>
  			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfset var local = structnew()>
		<cfset local.success = true>
		
		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_deleteCardsOnFile_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryCards" resultset="1">
			</cfstoredproc>

			<cfloop query="local.qryCards">
				<cfset local.thisItemID = local.qryCards.itemID>

				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. 												--->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and 	--->
				<!--- those items may be grabbed by another job, causing it to possible be processed twice.	--->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_deleteCardsOnFile as qi
					inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">
					and qs.queueStatus = 'grabbedForProcessing'
				</cfquery>

				<cfif local.checkItemID.itemCount>	
					<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @statusProcessing int;
						EXEC dbo.queue_getStatusIDbyType @queueType='deleteCardsOnFile', @queueStatus='Processing', @queueStatusID=@statusProcessing OUTPUT;

						UPDATE dbo.queue_deleteCardsOnFile
						SET statusID = @statusProcessing,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItemID#">;
					</cfquery>

					<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
						SELECT pr.siteID, s.orgID, pr.profileID, pr.profileCode, ga.gatewayType, pr.gatewayUsername, pr.gatewayPassword, pr.gatewayMerchantID
						FROM dbo.mp_profiles as pr
						INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
						INNER JOIN dbo.sites AS s ON s.siteID = pr.siteID
						WHERE pr.profileID = <cfqueryparam value="#local.qryCards.profileID#" cfsqltype="cf_sql_integer">
					</cfquery>

					<cfswitch expression="#local.qryGateWayID.gatewayType#">
						<cfcase value="AuthorizeCCCIM">
							<cfset local.tokenArgs = { profileID=local.qryGateWayID.profileID, pmid=local.qryCards.memberID, cpid=local.qryCards.customerProfileID, cppid=local.qryCards.paymentProfileID, overrideRemoveByMemberID=local.qryCards.recordedByMemberID } >
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="removePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						</cfcase>
						<cfcase value="SageCCCIM">
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.qryCards.memberID, customerPaymentProfileId=local.qryCards.paymentProfileID, formpost='', doRemCard=1, overrideRemoveByMemberID=local.qryCards.recordedByMemberID } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="removePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						</cfcase>
						<cfcase value="BankDraft">
							<cfset local.tokenArgs = { mcproxy_orgID=local.qryGateWayID.orgID, pmid=local.qryCards.memberID, payProfileID=local.qryCards.payProfileID, overrideRemoveByMemberID=local.qryCards.recordedByMemberID } >
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="removePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						</cfcase>
						<cfcase value="AffiniPayCC">
							<cfset local.tokenArgs = { profileID=local.qryGateWayID.profileID, pmid=local.qryCards.memberID, payProfileID=local.qryCards.payProfileID, overrideRemoveByMemberID=local.qryCards.recordedByMemberID } >
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="removePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						</cfcase>
					</cfswitch>
		
					<!--- DEL FROM QUEUE --->
					<cfquery name="local.updateStatus" datasource="#application.dsn.platformQueue.dsn#">
						DELETE FROM dbo.queue_deleteCardsOnFile
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;
					</cfquery>
				</cfif>	
			</cfloop>
			
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.success = false>
			</cfcatch>
		</cftry>	
		
		<cfreturn local.success>			
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) AS itemCount
			FROM dbo.queue_deleteCardsOnFile;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>
	
</cfcomponent>