<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset local.itemCount = getQueueItemCount()>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.itemCount)>

		<cfif local.itemCount GT 0>
			<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="SUBFORCEADDRPT")>
			<cfset local.success = cleanQueue(messageTypeID=local.messageTypeID)>
			<cfif NOT local.success>
				<cfthrow message="Error running cleanQueue()">
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="cleanQueue" access="private" output="false" returntype="boolean">
		<cfargument name="messageTypeID" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>
		
		<!--- ----------------------------------------------------------------------------------- --->
		<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
		<!--- ----------------------------------------------------------------------------------- --->
		<cftry>
			<cfstoredproc procedure="queue_subscriptionForceAdd_grabForNotification" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryNotifications" resultset="1">
			</cfstoredproc>
			<cfif local.qryNotifications.recordcount>
				<cfoutput query="local.qryNotifications" group="itemGroupUID">
					<cftry>
						<cfset local.thisReportMemberID = local.qryNotifications.reportMemberID>
						<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
						<cfset local.thisSiteName = local.qryNotifications.siteName>
						<cfset local.thisSiteCode = local.qryNotifications.siteCode>

						<cfset local.arrAdded = arrayNew(1)>
						<cfset local.arrNoAdd = arrayNew(1)>

						<cfset local.unableToAddCount = 0>
						<cfset local.hasSubCount = 0>
						<cfset local.noRateCount  = 0>
						<cfset local.errorCount = 0>
						<cfset local.missingParentCount = 0>
						<cfset local.maxReachedCount = 0>

						<cfoutput>
							<cfset local.errStruct = {
								memberID = local.qryNotifications.memberID,
								memberName = local.qryNotifications.subscriberName,
								rootSubscriberID = local.qryNotifications.rootSubscriberID,
								errType = local.qryNotifications.resultMessage,
								errMessage = ""
							}>
							<cfswitch expression="#local.qryNotifications.resultMessage#">
								<cfcase value="Added">
									<cfset local.errStruct.errMessage = "Added #local.qryNotifications.newTypeName# / #local.qryNotifications.newSubscriptionName#">
									<cfset arrayAppend(local.arrAdded, local.errStruct)>
								</cfcase>
								<cfcase value="Already Has Subscription">
									<cfset local.errStruct.errMessage = "Member already has this addon subscription">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.hasSubCount = local.hasSubCount + 1>
								</cfcase>
								<cfcase value="No Rate">
									<cfset local.errStruct.errMessage = "No rate available for member">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.noRateCount = local.noRateCount + 1>
								</cfcase>
								<cfcase value="Missing Parent">
									<cfset local.errStruct.errMessage = "Subscriber missing appropriate parent subscription for addon">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.missingParentCount = local.missingParentCount + 1>
								</cfcase>
								<cfcase value="Maximum Reached">
									<cfset local.errStruct.errMessage = "Maximum subscriptions for addon set reached">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.maxReachedCount = local.maxReachedCount + 1>
								</cfcase>
								<cfcase value="Error">
									<cfset local.errStruct.errMessage = "Error occurred while trying to add subscription">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.errorCount = local.errorCount + 1>
								</cfcase>
								<cfcase value="Unable to Add">
									<cfset local.errStruct.errMessage = "Member subscription did not support addition">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.unableToAddCount = local.unableToAddCount + 1>
								</cfcase>
								<cfcase value="System Error">
									<cfset local.errStruct.errMessage = "System Error occurred processing this request. MemberCentral has been notified.">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.errorCount = local.errorCount + 1>
								</cfcase>
								<cfdefaultcase>
									<cfset local.errStruct.errType = "Unknown">
									<cfset local.errStruct.errMessage = "Contact MemberCentral Support. Something unusual happened with this request.">
									<cfset arrayAppend(local.arrNoAdd, local.errStruct)>
									<cfset local.errorCount = local.errorCount + 1>
								</cfdefaultcase>
							</cfswitch>
						</cfoutput>

						<cfset local.resultMsg = "Subscription Force Add #local.qryNotifications.newTypeName# / #local.qryNotifications.newSubscriptionName# Results:<br />#ArrayLen(local.arrAdded)# successfully added<br />#local.unableToAddCount# Unable to be added<br />#local.hasSubCount# already had subscription<br />#local.noRateCount# did not have rates<br />#local.missingParentCount# did not have the appropriate parent subscription<br />#local.maxReachedCount# already had the maximum number of addons in the set<br />#local.errorCount# errors<br />">
						
						<cfset addSubForceAddHistory(orgID=local.qryNotifications.orgID, actorMemberID=local.qryNotifications.reportMemberID, 
								mainMessage=local.resultMsg, added=local.arrAdded, notAdded=local.arrNoAdd)>

						<cfif len(local.thisReportEmail)>
							<cfsavecontent variable="local.thisEmailContent">
								<table style="border:0;padding:4px;width:100%;margin-bottom:10px;" cellspacing="0">
									<tr valign="top">
										<td colspan="2" style="width:120px;font-weight:bold;border-bottom:1px solid ##ccc; font-size:9pt;">
											#local.resultMsg#
										</td>
									</tr>
									<cfset local.currLoopMemberID = 0>
									<cfloop array="#local.arrNoAdd#" index="local.thisMessage">
										<cfif local.thisMessage.memberID neq local.currLoopMemberID>
											<tr valign="top">
												<td colspan="2" style="width:120px;font-weight:bold;border-bottom:1px solid ##ccc; font-size:9pt;">
													<br /><nobr>#local.thisMessage.memberName#:</nobr>
													<cfset local.currLoopMemberID = local.thisMessage.memberID>
												</td>
											</tr>
										</cfif>
										<tr valign="top">
											<td style="width:2%">&nbsp;</td>
											<td style="border-bottom:1px solid ##ccc; font-size:9pt;">
												#local.thisMessage.errMessage#
											</td>
										</tr>
									</cfloop>
								</table>
							</cfsavecontent>
							<cfset local.thisEmailContent = trim(replace(replace(replace(local.thisEmailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
						
							<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].networkEmailFrom},
								emailto=[{ name="#local.qryNotifications.reportfirstname# #local.qryNotifications.reportlastname#", email=local.thisReportEmail }],
								emailreplyto=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].supportProviderEmail,
								emailsubject= "#local.thisSiteName# Add Subscription Report",
								emailtitle= "#local.thisSiteName# Add Subscription Report",
								emailhtmlcontent=local.thisEmailContent,
								siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
								memberID=local.thisReportMemberID,
								messageTypeID=arguments.messageTypeID,
								sendingSiteResourceID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteSiteResourceID
								)>
						</cfif>
						
						<!--- ------------- --->
						<!--- UPDATE STATUS --->
						<!--- ------------- --->
						<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;
	
							declare @newstatus int;
							EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionForceAdd', @queueStatus='done', @queueStatusID=@newstatus OUTPUT;

							UPDATE dbo.queue_subscriptionForceAdd
							SET statusID = @newstatus,
								dateUpdated = getdate()
							WHERE itemGroupUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryNotifications.itemGroupUID#">;
						</cfquery>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
					
				</cfoutput>

			</cfif>
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>			

		<!--- -------------------------------------------------------- --->
		<!--- post processing - delete any itemGroupUIDs that are done --->
		<!--- -------------------------------------------------------- --->
		<cftry>
			<cfstoredproc procedure="queue_subscriptionForceAdd_clearDone" datasource="#application.dsn.platformQueue.dsn#">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_subscriptionForceAdd;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

	<cffunction name="addSubForceAddHistory" access="private" output="false" returntype="boolean">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="mainMessage" type="string" required="true">
		<cfargument name="added" type="array" required="true">
		<cfargument name="notAdded" type="array" required="true">
	
		<cfset var local = StructNew()>

		<cftry>
			<cfset local.strRequest = {}>
			<cfset local.strRequest["c"] = "historyEntries_SYS_ADMIN_SUBFORCEADDUPDATE">
			<cfset local.strRequest["d"] = {}>
			<cfset local.strRequest["d"]["HISTORYCODE"] = "SYS_ADMIN_SUBFORCEADDUPDATE">
			<cfset local.strRequest["d"]["ORGID"] = arguments.orgID>
			<cfset local.strRequest["d"]["ACTORMEMBERID"] = arguments.actorMemberID>
			<cfset local.strRequest["d"]["MAINMESSAGE"] = arguments.mainMessage>
			<cfset local.strRequest["d"]["ADDED"] = arguments.added>
			<cfset local.strRequest["d"]["NOTADDED"] = arguments.notAdded>
	
			<!--- this uses the membercentral dsn because it may be called within a cftransaction. --->
			<!--- otherwise would result in this error: Datasource names for all the database tags within the cftransaction tag must be the same --->
			<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.membercentral.dsn#">
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
				VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
			</cfquery>

			<cfset local.success = true>
		<cfcatch type="any">
			<cfset local.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

</cfcomponent>