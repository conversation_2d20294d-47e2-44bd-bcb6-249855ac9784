ALTER PROC dbo.sub_populateSubsRenewEmailsQueue
@taskID int,
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @orgID int, @queueStatusID int, @nowDate datetime = getdate(), @activatedPaymentStatusID int,
		@inactiveStatusID int, @activeSubscriberStatusID int, @acceptedSubscriberStatusID int, @environmentID int,
		@environmentName varchar(50), @lastRunDate datetime, @dtNow dateTime, @taskCFC varchar(100),
		@dtLastRun datetime, @thisMonthStart datetime;

	DECLARE @tblOrgs TABLE (orgID int PRIMARY KEY);
	DECLARE @subscribersToCheck TABLE (subscriberID int PRIMARY KEY, orgID int);

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='subscriptionRenewEmails', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

	SELECT @taskCFC = taskCFC FROM dbo.scheduledTasks where taskid = @taskID;
	SELECT @environmentName = tier from dbo.fn_getServerSettings();
	SELECT @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;
	SELECT @inactiveStatusID = statusID from dbo.sub_statuses where statusCode = 'I';
	SELECT @activeSubscriberStatusID = statusID from dbo.sub_statuses where statusCode = 'A';
	SELECT @acceptedSubscriberStatusID = statusID from dbo.sub_statuses where statusCode = 'P';
	SELECT @activatedPaymentStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'P';

	SET @thisMonthStart = DATEFROMPARTS(YEAR(GETDATE()),MONTH(GETDATE()),1);
	SET @dtNow = DateAdd(Day, DateDiff(Day, 0, getDate()), 0);

	EXEC dbo.sched_getLastRunDate @taskCFC=@taskCFC, @defaultDate=@thisMonthStart, @lastDate=@dtLastRun OUTPUT;

	IF (DateDiff(dd, @dtLastRun, @dtNow) > 4)
		SET @lastRunDate = @dtNow;
	ELSE
		SET @lastRunDate = @dtLastRun;

	INSERT INTO @tblOrgs (orgID)
	select distinct s.orgID
	from dbo.sub_types as t
	inner join dbo.sites as s on s.siteID = t.typeID;

	select @orgID = min(orgID) from @tblOrgs;
	WHILE @orgID is not null BEGIN
		insert into @subscribersToCheck (subscriberID, orgID)
		select distinct sth.subscriberID, @orgID
		from dbo.sub_statusHistory as sth
		inner join dbo.sub_subscriptions subs on subs.orgID = @orgID
			and subs.subscriptionID = sth.subscriptionID
			and subs.renewEmailTemplateID is not null
		where sth.orgID = @orgID
		and sth.statusID in (@activeSubscriberStatusID,@acceptedSubscriberStatusID)
		and sth.updateDate >= @lastRunDate
		and (sth.oldStatusID is null or sth.oldStatusID <> @inactiveStatusID);

		insert into @subscribersToCheck (subscriberID, orgID)
		select distinct psh.subscriberID, @orgID
		from dbo.sub_paymentStatusHistory as psh
		inner join dbo.sub_subscribers ss on ss.orgID = @orgID
			and ss.subscriberID = psh.subscriberID  
		inner join dbo.sub_subscriptions subs on subs.orgID = @orgID
			and subs.subscriptionID = ss.subscriptionID
			and subs.renewEmailTemplateID is not null
		where psh.orgID = @orgID
		and psh.paymentStatusID = @activatedPaymentStatusID
		and psh.updateDate >= @lastRunDate
			except
		select subscriberID, @orgID
		FROM @subscribersToCheck;

		select @orgID = min(orgID) from @tblOrgs where orgID > @orgID;
	END

	-- remove subscribers who have received any join/renew notification previously for this same subscriberID
	-- also removes those who notification has been suppressed by an admin
	delete stc
	from @subscribersToCheck stc
	inner join dbo.ams_emailLog el on el.subscriberID = stc.subscriberID;

	INSERT INTO platformQueue.dbo.queue_subscriptionRenewEmails (orgID, siteID, subscriberID, statusID, dateAdded, dateUpdated)
	select distinct s.orgID, t.siteID, s.subscriberID, @queueStatusID, @nowDate, @nowDate
	from @subscribersToCheck stc
	inner join dbo.sub_subscribers s on s.orgID = stc.orgID
		and s.subscriberID = stc.subscriberID
		and s.statusID in (@activeSubscriberStatusID,@acceptedSubscriberStatusID)
		and s.paymentStatusID = @activatedPaymentStatusID
	inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
		and subs.renewEmailTemplateID is not null
	inner join dbo.sub_types t on t.typeID = subs.typeID
	inner join dbo.sub_rateFrequencies rf on rf.rfid = s.rfid
	inner join dbo.sub_rates r on r.rateID = rf.rateID
		and r.isRenewalRate = 1
	inner join dbo.siteHostnames as sh on sh.siteID = t.siteID
	inner join dbo.siteEnvironments senv on senv.siteID=sh.siteID
		and senv.environmentID = @environmentID
		and senv.mainHostnameID = sh.hostNameID
		except
	select orgID, siteID, subscriberID, statusID, @nowDate, @nowDate
	from platformQueue.dbo.queue_subscriptionRenewEmails;

	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC dbo.sched_resumeTask @name='Process Subscription Renew Emails Queue', @engine='MCLuceeLinux';

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO