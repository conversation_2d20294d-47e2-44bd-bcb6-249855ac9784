ALTER PROC dbo.queue_monthBillEmailIndiv_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @BillqueueTypeID int, @BillstatusWaiting int, @BillstatusReady int, @TSAqueueTypeID int, @TSAstatusDone int, 
		@billItemID int, @checkbillingPeriodID int;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillEmailIndiv', @queueTypeID=@BillqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSA', @queueTypeID=@TSAqueueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='waitingToProcess', @queueStatusID=@BillstatusWaiting OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@BillstatusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@TSAqueueTypeID, @queueStatus='done', @queueStatusID=@TSAstatusDone OUTPUT;

	-- look at anything waitingToProcess to see if any are readyToProcess
	UPDATE mbei
	SET mbei.statusID = @BillstatusReady,
		mbei.dateUpdated = getdate()
	FROM dbo.queue_monthBillEmailIndiv as mbei
	INNER JOIN dbo.queue_monthBillTSA as tsa on tsa.billingPeriodID = mbei.billingPeriodID
		and tsa.depomemberDataID = mbei.depoMemberDataID
		and tsa.statusID = @TSAstatusDone
		and tsa.isOrg = 0
	WHERE mbei.statusID = @BillstatusWaiting;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
