<cfcomponent output="false" cache="true">

	<cfset variables.userID = "1604">
	<cfset variables.userKey = "#application.strPlatformAPIKeys.encoding.userKey#">
	<cfset variables.encodingUrl = "http://manage.encoding.com">
	<cfset variables.s3Destination = "http://" & urlencodedformat("#application.strPlatformAPIKeys.s3encoding.key#") & ":" & urlencodedformat("#application.strPlatformAPIKeys.s3encoding.secret#") & "@seminarweb.s3.amazonaws.com/">

	<cfif application.MCEnvironment eq "production">
		<cfset variables.notifyAdmin = "http://mc.prod.membercentral.com/?event=encoding.notifySWVideoPreviewIsOnline&">
	<cfelse>
		<cfset variables.notifyAdmin = "http://mcbeta.membercentral.com/?event=encoding.notifySWVideoPreviewIsOnline&">
	</cfif>

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="25" } ];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc procedure="queue_SWVideoPreview_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryVideoPreviewQueue" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryVideoPreviewQueue.recordCount>

			<cfscript>
			QueryEach(local.qryVideoPreviewQueue, function(struct thisVideoPreview) {

				// item must still be in the grabbedForProcessing state for this job. else skip it. 
				// this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. 
				if (queueItemHasStatus(queueStatus='grabbedForProcessing', itemID=arguments.thisVideoPreview.itemID)) {
					try {
						
						queryExecute("
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								declare @statusProcessing int;
								EXEC dbo.queue_getStatusIDbyType @queueType='SWVideoPreview', @queueStatus='processingVideo', @queueStatusID=@statusProcessing OUTPUT;

								UPDATE dbo.queue_SWVideoPreview
								SET statusID = @statusProcessing
								WHERE itemID = :itemID;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
							END CATCH
						", 
						{ itemID = { value=arguments.thisVideoPreview.itemID, cfsqltype="cf_sql_integer" } }, 
						{ datasource=application.dsn.platformQueue.dsn } );

						var generateVideoPreviewRequest = generateVideoPreview(strVideoPreview=arguments.thisVideoPreview);						

						if (generateVideoPreviewRequest) {
							queryExecute("
								DELETE FROM dbo.queue_SWVideoPreview
								WHERE itemID = :itemID;
							", 
							{ itemID = { value=arguments.thisVideoPreview.itemID, cfsqltype="cf_sql_integer" } }, 
							{ datasource=application.dsn.platformQueue.dsn } );
						}

					} catch (e) {
						application.objError.sendError(cfcatch=e, objectToDump=local);
						rethrow;
					}
				}
			});
			</cfscript>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>			

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="generateVideoPreview" access="private" output="false" returntype="boolean">
		<cfargument name="strVideoPreview" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.success = false>
		
		<cfset local.JSONString = serializeJSON({"pid":arguments.strVideoPreview.previewID})>
		<cfset local.notifyURL = "#variables.notifyAdmin#id=" & encrypt(local.JSONString,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>

		<cfif val(arguments.strVideoPreview.seminarID) gt 0>
			<cfset local.previewVideoFileName = "#arguments.strVideoPreview.baseFileID#_s_#arguments.strVideoPreview.seminarID#_preview.mp4">
		<cfelse>
			<cfset local.previewVideoFileName = "#arguments.strVideoPreview.baseFileID#_t_#arguments.strVideoPreview.titleID#_preview.mp4">
		</cfif>

		<cfset local.startTime = "#DateFormat(now(),"yyyy-mm-dd")# #arguments.strVideoPreview.timeCodeStart#">
		<cfset local.endTime = "#DateFormat(now(),"yyyy-mm-dd")# #arguments.strVideoPreview.timeCodeEnd#">
		<cfset local.durationInSeconds = Datediff("s",local.startTime,local.endTime)>

		<cfset local.objectKeyPrefix = "swod/#arguments.strVideoPreview.participantOrgCode#/#arguments.strVideoPreview.participantID#/">
		<cfset local.sourceURL = "#variables.s3Destination##lcase("#local.objectKeyPrefix##arguments.strVideoPreview.baseFileID#.mp4")#?nocopy">
		<cfset local.destinationURL = "#variables.s3Destination##lcase("#local.objectKeyPrefix##local.previewVideoFileName#")#?acl=public-read">

		<cfsavecontent variable="local.requestXML">
		<cfoutput>
			<?xml version="1.0" ?>
			<query>
				<action>AddMedia</action>
				<userid>#variables.userID#</userid>
				<userkey>#variables.userKey#</userkey>
				<source>#local.sourceURL#</source>
				<notify>#XMLFormat(local.notifyURL)#</notify>
				<format>
					<output>mp4</output>
					<start>#XMLFormat(arguments.strVideoPreview.timeCodeStart)#:00</start>
					<duration>#XMLFormat(local.durationInSeconds)#</duration>
					<destination>#local.destinationURL#</destination>
				</format>
			</query>
		</cfoutput>
		</cfsavecontent>

		<cfhttp url="#variables.encodingUrl#" charset="utf-8" method="post" result="local.previewResult" redirect="false" useragent="SeminarWeb.com">
			<cfhttpparam type="formfield" name="xml" value="#local.requestXML#">
		</cfhttp>

		<cfset local.xmlResponse = XMLParse(local.previewResult.fileContent)>
		
		<cfif structKeyExists(local.xmlResponse.response,"errors")>
			<cfset local.success = false>
			<cfset local.tmpCatch = { type="", message="Investigation Needed: Video preview creation failed", detail="Video preview creation failed.", tagContext=arrayNew(1) } >
			<cfset local.tmpErr = { requestXML=local.requestXML, previewResult=local.previewResult } >
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
			<cfthrow message="Errors: #local.xmlResponse.response.errors.error.XMLText#">
		<cfelse>
			<cfset local.success = true>
		</cfif>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="queueItemHasStatus" access="private" output="false" returntype="boolean">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
			select count(qi.itemID) as itemCount
			from dbo.queue_SWVideoPreview as qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
			where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
		</cfquery>

		<cfreturn (local.checkItemID.itemCount gt 0)>
	</cffunction>	

</cfcomponent>