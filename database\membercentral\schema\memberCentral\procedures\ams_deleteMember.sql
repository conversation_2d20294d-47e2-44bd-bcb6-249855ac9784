ALTER PROC dbo.ams_deleteMember
@memberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @currentStatus char(1), @orgID int, @membernumber varchar(50), @orgcode varchar(10), @isProtected bit,
		@minSiteID int, @memberAdminSRID int, @queueTypeID int, @queueStatusID int, @dataXML xml;
	DECLARE @orgSites table (siteID int, memberAdminSiteResourceID int);
	
	SELECT @currentStatus = [status], @orgID = orgID, @membernumber = membernumber, @isProtected = isProtected
	FROM dbo.ams_members 
	WHERE memberID = @memberID;
	
	select @orgCode = orgcode from dbo.organizations where orgID = @orgID;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='MCFileDelete', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;
	
	insert into @orgSites (siteID, memberAdminSiteResourceID)
	select siteID, memberAdminSiteResourceID
	from dbo.sites
	where orgID = @orgID;

	IF @currentStatus = 'D'
		RAISERROR('Member already deleted.',16,1);

	IF @isProtected = 1 BEGIN
		IF EXISTS (SELECT 1 FROM dbo.organizations WHERE sysMemberID = @memberID AND orgID = @orgID)
			RAISERROR('Org System Member cannot be deleted.',16,1);

		IF EXISTS (SELECT 1 FROM dbo.api_users WHERE memberID = @memberID AND tokenStatus <> 'D')
			RAISERROR('API Member cannot be deleted.',16,1);
	END

	BEGIN TRAN;
		IF @currentStatus = 'A'
			UPDATE dbo.ams_members
			SET [status] = 'I', 
				dateLastUpdated = getdate()
			WHERE memberid = @memberID;

		UPDATE dbo.ams_members
		SET [status] = 'D', 
			groupPrintID = null,
			dateLastUpdated = getdate()
		WHERE memberid = @memberID;

		UPDATE msd
		SET msd.status = 'D'
		from dbo.ams_memberSiteDefaults as msd
		inner join dbo.sites as s on s.siteID = msd.siteID and s.orgID = @orgID
		WHERE msd.memberid = @memberID
		and msd.status in ('A','I');

		UPDATE dbo.ams_memberNetworkProfiles
		SET status = 'D'
		WHERE memberid = @memberID;

		UPDATE mh
		SET mh.status = 'D'
		from dbo.ams_memberHistory as mh
		inner join dbo.ams_members as m on m.memberID = mh.memberID
		WHERE m.activeMemberID = @memberID
		AND mh.siteID IN (SELECT siteID FROM @orgSites);

		UPDATE mh
		SET mh.status = 'D'
		from dbo.ams_memberHistory as mh
		inner join dbo.ams_members as m on m.memberID = mh.linkMemberID
		WHERE m.activeMemberID = @memberID
		AND mh.siteID IN (SELECT siteID FROM @orgSites);

		DELETE FROM dbo.cache_members_conditions
		WHERE memberID = @memberID;

		DELETE FROM dbo.cache_members_groups
		WHERE memberID = @memberID;

		DELETE FROM dbo.cache_members_merge
		WHERE memberID = @memberID
		OR matchMemberID = @memberID;

		DELETE FROM platformQueue.dbo.queue_memberDelete
		WHERE memberID = @memberID;
	COMMIT TRAN;
	
	/* remove member photos and from the thumb queue */
	INSERT INTO platformQueue.dbo.queue_MCFileDelete (filePath, dateAdded, dateUpdated, statusID)
	select userAssetsPath + lower(@orgcode + '\memberphotos\' + @membernumber + '.jpg'), getdate(), getdate(), @queueStatusID
	from dbo.fn_getServerSettings()
	where dbo.fn_fileExists(userAssetsPath + lower(@orgcode + '\memberphotos\' + @membernumber + '.jpg')) = 1;

	INSERT INTO platformQueue.dbo.queue_MCFileDelete (filePath, dateAdded, dateUpdated, statusID)
	select userAssetsPath + lower(@orgcode + '\memberphotosth\' + @membernumber + '.jpg'), getdate(), getdate(), @queueStatusID
	from dbo.fn_getServerSettings()
	where dbo.fn_fileExists(userAssetsPath + lower(@orgcode + '\memberphotosth\' + @membernumber + '.jpg')) = 1;

	DELETE FROM platformQueue.dbo.queue_memberPhotoThumb
	where memberID = @memberID;

	-- run the deleteMember hook for each site in the org
	DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));
	SELECT @dataXML = (
		SELECT @memberID AS memberid,  @membernumber AS membernumber, 'member deleted' AS reason 
		FOR XML PATH ('data'));
	
	SELECT @memberAdminSRID = MIN(memberAdminSiteResourceID) FROM @orgSites;
	WHILE @memberAdminSRID IS NOT NULL BEGIN
		INSERT INTO @tblHookListeners (executionType, objectPath)
		EXEC dbo.hooks_runHook @event='deleteMember', @siteResourceID=@memberAdminSRID, @dataXML=@dataXML;

		SELECT @memberAdminSRID = MIN(memberAdminSiteResourceID) FROM @orgSites WHERE memberAdminSiteResourceID > @memberAdminSRID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
