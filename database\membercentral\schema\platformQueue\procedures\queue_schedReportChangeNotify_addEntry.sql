ALTER PROC dbo.queue_schedReportChangeNotify_addEntry
@reportID int,
@actorMemberID int,
@changeSection varchar(100) = NULL

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @queueStatusID int, @nowDate datetime = GETDATE();

	IF EXISTS (SELECT 1 FROM membercentral.dbo.rpt_scheduledReports WHERE reportID = @reportID AND enteredByMemberID <> @actorMemberID) BEGIN
		EXEC dbo.queue_getStatusIDbyType @queueType='schedReportChangeNotify', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;
		
		INSERT INTO dbo.queue_schedReportChangeNotify (reportID, actorMemberID, changeSection, statusID, dateAdded, dateUpdated)
		SELECT @reportID, @actorMemberID, @changeSection, @queueStatusID, @nowDate, @nowDate
			EXCEPT
		SELECT reportID, actorMemberID, changeSection, @queueStatusID, @nowDate, @nowDate
		FROM dbo.queue_schedReportChangeNotify;

		EXEC membercentral.dbo.sched_resumeTask @name='Scheduled Report Change Notification', @engine='MCLuceeLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
