ALTER PROC dbo.queue_SWLWebinarReminder_grabForProcessing
@batchCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='SWLWebinarReminder', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded where run date passed
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_SWLWebinarReminder AS qid
	INNER JOIN (
		SELECT TOP (@batchCount) itemID
		FROM dbo.queue_SWLWebinarReminder
		WHERE statusID = @statusReady
		AND runDate <= GETDATE()
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.participantID, qid.seminarID, qid.matchingTimeFrame, qid.runDate,
		qid.isRegistrantInstructionsEnabled, qid.isSpeakerInstructionsEnabled, qid.isWebinarMaterialEnabled 
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_SWLWebinarReminder AS qid ON qid.itemID = tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
