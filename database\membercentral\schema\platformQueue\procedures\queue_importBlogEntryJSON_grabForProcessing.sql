ALTER PROC dbo.queue_importBlogEntryJSON_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='importBlogEntryJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;
	CREATE TABLE #tmpImportBlogEntryJSON (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportBlogEntryJSON
	FROM dbo.queue_importBlogEntryJSON AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_importBlogEntryJSON
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM dbo.queue_importBlogEntryJSON;

	SELECT DISTINCT qid.itemID, qid.itemGroupUID, qid.siteID, qid.blogID, qid.submittedMemberID, qid.blogEntryJSON, qid.errorMessage, @totalItemCount as totalItemCount
	FROM #tmpImportBlogEntryJSON AS tmp
	INNER JOIN dbo.queue_importBlogEntryJSON AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
