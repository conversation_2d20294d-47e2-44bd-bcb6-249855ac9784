<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset local.itemCount = getQueueItemCount()>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.itemCount)>

		<cfif local.itemCount GT 0>
			<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="PROCESSDOCUNZIP")>
			<cfset local.success = processQueue(messageTypeID=local.messageTypeID)>
			<cfif NOT local.success>
				<cfthrow message="Error running cleanQueue()">
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="messageTypeID" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_depoDocumentsUnzip_grabForProcessing">
				<cfprocresult name="local.qryDepoDocQueueItem" resultset="1">
			</cfstoredproc>

			<cfif local.qryDepoDocQueueItem.recordcount>
				<cftry>
					<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
						set nocount on;

						declare @statusProcessing int;
						EXEC dbo.queue_getStatusIDbyType @queueType='depoDocumentsUnzip', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

						UPDATE dbo.queue_depoDocumentsUnzip
						SET statusID = @statusProcessing,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryDepoDocQueueItem.itemID#">;
					</cfquery>

					<cfscript>
					if (fileExists(local.qryDepoDocQueueItem.pathtoZip)) {
						// create holding folder for extracted files
						local.strUnzipFolder = application.objDocDownload.createHoldingFolder('depozip');

						// unzip file
						cfexecute(name="/usr/bin/unzip", arguments="""#local.qryDepoDocQueueItem.pathtoZip#"" -d #local.strUnzipFolder.folderPath#/", variable="local.standardOut", errorVariable="local.errorMessage", timeout="600");

						// success
						if (NOT structKeyExists(local, "errorMessage") OR NOT len(local.errorMessage)) {

							// check for valid files
							local.qryFiles = DirectoryList(path=local.strUnzipFolder.folderPath, recurse=true, listInfo="query", sort="asc", type="file", filter=function(path){
								var thisFileName = listLast(arguments.path,'/');
								if (listLen(thisFileName,'.') EQ 2 AND listLast(thisFileName,'.') NEQ 'exe')
									return true;
								else 
									return false;
							});

							if (local.qryFiles.recordCount) {
								cfloop(query=local.qryFiles) {
									local.thisFileExt = listLast(local.qryFiles.name,'.');
										
									if (listFindNoCase("rar,zip",local.thisFileExt)) {
										local.pathToZip = "#local.qryFiles.directory#/#local.qryFiles.name#";
										addToDepoDocumentsUnzipQueue(pathToZip=local.pathToZip, depomemberdataID=local.qryDepoDocQueueItem.depomemberdataID, 
											docState=local.qryDepoDocQueueItem.State, DepoAmazonBucks=local.qryDepoDocQueueItem.DepoAmazonBucks, 
											DepoAmazonBucksFullName=local.qryDepoDocQueueItem.DepoAmazonBucksFullName, 
											DepoAmazonBucksEmail=local.qryDepoDocQueueItem.DepoAmazonBucksEmail, uploadSourceID=local.qryDepoDocQueueItem.uploadSourceID);
									} else {
										doSaveDocument(depomemberdataID=local.qryDepoDocQueueItem.depomemberdataID, docState=local.qryDepoDocQueueItem.State, 
											originalExt=local.thisFileExt, unzipFilePath=local.qryFiles.directory, fileName=local.qryFiles.name, 
											DepoAmazonBucks=local.qryDepoDocQueueItem.DepoAmazonBucks, DepoAmazonBucksFullName=local.qryDepoDocQueueItem.DepoAmazonBucksFullName, 
											DepoAmazonBucksEmail=local.qryDepoDocQueueItem.DepoAmazonBucksEmail, uploadSourceID=local.qryDepoDocQueueItem.uploadSourceID);
									}
								}
							}

							clearQueueItem(itemID=local.qryDepoDocQueueItem.itemID);
							local.success = true;

						} else {
							notifySupport(itemID=local.qryDepoDocQueueItem.itemID, pathToZip=local.qryDepoDocQueueItem.pathToZip, messageTypeID=arguments.messageTypeID, errorMessage=local.errorMessage);
							local.success = false;
						}
					} else {
						local.errorMessage = "File not found";
						notifySupport(itemID=local.qryDepoDocQueueItem.itemID, pathToZip=local.qryDepoDocQueueItem.pathToZip, messageTypeID=arguments.messageTypeID, errorMessage=local.errorMessage);
						local.success = true;
					}
					</cfscript>
				<cfcatch type="any">
					<cfif findNoCase("zipfile is empty",cfcatch.detail)>
						<cfset clearQueueItem(itemID=local.qryDepoDocQueueItem.itemID)>
					<cfelse>
						<cfset notifySupport(itemID=local.qryDepoDocQueueItem.itemID, pathToZip=local.qryDepoDocQueueItem.pathToZip, messageTypeID=arguments.messageTypeID, errorMessage="Message:#cfcatch.message#; Detail:#cfcatch.detail#;")>
					</cfif>
					<cfset local.success = true>
				</cfcatch>
				</cftry>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="doSaveDocument" access="private" output="false" returntype="numeric">
		<cfargument name="depoMemberDataID" type="numeric" required="true">
		<cfargument name="docState" type="string" required="true">
		<cfargument name="originalExt" type="string" required="true">
		<cfargument name="unzipFilePath" type="string" required="true">
		<cfargument name="fileName" type="string" required="true">
		<cfargument name="DepoAmazonBucks" type="boolean" required="true">
		<cfargument name="DepoAmazonBucksFullName" type="string" required="true">
		<cfargument name="DepoAmazonBucksEmail" type="string" required="true">
		<cfargument name="uploadSourceID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfstoredproc procedure="ts_addDepoDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docState#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.originalExt#">
			<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
			<cfif arguments.DepoAmazonBucks>
				<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksFullName#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksEmail#">
			<cfelse>
				<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" null="true">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" null="true">
			</cfif>
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.uploadSourceID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
			<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.documentID">
		</cfstoredproc>

		<cfset local.finalFileName = "#local.documentID#.#lcase(arguments.originalExt)#">

		<cfset local.extractedFilePath = "#arguments.unzipFilePath#/#local.finalFileName#">
		<cfset local.originalDocFilePath = "#application.paths.docs.originals.path##local.finalFileName#">

		<!--- rename file to documentID (ensuring extension is lower case) --->
		<cffile action="RENAME" source="#arguments.unzipFilePath#/#arguments.fileName#" destination="#local.extractedFilePath#">

		<!--- copy file to original docs --->
		<cffile action="COPY" source="#local.extractedFilePath#" destination="#local.originalDocFilePath#">
		
		<cfif arguments.originalExt EQ 'pdf'>
			<cfstoredproc procedure="ts_addDepoDocumentToAttachQueue" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
			</cfstoredproc>
		<cfelse>
			<cfset local.originalDocS3UploadFilePath = "#application.paths.docs.originals_s3upload.path##local.finalFileName#">
			<cfset local.s3keyMod = numberFormat(local.documentID mod 1000,"0000")>
			<cfset local.s3objectKey = lcase("depos/original/#local.s3keyMod#/#local.finalFileName#")>
			<cfset addToS3UploadQueue(filepath=local.originalDocS3UploadFilePath, objectKey=local.s3objectKey)>
		</cfif>

		<cfreturn local.documentID>
	</cffunction>

	<cffunction name="addToDepoDocumentsUnzipQueue" access="private" output="false" returntype="void">
		<cfargument name="pathToZip" type="string" required="true">
		<cfargument name="depoMemberDataID" type="numeric" required="true">
		<cfargument name="docState" type="string" required="true">
		<cfargument name="DepoAmazonBucks" type="string" required="true">
		<cfargument name="DepoAmazonBucksFullName" type="string" required="true">
		<cfargument name="DepoAmazonBucksEmail" type="string" required="true">
		<cfargument name="uploadSourceID" type="numeric" required="true">

		<cfstoredproc procedure="ts_addDepoDocumentToUnzipQueue" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.pathToZip#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docState#">
			<cfif arguments.DepoAmazonBucks>
				<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksFullName#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksEmail#">
			<cfelse>
				<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" null="true">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" null="true">
			</cfif>
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.uploadSourceID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="addToS3UploadQueue" access="private" output="false" returntype="void">
		<cfargument name="filepath" type="string" required="true">
		<cfargument name="objectKey" type="string" required="true">

		<cfset var qryAddDocToS3UploadQueue = "">

		<cfquery name="qryAddDocToS3UploadQueue" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @objectKey varchar(400), @s3bucketName varchar(100), @filePath varchar(400),
				@s3UploadReadyStatusID int, @nowDate datetime = getdate();

			SET @s3bucketName = 'trialsmith-depos';
			set @filePath = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.filepath#">
			set @objectKey = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.objectKey#">

			EXEC dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

			IF NOT EXISTS (select 1 from dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
				INSERT INTO dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
				VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePath, 1, @nowDate, @nowDate);
		</cfquery>
	</cffunction>

	<cffunction name="clearQueueItem" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var qryDeleteQueueItem = "">

		<cfquery name="qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
			DELETE FROM dbo.queue_depoDocumentsUnzip
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
		</cfquery>
	</cffunction>

	<cffunction name="notifySupport" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="pathToZip" type="string" required="true">
		<cfargument name="messageTypeID" type="numeric" required="true">
		<cfargument name="errorMessage" type="string" required="true">

		<cfset var local = structnew()>

		<cfset local.mc_siteInfo = application.objSiteInfo.mc_siteInfo['TS']>

		<cfsavecontent variable="local.emailContent">
			<cfoutput>
			<div>
				Depo Document Unzip Queue failed for the following File.<br/><br/>
				Zip File: <b>#arguments.pathToZip#</b><br/>
				Error Message: <span style="color:red;">#arguments.errorMessage#</span><br/>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="MemberCentral", email=local.mc_siteInfo.networkEmailFrom},
			emailto=[{ name=local.mc_siteInfo.supportProviderName, email=local.mc_siteInfo.supportProviderEmail }],
			emailreplyto="",
			emailsubject="[#Application.MCEnvironment#] Depo Document Unzip Queue Failed @dev@",
			emailtitle="Depo Document Unzip Queue failed",
			emailhtmlcontent=local.emailContent,
			siteID=local.mc_siteInfo.siteID,
			memberID=local.mc_siteInfo.sysMemberID,
			messageTypeID=arguments.messageTypeID,
			sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
		)>

		<cfquery name="local.qryUpdateQueueItemToFailed" datasource="#application.dsn.platformQueue.dsn#">
			set nocount on;

			declare @statusFailed int;
			EXEC dbo.queue_getStatusIDbyType @queueType='depoDocumentsUnzip', @queueStatus='failed', @queueStatusID=@statusFailed OUTPUT;

			UPDATE dbo.queue_depoDocumentsUnzip
			SET statusID = @statusFailed,
				isNotified = 1,
				errorMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.errorMessage#">,
				dateUpdated = GETDATE()
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(qi.itemID) as itemCount
			FROM dbo.queue_depoDocumentsUnzip AS qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID
			WHERE qs.queueStatus <> 'failed';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>