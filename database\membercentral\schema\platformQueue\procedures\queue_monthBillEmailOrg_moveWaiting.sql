ALTER PROC dbo.queue_monthBillEmailOrg_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @BillqueueTypeID int, @BillstatusWaiting int, @BillstatusReady int, @SWqueueTypeID int, @SWstatusDone int, 
		@TSRqueueTypeID int, @TSRstatusDone int, @TSAqueueTypeID int, @TSAstatusDone int, @billItemID int, 
		@checkbillingPeriodID int, @checkisOrg bit;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillEmailOrg', @queueTypeID=@BillqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillSW', @queueTypeID=@SWqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSRoyalty', @queueTypeID=@TSRqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSA', @queueTypeID=@TSAqueueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='waitingToProcess', @queueStatusID=@BillstatusWaiting OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@BillstatusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@SWqueueTypeID, @queueStatus='done', @queueStatusID=@SWstatusDone OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@TSRqueueTypeID, @queueStatus='done', @queueStatusID=@TSRstatusDone OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@TSAqueueTypeID, @queueStatus='done', @queueStatusID=@TSAstatusDone OUTPUT;

	-- look at anything waitingToProcess to see if any are readyToProcess
	select @checkbillingPeriodID = MIN(billingPeriodID) from dbo.queue_monthBillEmailOrg where statusID = @BillstatusWaiting;
	while @checkbillingPeriodID is not null begin

		IF NOT EXISTS (select top 1 itemID from dbo.queue_monthBillSW where billingPeriodID = @checkbillingPeriodID and statusID <> @SWstatusDone)
		AND NOT EXISTS (select top 1 itemID from dbo.queue_monthBillTSRoyalty where billingPeriodID = @checkbillingPeriodID and statusID <> @TSRstatusDone)
		AND NOT EXISTS (select top 1 itemID from dbo.queue_monthBillTSA where billingPeriodID = @checkbillingPeriodID and isOrg = 1 and statusID <> @TSAstatusDone)
			UPDATE dbo.queue_monthBillEmailOrg
			SET statusID = @BillstatusReady,
				dateUpdated = getdate()
			WHERE billingPeriodID = @checkbillingPeriodID;

		select @checkbillingPeriodID = MIN(billingPeriodID) from dbo.queue_monthBillEmailOrg where statusID = @BillstatusWaiting and billingPeriodID > @checkbillingPeriodID;
	end

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
