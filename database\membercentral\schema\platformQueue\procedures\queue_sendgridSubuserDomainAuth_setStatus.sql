ALTER PROC dbo.queue_sendgridSubuserDomainAuth_setStatus
@itemID int,
@statusCode varchar(30)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @subuserQueueStatusID int;
	EXEC dbo.queue_getStatusIDbyType @queueType='SendgridSubuserDomainAuth', @queueStatus=@statusCode, @queueStatusID=@subuserQueueStatusID OUTPUT;

	UPDATE dbo.queue_SendgridSubuserDomainAuth
	SET statusID = @subuserQueueStatusID,
		dateUpdated = getdate()
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
