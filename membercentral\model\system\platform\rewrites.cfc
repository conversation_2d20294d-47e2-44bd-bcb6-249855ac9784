<cfcomponent>
	<cfset variables.CRLF = Chr(13) & chr(10)>
	<cfset variables.privateIPregex = "(^127\.0\.0\.1)|(^10\.)|(^172\.1[6-9]\.)|(^172\.2[0-9]\.)|(^172\.3[0-1]\.)|(^192\.168\.)"/>
	<cfset variables.ipValidatorRegex = "^((2[0-5][0-5]|1[\d][\d]|[\d][\d]|[\d])\.){3}(2[0-5][0-5]|1[\d][\d]|[\d][\d]|[\d])$"/>

	<cfset variables.nginxRewrites = application.paths.nginxAutoManaged.path>
	<cfset variables.nginxRewritesBackup = lcase("#application.paths.RAIDRewriteRoot.path#_rewrites-backups/nginx/")>
	<cfset variables.rewritesCustom = createObject("component", "rewrites-custom")/>
	<cfset variables.nginxPaths = {
		automanagedTriggerFolder = "#variables.nginxRewrites#reloadrequest/",
		automanagedTriggerFilename = "reload"
	}>
	<cfset variables.nginxPaths.localDevelopment = {
		nginxRewrites = "/opt/sites/mc/mc-automanaged/",
		mcRootFolder = "/opt/sites/mc/mc-automanaged/localDevelopment/siteroot",
		sslCertFolder = "/etc/nginx/ssl/certs/",
		sslKeysFolder = "/etc/nginx/ssl/private/",
		logPath = "/opt/logs/"
	}>
	<cfset variables.nginxPaths.test = {
		nginxRewrites = "/opt/sites/test/wwwroot/mc-automanaged/",
		mcRootFolder = "/opt/sites/test/wwwroot/mc-automanaged/test/siteroot",
		sslCertFolder = "/etc/nginx/ssl/certs/",
		sslKeysFolder = "/etc/nginx/ssl/private/",
		logPath = "/opt/logs/"
	}>
		<cfset variables.nginxPaths.development = {
		nginxRewrites = "/opt/sites/dev/wwwroot/mc-automanaged/",
		mcRootFolder = "/opt/sites/dev/wwwroot/mc-automanaged/dev/siteroot",
		sslCertFolder = "/etc/nginx/ssl/certs/",
		sslKeysFolder = "/etc/nginx/ssl/private/",
		logPath = "/opt/logs/"
	}>
	<cfset variables.nginxPaths.beta = {
		nginxRewrites = "/opt/sites/beta/wwwroot/mc-automanaged/",
		mcRootFolder = "/opt/sites/beta/wwwroot/mc-automanaged/beta/siteroot",
		sslCertFolder = "/etc/nginx/ssl/certs/",
		sslKeysFolder = "/etc/nginx/ssl/private/",
		logPath = "/opt/logs/"
	}>
	<cfset variables.nginxPaths.newtest = {
		nginxRewrites = "/opt/sites/test/wwwroot/mc-automanaged/",
		mcRootFolder = "/opt/sites/test/wwwroot/mc-automanaged/test/siteroot",
		sslCertFolder = "/etc/nginx/ssl/certs/",
		sslKeysFolder = "/etc/nginx/ssl/private/",
		logPath = "/opt/logs/"
	}>
		<cfset variables.nginxPaths.newdevelopment = {
		nginxRewrites = "/opt/sites/dev/wwwroot/mc-automanaged/",
		mcRootFolder = "/opt/sites/dev/wwwroot/mc-automanaged/dev/siteroot",
		sslCertFolder = "/etc/nginx/ssl/certs/",
		sslKeysFolder = "/etc/nginx/ssl/private/",
		logPath = "/opt/logs/"
	}>
	<cfset variables.nginxPaths.newbeta = {
		nginxRewrites = "/opt/sites/beta/wwwroot/mc-automanaged/",
		mcRootFolder = "/opt/sites/beta/wwwroot/mc-automanaged/beta/siteroot",
		sslCertFolder = "/etc/nginx/ssl/certs/",
		sslKeysFolder = "/etc/nginx/ssl/private/",
		logPath = "/opt/logs/"
	}>
	<cfset variables.nginxPaths.production = {
		nginxRewrites = "/home/<USER>/nginx/nginxIncludes/mc-automanaged/",
		mcRootFolder = "/home/<USER>/nginx/nginxIncludes/mc-automanaged/production/siteroot",
		sslCertFolder = "/home/<USER>/nginx/nginxIncludes/certs/",
		sslKeysFolder = "/home/<USER>/nginx/nginxIncludes/certs/",
		logPath = "/var/log/nginx/"
	}>

	<cffunction name="refreshAllConfFiles" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="false" default="0">
		<cfscript>
			var local = structNew();
			if (isNGINXReloadPending())
				cancelNGINXReload();

			if (arguments.siteID gt 0) {
				updateHostnames(siteid=arguments.siteID);
				updateAliases(siteid=arguments.siteID);
				updateRobotsFile(siteid=arguments.siteID);
			} else {
				setting requesttimeout="600";

				application.objSiteInfo.mc_siteinfo.each(function(sitecode, siteData) {
					updateHostnames(siteid=siteData.siteID);
					updateAliases(siteid=siteData.siteID);
					updateRobotsFile(siteid=siteData.siteID);
				},true,10);
			}
			updateCommonFiles();
			updateWilcardHostnamesNGINX();
			triggerNGINXReload();
		</cfscript>

	</cffunction>
	<cffunction name="triggerNGINXReload" access="private" output="false" returntype="void">
		<cfset var local = structNew()>
		<cffile action="write" file="#variables.nginxPaths.automanagedTriggerFolder##variables.nginxPaths.automanagedTriggerFilename#" output="#dateFormat(now(),"mm/dd/yy")# #timeFormat(now(),"HH:mm:ss")#" nameconflict="overwrite">
	</cffunction>
	<cffunction name="cancelNGINXReload" access="private" output="false" returntype="void">
		<cfset var local = structNew()>
		<cffile action="delete" file="#variables.nginxPaths.automanagedTriggerFolder##variables.nginxPaths.automanagedTriggerFilename#" >
	</cffunction>
	<cffunction name="isNGINXReloadPending" access="private" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfreturn (directoryExists(variables.nginxPaths.automanagedTriggerFolder) and fileExists("#variables.nginxPaths.automanagedTriggerFolder##variables.nginxPaths.automanagedTriggerFilename#"))>
	</cffunction>

	<cffunction name="updateAliases" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="requestNGINXReload" type="boolean" required="false" default="false">
		
		<cfset var local = structNew()>
		
		<cfset updateAliasesNGINX(siteID=arguments.siteID)>
		<cfif requestNGINXReload>
			<cfset triggerNGINXReload()>
		</cfif>
	</cffunction>
	<cffunction name="updateAliasesNGINX" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<!--- get main hostname and sitecode for all environments --->
			<cfquery name="local.qrySiteMainHostnames" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select s.sitecode, sh.hostname, e.environmentName
				from dbo.sites as s
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.SiteHostnames as sh on sh.siteID = s.siteID
				inner join dbo.siteEnvironments se on se.siteID = sh.siteID
					and se.mainhostnameID = sh.hostnameID
				inner join dbo.platform_environments e on e.environmentID = se.environmentID
					<cfif application.MCEnvironment neq "localDevelopment">
						and e.environmentName = '#application.MCEnvironment#'
					</cfif>
				where s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteid#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qrySiteMainHostnames.recordcount>
				<cfset local.siteHostnameInfo = {}>
				<cfloop query="local.qrySiteMainHostnames">
					<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname] = {
						mainhostname = local.qrySiteMainHostnames.hostname,
						rewritePath = "#variables.nginxRewrites##local.qrySiteMainHostnames.environmentname#/rewrites/aliases-#local.qrySiteMainHostnames.sitecode#.conf"
					}>
				</cfloop>
				<cfquery name="local.qryAliases" datasource="#application.dsn.membercentral.dsn#">
					set nocount on;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select redirectName, redirectURL
					from dbo.siteRedirects 
					WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteid#">
					order by redirectName

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					set nocount off;
				</cfquery>
				<cfset local.qryAliases = local.qryAliases.map(function(row) {
					row.redirectName = replace(row.redirectName," ","\ ","ALL");
					return row;
					})> 

				<cfloop collection="#local.siteHostnameInfo#" item="local.thisEnvironment">
					<cfset local.customRules = variables.rewritesCustom.getCustomNGINXRewrites(sitecode=local.qrySiteMainHostnames.sitecode, environment=local.thisEnvironment, nginxpaths=variables.nginxPaths)>
					<!--- Create NGINX File for this environment--->
					<cfsavecontent variable="local.newText">
						<cfoutput>
							#local.customRules#
							
							location ~* ^/#local.qrySiteMainHostnames.sitecode#/?\b { rewrite (?i)/#local.qrySiteMainHostnames.sitecode#/?(.*)$ /$1 redirect; }
							<cfloop query="local.qryAliases">
								location ~* ^/#local.qryAliases.redirectName#$ { return 302 "#Replace(local.qryAliases.redirectURL,' ','%20','ALL')#"; }
							</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.newText = trim(Replace(Replace(local.newText,chr(9),'','ALL'),'#variables.CRLF##variables.CRLF#',variables.crlf,'ALL'))>
					<cfif FileExists(local.siteHostnameInfo[local.thisEnvironment].rewritePath)>
						<cffile action="delete" file="#local.siteHostnameInfo[local.thisEnvironment].rewritePath#">
					</cfif>
					<cfset local.outputPath = local.siteHostnameInfo[local.thisEnvironment].rewritePath>
					<!--- write out new file --->
					<cffile action="write" file="#local.outputPath#" output="#local.newText#">
				</cfloop>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="updateHostnames" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="requestNGINXReload" type="boolean" required="false" default="false">
		
		<cfset var local = structNew()>
		
		<cfset updateHostnamesNGINX(siteID=arguments.siteID)>
		<cfif requestNGINXReload>
			<cfset triggerNGINXReload()>
		</cfif>
	</cffunction>
	<cffunction name="updateHostnamesNGINX" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objURI = CreateObject("java","java.net.URI")>
		
		<cftry>
			<!--- get sitecode --->
			<cfquery name="local.qrySiteMainHostnames" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select s.sitecode, o.orgcode, sh.hostname, e.environmentName, se.enableNGINXConf, e.environmentID, sh.internalIpAddress, 
					sh.hasSSL, sh.sslCertFilename, sh.sslPrivateKeyFilename
				from dbo.sites as s
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.organizations o on o.orgID = s.orgID
				inner join dbo.SiteHostnames as sh on sh.siteID = s.siteID
				inner join dbo.siteEnvironments se on se.siteID = sh.siteID
					and se.mainhostnameID = sh.hostnameID
				inner join dbo.platform_environments e on e.environmentID = se.environmentID
					<cfif application.MCEnvironment neq "localDevelopment">
						and e.environmentName = '#application.MCEnvironment#'
					</cfif>
				where s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteid#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qrySiteMainHostnames.recordcount>
				<cfset local.siteHostnameInfo = {}>
				<cfloop query="local.qrySiteMainHostnames">
					<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname] = {
						mainhostname = local.qrySiteMainHostnames.hostname,
						sitecode =  local.qrySiteMainHostnames.sitecode,
						orgcode =  local.qrySiteMainHostnames.orgcode,
						internalIPAddress_orig = local.qrySiteMainHostnames.internalIpAddress,
						internalIpAddress = getIPAddressArray(local.qrySiteMainHostnames.internalIpAddress),
						hasSSL = local.qrySiteMainHostnames.hasSSL,
						sslCertFilename = local.qrySiteMainHostnames.sslCertFilename,
						sslPrivateKeyFilename = local.qrySiteMainHostnames.sslPrivateKeyFilename,
						enableNGINXConf = local.qrySiteMainHostnames.enableNGINXConf,
						rewritePath = "#variables.nginxRewrites##local.qrySiteMainHostnames.environmentname#/sites-enabled/hostnames-#local.qrySiteMainHostnames.sitecode#.conf",
						wildcardRewritePath = "#variables.nginxRewrites##local.qrySiteMainHostnames.environmentname#/sites-enabled/hostnames-#local.qrySiteMainHostnames.sitecode#-wildcardHostnames.conf",
						disabledrewritePath = "#variables.nginxRewrites##local.qrySiteMainHostnames.environmentname#/sites-disabled/hostnames-#local.qrySiteMainHostnames.sitecode#.conf"
					}>
					<!--- Disable Conf is no ipaddress found on mainhostname--->
					<cfif local.qrySiteMainHostnames.environmentname eq "production" and not local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].internalIpAddress.len()>
						<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].enableNGINXConf = 0>
					<cfelseif local.qrySiteMainHostnames.environmentname neq "production" and (local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].internalIPAddress_orig.len() and not local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].internalIpAddress.len())>
						<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].enableNGINXConf = 0>
					</cfif>

					<cfquery name="local.qryThisEnvironmentHostnames" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						declare @environmentID int = <cfqueryparam value="#local.qrySiteMainHostnames.environmentID#" cfsqltype="CF_SQL_INTEGER">;
						declare @siteID int = <cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">;

						SELECT sh.hostName, sh.useRedirect, msh.hostName AS mainHostName, sh.internalIpAddress, sh.hasSSL, sh.sslCertFilename, 
							sh.sslPrivateKeyFilename, sh.cloudflare_customHostnameID, sh.cloudflare_hostnamestatus, 
							sh.cloudflare_httpownershipverfication_body, sh.cloudflare_httpownershipverfication_url,
							sh.cloudflare_sslstatus, sh.cloudflare_sslvalidation_body, sh.cloudflare_sslvalidation_url, 
							0 as isWildcardHostname
						FROM dbo.sites s
						inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
						inner join dbo.siteHostNames AS sh on s.siteID = sh.siteID
							and s.siteID = @siteID
						inner join dbo.siteEnvironments se on se.siteID = sh.siteID and se.environmentID = sh.environmentID
							and se.environmentID = @environmentID
						inner join dbo.siteHostNames AS msh on se.mainhostnameID = msh.hostnameID
						ORDER BY msh.hostName, case when msh.hostName = sh.hostname then 1 else 0 end desc, sh.useRedirect, sh.hostName

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].qryHostnames = local.qryThisEnvironmentHostnames>
					<cfset local.currentMainHostName = "">
					<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostnameArray = ArrayNew(1)>
					<cfloop query="local.qryThisEnvironmentHostnames">
						<cfif local.qryThisEnvironmentHostnames.mainHostName neq local.currentMainHostName>
							<cfset local.currentMainHostName = local.qryThisEnvironmentHostnames.mainHostName>
							<cfset local.currentUseRedirect = "">
							<cfset arrayAppend(local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostnameArray, structNew())>
							<cfset local.currentElement = arrayLen(local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray)>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].isWildcardHostname = local.qryThisEnvironmentHostnames.isWildcardHostname>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].mainHostname = local.qryThisEnvironmentHostnames.mainHostName>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].internalIpAddress = getIPAddressArray(local.qryThisEnvironmentHostnames.internalIpAddress)>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].hasSSL = local.qryThisEnvironmentHostnames.hasSSL>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].sslCertFilename = local.qryThisEnvironmentHostnames.sslCertFilename>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].sslPrivateKeyFilename = local.qryThisEnvironmentHostnames.sslPrivateKeyFilename>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].cloudflare_hostnamestatus = local.qryThisEnvironmentHostnames.cloudflare_hostnamestatus>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].cloudflare_httpownershipverfication_body = local.qryThisEnvironmentHostnames.cloudflare_httpownershipverfication_body>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].cloudflare_httpownershipverfication_url = local.qryThisEnvironmentHostnames.cloudflare_httpownershipverfication_url>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].cloudflare_sslstatus = local.qryThisEnvironmentHostnames.cloudflare_sslstatus>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].cloudflare_sslvalidation_body = local.qryThisEnvironmentHostnames.cloudflare_sslvalidation_body>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].cloudflare_sslvalidation_url = local.qryThisEnvironmentHostnames.cloudflare_sslvalidation_url>
							<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].aliasDomainsWithRedirect = arrayNew(1)>
						</cfif>
						
						
						<cfif local.qryThisEnvironmentHostnames.hostName neq local.qryThisEnvironmentHostnames.mainHostName and not len(local.qryThisEnvironmentHostnames.useRedirect)>
							<!--- Add alias with mainhostname as redirect to struct --->
							<cfset local.newHostname = {
								hostname=local.qryThisEnvironmentHostnames.hostName, 
								redirect="http://#local.qryThisEnvironmentHostnames.mainHostName#$request_uri",
								internalIPAddress_orig = local.qryThisEnvironmentHostnames.internalIpAddress,
								internalIPAddress = getIPAddressArray(local.qryThisEnvironmentHostnames.internalIpAddress),
								hasSSL = local.qryThisEnvironmentHostnames.hasSSL,
								sslCertFilename = local.qryThisEnvironmentHostnames.sslCertFilename,
								sslPrivateKeyFilename = local.qryThisEnvironmentHostnames.sslPrivateKeyFilename,
								cloudflare_hostnamestatus = local.qryThisEnvironmentHostnames.cloudflare_hostnamestatus,
								cloudflare_httpownershipverfication_body = local.qryThisEnvironmentHostnames.cloudflare_httpownershipverfication_body,
								cloudflare_httpownershipverfication_url = local.qryThisEnvironmentHostnames.cloudflare_httpownershipverfication_url,
								cloudflare_sslstatus = local.qryThisEnvironmentHostnames.cloudflare_sslstatus,
								cloudflare_sslvalidation_body = local.qryThisEnvironmentHostnames.cloudflare_sslvalidation_body,
								cloudflare_sslvalidation_url = local.qryThisEnvironmentHostnames.cloudflare_sslvalidation_url
							}>
							<cfset arrayAppend( local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].aliasDomainsWithRedirect, local.newHostname)>
						<cfelseif len(local.qryThisEnvironmentHostnames.useredirect)>
							<cfset local.newRedirectHostname = {
								hostname=local.qryThisEnvironmentHostnames.hostName, 
								redirect="http://#local.qryThisEnvironmentHostnames.mainHostName#/#local.qryThisEnvironmentHostnames.useRedirect#",
								internalIPAddress_orig = local.qryThisEnvironmentHostnames.internalIpAddress,
								internalIPAddress = getIPAddressArray(local.qryThisEnvironmentHostnames.internalIpAddress),
								hasSSL = local.qryThisEnvironmentHostnames.hasSSL,
								sslCertFilename = local.qryThisEnvironmentHostnames.sslCertFilename,
								sslPrivateKeyFilename = local.qryThisEnvironmentHostnames.sslPrivateKeyFilename,
								cloudflare_hostnamestatus = local.qryThisEnvironmentHostnames.cloudflare_hostnamestatus,
								cloudflare_httpownershipverfication_body = local.qryThisEnvironmentHostnames.cloudflare_httpownershipverfication_body,
								cloudflare_httpownershipverfication_url = local.qryThisEnvironmentHostnames.cloudflare_httpownershipverfication_url,
								cloudflare_sslstatus = local.qryThisEnvironmentHostnames.cloudflare_sslstatus,
								cloudflare_sslvalidation_body = local.qryThisEnvironmentHostnames.cloudflare_sslvalidation_body,
								cloudflare_sslvalidation_url = local.qryThisEnvironmentHostnames.cloudflare_sslvalidation_url
							}>
							<cfset arrayAppend( local.siteHostnameInfo[local.qrySiteMainHostnames.environmentname].mainHostNameArray[local.currentElement].aliasDomainsWithRedirect, local.newRedirectHostname)>
						</cfif>
					</cfloop>

				</cfloop>
	

				<cfloop collection="#local.siteHostnameInfo#" item="local.thisEnvironment">

					<cfset local.definedHostnameConfig = "">
					<cfset local.wildcardHostnameConfig = "">

					<cfloop array="#local.siteHostnameInfo[local.thisEnvironment].mainHostnameArray#" index="local.arrEl">
						<cfset local.newText = "">
						<cfsavecontent variable="local.newText">
							<cfoutput>
								<cfif local.arrEl.hasSSL and len(local.arrEl.sslCertFilename) and len(local.arrEl.sslPrivateKeyFilename)>
									## Main Server block nonssl redirect
									server {
										server_name #local.arrEl.mainHostname#;
										<cfif not local.arrEl.internalIpAddress.len()>
											listen 80;
										<cfelse>
											<cfloop index="local.currentIndex" item="local.thisIP" array="#local.arrEl.internalIpAddress#">
												listen #local.thisIP#:80;
											</cfloop>
										</cfif>
										include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/setdefaults.conf;
										include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/parseEnvironmentHostnames.conf;

										return 302 https://$host$request_uri;
									}

								</cfif>

								## Main Server block
								server {
									<cfif local.thisEnvironment eq "localDevelopment" AND server.machinename neq "local" AND findnocase(".local.membercentral.com",local.arrEl.mainHostname)>
										server_name #local.arrEl.mainHostname# #replace(local.arrEl.mainHostname, "local", "#server.machinename#")#;
									<cfelse>
										server_name #local.arrEl.mainHostname#;
									</cfif>
									<cfif local.arrEl.hasSSL and len(local.arrEl.sslCertFilename) and len(local.arrEl.sslPrivateKeyFilename)>
										<cfif not local.arrEl.internalIpAddress.len()>
											listen 443 ssl http2;
										<cfelse>
											<cfloop index="local.currentIndex" item="local.thisIP" array="#local.arrEl.internalIpAddress#">
												listen #local.thisIP#:443 ssl http2;
											</cfloop>
										</cfif>
										include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/membercentral-ssl.conf;
										ssl_certificate      #variables.nginxPaths[local.thisEnvironment].sslCertFolder##local.arrEl.sslCertFilename#;
										ssl_certificate_key  #variables.nginxPaths[local.thisEnvironment].sslKeysFolder##local.arrEl.sslPrivateKeyFilename#;

									<cfelse>
										<cfif not local.arrEl.internalIpAddress.len()>
											listen 80;
										<cfelse>
											<cfloop index="local.currentIndex" item="local.thisIP" array="#local.arrEl.internalIpAddress#">
												listen #local.thisIP#:80;
											</cfloop>
										</cfif>
									</cfif>

									include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/setdefaults.conf;

									set $mainhostname "#local.arrEl.mainHostname#";
									set $sitecode "#local.siteHostnameInfo[local.thisEnvironment].sitecode#";
									set $orgcode "#local.siteHostnameInfo[local.thisEnvironment].orgcode#";
									set $sitecodepathsegment "#local.siteHostnameInfo[local.thisEnvironment].orgcode#/#local.siteHostnameInfo[local.thisEnvironment].sitecode#";
									set $rootfolder "#variables.nginxPaths[local.thisEnvironment].mcRootFolder#";

									root "$rootfolder";
									index index.cfm;

									## Well-Known Under rootFolder
									location /.well-known {
										## return Apple Pay platform Integrator file for all merchant domains
										location = /.well-known/apple-developer-merchantid-domain-association {
											add_header Content-Type text/plain;
											return 200 '7b2276657273696f6e223a312c227073704964223a2233313341413632343831353638313834423445354637304435414343323342453230313441463130393233333532464638333143334434444338393544423931222c22637265617465644f6e223a313732313430303135303031317d';
										}

										try_files $uri =404;      
									}
									
									include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/membercentral-perserversettings.conf;

									## Site specific aliases
									include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/rewrites/aliases-#local.siteHostnameInfo[local.thisEnvironment].sitecode#.conf*;

									include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/mcdetectedhostname-rewrites.conf;
									include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/common-rewrites.conf;

									## LOG FILES 
									access_log      "#variables.nginxPaths[local.thisEnvironment].logPath##local.thisEnvironment#-luceeLinux-membercentral-access.json" bkenddirect_json;
									error_log       "#variables.nginxPaths[local.thisEnvironment].logPath##local.thisEnvironment#-luceeLinux-membercentral-error.log";

									## Assigned Platform base locations
									include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/baselocations-luceeLinux.conf;

									<cfif local.arrEl.cloudflare_hostnamestatus NEQ 'active' AND local.arrEl.cloudflare_httpownershipverfication_url.len() AND local.arrEl.cloudflare_httpownershipverfication_body.len()>
										<cfset local.thisHTTPOwnershipVerficationURL = local.objURI.init(local.arrEl.cloudflare_httpownershipverfication_url)>
										location "#local.thisHTTPOwnershipVerficationURL.getPath()#" {
											return 200 "#local.arrEl.cloudflare_httpownershipverfication_body#\n";
										}
									</cfif>
									<cfif local.arrEl.cloudflare_sslstatus NEQ 'active' AND local.arrEl.cloudflare_sslvalidation_url.len() AND local.arrEl.cloudflare_sslvalidation_body.len()>
										<cfset local.thisSSLValidationURL = local.objURI.init(local.arrEl.cloudflare_sslvalidation_url)>
										location "#local.thisSSLValidationURL.getPath()#" {
											return 200 "#local.arrEl.cloudflare_sslvalidation_body#\n";
										}
									</cfif>
								}
								<cfif arrayLen(local.arrEl.aliasDomainsWithRedirect)>
									<cfloop array="#local.arrEl.aliasDomainsWithRedirect#" index="local.thisAliasesWithRedirect">
										## Alias Hostname #local.thisAliasesWithRedirect.hostname# for mainhostname #local.arrEl.mainHostname#
										<cfif local.siteHostnameInfo[local.thisEnvironment].enableNGINXConf and local.thisEnvironment eq "production" and not local.thisAliasesWithRedirect.internalIpAddress.len()>
											## Skipped #local.thisAliasesWithRedirect.hostname# since no IP defined or non-private IP entered
										<cfelseif local.siteHostnameInfo[local.thisEnvironment].enableNGINXConf and local.thisEnvironment neq "production" and (local.thisAliasesWithRedirect.internalIPAddress_orig.len() and not local.thisAliasesWithRedirect.internalIpAddress.len())>
											## Skipped #local.thisAliasesWithRedirect.hostname# since no IP defined or non-private IP entered
										<cfelse>
											server {
												server_name #local.thisAliasesWithRedirect.hostname#;
												<cfif not local.thisAliasesWithRedirect.internalIpAddress.len()>
													listen 80;
													<cfif local.thisAliasesWithRedirect.hasSSL and len(local.thisAliasesWithRedirect.sslCertFilename) and len(local.thisAliasesWithRedirect.sslPrivateKeyFilename)>
														listen 443 ssl http2;
													</cfif>
												<cfelse>
													<cfloop index="local.currentIndex" item="local.thisIP" array="#local.thisAliasesWithRedirect.internalIpAddress#">
														listen #local.thisIP#:80;
														<cfif local.thisAliasesWithRedirect.hasSSL and len(local.thisAliasesWithRedirect.sslCertFilename) and len(local.thisAliasesWithRedirect.sslPrivateKeyFilename)>
															listen #local.thisIP#:443 ssl http2;
														</cfif>
													</cfloop>
												</cfif>
		
												<cfif local.thisAliasesWithRedirect.hasSSL and len(local.thisAliasesWithRedirect.sslCertFilename) and len(local.thisAliasesWithRedirect.sslPrivateKeyFilename)>
													include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/membercentral-ssl.conf;
													ssl_certificate      #variables.nginxPaths[local.thisEnvironment].sslCertFolder##local.thisAliasesWithRedirect.sslCertFilename#;
													ssl_certificate_key  #variables.nginxPaths[local.thisEnvironment].sslKeysFolder##local.thisAliasesWithRedirect.sslPrivateKeyFilename#;
												</cfif>
												
												<cfif local.thisAliasesWithRedirect.cloudflare_hostnamestatus NEQ 'active' AND local.thisAliasesWithRedirect.cloudflare_httpownershipverfication_url.len() AND local.thisAliasesWithRedirect.cloudflare_httpownershipverfication_body.len()>
													<cfset local.thisHTTPOwnershipVerficationURL = local.objURI.init(local.thisAliasesWithRedirect.cloudflare_httpownershipverfication_url)>
													location "#local.thisHTTPOwnershipVerficationURL.getPath()#" {
														return 200 "#local.thisAliasesWithRedirect.cloudflare_httpownershipverfication_body#\n";
													}
												</cfif>
												<cfif local.thisAliasesWithRedirect.cloudflare_sslstatus NEQ 'active' AND local.thisAliasesWithRedirect.cloudflare_sslvalidation_url.len() AND local.thisAliasesWithRedirect.cloudflare_sslvalidation_body.len()>
													<cfset local.thisSSLValidationURL = local.objURI.init(local.thisAliasesWithRedirect.cloudflare_sslvalidation_url)>
													location "#local.thisSSLValidationURL.getPath()#" {
														return 200 "#local.thisAliasesWithRedirect.cloudflare_sslvalidation_body#\n";
													}
												</cfif>

												location / {
													return 302 #local.thisAliasesWithRedirect.redirect#;
												}
											}
										</cfif>
									</cfloop>
								</cfif>
							</cfoutput>
						</cfsavecontent>
						<cfset local.definedHostnameConfig = local.definedHostnameConfig & variables.CRLF & variables.CRLF & local.newText>
					</cfloop>
					
					<cfset local.definedHostnameConfig = trim(Replace(Replace(local.definedHostnameConfig,chr(9),'','ALL'),'#variables.CRLF##variables.CRLF#',variables.crlf,'ALL'))>

					<cfif FileExists(local.siteHostnameInfo[local.thisEnvironment].rewritePath)>
						<cffile action="delete" file="#local.siteHostnameInfo[local.thisEnvironment].rewritePath#">
					</cfif>
					<cfif FileExists(local.siteHostnameInfo[local.thisEnvironment].disabledrewritePath)>
						<cffile action="delete" file="#local.siteHostnameInfo[local.thisEnvironment].disabledrewritePath#">
					</cfif>
					<cfif FileExists(local.siteHostnameInfo[local.thisEnvironment].wildcardRewritePath)>
						<cffile action="delete" file="#local.siteHostnameInfo[local.thisEnvironment].wildcardRewritePath#">
					</cfif>
					<cfif local.siteHostnameInfo[local.thisEnvironment].enableNGINXConf>
						<cfset local.outputPath = local.siteHostnameInfo[local.thisEnvironment].rewritePath>
					<cfelse>
						<cfset local.outputPath = local.siteHostnameInfo[local.thisEnvironment].disabledrewritePath>
					</cfif>

					<!--- write out new file --->
					<cffile action="write" file="#local.outputPath#" output="#local.definedHostnameConfig#">
				</cfloop>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="getIPAddressArray" access="private" output="false" returntype="array" hint="remove whitespace from IP Address list, remove duplicates, and filter out any non-private IPs">
		<cfargument name="IPList" type="string" required="true">

		<cfreturn listToArray(listRemoveDuplicates(arguments.IPList.rereplace("\s","","all"))).filter((thisIP) => application.objPlatform.isPrivateIPAddress(thisIP))>
	</cffunction>
	<cffunction name="updateRobotsFile" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="requestNGINXReload" type="boolean" required="false" default="false">
		
		<cfset var local = structNew()>
		
		<cfset updateRobotsFileNGINX(siteID=arguments.siteID)>
		<cfif requestNGINXReload>
			<cfset triggerNGINXReload()>
		</cfif>
	</cffunction>
	<cffunction name="updateRobotsFileNGINX" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.rewritesTemplatesPath = "#application.paths.RAIDRewriteRoot.path#rewrites-templates/">
		<cfset local.commonRobotsFile = "#local.rewritesTemplatesPath#common/sitemaps/robots.txt">
		<cfset local.nl = CreateObject("java", "java.lang.System").getProperty("line.separator")>

		<cftry>
			<!--- get main hostname for all environments for this site --->
			<cfquery name="local.qrySiteMainHostnames" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select s.sitecode, o.orgcode, sh.hostname, e.environmentName, scheme = case when sh.hasssl = 1 then 'https' else 'http' end
				from dbo.sites as s
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.organizations as o on o.orgID = s.orgID
				inner join dbo.siteHostnames as sh on sh.siteID = s.siteID
				inner join dbo.siteEnvironments se on se.siteID = sh.siteID
					and se.mainhostnameID = sh.hostnameID
				inner join dbo.platform_environments e on e.environmentID = se.environmentID
					<cfif application.MCEnvironment neq "localDevelopment">
						and e.environmentName = '#application.MCEnvironment#'
					</cfif>
				where s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteid#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qrySiteMainHostnames.recordcount>
				<cfset local.siteHostnameInfo = {}>

				<cfloop query="local.qrySiteMainHostnames">
					<cfset local.siteHostnameInfo[local.qrySiteMainHostnames.environmentName] = {
						mainhostname = local.qrySiteMainHostnames.scheme & "://" & local.qrySiteMainHostnames.hostname,
						rewriteSiteDirectory = "#variables.nginxRewrites##local.qrySiteMainHostnames.environmentName#/sitemaps/#local.qrySiteMainHostnames.sitecode#",
						rewritePath = "#variables.nginxRewrites##local.qrySiteMainHostnames.environmentName#/sitemaps/#local.qrySiteMainHostnames.sitecode#/robots.txt"
					}>
				</cfloop>

				<!--- look for custom sitemap once per site --->
				<cfset local.customSiteMapFileLocation = "#application.paths.localSiteComponentsRoot.path##UCASE(local.qrySiteMainHostnames.orgcode)#/#UCASE(local.qrySiteMainHostnames.sitecode)#/sitemaps/sitemap.xml">
				<cfset local.includeCustomSiteMapDirective = false>
				<cfif fileExists(local.customSiteMapFileLocation)>
					<cfset local.includeCustomSiteMapDirective = true>
				</cfif>

				<cfloop collection="#local.siteHostnameInfo#" item="local.thisEnvironment">
					<cfif not(directoryExists(local.siteHostnameInfo[local.thisEnvironment].rewriteSiteDirectory))>
						<cfdirectory action="create" directory="#local.siteHostnameInfo[local.thisEnvironment].rewriteSiteDirectory#">
					</cfif>					

					<!--- if env robots template exists, use that. otherwise use the common one --->
					<cfset local.environmentRobotsFile = "#local.rewritesTemplatesPath##local.thisEnvironment#/sitemaps/robots.txt">
					<cfif FileExists(local.environmentRobotsFile)>
						<cffile action="copy" source="#local.environmentRobotsFile#" destination="#local.siteHostnameInfo[local.thisEnvironment].rewritePath#" nameconflict="overwrite">
					<cfelse>
						<cffile action="copy" source="#local.commonRobotsFile#" destination="#local.siteHostnameInfo[local.thisEnvironment].rewritePath#" nameconflict="overwrite">
					</cfif>

					<!--- append the sitemap directive(s) looking for custom sitemaps --->
					<cfset local.sitemapdirective = local.nl & local.nl & "## sitemaps" & local.nl & "Sitemap: #local.siteHostnameInfo[local.thisEnvironment].mainhostname#/sitemap.xml" & local.nl>
					<cfif local.includeCustomSiteMapDirective>
						<cfset local.sitemapdirective &= "Sitemap: #local.siteHostnameInfo[local.thisEnvironment].mainhostname#/customsitemap/sitemap.xml" & local.nl>
					</cfif>
					<cffile action="append" file="#local.siteHostnameInfo[local.thisEnvironment].rewritePath#" output="#local.sitemapdirective#" addnewline="false">
				</cfloop>

			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="updateWilcardHostnamesNGINX" access="private" output="false" returntype="void">
		<cfset var local = structNew()>
		
		<cftry>
			<!--- get sitecode --->
			<cfquery name="local.qryWildcardHostnames" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select whn.wildcardhostName, whn.nginxServerNameRegex, whn.filenamePrefix, whn.internalIpAddress, whn.hasSSL, 
					whn.sslCertFilename, whn.sslPrivateKeyFilename, e.environmentName, e.environmentID
				from dbo.platform_wildcardHostnames whn
				inner join dbo.platform_environments e on e.environmentID = whn.environmentID
					<cfif application.MCEnvironment neq "localDevelopment">
						and e.environmentName = '#application.MCEnvironment#'
					</cfif>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryWildcardHostnames.recordcount>
				<cfloop query="local.qryWildcardHostnames">
					<cfset local.newText = "">
					<cfset local.enableNGINXConf = 1>
					<cfset local.oldrewritePath = "#variables.nginxRewrites##local.qryWildcardHostnames.environmentname#/wildcardsites-enabled/wildcard-#local.qryWildcardHostnames.wildcardhostName#.conf">
					<cfset local.rewritePath = "#variables.nginxRewrites##local.qryWildcardHostnames.environmentname#/wildcardsites-enabled/wildcard-#local.qryWildcardHostnames.filenamePrefix#-#local.qryWildcardHostnames.wildcardhostName#.conf">
					<cfset local.olddisabledrewritePath = "#variables.nginxRewrites##local.qryWildcardHostnames.environmentname#/wildcardsites-disabled/wildcard-#local.qryWildcardHostnames.wildcardhostName#.conf">
					<cfset local.disabledrewritePath = "#variables.nginxRewrites##local.qryWildcardHostnames.environmentname#/wildcardsites-disabled/wildcard-#local.qryWildcardHostnames.filenamePrefix#-#local.qryWildcardHostnames.wildcardhostName#.conf">
					<cfset local.wildcardIPAddresses = getIPAddressArray(local.qryWildcardHostnames.internalIpAddress)>

					<!--- Disable Conf is no ipaddress found on mainhostname--->
					<cfif local.qryWildcardHostnames.environmentname eq "production" and not local.wildcardIPAddresses.len()>
						<cfset local.enableNGINXConf = 0>
					<cfelseif local.qryWildcardHostnames.environmentname neq "production" and (len(local.qryWildcardHostnames.internalIpAddress) and not local.wildcardIPAddresses.len())>
						<cfset local.enableNGINXConf = 0>
					</cfif>

					<cfsavecontent variable="local.newText">
						<cfoutput>
							<cfif local.qryWildcardHostnames.hasSSL and len(local.qryWildcardHostnames.sslCertFilename) and len(local.qryWildcardHostnames.sslPrivateKeyFilename)>
								## Main Server block nonssl redirect for Wildcard hostname #local.qryWildcardHostnames.wildcardhostName#
								server {
									server_name ~#local.qryWildcardHostnames.nginxServerNameRegex#;
									<cfif not local.wildcardIPAddresses.len()>
										listen 80;
									<cfelse>
										<cfloop index="local.currentIndex" item="local.thisIP" array="#local.wildcardIPAddresses#">
											listen #local.thisIP#:80;
										</cfloop>
									</cfif>
									include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/setdefaults.conf;
									include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/parseEnvironmentHostnames.conf;

									return 302 https://$host$request_uri;
								}

							</cfif>
							## Main Server block for Wildcard hostname #local.qryWildcardHostnames.wildcardhostName#
							server {
								server_name ~#local.qryWildcardHostnames.nginxServerNameRegex#;
								<cfif local.qryWildcardHostnames.hasSSL and len(local.qryWildcardHostnames.sslCertFilename) and len(local.qryWildcardHostnames.sslPrivateKeyFilename)>
									<cfif not local.wildcardIPAddresses.len()>
										listen 443 ssl http2;
									<cfelse>
										<cfloop index="local.currentIndex" item="local.thisIP" array="#local.wildcardIPAddresses#">
											listen #local.thisIP#:443 ssl http2;
										</cfloop>
									</cfif>
									include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/membercentral-ssl.conf;
									ssl_certificate      #variables.nginxPaths[local.qryWildcardHostnames.environmentname].sslCertFolder##local.qryWildcardHostnames.sslCertFilename#;
									ssl_certificate_key  #variables.nginxPaths[local.qryWildcardHostnames.environmentname].sslKeysFolder##local.qryWildcardHostnames.sslPrivateKeyFilename#;
								<cfelse>
									listen <cfif len(local.qryWildcardHostnames.internalIpAddress)>#local.qryWildcardHostnames.internalIpAddress#:</cfif>80;
								</cfif>
			
								include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/setdefaults.conf;
								include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/parseEnvironmentHostnames.conf;
								set $rootfolder "#variables.nginxPaths[local.qryWildcardHostnames.environmentname].mcRootFolder#";

								root "$rootfolder";
								index index.cfm;

								## Well-Known Under rootFolder
								location /.well-known {
									## return Apple Pay platform Integrator file for all merchant domains
									location = /.well-known/apple-developer-merchantid-domain-association {
										add_header Content-Type text/plain;
										return 200 '7b2276657273696f6e223a312c227073704964223a2233313341413632343831353638313834423445354637304435414343323342453230313441463130393233333532464638333143334434444338393544423931222c22637265617465644f6e223a313732313430303135303031317d';
									}

									try_files $uri =404;      
								}

								include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/membercentral-perserversettings.conf;
								include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/mcdetectedhostname-rewrites.conf;
								include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/common-rewrites.conf;

								## LOG FILES 
								access_log      "#variables.nginxPaths[local.qryWildcardHostnames.environmentname].logPath##local.qryWildcardHostnames.environmentname#-luceeLinux-membercentral-access.json" bkenddirect_json;
								error_log       "#variables.nginxPaths[local.qryWildcardHostnames.environmentname].logPath##local.qryWildcardHostnames.environmentname#-luceeLinux-membercentral-error.log";

								## Assigned Platform base locations
								include #variables.nginxPaths[local.qryWildcardHostnames.environmentname].nginxRewrites##local.qryWildcardHostnames.environmentname#/mc-snippets/baselocations-luceeLinux.conf;

								add_header X-Content-Type-Options "nosniff";
								add_header X-Frame-Options "SAMEORIGIN";
								add_header X-Permitted-Cross-Domain-Policies "master-only";
								add_header X-Robots-Tag "noindex, nofollow";
							}
						</cfoutput>
					</cfsavecontent>
					<cfset local.newText = trim(Replace(Replace(local.newText,chr(9),'','ALL'),'#variables.CRLF##variables.CRLF#',variables.crlf,'ALL'))>
					<cfif FileExists(local.rewritePath)>
						<cffile action="delete" file="#local.rewritePath#">
					</cfif>
					<cfif FileExists(local.disabledrewritePath)>
						<cffile action="delete" file="#local.disabledrewritePath#">
					</cfif>
					<cfif FileExists(local.oldrewritePath)>
						<cffile action="delete" file="#local.oldrewritePath#">
					</cfif>
					<cfif FileExists(local.olddisabledrewritePath)>
						<cffile action="delete" file="#local.olddisabledrewritePath#">
					</cfif>


					<cfif local.enableNGINXConf>
						<cfset local.outputPath = local.rewritePath>
					<cfelse>
						<cfset local.outputPath = local.disabledrewritePath>
					</cfif>
					<!--- write out new file --->
					<cffile action="write" file="#local.outputPath#" output="#local.newText#">

				</cfloop>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>
	</cffunction>


	<cffunction name="updateOldOrgCodes" access="public" output="false" returntype="void">
		<cfset var local = structNew()>
		<cfset local.rewritePath = "#application.paths.RAIDRewriteRoot.path#rewrites/rewrites-oldorgcodes.txt">

		<cftry>
			<cfquery name="local.qryOrgs" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select [state] as orgCode
				from dbo.depoTLA 
				order by orgCode

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				set nocount off;
			</cfquery>
			<cfset local.newText = "RewriteRule ^/(#ValueList(local.qryOrgs.orgcode,'|')#)/(.*) /$2 [NC]">

			<!--- write out new file --->
			<cffile action="write" file="#local.rewritePath#" output="#local.newText#">
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="updateCommonFiles" access="private" output="false" returntype="void">
		<cfset var local = structNew()>
		<cftry>
			<cfset local.environmentList = "localDevelopment,test,newtest,development,newdevelopment,beta,newbeta,production">
			<cfset local.rewritesTemplatesPath = "#application.paths.RAIDRewriteRoot.path#rewrites-templates/">

			<cfquery name="local.qrySites" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select s.sitecode, o.orgcode
				FROM dbo.sites as s
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1 
				inner join dbo.organizations o on o.orgID = s.orgID
				order by orgcode, sitecode;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfsavecontent variable="local.orgcodeLookupMap">
				<cfoutput>
					## Sitecode -> orgcode lookup map
				
					map $sitecode $sitecode {
						default '';
					}
					map $sitecode $detectedOrgCode {
						default "";
						<cfloop query="local.qrySites">
						"#ucase(local.qrySites.sitecode)#" "#ucase(local.qrySites.orgcode)#";
						</cfloop>
					}
				</cfoutput>
			</cfsavecontent>
			<cfset local.orgcodeLookupMap = trim(Replace(Replace(local.orgcodeLookupMap,chr(9),'','ALL'),'#variables.CRLF##variables.CRLF#',variables.crlf,'ALL'))>
			<cfloop list="#local.environmentList#" index="local.thisEnvironment">

				<cfquery name="local.qryMainHostnames" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @environmentID int, @environmentName varchar(50) = <cfqueryparam value="#local.thisEnvironment#" cfsqltype="CF_SQL_VARCHAR">;
					select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

					select s.sitecode, sh.hostname
					FROM dbo.sites as s 
					inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
					inner join dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
					inner join dbo.siteHostnames sh on sh.environmentID = se.environmentID
						and sh.siteID = se.siteID
						and sh.hostnameID = se.mainHostnameID;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfsavecontent variable="local.thisEnvironmentSiteMaps">
					<cfoutput>
						#local.orgcodeLookupMap#

						## Sitecode -> MainHostname lookup map
					
						map $sworgcode $detectedMainHostName {
							default "";
							<cfloop query="local.qryMainHostnames">
							~(?i)^#ucase(local.qryMainHostnames.sitecode)#$ "http://#local.qryMainHostnames.hostname#";
							</cfloop>
						}
					</cfoutput>
				</cfsavecontent>
				<cfset application.objCommon.directoryCopy("#local.rewritesTemplatesPath#common/", "#variables.nginxRewrites##local.thisEnvironment#/", true)>
				<cfset application.objCommon.directoryCopy("#local.rewritesTemplatesPath##local.thisEnvironment#/", "#variables.nginxRewrites##local.thisEnvironment#/", true)>
				<cffile action="write" file="#variables.nginxRewrites##local.thisEnvironment#/mc-snippets/orgcodemap.conf" output="#local.thisEnvironmentSiteMaps#" nameconflict="overwrite">

				<cfsavecontent variable="local.memberCentralConfig">
					<cfoutput>
						## main config file for inclusion by NGINX

						include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/membercentral-globalsettings.conf;
						include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/mc-snippets/orgcodemap.conf;
						include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/sites-enabled/hostnames-*.conf;
						include #variables.nginxPaths[local.thisEnvironment].nginxRewrites##local.thisEnvironment#/wildcardsites-enabled/wildcard-*.conf;
					</cfoutput>
				</cfsavecontent>
				<cfset local.memberCentralConfig = trim(Replace(Replace(local.memberCentralConfig,chr(9),'','ALL'),'#variables.CRLF##variables.CRLF#',variables.crlf,'ALL'))>
				<cfset local.memberCentralConfigFilePath = variables.nginxRewrites & local.thisEnvironment & "/memberCentralConfig.conf">
				<cffile action="write" file="#local.memberCentralConfigFilePath#" output="#local.memberCentralConfig#" nameconflict="overwrite">
			</cfloop>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

	</cffunction>
</cfcomponent>