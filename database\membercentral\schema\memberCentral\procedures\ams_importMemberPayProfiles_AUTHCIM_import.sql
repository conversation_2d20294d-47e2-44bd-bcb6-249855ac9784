ALTER PROC dbo.ams_importMemberPayProfiles_AUTHCIM_import
@siteID int,
@memberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @itemGroupUID uniqueidentifier = NEWID();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='importAuthCIM', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	BEGIN TRY
		INSERT INTO platformQueue.dbo.queue_importAuthCIM (itemGroupUID, recordedByMemberID, siteID, profileID,
			memberID, CardNumber, Expiration, FirstNameOnCard, LastNameOnCard, BillingAddress, BillingCity, BillingState,
			BillingZIP, BillingCountry, NickName, statusID, dateAdded, dateUpdated)
		select @itemGroupUID, @memberID, @siteID, MCPayProfileID, MCMemberID, CardNumber, 
			right('00' + ExpirationMonth,2) + '/' + right('0000' + ExpirationYear,2), FirstNameOnCard, LastNameOnCard, 
			Billing<PERSON>ddress, BillingCity, BillingState, BillingZIP, BillingCountry, NickName, @statusReady, getdate(), getdate()
		from #mcAuthCIMImport;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Process Import Authorize CIM Queue', @engine='MCLuceeLinux';
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAUTHCIMProfileErrors (msg)
		VALUES ('Unable to queue profiles for import.');

		INSERT INTO #tblAUTHCIMProfileErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblAUTHCIMProfileErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
