ALTER PROC dbo.queue_goDaddyOrders_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @statusReady int;
	EXEC dbo.queue_getStatusIDbyType @queueType='goDaddyOrders', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	SELECT TOP (@batchSize) itemID, orderID, dateAdded, dateUpdated
	FROM dbo.queue_goDaddyOrders
	WHERE statusID = @statusReady
	ORDER BY itemID;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
