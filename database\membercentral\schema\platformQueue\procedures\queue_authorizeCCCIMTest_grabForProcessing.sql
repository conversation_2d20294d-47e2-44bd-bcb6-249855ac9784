ALTER PROC dbo.queue_authorizeCCCIMTest_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = GETDATE();
	EXEC dbo.queue_getQueueTypeID @queueType='authorizeCCCIMTest', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- Dequeue in order of nextAttemptDate (instead of dateAdded), ensuring only items where nextAttemptDate < GETDATE() are selected
	UPDATE qid WITH (UPDLOCK, READPAST)
    SET qid.statusID = @statusGrabbed,
        qid.dateUpdated = @nowDate
    OUTPUT inserted.itemID
    INTO #tmpQueueItems
    FROM dbo.queue_authorizeCCCIMTest AS qid
    INNER JOIN (
        SELECT TOP (@batchSize) itemID 
        FROM dbo.queue_authorizeCCCIMTest
        WHERE statusID = @statusReady 
		AND nextAttemptDate < @nowDate
        ORDER BY nextAttemptDate, itemID
    ) AS batch ON batch.itemID = qid.itemID
    WHERE qid.statusID = @statusReady;
	
	SELECT qid.itemID, qid.profileID, qid.recordedByMemberID, qid.attemptNum, qid.nextAttemptDate
    FROM #tmpQueueItems AS tmp
    INNER JOIN dbo.queue_authorizeCCCIMTest AS qid ON qid.itemID = tmp.itemID
    ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
