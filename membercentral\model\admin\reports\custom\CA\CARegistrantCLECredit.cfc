﻿<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf' ]>
	<cfset variables.AllowScheduling = false>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.strEventWidgetData = { title='Define Event Filter', 
				description='Filter the events appearing on this report using the defined criteria below.',
				gridext="#this.siteResourceID#_1", gridClassList='mb-5 stepDIV', 
				initGridOnLoad=true, controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
				filterMode=1, showIcons=1 }>
			<!--- hide icons if unable to change report --->
			<cfif NOT hasReportEditRights(event=arguments.event)>
				<cfset local.strEventWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strEventWidget = createObject("component","model.admin.common.modules.eventWidget.eventWidget").renderWidget(strWidgetData=local.strEventWidgetData)>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				#local.strEventWidget.js#
				<script language="javascript">
					function forceEvent() {
						var feResult = false;
						var forceEventResult = function(s) {
							if (s.success && s.success.toLowerCase() == 'true' && s.eventcount > 0) feResult=true; 
							else { rptShowAlert('This report requires you to select one or more events in the Event Filter.'); feResult=false; }
						};
						var objParams = { rptid:#arguments.event.getValue('qryReportInfo').reportID#, csrid:#this.siteResourceID# };
						TS_AJX_SYNC('EVWIDGET','checkEventFilterLength',objParams,forceEventResult,forceEventResult,5000,forceEventResult);
						if (feResult) {
							if ($('##reportDefs ##reportAction').val() == 'pdf') {
								$('##reportDefs ##reportAction').val('outputToScreen');
							}
						}
						return feResult;
					}
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
			
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.frmTitle = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmtitle/text())") />
					<cfset local.frmMembershipBebefitsDesc = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmmembershipbebefitsdesc/text())") />
					<cfset local.frmRecordsInstructionsTitle = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrecordsinstructionstitle/text())") />
					<cfset local.frmRecordsInstructionsDesc = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrecordsinstructionsdesc/text())") />
					<cfset local.frmRollupCredits = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrollupcredits/text())") />
					
					<cfif not arraylen(xmlparse(arguments.event.getValue('qryReportInfo').otherXML).report.extra.xmlChildren)>
						<cfset local.frmTitle = "Certificate of Attendance for California MCLE" />
						<cfset local.frmMembershipBebefitsDesc = "As one of your membership benefits, we are providing the list of your MCLE credit earned through CAOC. The state bar<br>reporting period for Group ___ is ____. If you are in need of more CLE credits before the reporting deadline, please<br>go to the CAOC homepage <i>www.caoc.org</i> and click on <i>Education > Online MCLE SeminarWeb</i> to view our selection of online or<br>OnDemand web seminars. To view further resources CAOC has to offer, including upcoming events, seminars and networking<br>opportunities, please go to www.caoc.org." />
						<cfset local.frmRecordsInstructionsTitle = "KEEP THIS FOR YOU RECORDS" />
						<cfset local.frmRecordsInstructionsDesc = "Please review your copy of the MCLE Certificates of Attendance for the following seminars to ensure the credits coincide.<br>In the event of an audit, these will be the credits CAOC will be reporting to the State Bar. If you have any discrepancies,<br>please contact Wendy Murphy, Education Manager, at 916-442-6902 ext. 110 or e-mail <EMAIL>.<br>Thank you for supporting CAOC and for choosing us to be your MCLE provider. <br>NOTE TO SPEAKERS: For information on how to calculate your speaking credit go to the Sate Bar's website<br>http://rules.calbar.ca.gov/Portals/10/documents/Rules_Div4-MCLE.pdf and refer to Rule 2.81." />
						<cfset local.frmRollupCredits = 1 />
					</cfif>
				
					<cfform name="frmReport"  id="frmReport" method="post">
						<cfinput type="hidden" name="reportAction"  id="reportAction" value="" />
						#local.strEventWidget.html#
						#showStepMemberCriteria(event=arguments.event, title="Optionally Define Member Filter", desc="Optionally filter the members appearing on this report using the defined criteria below.")#
						
						<div class="mb-5 stepDIV divFullReportViewOnly">
							<h5>Define Extra Options</h5>
							<div class="row mt-2">
								<div class="col-sm-12">
									<div class="form-group row">
										<label for="frmTitle" class="col-md-4 col-sm-12 col-form-label">Title</label>
										<div class="col-md-8 col-sm-12">
											<input type="text" name="frmTitle" id="frmTitle" value="#local.frmTitle#" class="form-control form-control-sm" />
										</div>
									</div>
									<div class="form-group row">
										<label for="frmMembershipBebefitsDesc" class="col-md-4 col-sm-12 col-form-label">Membership Benefits</label>
										<div class="col-md-8 col-sm-12">
											<textarea name="frmMembershipBebefitsDesc" id="frmMembershipBebefitsDesc" rows="4" class="form-control form-control-sm">#local.frmMembershipBebefitsDesc#</textarea>
										</div>
									</div>
									<div class="form-group row mt-2">
										<label for="frmRecordsInstructionsTitle" class="col-md-4 col-sm-12 col-form-label">Instructions Title</label>
										<div class="col-md-8 col-sm-12">
											<input type="text" name="frmRecordsInstructionsTitle" id="frmRecordsInstructionsTitle" value="#local.frmRecordsInstructionsTitle#" class="form-control form-control-sm" />
										</div>
									</div>
									<div class="form-group row">
										<label for="frmRecordsInstructionsDesc" class="col-md-4 col-sm-12 col-form-label">Instructions</label>
										<div class="col-md-8 col-sm-12">
											<textarea name="frmRecordsInstructionsDesc" id="frmRecordsInstructionsDesc" rows="4" class="form-control form-control-sm">#local.frmRecordsInstructionsDesc#</textarea>
										</div>
									</div>
									<div class="form-group row mt-1">
										<label for="frmRollupCredits" class="col-md-4 col-sm-12 col-form-label">Roll up credits into main Event</label>
										<div class="col-md-8 col-sm-12">
											<input type="checkbox" name="frmRollupCredits" id="frmRollupCredits" value="1" <cfif val(local.frmRollupCredits)>checked</cfif> />
										</div>
									</div>
								</div>
							</div>
						</div>
						#showButtonBar(event=arguments.event,validateFunction='forceEvent')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>
	
	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfset local.frmRollupCredits = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrollupcredits/text())")>

			<cfset local.qryStruct = structNew()>
			<cfif local.frmRollupCredits is not 1>
				<cfset local.qryStruct = getAllEvents(strSQLPrep=local.strSQLPrep, orgID=local.mc_siteInfo.orgID)>
			<cfelse>
				<cfset local.qryStruct = getRolledUpEvents(strSQLPrep=local.strSQLPrep, orgID=local.mc_siteInfo.orgID)>
			</cfif>
			<cfset local.qryData = local.qryStruct.qryData>
			<cfset local.qryDataResult = local.qryStruct.qryDataResult>			
	
			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
					<table class="table table-sm table-borderless">
					<thead>
					<tr>
						<th class="text-left">Member / Event</th>
						<th class="text-left">Credits Awarded</th>
					</tr>
					</thead>
					<tbody>
					</cfoutput>

					<cfoutput query="local.qryData" group="memberid">
						<tr>
							<td colspan="2" class="align-top pt-2">
								<cfif arguments.reportAction eq "screen">
									<a href="#local.memberLink#&memberid=#local.qryData.memberid#" target="_blank">#local.qryData.mc_combinedName#</a><br/>
								<cfelse>
									<b>#local.qryData.mc_combinedName#</b><br/>
								</cfif>
								<cfif len(local.qryData.company)>#local.qryData.company#<br/></cfif>
							</td>
						</tr>
						<cfoutput group="eventid">
							<tr>
								<td class="align-top pl-3 pr-2">#dateformat(local.qryData.startTime,'m/d/yyyy')# - #local.qryData.eventTitle#</td>
								<td class="align-top" nowrap>
									<cfoutput group="creditType">#local.qryData.creditValueAwarded# &nbsp; #local.qryData.creditType#<br/></cfoutput>
								</td>
							</tr>
						</cfoutput>
					</cfoutput>
					<cfoutput>
					</tbody>
					</table>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.qryData", strQryResult=local.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
		
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>	
	
	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML, 
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<!--- Use the queueing mechanism --->
			<cfset local.qryReport = qryFullReport(strSQLPrep=local.strSQLPrep, qryReportInfo=arguments.qryReportInfo, orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID)>		

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
					<cfif local.qryReport.qryData.memberCount>
						<table class="table table-sm table-borderless mb-1">
						<tr>
							<td class="align-top" width="100%"><h4>CLE Credit Report Has Been Scheduled</h4></td>
							<td class="align-top text-center" nowrap><i>Report generated<br/>#dateformat(now(),"m/d/yyyy")# #timeformat(now(),"h:mm tt")# Central</i></td>
						</tr>
						</table>
						<div>
							The CLE Credit report containing #local.qryReport.qryData.memberCount# member(s) has been scheduled and will begin shortly.<br/>
							You will be sent an e-mail with the results with the report.<br/><br/>
							Please wait until you receive the emailed report before contacting MemberCentral with any questions.
						</div>
					<cfelse>
						<cfset local.strReturn.isReportEmpty = true>
						<table class="table table-sm table-borderless mb-1">
						<tr>
							<td class="align-top" width="100%"><h4>No Matching Members Found</h4></td>
							<td class="align-top text-center" nowrap><i>Report generated<br/>#dateformat(now(),"m/d/yyyy")# #timeformat(now(),"h:mm tt")# Central</i></td>
						</tr>
						</table>
						<div>
							There are no matches for the combination of Events and Members filters that you selected .<br/>
							Please adjust your report options. You can use the Screen Report View to see spot check the member list after you made adjustments.<br/><br/>
						</div>
					</cfif>
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="qryFullReport" access="private" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qryData" result="local.returnStruct.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				declare @orgID int, @siteID int, @rptID int, @recordedByMemberID int;

				set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteid#">;
				set @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">;
				set @rptID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.qryReportInfo.reportID#">;
				set @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;

				-- put nodes from /report/extra xml into temp table
				declare @settings TABLE (name varchar(100), value varchar(max));
				
				insert into @settings (name, value)
				select 
					fs.value('fn:local-name(.)','varchar(100)') AS name,
					fs.value('text()[1]','varchar(max)') AS value
				from dbo.rpt_SavedReports as sr
				CROSS APPLY otherXML.nodes('/report/extra/*') as F(fs)
				where sr.reportID = @rptID;		
				
				declare  @authorityID int;
				declare @tblSubEvent TABLE (eventID int, parentEventID int, memberID int);
				declare @qryMembersEvents table (memberID int, eventID int, registrantID int);
				
				select @authorityID = authorityID from dbo.crd_authorities where authorityName = 'State Bar Of California';
				
				#qryGetFullReportMemberEventsSQLString(strSQLPrep=arguments.strSQLPrep, qryReportInfo=arguments.qryReportInfo, orgID=arguments.orgID)#
				
				-- create UID for each member
				declare @tblMembers TABLE (memberID int PRIMARY KEY, uid uniqueIdentifier NOT NULL default newid());
				
				insert into @tblMembers (memberID)
				select distinct memberID
				from @qryMembersEvents;
				
				declare @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int, @jobUID uniqueIdentifier = NEWID();
				EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='caCLECredits', @queueTypeID=@queueTypeID OUTPUT;
				EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
				EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

				insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID, dateAdded, dateUpdated)
				select tm.uid as itemUID, @insertingQueueStatusID, getdate(), getdate()
				from @tblMembers tm;
					
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueInteger)
				select @jobUID, @recordedByMemberID, @siteID, tm.uid as itemUID, dc.columnID, tm.memberID as dataKey, me.eventID as columnValueInteger
				from @qryMembersEvents me
				inner join @tblMembers tm on tm.memberID = me.memberID
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.queueTypeID = @queueTypeID
					and dc.columnname = 'registrantID';
						
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueString)
				select @jobUID, @recordedByMemberID, @siteID, tm.uid as itemUID, dc.columnID, s.name as dataKey, s.value as columnValueString
				from platformQueue.dbo.tblQueueTypeDataColumns as dc
				inner join @settings as s on s.name in ('frmtitle','frmrecordsinstructionstitle')
				cross join @tblMembers as tm
				where dc.queueTypeID = @queueTypeID
				and dc.columnname = 'configParam';
					
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueBit)
				select @jobUID, @recordedByMemberID, @siteID, tm.uid as itemUID, dc.columnID, s.name as dataKey, s.value as columnValueString
				from platformQueue.dbo.tblQueueTypeDataColumns as dc
				inner join @settings as s on s.name in ('frmrollupcredits')
				cross join @tblMembers as tm
				where dc.queueTypeID = @queueTypeID
				and dc.columnname = 'configParam';

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueText)
				select @jobUID, @recordedByMemberID, @siteID, tm.uid as itemUID, dc.columnID, s.name as dataKey, s.value as columnValueText
				from platformQueue.dbo.tblQueueTypeDataColumns as dc
				inner join @settings as s on s.name in ('frmmembershipbebefitsdesc','frmrecordsinstructionsdesc')
				cross join @tblMembers as tm
				where dc.queueTypeID = @queueTypeID
				and dc.columnname = 'configText';

				-- resume task
				EXEC dbo.sched_resumeTask @name='CLE Report Queue', @engine='BERLinux';
					
				update qi 
				set qi.queueStatusID = @readyQueueStatusID
				from platformQueue.dbo.tblQueueItems as qi
				inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
					and qid.itemGroupUID = @jobUID 
					and queueStatusID = @insertingQueueStatusID;

				select count(*) as memberCount
				from @tblMembers;

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
	
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.returnStruct>
	</cffunction>		

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);
		
		local.strFields = structNew();
		local.strFields.frmtitle = { label="Title", value=arguments.event.getValue('frmtitle','') };
		local.strFields.frmmembershipbebefitsdesc = { label="Membership Benefits", value=arguments.event.getValue('frmmembershipbebefitsdesc','') };
		local.strFields.frmrecordsinstructionstitle = { label="Instructions Title", value=arguments.event.getValue('frmrecordsinstructionstitle','') };
		local.strFields.frmrecordsinstructionsdesc = { label="Instructions", value=arguments.event.getValue('frmrecordsinstructionsdesc','') };
		local.strFields.frmrollupCredits = { label="Roll up credits into main Event", value=arguments.event.getValue('frmrollupcredits','0') };
		local.strFields.eidList = { label="Events List", value=XMLSearch(local.otherXML,'string(/report/extra/eidlist/text())') };

		reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
		return returnAppStruct('','echo');
		</cfscript>
	</cffunction>
	
	<cffunction name="qryGetFullReportMemberEventsSQLString" access="private" output="false" returntype="String">	
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.qryStruct = structNew()>

		<cfset local.frmRollupCredits = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrollupcredits/text())")>

		<cfsavecontent variable="local.sqlString">
			<cfoutput>

			<cfif not local.frmRollupCredits>				
				insert into @tblSubEvent
				select distinct subEvent.eventID, subEvent.parentEventID, m.memberID
				from dbo.ev_events e
				inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID
				inner join dbo.ev_events as e2 on e2.eventID = subEvent.eventID  
					and e2.status = 'A'
				inner join dbo.ev_registration as rn on rn.eventID = e2.eventID  and rn.siteID = e2.siteID
				inner join dbo.ev_registrants as r on r.registrationID = rn.registrationID 
					and r.status = 'A'
				inner join dbo.ams_members as mReg on mReg.memberID = r.memberID
				inner join dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.isProtected = 0
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>				
					
				insert into @qryMembersEvents (memberID, eventID, registrantID)
				select memberID, eventID, registrantID
				from (
					select m.memberID, e.eventID, r.registrantID
					from dbo.ev_registrants as r
					inner join dbo.ev_registration as rn on rn.registrationID = r.registrationID and r.recordedOnSiteID = rn.siteID
					inner join dbo.ev_events as e on e.eventID = rn.eventID and e.siteID = rn.siteID
					inner join ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
					inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
					inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
					inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
					inner join dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID and cat.authorityID = @authorityID
					inner join dbo.ams_members as mReg on mReg.memberID = r.memberID
					inner join dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.isProtected = 0
					<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					where m.orgID = #arguments.orgID#
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
					and e.status = 'A'
					and rn.status = 'A'
					and r.status = 'A'
					and r.attended = 1
					and rc.creditAwarded = 1
						UNION ALL
					select m.memberID, e.eventID, r.registrantID
					from dbo.ev_registrants as r
					inner join dbo.ev_registration as rn on rn.registrationID = r.registrationID and r.recordedOnSiteID = rn.siteID
					inner join dbo.ev_events as e on e.eventID = rn.eventID and e.siteID = rn.siteID
					inner join @tblSubEvent subEventTbl on subEventTbl.eventID = e.eventID	
						and subEventTbl.memberID = r.memberID				
					inner join ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
					inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
					inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
					inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
					inner join dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID and cat.authorityID = @authorityID
					inner join dbo.ams_members as mReg on mReg.memberID = r.memberID
					inner join dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.isProtected = 0 
					where m.orgID = #arguments.orgID#
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
					and e.status = 'A'
					and rn.status = 'A'
					and r.status = 'A'
					and r.attended = 1
					and rc.creditAwarded = 1
				) tmp
				group by memberID, eventID, registrantID
				order by memberID, eventID, registrantID;
			<cfelse>
				declare @tblCreditsAwarded TABLE (eventID int, memberID int, creditValue decimal(6,2), creditValueAwarded decimal(6,2));
				
				delete from @tblE where eventID in (select eventID from dbo.ev_subEvents);
				
				insert into @tblSubEvent
				select distinct subEvent.eventID, subEvent.parentEventID, m.memberID
				from dbo.ev_events e
				inner join dbo.ev_subEvents as subEvent on subEvent.eventID = e.eventID
				inner join dbo.ev_events as e2 on e2.eventID = subEvent.eventID  
					and e2.status = 'A'
				inner join dbo.ev_registration as rn on rn.eventID = e2.eventID  and rn.siteID = e2.siteID
				inner join dbo.ev_registrants as r on r.registrationID = rn.registrationID 
					and r.status <> 'D'
				inner join dbo.ams_members as mReg on mReg.memberID = r.memberID
				inner join dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.isProtected = 0 
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>		
					union
				select distinct subEvent.eventID, subEvent.parentEventID, m.memberID
				from dbo.ev_events e
				inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID
				inner join dbo.ev_events as e2 on e2.eventID = subEvent.eventID  
					and e2.status = 'A'
				inner join dbo.ev_registration as rn on rn.eventID = e2.eventID and rn.siteID = e2.siteID
				inner join dbo.ev_registrants as r on r.registrationID = rn.registrationID 
					and r.status <> 'D'
				inner join dbo.ams_members as mReg on mReg.memberID = r.memberID
				inner join dbo.ams_members as m on m.memberID = mReg.activeMemberID	 and m.isProtected = 0 
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>										
					
				insert into @qryMembersEvents (memberID, eventID, registrantID)
				select memberID, eventID, registrantID						
				from (
					select m.memberID,e.eventID, r.registrantID
					from dbo.ev_registrants as r
					inner join dbo.ev_registration as rn on rn.registrationID = r.registrationID and r.recordedOnSiteID = rn.siteID
					inner join dbo.ev_events as e on e.eventID = rn.eventID and e.siteID = rn.siteID
					inner join ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
					inner join dbo.ams_members as mReg on mReg.memberID = r.memberID
					inner join dbo.ams_members as m on m.memberID = mReg.activeMemberID  and m.isProtected = 0
					left outer join dbo.ev_subEvents as subEvent on subEvent.eventID = e.eventID					
					<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA) and findNoCase("##tmpVGRMembers", arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>
						inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID
					</cfif>
					outer apply  (		
						select 
							at.typeID,
							0 as subEventID, 
							isNull(cat.ovTypeName, at.typeName) as creditType, 
							isNull(ot.creditValue, 0) as creditValue,
							isNull(req.creditAwarded, 0) as creditAwarded,
							isNull(req.creditValueAwarded, 0) as creditValueAwarded
						from
							membercentral.dbo.crd_sponsors cs
							inner join membercentral.dbo.crd_authoritySponsors cas on
								cas.sponsorID = cs.sponsorID
								and cs.orgID = #arguments.orgID#
							inner join membercentral.dbo.crd_authorities ca on
								ca.authorityID = cas.authorityID
								and ca.authorityID = @authorityID
							inner join membercentral.dbo.crd_authoritySponsorTypes cat on
								cat.asid = cas.asid
							inner join membercentral.dbo.crd_authorityTypes at on
								at.typeID = cat.typeID	
							inner join membercentral.dbo.crd_offerings o on
								o.asid = cat.asid 
								and o.eventID = e.eventID
							inner join membercentral.dbo.crd_offeringTypes ot on 
								ot.offeringID = o.offeringID and ot.ASTID = cat.ASTID
								inner join membercentral.dbo.crd_requests req on
									req.offeringTypeID = ot.offeringTypeID
									and req.creditAwarded = 1
								inner join membercentral.dbo.ev_registrants r on
									r.registrantID = req.registrantID 
									and r.status = 'A'
								inner join membercentral.dbo.ams_members m2 on
									m2.memberID = r.memberID
									and m2.memberID = m2.activeMemberID
									and m2.status <> 'D'
									and m2.orgID = #arguments.orgID#
									and m2.memberID = m.memberID
									and m2.isProtected = 0
						group by 
							at.typeID, isNull(cat.ovTypeName, at.typeName), isNull(ot.creditValue, 0),
							isNull(req.creditAwarded, 0), isNull(req.creditValueAwarded, 0), isNull(req.registrantID, 0)
						union all
						select 
							at.typeID,
							se.eventID as subEventID, 
							isNull(cat.ovTypeName, at.typeName) as creditType, 
							isNull(ot.creditValue, 0) as creditValue,
							isNull(req.creditAwarded, 0) as creditAwarded,
							isNull(req.creditValueAwarded, 0) as creditValueAwarded
						from
							membercentral.dbo.crd_sponsors cs
							inner join membercentral.dbo.crd_authoritySponsors cas on
								cas.sponsorID = cs.sponsorID
								and cs.orgID = #arguments.orgID#
							inner join membercentral.dbo.crd_authorities ca on
								ca.authorityID = cas.authorityID
								and ca.authorityID = @authorityID
							inner join membercentral.dbo.crd_authoritySponsorTypes cat on
								cat.asid = cas.asid
							inner join membercentral.dbo.crd_authorityTypes at on
								at.typeID = cat.typeID	
							inner join membercentral.dbo.crd_offerings o on
								o.asid = cat.asid 
							inner join @tblSubEvent se on 
								se.eventID = o.eventID
								and e.eventID = se.parentEventID 
							inner  join membercentral.dbo.crd_offeringTypes ot	on 
									ot.offeringID = o.offeringID
									and ot.ASTID = cat.ASTID
								inner join membercentral.dbo.crd_requests req on
									req.offeringTypeID = ot.offeringTypeID
									and req.creditAwarded = 1
								inner join membercentral.dbo.ev_registrants r on
									r.registrantID = req.registrantID 
									and r.status = 'A'
								inner join membercentral.dbo.ams_members m2 on
									m2.memberID = r.memberID
									and m2.memberID = m2.activeMemberID
									and m2.status <> 'D'
									and m2.orgID = #arguments.orgID#
									and m2.memberID = m.memberID
									and m2.isProtected = 0
							group by 
							at.typeID, se.eventID, isNull(cat.ovTypeName, at.typeName), isNull(ot.creditValue, 0),
							isNull(req.creditAwarded, 0), isNull(req.creditValueAwarded, 0), isNull(req.registrantID, 0)		
						) tmp2 
					where m.orgID = #arguments.orgID#
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
					and e.status = 'A'
					and rn.status = 'A'
					and r.status = 'A'
					and r.attended = 1
					and subEvent.subEventID is null
					and (tmp2.creditAwarded = 1 or e.eventID in (select parentEventID from @tblSubEvent))	
					and tmp2.creditValueAwarded > 0
				) tmp
				group by memberID, eventID, registrantID
				order by memberID, eventID, registrantID			
			</cfif>	
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.sqlString>
	</cffunction>

	<cffunction name="getAllEvents" returntype="Struct" access="private" output="false">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="orgID" type="numeric" required="true">
	
		<cfset var local = structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				declare @authorityID int, @orgID int;
				declare @tblSubEvent TABLE (eventID int, parentEventID int, memberID int);

				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				
				select @authorityID = authorityID from dbo.crd_authorities where authorityName = 'State Bar Of California';
				
				insert into @tblSubEvent
				select distinct subEvent.eventID, subEvent.parentEventID, m.memberID
				from dbo.ev_events e
				inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID
				inner join dbo.ev_events as e2 on e2.eventID = subEvent.eventID  
					and e2.status = 'A'
				inner join dbo.ev_registration as rn on rn.eventID = e2.eventID  and rn.siteID = e2.siteID
				inner join dbo.ev_registrants as r on r.registrationID = rn.registrationID 
					and r.status <> 'D'
				inner join dbo.ams_members as mReg on mReg.orgID = @orgID and mReg.memberID = r.memberID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = mReg.activeMemberID and m.isProtected = 0
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>				
				
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
					
				select distinct memberID, firstname, lastname, memberNumber, company, startTime, eventID,  eventTitle, 
					reportCode, creditValueAwarded, creditType, parentEventID, isMaster
				into ###local.tempTableName#
				from (
					select m.memberID, m.firstname, m.lastname, m.memberNumber, m.company, 
						tmp.startTime, e.eventID, tmp.eventTitle, e.reportCode, 
						rc.creditValueAwarded, isnull(ecast.ovTypeName,cat.typeName) as creditType,
						e.eventID as parentEventID, 
						isMaster = (select count (*) from dbo.ev_subEvents te where te.eventID = e.eventID)
					from dbo.ev_registrants as r
					inner join dbo.ev_registration as rn on rn.registrationID = r.registrationID and rn.siteID = r.recordedOnSiteID
					inner join dbo.ev_events as e on e.eventID = rn.eventID and e.siteID = rn.siteID
					inner join ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
					inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
					inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
					inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
					inner join dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID and cat.authorityID = @authorityID
					inner join dbo.ams_members as mReg on mReg.orgID = @orgID and mReg.memberID = r.memberID
					inner join dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.isProtected = 0
					<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					where m.orgID = @orgID
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
					and e.status = 'A'
					and rn.status = 'A'
					and r.status = 'A'
					and r.attended = 1
					and rc.creditAwarded = 1
						UNION ALL
					select m.memberID, m.firstname, m.lastname, m.memberNumber, m.company, 
						tmp.startTime, e.eventID, tmp.eventTitle, e.reportCode, 
						rc.creditValueAwarded, isnull(ecast.ovTypeName,cat.typeName) as creditType,
						subEventTbl.parentEventID, '0' as isMaster
					from dbo.ev_registrants as r
					inner join dbo.ev_registration as rn on rn.registrationID = r.registrationID and r.recordedOnSiteID = rn.siteID
					inner join dbo.ev_events as e on e.eventID = rn.eventID and e.siteID = rn.siteID
					inner join @tblSubEvent subEventTbl on subEventTbl.eventID = e.eventID	
						and subEventTbl.memberID = r.memberID				
					inner join ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
					inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
					inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
					inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
					inner join dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID and cat.authorityID = @authorityID
					inner join dbo.ams_members as mReg on mReg.orgID = @orgID and mReg.memberID = r.memberID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = mReg.activeMemberID and m.isProtected = 0
					where m.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
					and e.status = 'A'
					and rn.status = 'A'
					and r.status = 'A'
					and r.attended = 1
					and rc.creditAwarded = 1
				) tmp;
					
				CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName# (memberID);

				select tm.*, 
					m.lastname + ', ' + isnull(nullif(m.suffix,'') + ', ','') + isnull(nullif(m.professionalsuffix,'') + ', ','') 
						+ isnull(nullif(m.prefix,'') + ' ','') + m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' (' + m.membernumber + ')' as mc_combinedName
				from ###local.tempTableName# as tm
				inner join dbo.ams_members as m on m.orgID = @orgID 
					and m.memberID = tm.memberID
				order by tm.lastname, tm.firstname, tm.membernumber;

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>		

		<cfreturn local>
	</cffunction>
	
	<cffunction name="getRolledUpEvents" returntype="Struct" output="false" access="private">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="orgID" type="numeric" required="true">
	
		<cfset var local = structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				declare @authorityID int, @orgID int;
				declare @tblSubEvent TABLE (eventID int, parentEventID int, memberID int);
				declare @tblCreditsAwarded TABLE (autoID int identity(1,1), eventID int, memberID int, typeName varchar (255), creditValue decimal(6,2), creditValueAwarded decimal(6,2));
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select @authorityID = authorityID from dbo.crd_authorities where authorityName = 'State Bar Of California';
				
				delete from @tblE where eventID in (select eventID from dbo.ev_subEvents);
				
				insert into @tblSubEvent
				select distinct subEvent.eventID, subEvent.parentEventID, m.memberID
				from dbo.ev_events e
				inner join dbo.ev_subEvents as subEvent on subEvent.eventID = e.eventID
				inner join dbo.ev_events as e2 on e2.eventID = subEvent.eventID  
					and e2.status = 'A'
				inner join dbo.ev_registration as rn on rn.eventID = e2.eventID and rn.siteID = e2.siteID
				inner join dbo.ev_registrants as r on r.registrationID = rn.registrationID 
					and r.status <> 'D'
				inner join dbo.ams_members as mReg on mReg.orgID = @orgID and mReg.memberID = r.memberID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = mReg.activeMemberID and m.isProtected = 0
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>		
					union
				select distinct subEvent.eventID, subEvent.parentEventID, m.memberID
				from dbo.ev_events e
				inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID
				inner join dbo.ev_events as e2 on e2.eventID = subEvent.eventID  
					and e2.status = 'A'
				inner join dbo.ev_registration as rn on rn.eventID = e2.eventID and e2.siteID = rn.siteID
				inner join dbo.ev_registrants as r on r.registrationID = rn.registrationID 
					and r.status <> 'D'
				inner join dbo.ams_members as mReg on mReg.orgID = @orgID and mReg.memberID = r.memberID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = mReg.activeMemberID	and m.isProtected = 0 
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>;
				
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#_temp') IS NOT NULL
					DROP TABLE ###local.tempTableName#_temp;
					
				select distinct memberID, firstname, lastname, memberNumber, company, startTime, eventID, eventTitle, 
					reportCode, creditValueAwarded, creditType, parentEventID, subEventID, isMaster
				into ###local.tempTableName#_temp
				from (
					select m.memberID, m.firstname, m.lastname, m.memberNumber, m.company, 
						tmp.startTime, e.eventID, tmp.eventTitle, e.reportCode, 
						tmp2.creditValueAwarded, tmp2.subEventID, tmp2.creditType,
						e.eventID as parentEventID, 
						isMaster = (select count (*) from  dbo.ev_subEvents te where te.eventID = e.eventID)
					from dbo.ev_registrants as r
					inner join dbo.ev_registration as rn on rn.registrationID = r.registrationID and r.recordedOnSiteID = rn.siteID
					inner join dbo.ev_events as e on e.eventID = rn.eventID and e.siteID = rn.siteID
					inner join ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID				
					inner join dbo.ams_members as mReg on mReg.orgID = @orgID and mReg.memberID = r.memberID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = mReg.activeMemberID and m.isProtected = 0
					left outer join dbo.ev_subEvents as subEvent on subEvent.eventID = e.eventID					
					<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA) and findNoCase("##tmpVGRMembers", arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>
						inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID
					</cfif>
					outer apply  (		
						select 
							at.typeID,
							0 as subEventID, 
							isNull(cat.ovTypeName, at.typeName) as creditType, 
							isNull(ot.creditValue, 0) as creditValue,
							isNull(req.creditAwarded, 0) as creditAwarded,
							isNull(req.creditValueAwarded, 0) as creditValueAwarded
						from
							membercentral.dbo.crd_sponsors cs
							inner join membercentral.dbo.crd_authoritySponsors cas on
								cas.sponsorID = cs.sponsorID
								and cs.orgID = @orgID
							inner join membercentral.dbo.crd_authorities ca on
								ca.authorityID = cas.authorityID
								and ca.authorityID = @authorityID
							inner join membercentral.dbo.crd_authoritySponsorTypes cat on
								cat.asid = cas.asid
							inner join membercentral.dbo.crd_authorityTypes at on
								at.typeID = cat.typeID	
							inner join membercentral.dbo.crd_offerings o on
								o.asid = cat.asid 
								and o.eventID = e.eventID
							inner join membercentral.dbo.crd_offeringTypes ot on 
								ot.offeringID = o.offeringID and ot.ASTID = cat.ASTID
								inner join membercentral.dbo.crd_requests req on
									req.offeringTypeID = ot.offeringTypeID
									and req.creditAwarded = 1
								inner join membercentral.dbo.ev_registrants r on
									r.registrantID = req.registrantID 
									and r.status = 'A'
								inner join membercentral.dbo.ams_members m2 on
									m2.memberID = r.memberID
									and m2.memberID = m2.activeMemberID
									and m2.status <> 'D'
									and m2.orgID = @orgID
									and m2.memberID = m.memberID
									and m2.isProtected = 0
						group by 
							at.typeID, isNull(cat.ovTypeName, at.typeName), isNull(ot.creditValue, 0),
							isNull(req.creditAwarded, 0), isNull(req.creditValueAwarded, 0), isNull(req.registrantID, 0)
						union all
						select 
							at.typeID,
							se.eventID as subEventID, 
							isNull(cat.ovTypeName, at.typeName) as creditType, 
							isNull(ot.creditValue, 0) as creditValue,
							isNull(req.creditAwarded, 0) as creditAwarded,
							isNull(req.creditValueAwarded, 0) as creditValueAwarded
						from
							membercentral.dbo.crd_sponsors cs
							inner join membercentral.dbo.crd_authoritySponsors cas on
								cas.sponsorID = cs.sponsorID
								and cs.orgID = @orgID
							inner join membercentral.dbo.crd_authorities ca on
								ca.authorityID = cas.authorityID
								and ca.authorityID = @authorityID
							inner join membercentral.dbo.crd_authoritySponsorTypes cat on
								cat.asid = cas.asid
							inner join membercentral.dbo.crd_authorityTypes at on
								at.typeID = cat.typeID	
							inner join membercentral.dbo.crd_offerings o on
								o.asid = cat.asid 
							inner join @tblSubEvent se on 
								se.eventID = o.eventID
								and e.eventID = se.parentEventID 
							inner  join membercentral.dbo.crd_offeringTypes ot on 
									ot.offeringID = o.offeringID
									and ot.ASTID = cat.ASTID
								inner join membercentral.dbo.crd_requests req on
									req.offeringTypeID = ot.offeringTypeID
									and req.creditAwarded = 1
								inner join membercentral.dbo.ev_registrants r on
									r.registrantID = req.registrantID 
									and r.status = 'A'
								inner join membercentral.dbo.ams_members m2 on
									m2.memberID = r.memberID
									and m2.memberID = m2.activeMemberID
									and m2.status <> 'D'
									and m2.orgID = @orgID
									and m2.memberID = m.memberID
									and m2.isProtected = 0
						group by 
							at.typeID, se.eventID, isNull(cat.ovTypeName, at.typeName), isNull(ot.creditValue, 0),
							isNull(req.creditAwarded, 0), isNull(req.creditValueAwarded, 0), isNull(req.registrantID, 0)		
					) tmp2 
					where m.orgID = @orgID
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
					and e.status = 'A'
					and rn.status = 'A'
					and r.status = 'A'
					and r.attended = 1
					and subEvent.subEventID is null
					and (tmp2.creditAwarded = 1 or e.eventID in (select parentEventID from @tblSubEvent))	
					and tmp2.creditValueAwarded > 0
				) tmp;
					
				CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName#_temp (memberID);
				
				select memberID, firstname, lastname, memberNumber, company, startTime, eventID, eventTitle, 
					reportCode, creditType, sum(creditValueAwarded) as creditValueAwarded
				into ###local.tempTableName#
				from ###local.tempTableName#_temp
				group by memberID, firstname, lastname, memberNumber, company, startTime, eventID, eventTitle, 
					reportCode, creditType;
				
				select tm.*, 
					m.lastname + ', ' + isnull(nullif(m.suffix,'') + ', ','') + isnull(nullif(m.professionalsuffix,'') + ', ','') 
						+ isnull(nullif(m.prefix,'') + ' ','') + m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' (' + m.membernumber + ')' as mc_combinedName
				from ###local.tempTableName# as tm
				inner join dbo.ams_members as m on m.orgID = @orgID 
					and m.memberID = tm.memberID
				order by tm.lastname, tm.firstname, tm.membernumber;

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#_temp') IS NOT NULL
					DROP TABLE ###local.tempTableName#_temp;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local>
	</cffunction>
</cfcomponent>