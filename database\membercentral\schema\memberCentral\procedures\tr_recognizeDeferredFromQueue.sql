ALTER PROC dbo.tr_recognizeDeferredFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @doneStatusID int, @recordedByMemberID int, 
		@recogAmount decimal(18,2), @recordedOnSiteID int, @recogdebitGLAccountID int, @recogcreditGLAccountID int, @recogsaleAdjTID int,
		@dittransactionID int, @nowDate datetime, @recogTransactionID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='deferredRecognition', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;
	
	set @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();
	set @nowDate = getdate();

	-- get info from queue table
	select @recogAmount=amount*-1, @recordedOnSiteID=recordedOnSiteID, @recogdebitGLAccountID=creditGLAccountID,
		@recogcreditGLAccountID=debitGLAccountID, @recogsaleAdjTID=saleAdjTID, @dittransactionID=transactionID
	from platformQueue.dbo.queue_deferredRecognition
	where itemID = @itemID;

	IF @dittransactionID IS NULL
		goto on_end;

	update platformQueue.dbo.queue_deferredRecognition
	set statusID = @processingStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;
	
	-- recognize revenue
	EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
		@statsSessionID=0, @amount=@recogAmount, @transactionDate=@nowDate, @recognitionDate=null, 
		@debitGLAccountID=@recogdebitGLAccountID, @creditGLAccountID=@recogcreditGLAccountID, 
		@saleTransactionID=@recogsaleAdjTID, @DITTransactionID=@ditTransactionID, @batchAsRecogJob=1, 
		@bypassInBounds=0, @transactionID=@recogTransactionID OUTPUT;

	update platformQueue.dbo.queue_deferredRecognition
	set statusID = @doneStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	delete from platformQueue.dbo.queue_deferredRecognition
	where itemID = @itemID;

	on_end:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
