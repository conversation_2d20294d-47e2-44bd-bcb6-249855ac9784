<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>
		
		<cfsetting requesttimeout="1800">

		<cfset local.itemCount = getQueueItemCount()>	
	
		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_EventCertificate_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryGetEventRegistrants" resultset="1">
			</cfstoredproc>
			
			<cfoutput query="local.qryGetEventRegistrants" group="itemID">
				<cfset local.thisItemID = local.qryGetEventRegistrants.itemID>
				
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_eventCertificate as qi
					inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">
					and qs.queueStatus = 'grabbedForProcessing'
				</cfquery>		
				
				<cfif local.checkItemID.itemCount>
					<cftry>
						<!--- UPDATE STATUS --->
						<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;
	
							declare @newstatus int;
							select @newstatus = qs.queueStatusID 
								from dbo.tblQueueStatuses as qs
								inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
								where qt.queueType = 'eventCertificate'
								and qs.queueStatus = 'processingItem';
							
							IF @newstatus is not null
								update dbo.queue_eventCertificate
								set statusID = @newstatus,
									dateUpdated = getdate()
								where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;
						</cfquery>
						
						<cfquery name="local.qryGetCurrentRegistrant" dbtype="query">
							select recipientID, registrantID, siteID, messageID
							from [local].qryGetEventRegistrants
							where isProcessed = 0
							and itemID = <cfqueryparam value="#local.thisItemID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>

						<cfif local.qryGetCurrentRegistrant.recordCount>					
							<cfset doInsertCertAttachments( itemID=local.thisItemID, recipientID=local.qryGetCurrentRegistrant.recipientID, siteID=local.qryGetCurrentRegistrant.siteID,messageID=local.qryGetCurrentRegistrant.messageID, registrantID=local.qryGetCurrentRegistrant.registrantID)>
						</cfif>
						
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
							<cfset local.success = false>
						</cfcatch>
					</cftry>					
				
				</cfif>
			
			</cfoutput>
			
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.success = false>
			</cfcatch>		
		</cftry>

		<cftry>
			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_EventCertificate_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
			
		<cfreturn local.success>
	</cffunction>
	
	<cffunction name="doInsertCertAttachments" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="recipientID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="messageID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.objCertificate = CreateObject('component', 'model.admin.events.certificate')>

		<cfset local.strCertificate = local.objCertificate.generateCertificate(registrantID=arguments.registrantID)>
		<cfif StructKeyExists(local.strCertificate,"certificatePath")>
			<!--- insert email attachments --->
			<cfif FileExists(local.strCertificate.certificatePath)>
				<cftry>

					<cfset local.pathFromS3Uploader = replacenocase(local.strCertificate.certificatePath,application.paths.SharedTempNoWeb.path,application.paths.SharedTempNoWeb.pathS3Uploader)>
					<cfset local.pathFromS3Uploader = replace(local.pathFromS3Uploader,'/','\','all')>

					<cfquery name="local.qryInsertEmailAttachments" datasource="#application.dsn.platformMail.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @itemID int, @fileName varchar(400), @localDirectory varchar(400), @s3keyMod varchar(4), @recipientID int, @siteID int, @messageID int,
								@objectKey varchar(400), @filePathForS3Upload varchar(400), @attachmentID int, @s3bucketName varchar(100),
								@s3UploadReadyStatusID int, @evCertDoneStatusID int, @nowDate datetime = getdate();
							SET @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
							SET @recipientID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recipientID#">;
							SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
							SET @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messageID#">;
							SET @fileName = 'certificate.pdf';
							SET @localDirectory = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(local.strCertificate.certificatePath,'/certificate.pdf','','one')#">;
							SET @filePathForS3Upload = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.pathFromS3Uploader#">;
							SET @s3bucketName = 'platformmail-membercentral-com';

							EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;
							EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='eventCertificate', @queueStatus='done', @queueStatusID=@evCertDoneStatusID OUTPUT;

							BEGIN TRAN;
								EXEC dbo.email_insertAttachment @fileName=@fileName, @localDirectory=@localDirectory, @attachmentID=@attachmentID OUTPUT;

								INSERT INTO dbo.email_messageRecipientAttachments (recipientID, attachmentID)
								VALUES(@recipientID, @attachmentID);

								<!--- insert to s3 upload queue --->
								SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
								SET @objectKey = LOWER('#application.MCEnvironment#/outgoing/' + @s3keyMod + '/' + cast(@attachmentID as varchar(10)) + '/' + @fileName);

								IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
									INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
									VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);

								-- set recipient as ready
								EXEC platformMail.dbo.email_setMessageRecipientHistoryStatus @siteID=@siteID, @messageID=@messageID, @recipientID=@recipientID, @statusCode='Q', @updateDate=0;

								-- update queue item status as done
								UPDATE platformQueue.dbo.queue_eventCertificate
								SET statusID = @evCertDoneStatusID,
									dateUpdated = @nowDate,
									isProcessed = 1, 
									errMessage = ''
								WHERE itemID = @itemID;
							COMMIT TRAN;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				<cfcatch type="Any">
					<cfquery name="local.updateQueueItem" datasource="#application.dsn.platformQueue.dsn#">
						UPDATE dbo.queue_eventCertificate
						SET isProcessed = 0, 
							errMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#cfcatch.message#">,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
					</cfquery>

					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_eventCertificate;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>