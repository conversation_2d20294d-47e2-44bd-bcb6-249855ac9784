<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'pdf','customcsv' ]>
	<cfset variables.AllowScheduling = false>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				<script language="javascript">
					function validateNJFirmStatement() {
						var pass = true;

						var imageRegEX = /^[^\\/:"*?<>|]+\.(jpg|png|gif)$/; /* " */

						var countEl = $('##stepFieldsetsDIVfieldsets span.selFSCount');
						var selectedFSCount = countEl.length && countEl.text() != '' ? parseInt(countEl.text()) : 0;
						if (selectedFSCount != 1) {
							alert('One field set can be chosen for Organization');
							pass = false;
						}	

						if (!(imageRegEX.test($('##headerImgFileName')[0].value.toLowerCase()))) {
							alert('Header Image must be a JPG, GIF, of PNG Filename.');
							pass = false;
						}

						if (!(imageRegEX.test($('##pricingImgFileName')[0].value.toLowerCase()))) {
							alert('Pricing Image must be a JPG, GIF, of PNG Filename.');
							pass = false;
						}

						if (pass) {
							if ( $('##reportAction').val() == "pdf" ) {
								$('##reportDefs ##reportAction').val('outputToScreen');
							}
						}
						return pass;
					}

					$(function() {
						$('.tbody_ovfsimg').hide();
						$('.tbody_ovfsmn').hide();
						$('.tbody_ovfsmc').hide();

						setupRptFilterDateRange('frmStartFrom','frmStartTo');
						setupRptFilterDateRange('frmEndFrom','frmEndTo');
						mca_setupDatePickerField('frmStatementDt');
						mca_setupCalendarIcons('frmReport');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.dataHead#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.frmStartFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmstartfrom/text())")>
					<cfset local.frmStartTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmstartto/text())")>
					<cfset local.frmEndFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmendfrom/text())")>
					<cfset local.frmEndTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmendto/text())")>
					<cfset local.headerImgFileName = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/headerimgfilename/text())")>
					<cfset local.pricingImgFileName = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/pricingimgfilename/text())")>
					<cfset local.reportHeadingText = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/reportheadingtext/text())")>
					<cfset local.reportFooterText = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/reportfootertext/text())")>
					<cfset local.frmStatementDt = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmstatementdt/text())")>

					<cfif not arraylen(xmlparse(arguments.event.getValue('qryReportInfo').otherXML).report.extra.xmlChildren)>
						<cfset local.headerImgFileName = "report-logo.jpg">
						<cfset local.pricingImgFileName = "report-pricing.jpg">
						<cfset local.reportHeadingText = "2017-2018 MembershipDues Statement">
						<cfset local.reportFooterText = "PLEASE NOTE: Although the New Jersey Association for Justice, Inc., is an affiliate of AAJ, membership is independent of the national association, as are our sources of funding. This dues statement is for NJAJ dues only">
						<cfset local.frmStatementDt = dateFormat(now(), "mm/dd/yyyy")>
					</cfif>

					<cfform name="frmReport"  id="frmReport" method="post">
					<input type="hidden" name="reportAction" id="reportAction" value="">

					#showStepMemberCriteria(event=arguments.event, title="Define Optional Firm Filter", desc="Optionally filter the firm records on this report using the defined criteria below.")#

					<div class="mb-5 stepDIV">
						<h5>Define Extra Options</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmStartFrom" class="col-md-4 col-sm-12 col-form-label">Subscription Start Date between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmStartFrom" id="frmStartFrom" value="#local.frmStartFrom#" mcrdtxt="Subscription Start Date Start" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmStartFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmStartTo" id="frmStartTo" value="#local.frmStartTo#" mcrdtxt="Subscription Start Date End" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmStartTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="mca_clearDateRangeField('frmStartFrom','frmStartTo')">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmEndFrom" class="col-md-4 col-sm-12 col-form-label">Subscription End Date between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmEndFrom" id="frmEndFrom" value="#local.frmEndFrom#" mcrdtxt="Subscription End Date Start" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmEndFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmEndTo" id="frmEndTo" value="#local.frmEndTo#" mcrdtxt="Subscription End Date End" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmEndTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="mca_clearDateRangeField('frmEndFrom','frmEndTo');">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="headerImgFileName" class="col-md-4 col-sm-12 col-form-label">Header Image Filename</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="headerImgFileName" id="headerImgFileName" value="#local.headerImgFileName#" class="form-control form-control-sm" placeholder="report-logo.jpg" onchange="enableButtonBar();">
									</div>
								</div>
								<div class="form-group row">
									<label for="pricingImgFileName" class="col-md-4 col-sm-12 col-form-label">Pricing Image Filename</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="pricingImgFileName" id="pricingImgFileName" value="#local.pricingImgFileName#" class="form-control form-control-sm" placeholder="report-pricing.jpg" onchange="enableButtonBar();">
									</div>
								</div>
								<div class="form-group row">
									<label for="reportHeadingText" class="col-md-4 col-sm-12 col-form-label">Report Heading Text</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="reportHeadingText" id="reportHeadingText" value="#local.reportHeadingText#" class="form-control form-control-sm" placeholder="2017-2018 MembershipDues Statement" onchange="enableButtonBar();">
									</div>
								</div>
								<div class="form-group row">
									<label for="reportFooterText" class="col-md-4 col-sm-12 col-form-label">Report Footer Text</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="reportFooterText" id="reportFooterText" value="#local.reportFooterText#" class="form-control form-control-sm" placeholder="PLEASE NOTE: Although the New Jersey Association for Justice, Inc., is an affiliate of AAJ, membership is independent of the national association, as are our sources of funding. This dues statement is for NJAJ dues only" onchange="enableButtonBar();">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmStatementDt" class="col-md-4 col-sm-12 col-form-label">Statement Date</label>
									<div class="col-md-8 col-sm-12">
										<div class="input-group input-group-sm">
											<input type="text" name="frmStatementDt" id="frmStatementDt" value="#local.frmStatementDt#" mcrdtxt="Report Statement Date" class="form-control form-control-sm dateControl rolldate">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="frmStatementDt"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					#showStepFieldsets(event=arguments.event, title="Select a Single Field Set for the Organization", desc="The field set you select needs to include the address, city, state, zip, phone, and fax for the firm. Please ensure that the field set selected for the Organization is <b><i>Firm Billing Statement Firm Details</i></b> and that it contains the fields previously mentioned.", mode=1)#
					#showButtonBar(event=arguments.event,validateFunction='validateNJFirmStatement')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="qryPDFReport" access="private" output="false" returntype="query">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="frmStartFrom" type="string" required="true">
		<cfargument name="frmStartTo" type="string" required="true">
		<cfargument name="frmEndFrom" type="string" required="true">
		<cfargument name="frmEndTo" type="string" required="true">
		<cfargument name="reportXML" type="xml" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFieldSetFields">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select fieldset.fieldsetid, fieldset.fieldsetname, 
				fieldset.uid, mf.fieldID, mf.dbObjectAlias, mf.dbField,
				mf.fieldLabel, mf.fieldOrder
			from dbo.rpt_SavedReports as sr
			cross apply otherXML.nodes('/report/fieldsets/fieldset') as F(fs)
			inner join dbo.ams_memberFieldSets as fieldset on fieldset.uid = F.fs.value('@uid','uniqueidentifier')
			inner join dbo.ams_memberFields as mf on mf.fieldsetID = fieldset.fieldsetID 
				and mf.isGrouped = 0
			where sr.reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#" />
			order by mf.fieldOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strFirmAddressFieldSet = {
			"firmAddress1": "",
			"firmAddress2": "",
			"firmAddress2": "",
			"firmCity": "",
			"firmStateCode": "",
			"firmPostalCode": "",
			"firmCountry": "",
			"firmPhone": "",
			"firmFax": ""
		}>
		<cfloop query="local.qryFieldSetFields">
			<cfif findNoCase("address 1", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmAddress1 = "[#local.qryFieldSetFields.fieldLabel#]">
			<cfelseif findNoCase("address 2", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmAddress2 = "[#local.qryFieldSetFields.fieldLabel#]">
			<cfelseif findNoCase("city", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmCity = "[#local.qryFieldSetFields.fieldLabel#]">
			<cfelseif findNoCase("state", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmStateCode = "[#local.qryFieldSetFields.fieldLabel#]">
			<cfelseif findNoCase("postal", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmPostalCode = "[#local.qryFieldSetFields.fieldLabel#]">
			<cfelseif findNoCase("country", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmCountry = "[#local.qryFieldSetFields.fieldLabel#]">
			<cfelseif findNoCase("phone", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmPhone = "[#local.qryFieldSetFields.fieldLabel#]">
			<cfelseif findNoCase("fax", local.qryFieldSetFields.fieldLabel)>
				<cfset local.strFirmAddressFieldSet.firmFax = "[#local.qryFieldSetFields.fieldLabel#]">
			</cfif>
		</cfloop>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;		

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>

				declare @orgID int, @siteID int, @startFrom datetime, @startTo datetime, @endFrom datetime, @endTo datetime,
					@subUID uniqueidentifier, @filterColUID uniqueidentifier, @pastSubUID uniqueidentifier, @subType uniqueidentifier, 
					@pastSubType uniqueidentifier, @configXML xml, @firmCount int, @recordedByMemberID int, @outputFieldsXML xml;
				declare @qryAllFirmMembers table (masterMemberID int, masterName varchar(1000), childMemberID int, childName varchar(1000), 
					firstName varchar(250), lastName varchar(250), relationshipTypeName varchar(1000));	
				declare @tblS TABLE (subscriberID int PRIMARY KEY);

				set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
				set @startFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.frmStartFrom#">;
				set @startTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.frmStartTo#">;
				set @endFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.frmEndFrom#">;
				set @endTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.frmEndTo#">;
				set @subUID = 'eeda7a27-bbfe-4af7-ba77-136de978b2d7';
				set @filterColUID = 'e5fe9a7d-c801-4af8-bc7e-48123504237e';
				set @pastSubUID = 'D375D136-2689-4784-9FFE-7922E53BE1B8';
				set @subType = '1c98f1bb-ea76-405f-8402-68478eb4017c';
				set @pastSubType = 'fde70458-802e-4877-b48b-e4d5235a5c39';
				set @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;
				select @orgID = orgID from dbo.sites where siteID = @siteID;

				-- put extra option fields into temp table
				declare @tblAllParams TABLE (reportParam varchar(100), paramValue varchar(max));
				insert into @tblAllParams (reportParam, paramValue)
				values ('imagePath', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#replaceNoCase(application.paths.internalPlatform.url,'*SITECODE*','nj')#/userassets/nj/nj/userimages/">);
				insert into @tblAllParams (reportParam, paramValue)
				values ('logoImage', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#XMLSearch(arguments.reportXML,"string(/report/extra/headerimgfilename/text())")#">);
				insert into @tblAllParams (reportParam, paramValue)
				values ('sidePricingImage', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#XMLSearch(arguments.reportXML,"string(/report/extra/pricingimgfilename/text())")#">);
				insert into @tblAllParams (reportParam, paramValue)
				values ('reportHeading', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#XMLSearch(arguments.reportXML,"string(/report/extra/reportheadingtext/text())")#">);
				insert into @tblAllParams (reportParam, paramValue)
				values ('bottomMessage', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#XMLSearch(arguments.reportXML,"string(/report/extra/reportfootertext/text())")#">);
				insert into @tblAllParams (reportParam, paramValue)
				values ('statementDate', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#dateFormat(XMLSearch(arguments.reportXML,"string(/report/extra/frmstatementdt/text())"),'long')#">);

				select @configXML = isnull((
					select reportParam, paramValue
					from @tblAllParams
					for xml path('param'), root('params'), type
				),'<params/>');
				
				-- split subscription status IDs
				DECLARE @tblSubStatusID table (subStatusID int);
				INSERT INTO @tblSubStatusID (subStatusID)
				select statusID
				from dbo.sub_statuses
				where statusName in ('Active','Billed','Accepted')
				order by statusName;

				insert into @qryAllFirmMembers
				select distinct masterMemberID, masterName, childMemberID, childName, m2.firstname, m2.lastname, relationshipList 
				from (
					select masterMemberID, masterName, childMemberID, childName, STRING_AGG(relationshipTypeName,', ') as relationshipList, 
						count(*) over (partition by masterMemberID) as masterCount
					from (
						select 
							distinct m.memberID as masterMemberID, mChild.memberID as childMemberID,
							  case when rtMaster.isOrganization = 1 then m.company else m.lastName + ', ' + m.firstName end as masterName,
							  case when rtChild.isOrganization = 1 then mChild.company else mChild.lastName + ', ' + mChild.firstName end as childName,
							  rrt.relationshipTypeName
						from dbo.ams_recordTypesRelationshipTypes rtrt 
						inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID and rr.recordTypeRelationshipTypeID = rtrt.recordTypeRelationshipTypeID 
							and rr.isActive = 1
						inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID 
							and rrt.orgID = @orgID
						inner join dbo.ams_recordTypes rtMaster on rtMaster.recordTypeID = rtrt.masterRecordTypeID  
							and rtMaster.orgID = @orgID
						inner join dbo.ams_recordTypes rtChild on rtChild.recordTypeID = rtrt.childRecordTypeID 
							and rtChild.orgID = @orgID
						inner join dbo.ams_members mMaster on mMaster.memberID = rr.masterMemberID 
							and mMaster.orgID = @orgID
						inner join dbo.ams_members m on m.memberID = mMaster.memberID 
							and m.orgID = @orgID 
							and m.isProtected = 0 
							and m.status <> 'D'
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						inner join dbo.ams_members mChild on mChild.memberID = rr.childMemberID 
							and mChild.orgID = @orgID 
							and mChild.memberID = mChild.activeMemberID 
							and mChild.isProtected = 0
							and mChild.status <> 'D'
						where rtrt.isActive = 1
					) x
					group by masterName, childName, masterMemberID, childMemberID
				) as outertbl
				inner join dbo.ams_members m2 on m2.memberid = outertbl.childMemberID
				order by masterName, childName;

				insert into @tblS (subscriberID)
				select s.subscriberID
				from dbo.sub_subscribers as s
				inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID 
				inner join dbo.sub_types as subType on subType.typeID = sub.typeID 
					and subType.siteID = @siteID 
					and subType.uid in (@subType,@pastSubType) 
				inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID 
				inner join @tblSubStatusID as tmpStatus on tmpStatus.subStatusID = substatus.statusID
				inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
				inner join dbo.sub_rateFrequencies subrf on subrf.RFID = s.RFID
				inner join dbo.sub_rates subrate on subrate.rateID = subrf.rateID
				where 1=1
					<cfif len(arguments.frmStartFrom)>
						and s.subStartDate >= @startFrom
					</cfif>
					<cfif len(arguments.frmStartTo)>
						and s.subStartDate <= @startTo
					</cfif>
					<cfif len(arguments.frmEndFrom)>
						and s.subEndDate >= @endFrom
					</cfif>
					<cfif len(arguments.frmEndTo)>
						and s.subEndDate <= @endTo
					</cfif>;
				
				IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
					DROP TABLE ##tmpSubscribers;
				IF OBJECT_ID('tempdb..##tmpSubscribersTmp') IS NOT NULL
					DROP TABLE ##tmpSubscribersTmp;
				IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
					DROP TABLE ##tmpSubscribersInv;
				IF OBJECT_ID('tempdb..##tmpSubscribersDues') IS NOT NULL
					DROP TABLE ##tmpSubscribersDues;
				IF OBJECT_ID('tempdb..##tmpFirmMembers') IS NOT NULL
					DROP TABLE ##tmpFirmMembers;
				IF OBJECT_ID('tempdb..##tmpFirmMembersFS') IS NOT NULL
					DROP TABLE ##tmpFirmMembersFS;
				IF OBJECT_ID('tempdb..##tmpFinalData') IS NOT NULL
					DROP TABLE ##tmpFinalData;
				IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
					DROP TABLE ##mcSubscribersForAcct;
				IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
					DROP TABLE ##mcSubscriberTransactions;
				CREATE TABLE ##tmpSubscribers (subscriberID int INDEX IX_subscriberid, memberID int INDEX IX_memberid, 
					lastName varchar(75), firstName varchar(75), memberNumber varchar(50), company varchar(200), 
					typeName varchar(100), subTypeUID uniqueidentifier, subscriptionName varchar(300), 
					subUID uniqueidentifier, rateName varchar(200), subStartDate datetime, subEndDate datetime, 
					SubscriptionStatusCode varchar(1), SubscriptionStatus varchar(50), PaymentStatus varchar(50), 
					subscriberPath varchar(200), frequencyID int, frequencyName varchar(50), 
					frequencyShortName varchar(10), PCFree bit, modifiedRate decimal(18,2), glAccountID int, 
					lastPrice decimal(18,2), rootSubscriberID int, amtBilled decimal(18,2), amtDue decimal(18,2),
					amtPaid decimal(18,2));
				CREATE TABLE ##tmpSubscribersInv (invoiceID int, subscriberID int);
				CREATE TABLE ##tmpSubscribersTmp (subscriberID int, memberID int, 
					lastName varchar(75), firstName varchar(75), memberNumber varchar(50), company varchar(200), 
					typeName varchar(100), subTypeUID uniqueidentifier, subscriptionName varchar(300), 
					subUID uniqueidentifier, rateName varchar(200), subStartDate datetime, subEndDate datetime, 
					SubscriptionStatusCode varchar(1), SubscriptionStatus varchar(50), PaymentStatus varchar(50), 
					subscriberPath varchar(200), frequencyID int, frequencyName varchar(50), 
					frequencyShortName varchar(10), rootSubscriberID int, amtBilled decimal(18,2), amtDue decimal(18,2),
					amtPaid decimal(18,2));
				CREATE TABLE ##tmpSubscribersDues (subscriberID int, memberID int, 
					lastName varchar(75), firstName varchar(75), memberNumber varchar(50), company varchar(200), 
					typeName varchar(100), subTypeUID uniqueidentifier, subscriptionName varchar(300), 
					subUID uniqueidentifier, rateName varchar(200), subStartDate datetime, subEndDate datetime, 
					SubscriptionStatusCode varchar(1), SubscriptionStatus varchar(50), PaymentStatus varchar(50), 
					subscriberPath varchar(200), frequencyID int, frequencyName varchar(50), 
					frequencyShortName varchar(10), rootSubscriberID int, amtBilled decimal(18,2), amtDue decimal(18,2),
					amtPaid decimal(18,2));
				CREATE TABLE ##tmpFirmMembers (memberID int, lastName varchar(75), firstName varchar(75), memberNumber varchar(50), company varchar(200));
				CREATE TABLE ##tmpFirmMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);
				CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
				CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
					invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
					amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
					assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
					creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_subscriberID_transactionID (subscriberID, transactionID));

				-- subscriber data
				insert into ##tmpSubscribers
				select s.subscriberID, m.memberid, m.lastname, m.firstname, m.membernumber, m.company, 
					stypes.typeName, stypes.uid as subTypeUID,
					sub.subscriptionName, sub.uid as subUID, subrate.rateName, s.subStartDate, s.subEndDate, 
					substatus.statusCode as SubscriptionStatusCode, substatus.statusName as SubscriptionStatus, 
					subpaystatus.statusName as PaymentStatus, s.subscriberPath,
					freq.frequencyID, freq.frequencyName, freq.frequencyShortName,
					s.PCFree, s.modifiedRate, s.glAccountID, s.lastPrice, s.rootSubscriberID, 
					0 as amtBilled, 0 as amtDue, 0 as amtPaid
				from dbo.sub_subscribers as s  
				inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_types stypes on stypes.typeID = sub.typeID
				inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID
				inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
				inner join dbo.sub_rateFrequencies as subrf on subrf.RFID = s.RFID
				inner join dbo.sub_frequencies as freq on freq.frequencyID = subrf.frequencyID					
				inner join dbo.sub_rates as subrate on subrate.rateID = subrf.rateID
				inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = s.memberID
				inner join dbo.ams_members as m on m.memberid = m2.activeMemberID and m.isProtected = 0
				inner join @tblS as tblS on tblS.subscriberID = s.subscriberID
				where m.orgID = @orgID
				and m.status <> 'D';

				-- get existing invoices and populate mcSubscriberTransactions
				INSERT INTO ##mcSubscribersForAcct (subscriberID)
				SELECT distinct subscriberID
				FROM ##tmpSubscribers;

				EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				INSERT INTO ##tmpSubscribersInv (invoiceID, subscriberID)
				select distinct st.invoiceID, st.subscriberID
				from ##mcSubscriberTransactions as st;

				-- billed (R and O) have no transactions - amtBilled and AmtDue are the same
				-- other with no invoices - amtBilled is here but amtDue is 0
				update tmp
				set tmp.amtBilled = 
						case 
						when tmp.PCFree = 1 then 0
						when tmp.modifiedRate is not null then tmp.modifiedRate + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.modifiedRate,getdate(),ma.stateID)),0)
						else tmp.lastPrice + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.lastPrice,getdate(),ma.stateID)),0)
						end
				from ##tmpSubscribers as tmp
				left outer join dbo.ams_memberAddresses as ma
					inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
						AND matag.memberID = ma.memberID
						and matag.addressTypeID = ma.addressTypeID
					inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
						and matagt.addressTagTypeID = matag.addressTagTypeID
						and matagt.addressTagType = 'Billing'
					on ma.orgID = @orgID
					and ma.memberID = tmp.memberID
				where tmp.SubscriptionStatusCode in ('R','O')
				OR NOT EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID);

				update ##tmpSubscribers
				set amtDue = amtBilled
				where SubscriptionStatusCode in ('R','O');

				-- other when there are invoices for the subs
				update tmp
				set tmp.amtBilled = innertmp.amtBilled,
					tmp.amtDue = innertmp.amtDue,
					tmp.amtPaid = innertmp.amtPaid
				from ##tmpSubscribers as tmp
				inner join (
					select tmp.subscriberID, 
						sum(tsFull.cache_amountAfterAdjustment) as amtBilled, 
						sum(tsFull.cache_amountAfterAdjustment)-sum(tsFull.cache_activePaymentAllocatedAmount) as amtDue,
						sum(tsFull.cache_activePaymentAllocatedAmount) as amtPaid
					from ##tmpSubscribers as tmp
					inner join ##mcSubscriberTransactions as st on st.subscriberID = tmp.subscriberID
					cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,st.transactionID) as tsFull
					where tmp.SubscriptionStatusCode not in ('R','O')
					AND EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID)
					group by tmp.subscriberID
				) as innertmp on innertmp.subscriberID = tmp.subscriberID;

				-- get results temp results
				INSERT INTO ##tmpSubscribersTmp (subscriberID, memberid, lastname, firstname, membernumber, company,
					typeName, subTypeUID, subscriptionName, subUID, rateName, subStartDate, subEndDate, rootSubscriberID, 
					SubscriptionStatusCode, SubscriptionStatus, PaymentStatus, subscriberPath, frequencyID, frequencyName, 
					frequencyShortName, amtBilled, amtDue, amtPaid)
				select tm.subscriberID, tm.memberid, tm.lastname, tm.firstname, tm.membernumber, tm.company, tm.typeName, 
					tm.subTypeUID, tm.subscriptionName, tm.subUID, tm.rateName, tm.subStartDate, tm.subEndDate, 
					tm.rootSubscriberID, tm.SubscriptionStatusCode, tm.SubscriptionStatus, tm.PaymentStatus, tm.subscriberPath, 
					tm.frequencyID, tm.frequencyName, tm.frequencyShortName, isnull(tm.amtBilled,0) as amtBilled, 
					isnull(tm.amtDue,0) as amtDue, isnull(tm.amtPaid,0) as amtPaid					
				from ##tmpSubscribers as tm;

				-- get results with dues data
				INSERT INTO ##tmpSubscribersDues (subscriberID, memberid, lastname, firstname, membernumber, company,
					typeName, subTypeUID, subscriptionName, subUID, rateName, subStartDate, subEndDate, rootSubscriberID, 
					SubscriptionStatusCode, SubscriptionStatus, PaymentStatus, subscriberPath, frequencyID, frequencyName, 
					frequencyShortName, amtBilled, amtDue, amtPaid)
				select subscriberID, memberid, lastname, firstname, membernumber, company,
					typeName, subTypeUID, subscriptionName, subUID, rateName, subStartDate, subEndDate, rootSubscriberID, 
					SubscriptionStatusCode, SubscriptionStatus, PaymentStatus, subscriberPath, frequencyID, frequencyName, 
					frequencyShortName, amtBilled, amtDue, amtPaid
				from ##tmpSubscribersTmp
				where subUID = @subUID;

				-- firm members
				INSERT INTO ##tmpFirmMembers (memberID, lastName, firstName, memberNumber, company)
				SELECT DISTINCT m.memberID, m.lastName, m.firstName, m.memberNumber, m.company
				FROM @qryAllFirmMembers AS fm
				INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
					AND m.memberID = fm.masterMemberID 
					AND m.isProtected = 0;

				-- get members fieldset data and set back to snapshot because proc ends in read committed
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
					@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.fieldSetIDList#">,
					@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.ovNameFormat#">,
					@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strSQLPrep.ovMaskEmails#">,
					@membersTableName='##tmpFirmMembers', @membersResultTableName='##tmpFirmMembersFS', 
					@linkedMembers=0, @mode='report', @outputFieldsXML=@outputFieldsXML OUTPUT;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				EXEC tempdb..sp_rename '##tmpFirmMembersFS.memberID', 'masterMemberID', 'COLUMN';

				select tmp.subscriberID, tmp.memberid, tmp.lastname, tmp.firstname, tmp.membernumber, tmp.company,
					tmp.typeName, tmp.subTypeUID, tmp.subscriptionName, tmp.subUID, tmp.rateName, tmp.subStartDate, 
					tmp.subEndDate, tmp.rootSubscriberID, tmp.SubscriptionStatusCode, tmp.SubscriptionStatus, 
					tmp.PaymentStatus, tmp.subscriberPath, tmp.frequencyID, tmp.frequencyName, tmp.frequencyShortName, 
					tmp.amtBilled, tmp.amtDue, tmp.amtPaid, tmp.pastSubUID, tmp.pastAmtBilled, tmp.pastAmtDue, 
					tmp.pastAmtPaid, fm.membernumber as masterMemberNumber, fm.firstName as masterFirstName, 
					fm.lastName as masterLastName, fm.company as masterCompany, fmfs.*,
					cast(0 as decimal(18,2)) as pastDuesTotal, cast(0 as decimal(18,2)) as duesTotal, 
					cast(0 as decimal(18,2)) as indivPastAmtDue, 
					ROW_NUMBER() OVER(ORDER BY tmpFM.masterName, tmp.lastName, tmp.firstName, tmp.memberNumber, tmp.subStartDate) as sortRow
				into ##tmpFinalData
				from (
					select dues.subscriberID, dues.memberid, dues.lastname, dues.firstname, dues.membernumber, dues.company,
						dues.typeName, dues.subTypeUID, dues.subscriptionName, dues.subUID, dues.rateName, dues.subStartDate, 
						dues.subEndDate, dues.rootSubscriberID, dues.SubscriptionStatusCode, dues.SubscriptionStatus, 
						dues.PaymentStatus, dues.subscriberPath, dues.frequencyID, dues.frequencyName, dues.frequencyShortName, 
						dues.amtBilled, dues.amtDue, dues.amtPaid, tmp.subUID as pastSubUID, tmp.amtBilled as pastAmtBilled, 
						tmp.amtDue as pastAmtDue, tmp.amtPaid as pastAmtPaid
					from ##tmpSubscribersDues as dues
					inner join @qryAllFirmMembers fm on fm.childMemberID = dues.memberID
					left outer join ##tmpSubscribersTmp as tmp on tmp.memberID = dues.memberID and tmp.subTypeUID = @pastSubType
				) as tmp
				inner join @qryAllFirmMembers as tmpFM on tmpFM.childMemberID = tmp.memberID
				inner join ##tmpFirmMembers as fm on fm.memberID = tmpFM.masterMemberID
				inner join ##tmpFirmMembersFS as fmfs ON fmfs.masterMemberID = fm.memberID
				inner join dbo.ams_memberData as amd  ON amd.memberID = tmp.memberid 
				inner join dbo.ams_memberDataColumnValues as amdv ON amdv.valueID = amd.valueID and amdv.columnValueString LIKE '%Attorney%'  
				inner join dbo.ams_memberDataColumns as mdc ON mdc.columnID = amdv.columnID 
				where mdc.uid = @filterColUID;

				update tmp
				set tmp.pastDuesTotal = tmpPD.pastDuesTotal
				from ##tmpFinalData as tmp
				inner join (
					select masterMemberID, sum(pastAmtDue) as pastDuesTotal
					from ##tmpFinalData
					group by masterMemberID
				) as tmpPD on tmpPD.masterMemberID = tmp.masterMemberID;

				update tmp
				set tmp.duesTotal = tmpPD.duesTotal
				from ##tmpFinalData as tmp
				inner join (
					select masterMemberID, sum(amtDue) as duesTotal
					from (
						select distinct masterMemberID, subscriberID, amtDue
						from ##tmpFinalData
					) as tmp1
					group by masterMemberID
				) as tmpPD on tmpPD.masterMemberID = tmp.masterMemberID;

				update tmp
				set tmp.indivPastAmtDue = tmpPD.indivPastAmtDue
				from ##tmpFinalData as tmp
				inner join (
					select masterMemberID, memberID, sum(pastAmtDue) as indivPastAmtDue
					from ##tmpFinalData
					group by masterMemberID, memberID
				) as tmpPD on tmpPD.masterMemberID = tmp.masterMemberID and tmpPD.memberID = tmp.memberID;

				
				SELECT @firmCount = COUNT(DISTINCT masterMemberID)
				FROM ##tmpFinalData
				WHERE ISNULL(pastDuesTotal,0) + ISNULL(duesTotal,0) > 0;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

				IF @firmCount > 0 BEGIN
					declare @insertingQueueStatusID int, @readyQueueStatusID int, @itemGroupUID uniqueIdentifier = NEWID();
					EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
					EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

					INSERT INTO platformQueue.dbo.queue_NJFirmSubStatements (itemGroupUID, firmMemberID, firmAddress1, firmAddress2, firmCity,
						firmStateCode, firmPostalCode, firmCountry, firmPhone, firmFax, firmPastDues, firmDues, firmGrandTotalDues, xmlConfigParam, 
						recordedByMemberID, statusID, dateAdded, dateUpdated)
					SELECT DISTINCT @itemGroupUID, masterMemberID, 
						<cfif local.qryFieldSetFields.recordCount>
							<cfif len(local.strFirmAddressFieldSet.firmAddress1)>#local.strFirmAddressFieldSet.firmAddress1#<cfelse>''</cfif>,
							<cfif len(local.strFirmAddressFieldSet.firmAddress2)>#local.strFirmAddressFieldSet.firmAddress2#<cfelse>''</cfif>,
							<cfif len(local.strFirmAddressFieldSet.firmCity)>#local.strFirmAddressFieldSet.firmCity#<cfelse>''</cfif>,
							<cfif len(local.strFirmAddressFieldSet.firmStateCode)>#local.strFirmAddressFieldSet.firmStateCode#<cfelse>''</cfif>,
							<cfif len(local.strFirmAddressFieldSet.firmPostalCode)>#local.strFirmAddressFieldSet.firmPostalCode#<cfelse>''</cfif>,
							<cfif len(local.strFirmAddressFieldSet.firmCountry)>#local.strFirmAddressFieldSet.firmCountry#<cfelse>''</cfif>,
							<cfif len(local.strFirmAddressFieldSet.firmPhone)>#local.strFirmAddressFieldSet.firmPhone#<cfelse>''</cfif>,
							<cfif len(local.strFirmAddressFieldSet.firmFax)>#local.strFirmAddressFieldSet.firmFax#<cfelse>''</cfif>,
						<cfelse>
							'', '', '', '', '', '', '', '',
						</cfif>
						ISNULL(pastDuesTotal,0), ISNULL(duesTotal,0), ISNULL(pastDuesTotal,0) + ISNULL(duesTotal,0), CAST(@configXML AS varchar(max)), 
						@recordedByMemberID, @insertingQueueStatusID, GETDATE(), GETDATE()
					FROM ##tmpFinalData
					WHERE ISNULL(pastDuesTotal,0) + ISNULL(duesTotal,0) > 0;

					INSERT INTO platformQueue.dbo.queue_NJFirmSubStatementsDetail (itemID, firmChildMemberID, firmChildDues)
					SELECT DISTINCT qi.itemID, tmp.memberID, ISNULL(tmp.amtDue,0) + ISNULL(tmp.pastAmtDue,0)
					FROM platformQueue.dbo.queue_NJFirmSubStatements AS qi
					INNER JOIN ##tmpFinalData AS tmp ON tmp.masterMemberID = qi.firmMemberID
					WHERE qi.itemGroupUID = @itemGroupUID;

					-- resume task
					EXEC dbo.sched_resumeTask @name='NJ Firm Billing Report Queue', @engine='BERLinux';

					UPDATE platformQueue.dbo.queue_NJFirmSubStatements 
					SET statusID = @readyQueueStatusID,
						dateUpdated = GETDATE()
					WHERE statusID = @insertingQueueStatusID
					AND itemGroupUID = @itemGroupUID;
				END

				-- return firms count
				SELECT ISNULL(@firmCount,0) AS firmCount;

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
				IF OBJECT_ID('tempdb..##tmpFirmMembersFS') IS NOT NULL
					DROP TABLE ##tmpFirmMembersFS;
				IF OBJECT_ID('tempdb..##tmpFirmMembers') IS NOT NULL
					DROP TABLE ##tmpFirmMembers;
				IF OBJECT_ID('tempdb..##tmpFinalData') IS NOT NULL
					DROP TABLE ##tmpFinalData;
				IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
					DROP TABLE ##tmpSubscribers;
				IF OBJECT_ID('tempdb..##tmpSubscribersTmp') IS NOT NULL
					DROP TABLE ##tmpSubscribersTmp;
				IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
					DROP TABLE ##tmpSubscribersInv;
				IF OBJECT_ID('tempdb..##tmpSubscribersDues') IS NOT NULL
					DROP TABLE ##tmpSubscribersDues;
				IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
					DROP TABLE ##mcSubscribersForAcct;
				IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
					DROP TABLE ##mcSubscriberTransactions;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.validationPassed = true>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin', mca_ta='edit')>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria. Unable to run rule." />
			</cfif>

			<cfset local.userImagesFolder = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/" & LCASE(arguments.sitecode) & "/userimages/">

			<cfset local.headerImgFileName = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/headerimgfilename/text())")>
			<cfset local.pricingImgFileName = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/pricingimgfilename/text())")>
			<cfset local.reportHeadingText = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/reportheadingtext/text())")>
			<cfset local.reportFooterText = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/reportfootertext/text())")>
			<cfset local.frmStatementDt = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstatementdt/text())")>
			<cfset local.frmStartFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartfrom/text())")>
			<cfset local.frmStartTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartto/text())")>
			<cfset local.frmEndFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendfrom/text())")>
			<cfset local.frmEndTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendto/text())")>
			
			<cfif len(local.frmStartFrom)>
				<cfset local.frmStartFrom = dateFormat(local.frmStartFrom,'mm/dd/yyyy')>
			</cfif>
			<cfif len(local.frmStartTo)>
				<cfset local.frmStartTo = dateFormat(local.frmStartTo,'mm/dd/yyyy') & ' 23:59:59.997'>
			</cfif>
			<cfif len(local.frmEndFrom)>
				<cfset local.frmEndFrom = dateFormat(local.frmEndFrom,'mm/dd/yyyy')>
			</cfif>
			<cfif len(local.frmEndTo)>
				<cfset local.frmEndTo = dateFormat(local.frmEndTo,'mm/dd/yyyy') & ' 23:59:59.997'>
			</cfif>

			<cfif not fileExists(local.userImagesFolder & LCASE(local.headerImgFileName))>
				<cfthrow message="#local.headerimgfilename# does not exist in userimages." type="RptError">
			</cfif>
			<cfif not fileExists(local.userImagesFolder & LCASE(local.pricingImgFileName))>
				<cfthrow message="#local.pricingImgFileName# does not exist in userimages." type="RptError">
			</cfif>							

			<cfif local.validationPassed>
				<cfset local.qryData = qryPDFReport(reportID=arguments.qryReportInfo.reportID, strSQLPrep=local.strSQLPrep, siteID=local.mc_siteInfo.siteID, 
				 	frmStartFrom=local.frmStartFrom, frmStartTo=local.frmStartTo, frmEndFrom=local.frmEndFrom, frmEndTo=local.frmEndTo, reportXML=arguments.qryReportInfo.otherXML)>

				<cfsavecontent variable="local.strReturn.data">
					<cfoutput>
					<div id="screenreport">
						<cfif local.qryData.firmCount>
							<h4>NJ Firm Billing Has Been Scheduled</h4>
							<div>
								The firm billing report containing #local.qryData.firmCount# firm(s) has been scheduled and will begin shortly.<br/>
								You will be sent an e-mail with the results with the report.<br/><br/>
								Please wait until you receive the emailed report before contacting MemberCentral with any questions.
							</div>
						<cfelse>
							<cfset local.strReturn.isReportEmpty = true>
							<h4>No Matching Firms Found</h4>
							<div>
								There are no matches for the combination of subscription, firm filters, and other report options that you selected.<br/>
							</div>
						</cfif>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		
		<cfcatch type="RptError">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
			<cfset local.strReturn.errMsg = cfcatch.message>
		</cfcatch>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
	
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfset local.frmStartFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartfrom/text())")>
			<cfset local.frmStartTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartto/text())")>
			<cfset local.frmEndFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendfrom/text())")>
			<cfset local.frmEndTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendto/text())")>
			
			<cfif len(local.frmStartFrom)>
				<cfset local.frmStartFrom = dateFormat(local.frmStartFrom,'mm/dd/yyyy')>
			</cfif>
			<cfif len(local.frmStartTo)>
				<cfset local.frmStartTo = dateFormat(local.frmStartTo,'mm/dd/yyyy') & ' 23:59:59.997'>
			</cfif>
			<cfif len(local.frmEndFrom)>
				<cfset local.frmEndFrom = dateFormat(local.frmEndFrom,'mm/dd/yyyy')>
			</cfif>
			<cfif len(local.frmEndTo)>
				<cfset local.frmEndTo = dateFormat(local.frmEndTo,'mm/dd/yyyy') & ' 23:59:59.997'>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					<cfif len(local.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>

					declare @orgID int, @siteID int, @startFrom datetime, @startTo datetime, @endFrom datetime, @endTo datetime,
						@subUID uniqueidentifier, @pastSubUID uniqueidentifier, @subType uniqueidentifier, @pastSubType uniqueidentifier,
						@outputFieldsXML xml;

					set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.mc_siteInfo.siteID#">;
					set @startFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.frmStartFrom#"/>;
					set @startTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.frmStartTo#"/>;
					set @endFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.frmEndFrom#"/>;
					set @endTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.frmEndTo#"/>;
					set @subUID = 'eeda7a27-bbfe-4af7-ba77-136de978b2d7';
					set @pastSubUID = 'D375D136-2689-4784-9FFE-7922E53BE1B8';
					set @subType = '1c98f1bb-ea76-405f-8402-68478eb4017c';
					set @pastSubType = 'fde70458-802e-4877-b48b-e4d5235a5c39';
					select @orgID = orgID from dbo.sites where siteID = @siteID;

					-- split subscription status IDs
					DECLARE @tblSubStatusID table (subStatusID int);

					INSERT INTO @tblSubStatusID (subStatusID)
					select statusID
					from dbo.sub_statuses
					where statusName in ('Active','Billed','Accepted')
					order by statusName;

					declare 
						@qryAllFirmMembers table (
							masterMemberID int, 
							masterName varchar(1000), 
							childMemberID int, 
							childName varchar(1000), 
							firstName varchar(250), 
							lastName varchar(250), 
							relationshipTypeName varchar(1000) 
						);	

					insert into @qryAllFirmMembers
					select distinct masterMemberID, masterName, childMemberID, childName, m2.firstname, m2.lastname, relationshipList 
					from (
						select masterMemberID, masterName, childMemberID, childName, STRING_AGG(relationshipTypeName,', ') as relationshipList, 
							count(*) over (partition by masterMemberID) as masterCount
						from (
							select 
								distinct m.memberID as masterMemberID, mChild.memberID as childMemberID,
								  case when rtMaster.isOrganization = 1 then m.company else m.lastName + ', ' + m.firstName end as masterName,
								  case when rtChild.isOrganization = 1 then mChild.company else mChild.lastName + ', ' + mChild.firstName end as childName,
								  rrt.relationshipTypeName
							from 
								dbo.ams_recordTypesRelationshipTypes rtrt 
								inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID and rr.recordTypeRelationshipTypeID = rtrt.recordTypeRelationshipTypeID 
									and rr.isActive = 1
								inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID 
									and rrt.orgID = @orgID
								inner join dbo.ams_recordTypes rtMaster on rtMaster.recordTypeID = rtrt.masterRecordTypeID  
									and rtMaster.orgID = @orgID
								inner join dbo.ams_recordTypes rtChild on rtChild.recordTypeID = rtrt.childRecordTypeID 
									and rtChild.orgID = @orgID
								inner join dbo.ams_members mMaster on mMaster.memberID = rr.masterMemberID 
									and mMaster.orgID = @orgID
								inner join dbo.ams_members m on m.memberID = mMaster.memberID 
									and m.orgID = @orgID 
									and m.isProtected = 0
									and m.status <> 'D'
								<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
								inner join dbo.ams_members mChild on mChild.memberID = rr.childMemberID 
									and mChild.orgID = @orgID 
									and mChild.memberID = mChild.activeMemberID 
									and mChild.isProtected = 0
									and mChild.status <> 'D'
							where 
								rtrt.isActive = 1
							) x
						group by masterName, childName, masterMemberID, childMemberID
						) as outertbl
					inner join dbo.ams_members m2 on m2.memberid = outertbl.childMemberID
					order by masterName, childName

					declare @tblS TABLE (subscriberID int PRIMARY KEY);
			
					insert into @tblS (subscriberID)
					select 
						s.subscriberID
					from 
						dbo.sub_subscribers as s
						inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID 
						inner join dbo.sub_types as subType on subType.typeID = sub.typeID 
							and subType.siteID = @siteID 
							and  subType.uid in (@subType,@pastSubType) 
						inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID 
						inner join @tblSubStatusID as tmpStatus on tmpStatus.subStatusID = substatus.statusID
						inner join dbo.sub_paymentstatuses as subpaystatus  on subpaystatus.statusID = s.paymentStatusID
						inner join dbo.sub_rateFrequencies subrf on subrf.RFID = s.RFID
						inner join dbo.sub_rates subrate  on subrate.rateID = subrf.rateID
					where 1=1
						<cfif len(local.frmStartFrom)>
							and s.subStartDate >= @startFrom
						</cfif>
						<cfif len(local.frmStartTo)>
							and s.subStartDate <= @startTo
						</cfif>
						<cfif len(local.frmEndFrom)>
							and s.subEndDate >= @endFrom
						</cfif>
						<cfif len(local.frmEndTo)>
							and s.subEndDate <= @endTo
						</cfif>						
						
					IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
						DROP TABLE ##tmpSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersTmp') IS NOT NULL
						DROP TABLE ##tmpSubscribersTmp;
					IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
						DROP TABLE ##tmpSubscribersInv;
					IF OBJECT_ID('tempdb..##tmpSubscribersDues') IS NOT NULL
						DROP TABLE ##tmpSubscribersDues;	
					IF OBJECT_ID('tempdb..##tmpSubscribersPast') IS NOT NULL
						DROP TABLE ##tmpSubscribersPast;							
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;
					IF OBJECT_ID('tempdb..##tmpFirmMembers') IS NOT NULL
						DROP TABLE ##tmpFirmMembers;
					IF OBJECT_ID('tempdb..##tmpFirmMembersFS') IS NOT NULL
						DROP TABLE ##tmpFirmMembersFS;
					CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
					CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
						invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
						amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
						assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
						creditGLAccountID int);
					CREATE TABLE ##tmpFirmMembers (memberID int, [Last Name] varchar(75), [First Name] varchar(75), MemberNumber varchar(50), Company varchar(200));
					CREATE TABLE ##tmpFirmMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

					-- subscriber data
					select 
						s.subscriberID, m.memberid, m.lastname, m.firstname, m.membernumber, m.company, 
						stypes.typeName, stypes.uid as subTypeUID,
						sub.subscriptionName, sub.uid as subUID, subrate.rateName, s.subStartDate, s.subEndDate, s.graceEndDate,
						substatus.statusCode as SubscriptionStatusCode, substatus.statusName as SubscriptionStatus, 
						subpaystatus.statusName as PaymentStatus, s.subscriberPath,
						freq.frequencyID, freq.frequencyName, freq.frequencyShortName,
						s.PCFree, s.modifiedRate, s.glAccountID, s.lastPrice, s.rootSubscriberID, 
						cast(0 as decimal(18,2)) as amtBilled, cast(0 as decimal(18,2)) as amtDue, cast(0 as decimal(18,2)) as amtPaid
					into ##tmpSubscribers
					from dbo.sub_subscribers as s
					inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
					inner join dbo.sub_types stypes on stypes.typeID = sub.typeID
					inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID
					inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
					inner join dbo.sub_rateFrequencies as subrf on subrf.RFID = s.RFID
					inner join dbo.sub_frequencies as freq on freq.frequencyID = subrf.frequencyID					
					inner join dbo.sub_rates as subrate on subrate.rateID = subrf.rateID
					inner join dbo.ams_members as m2 on m2.memberid = s.memberID
					inner join dbo.ams_members as m on m.memberid = m2.activeMemberID and m.isProtected = 0
					inner join @tblS as tblS on tblS.subscriberID = s.subscriberID
					where m.orgID = @orgID
					and m.status <> 'D';

					CREATE NONCLUSTERED INDEX IX_memberid ON ##tmpSubscribers (memberID);
					CREATE NONCLUSTERED INDEX IX_subscriberid ON ##tmpSubscribers (subscriberID);

					-- get existing invoices and populate mcSubscriberTransactions
					INSERT INTO ##mcSubscribersForAcct (subscriberID)
					SELECT distinct subscriberID
					FROM ##tmpSubscribers;

					EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select distinct st.invoiceID, st.subscriberID
					INTO ##tmpSubscribersInv
					from ##mcSubscriberTransactions as st;

					-- billed (R and O) have no transactions - amtBilled and AmtDue are the same
					-- other with no invoices - amtBilled is here but amtDue is 0
					update tmp
					set tmp.amtBilled = 
							case 
							when tmp.PCFree = 1 then 0
							when tmp.modifiedRate is not null then tmp.modifiedRate + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.modifiedRate,getdate(),ma.stateID)),0)
							else tmp.lastPrice + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.lastPrice,getdate(),ma.stateID)),0)
							end
					from ##tmpSubscribers as tmp
					left outer join dbo.ams_memberAddresses as ma
						inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
							AND matag.memberID = ma.memberID 
							and matag.addressTypeID = ma.addressTypeID
						inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
							and matagt.addressTagTypeID = matag.addressTagTypeID
							and matagt.addressTagType = 'Billing'
						on ma.orgID = @orgID
						and ma.memberID = tmp.memberID
					where tmp.SubscriptionStatusCode in ('R','O')
					OR NOT EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID);

					update ##tmpSubscribers
					set amtDue = amtBilled
					where SubscriptionStatusCode in ('R','O');

					-- other when there are invoices for the subs
					update tmp
					set tmp.amtBilled = innertmp.amtBilled,
						tmp.amtDue = innertmp.amtDue,
						tmp.amtPaid = innertmp.amtPaid
					from ##tmpSubscribers as tmp
					inner join (
						select tmp.subscriberID, 
							sum(tsFull.cache_amountAfterAdjustment) as amtBilled, 
							sum(tsFull.cache_amountAfterAdjustment)-sum(tsFull.cache_activePaymentAllocatedAmount) as amtDue,
							sum(tsFull.cache_activePaymentAllocatedAmount) as amtPaid
						from ##tmpSubscribers as tmp
						inner join ##mcSubscriberTransactions as st on st.subscriberID = tmp.subscriberID
						cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,st.transactionID) as tsFull
						where tmp.SubscriptionStatusCode not in ('R','O')
						AND EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID)
						group by tmp.subscriberID
					) as innertmp on innertmp.subscriberID = tmp.subscriberID;

					-- get results temp results
					select 
						tm.subscriberID, tm.memberid, tm.lastname as [Last Name], tm.firstname as [First Name], tm.membernumber, tm.company,
						tm.typeName, tm.subTypeUID, tm.subscriptionName, tm.subUID, tm.rateName, 
						cast(tm.subStartDate as date) as subStartDate, cast(tm.subEndDate as date) as subEndDate, cast(tm.graceEndDate as date) as graceEndDate, 
						tm.rootSubscriberID, tm.SubscriptionStatusCode, tm.SubscriptionStatus, tm.PaymentStatus, tm.subscriberPath, tm.frequencyID, tm.frequencyName, 
						tm.frequencyShortName, isnull(tm.amtBilled,0) as amtBilled, isnull(tm.amtDue,0) as amtDue, isnull(tm.amtPaid,0) as amtPaid					
						into ##tmpSubscribersTmp				
					from 
						##tmpSubscribers as tm;

					-- get results with dues data
					select * into ##tmpSubscribersDues
					from ##tmpSubscribersTmp
					where subUID = @subUID;

					select 
						dues.memberID, tmp.subTypeUID, 
						sum(tmp.amtBilled) as pastAmtBilled, sum(tmp.amtDue) as pastAmtDue, sum(tmp.amtPaid) as pastAmtPaid
						into ##tmpSubscribersPast
					from 
						##tmpSubscribersDues as dues
						left outer join ##tmpSubscribersTmp as tmp on
							tmp.memberID = dues.memberID
							and tmp.subTypeUID = @pastSubType
						inner join @qryAllFirmMembers fm on fm.childMemberID = dues.memberID
					where 
						tmp.subTypeUID is not null
					group by dues.memberID, tmp.subTypeUID;

					-- firm members
					INSERT INTO ##tmpFirmMembers (memberID, [Last Name], [First Name], MemberNumber, Company)
					SELECT DISTINCT m.memberID, m.lastName, m.firstName, m.memberNumber, m.company
					FROM @qryAllFirmMembers AS fm
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
						AND m.memberID = fm.masterMemberID 
						AND m.isProtected = 0;

					-- get members fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
						@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
						@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
						@membersTableName='##tmpFirmMembers', @membersResultTableName='##tmpFirmMembersFS', 
						@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					-- final results with member data
					select
						tmp.[Last Name], 
						tmp.[First Name], 
						tmp.MemberNumber, 
						tmp.Company as [Company], 
						tmp.subscriptionName as [Subscription1_Subscription],
						tmp.rateName as [Subscription1_RateName], 
						tmp.subStartDate as [Subscription1_StartDate], 
						tmp.subEndDate as [Subscription1_EndDate], 
						tmp.graceEndDate as [Subscription1_GraceEndDate],
						tmp.SubscriptionStatus as [Subscription1_SubscriptionStatus], 
						tmp.PaymentStatus as [Subscription1_PaymentStatus], 
						tmp.frequencyName as [Subscription1_Frequency], 
						tmp.amtBilled as [Subscription1_BilledAmount], 
						tmp.amtDue as [Subscription1_DueAmount], 
						tmp.amtPaid	as [Subscription1_PaidAmount],				
						'Past Dues' as [Subscription2_Subscription],
						tmp.pastAmtBilled as [Subscription2_BilledAmount], 
						tmp.pastAmtDue as [Subscription2_DueAmount], 
						tmp.pastAmtPaid	as [Subscription2_PaidAmount],
						isNull(tmp.amtDue,0) + isNull(tmp.pastAmtDue,0) as [Total Due],
						fmfs.*
					into ###local.tempTableName#
					from (
						select dues.*, past.pastAmtBilled, past.pastAmtDue, past.pastAmtPaid
						from ##tmpSubscribersDues as Dues
						left outer join ##tmpSubscribersPast past on dues.memberID = past.memberID
					) as tmp
					inner join @qryAllFirmMembers fm on fm.childMemberID = tmp.memberID
					inner join ##tmpFirmMembersFS as fmfs on fmfs.memberID = fm.masterMemberID
					order by fm.masterName, tmp.[Last Name], tmp.[First Name], tmp.memberNumber, tmp.subStartDate;	

					#generateFinalBCPTable(tblName="###local.tempTableName#", dropFields="memberID")#

					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>
					IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
						DROP TABLE ##tmpSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersTmp') IS NOT NULL
						DROP TABLE ##tmpSubscribersTmp;
					IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
						DROP TABLE ##tmpSubscribersInv;
					IF OBJECT_ID('tempdb..##tmpSubscribersDues') IS NOT NULL
						DROP TABLE ##tmpSubscribersDues;
					IF OBJECT_ID('tempdb..##tmpSubscribersPast') IS NOT NULL
						DROP TABLE ##tmpSubscribersPast;							
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;
					IF OBJECT_ID('tempdb..##tmpFirmMembers') IS NOT NULL
						DROP TABLE ##tmpFirmMembers;
					IF OBJECT_ID('tempdb..##tmpFirmMembersFS') IS NOT NULL
						DROP TABLE ##tmpFirmMembersFS;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>			

			<cfset local.arrInitialReportSort = arrayNew(1) />
			<cfset local.strTemp = { field='Last Name', dir='asc' } />
			<cfset arrayAppend(local.arrInitialReportSort,local.strTemp) />
			<cfset local.strTemp = { field='First Name', dir='asc' } /> 
			<cfset arrayAppend(local.arrInitialReportSort,local.strTemp) />
			<cfset local.strTemp = { field='MemberNumber', dir='asc' } /> 
			<cfset arrayAppend(local.arrInitialReportSort,local.strTemp) />
			<cfset local.strReportQry = { qryReportFields=local.qryData, strQryResult=local.qryDataResult }>
			<cfset local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML)>
		
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>	
	
	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew() />
		<cfset local.strFields = structNew() />
		<cfset local.strFields.frmstartfrom = { label="Subscription Start Date Start", value=arguments.event.getValue('frmStartFrom','') }/>
		<cfset local.strFields.frmstartto = { label="Subscription Start Date End", value=arguments.event.getValue('frmStartTo','') }/>
		<cfset local.strFields.frmendfrom = { label="Subscription End Date Start", value=arguments.event.getValue('frmEndFrom','') }/>
		<cfset local.strFields.frmendto = { label="Subscription End Date End", value=arguments.event.getValue('frmEndTo','') }/>
		<cfset local.strFields.headerimgfilename = { label="Header Image Filename", value=arguments.event.getValue('headerImgFileName','') }/>
		<cfset local.strFields.pricingimgfilename = { label="Pricing Image Filename", value=arguments.event.getValue('pricingImgFileName','') }/>
		<cfset local.strFields.reportheadingtext = { label="Report Heading Text", value=arguments.event.getValue('reportHeadingText','') }/>
		<cfset local.strFields.reportfootertext = { label="Report Footer Text", value=arguments.event.getValue('reportFooterText','') }/>
		<cfset local.strFields.frmstatementdt = { label="Statement Date", value=arguments.event.getValue('frmStatementDt','') }/>

		<cfset reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event) />
		
		<cfreturn returnAppStruct('','echo') />
	</cffunction>

</cfcomponent>