ALTER PROC dbo.queue_SubscriptionBilled_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpHoldingReprocessConditionsBySubscriberBulk (orgID int INDEX IX_tmpHolding_orgID, memberID int, subTypeID int, subscriptionID int);

	DECLARE @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionBilled', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	-- which itemGroupUIDs are all done
	INSERT INTO #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_subscriptionBilled
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionBilled
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;

	WITH allSubscribers AS (
		SELECT qi.siteID, qi.subscriberID
		FROM dbo.queue_subscriptionBilled as qi
		INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID
			UNION ALL
		SELECT alls.siteID, s.subscriberID
		FROM membercentral.dbo.sub_subscribers as s
		INNER JOIN allSubscribers as alls on alls.subscriberID = s.parentSubscriberID
	)
	INSERT INTO #tmpHoldingReprocessConditionsBySubscriberBulk (orgID, memberID, subTypeID, subscriptionID)
	SELECT DISTINCT s.orgID, m.activeMemberID, ssub.typeID, ssub.subscriptionID
	FROM allSubscribers as tmp
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmp.siteID
	INNER JOIN membercentral.dbo.sub_subscribers as sub on sub.orgID = s.orgID and sub.subscriberID = tmp.subscriberID
	INNER JOIN membercentral.dbo.sub_subscriptions as ssub on ssub.orgID = s.orgID and ssub.subscriptionID = sub.subscriptionID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = sub.memberID;

	-- call the bulk queue processing
	EXEC membercentral.dbo.sub_reprocessConditionsBySubscriberBulk;

	DELETE qi
	from dbo.queue_subscriptionBilled as qi
	inner join #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
