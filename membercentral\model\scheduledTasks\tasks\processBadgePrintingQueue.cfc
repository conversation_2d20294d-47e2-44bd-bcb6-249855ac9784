<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfscript>
				local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="20" } ];
				local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
			</cfscript>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.objEvents = CreateObject("component","model.admin.events.event")>

		<cftry>
			<cfstoredproc procedure="queue_badgePrinting_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryRegistrants" resultset="1">
			</cfstoredproc>

			<cfloop query="local.qryRegistrants">
				<cfset local.thisItemID = local.qryRegistrants.itemID>

				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. 												--->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and 	--->
				<!--- those items may be grabbed by another job, causing it to possible be processed twice.	--->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_badgePrinting as qi
					inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">
					and qs.queueStatus = 'grabbedForProcessing'
				</cfquery>

				<cfif local.checkItemID.itemCount>
					<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @statusProcessing int;
						EXEC dbo.queue_getStatusIDbyType @queueType='badgePrinting', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

						UPDATE dbo.queue_badgePrinting
						SET statusID = @statusProcessing,
							dateUpdated = GETDATE()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;
					</cfquery>

					<cfset local.printBadge = local.objEvents.printRegistrantBadge(siteID=local.qryRegistrants.siteID, siteCode=local.qryRegistrants.siteCode,
							registrantID=local.qryRegistrants.registrantID, deviceID=local.qryRegistrants.deviceID, templateContent=local.qryRegistrants.templateContent)>
					
					<cfquery name="local.qryRemoveFromQueue" datasource="#application.dsn.platformQueue.dsn#">
						DELETE FROM dbo.queue_badgePrinting
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">
					</cfquery>
				</cfif>
			</cfloop>
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_badgePrinting;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>