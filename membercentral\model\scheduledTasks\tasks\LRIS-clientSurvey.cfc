<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="30" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<!--- Process queue --->
		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="setSettings" access="private" output="false" returntype="struct">
		<cfargument name="referralID" type="numeric" required="true">

		<cfset var local = structnew()>
		
		<cfset local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals')>
		<cfset local.qryGetSiteInfo = getSiteInfo(referralID=arguments.referralID)>
		<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(local.qryGetSiteInfo.sitecode).mainhostname>
		<cfset local.siteResourceID = local.objAdminReferrals.getReferralSettings(local.qryGetSiteInfo.siteID).siteResourceID>
		<cfset local.applicationInstanceID = local.objAdminReferrals.getReferralSettings(local.qryGetSiteInfo.siteID).applicationInstanceID>
		<cfset local.emailRecipient = local.objAdminReferrals.getReferralSettings(local.qryGetSiteInfo.siteID).emailRecipient>

		<cfif len(local.emailRecipient)>
			<cfset local.emailAll = replace(local.emailRecipient,";",",","all")>
			<cfset local.sendFrom = trim(listFirst(local.emailRecipient,";"))>
		<cfelse>
			<cfset local.emailAll = "">
			<cfset local.sendFrom = application.objSiteInfo.getSiteInfo(local.qryGetSiteInfo.sitecode).supportProviderEmail>
		</cfif>

		<cfset local.referralID = local.objAdminReferrals.getReferralSettings(local.qryGetSiteInfo.siteID).referralID>
		<cfset local.qryGetAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=local.qryGetSiteInfo.orgID)>
		<cfset local.qryResultFieldsetID = local.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.siteResourceID, area='referralresult')>		
		<cfset local.xmlResultFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryResultFieldsetID.fieldsetID), usage="referralresult")>
		
		<cfreturn local>
	</cffunction>
	
	<cffunction name="getSiteInfo" access="private" output="false" returntype="query">
		<cfargument name="referralID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryGetSiteInfo" datasource="#application.dsn.membercentral.dsn#">
			select s.siteID, o.orgID, s.siteName, s.siteCode, o.orgCode, oi.organizationName as orgName, r.referralID, r.clientFeeMemberID, 
				s.showCurrencyType, r.applicationInstanceID, s.defaultTimeZoneID
			from dbo.sites as s
			inner join dbo.organizations as o on o.orgID = s.orgID
			inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			inner join dbo.cms_applicationInstances as ai on s.siteID = ai.siteID
			inner join dbo.cms_siteResources sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join 	dbo.ref_referrals r on r.applicationInstanceID = ai.applicationInstanceID	
				and r.referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfreturn local.qryGetSiteInfo>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_clientSurveys_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryGetCaseDataAll" resultset="1">
			</cfstoredproc>

			<cfquery name="local.qryDistinctItems" dbtype="query">
				select distinct itemID
				from [local].qryGetCaseDataAll
			</cfquery>
			<cfset local.returnStruct.itemCount = local.qryDistinctItems.recordCount>
			
			<cfoutput query="local.qryGetCaseDataAll" group="itemID">
				<cfset local.thisItemID = local.qryGetCaseDataAll.itemID>
				<cfset local.thisStr = setSettings(referralID=local.qryGetCaseDataAll.referralID)>
				
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_clientSurveys as qi
					INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItemID#">
					AND qs.queueStatus = 'grabbedForProcessing'
				</cfquery>		
				
				<cfif local.checkItemID.itemCount>
					<cftry>
						<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @statusProcessing int;
							EXEC dbo.queue_getStatusIDbyType @queueType='clientSurveys', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

							UPDATE dbo.queue_clientSurveys
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItemID#">;
						</cfquery>
			
						<!--- First, get retained cases --->
						<cfquery name="local.qryGetCaseData" dbtype="query">
							select * 
							from [local].qryGetCaseDataAll  
							where isRetainedCase = 1 
							and emailSent = 0
							and referralID = <cfqueryparam value="#local.thisStr.referralID#" cfsqltype="cf_sql_integer">
							and itemID = <cfqueryparam value="#local.thisItemID#" cfsqltype="cf_sql_integer">
						</cfquery>			
		
						<!--- SEND Retained Case E-mail --->						
 						<cfset doSendCaseEmail(qryGetCaseData=local.qryGetCaseData, itemID=local.thisItemID, thisStr=local.thisStr)>
						
						<!--- Second, get not retained cases --->
						<cfquery name="local.qryGetCaseData" dbtype="query">
							select * 
							from [local].qryGetCaseDataAll  
							where isRetainedCase = 0 
							and emailSent = 0
							and referralID = <cfqueryparam value="#local.thisStr.referralID#" cfsqltype="cf_sql_integer">
							and itemID = <cfqueryparam value="#local.thisItemID#" cfsqltype="cf_sql_integer">
						</cfquery>				
								
						<cfif local.qryGetCaseData.recordCount>	
							<!--- SEND NOT Retained Case E-mail --->						
 							<cfset doSendReferralEmail(qryGetCaseData=local.qryGetCaseData, itemID=local.thisItemID, thisStr=local.thisStr)>
						</cfif>	
				
						<!--- ------------- --->
						<!--- UPDATE STATUS --->
						<!--- ------------- --->
						<cfset updateItemStatus(itemID=local.thisItemID)>
						
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
							<cfset local.success = false>
						</cfcatch>
					</cftry>					
				
				</cfif>								
			
			</cfoutput> <!--- // group by itemID --->
					
			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_clientSurveys_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
			</cftry>																				
		
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStruct.success = false>
			</cfcatch>
		</cftry>						
					
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="updateItemStatus" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		
		<cfset var updateStatus = "">

		<cfquery name="updateStatus" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @statusDone int;
			EXEC dbo.queue_getStatusIDbyType @queueType='clientSurveys', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

			UPDATE dbo.queue_clientSurveys
			SET statusID = @statusDone,
				dateUpdated = getdate()
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>	
	
	<cffunction name="updateDetailEmailStatus" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="clientReferralID" type="numeric" required="true">
		
		<cfset var updateEmailSent = "">

		<cfquery name="updateEmailSent" datasource="#application.dsn.platformQueue.dsn#">
			update dbo.queue_clientSurveysDetail
			set emailSent = 1
			where itemID = <cfqueryparam value="#arguments.itemID#" cfsqltype="cf_sql_integer">
			and clientReferralID = 	<cfqueryparam value="#arguments.clientReferralID#" cfsqltype="cf_sql_integer">
		</cfquery>	
	</cffunction>	
	
	<cffunction name="doSendReferralEmail" access="private" output="false" returntype="void">
		<cfargument name="qryGetCaseData" type="query" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="thisStr" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.qryGetCaseData = arguments.qryGetCaseData>
		<cfset local.itemID = arguments.itemID>
		<cfset local.thisStr = arguments.thisStr>
		<cfset local.qryGetSiteInfo = local.thisStr.qryGetSiteInfo>
		<cfset local.referralEmailSent = 0>
		<cfset local.thisHostname = local.thisStr.thisHostname>

		<cfoutput query="local.qryGetCaseData" group="itemID">																		
			<cfset local.thisClientID = val(local.qryGetCaseData.clientID) />
			<cfif val(local.qryGetCaseData.clientParentID)>
				<cfset local.thisClientID = val(local.qryGetCaseData.clientParentID)/>
			</cfif>

			<cfset local.thisClientEmail = trim(local.qryGetCaseData.email) />
			<cfif not len(local.thisClientEmail)>
				<cfset local.thisClientEmail = trim(local.qryGetCaseData.repEmail) />
			</cfif>
			
			<cfset local.referralEmailSent = 0 />

			<cfif len(local.thisClientEmail)>
				
				<cfoutput group="firstName">			
					<cftry>							
						<cfset local.fromName = local.qryGetSiteInfo.orgname>
						<cfset local.fromEmail = '<EMAIL>'>
						<cfset local.emailAll = local.thisStr.sendFrom>
						<cfset local.replyToEmail = "">
						<cftry>
							<cfif find(";",local.thisStr.sendFrom)>
								<cfset local.emailAll = replace(local.thisStr.sendFrom,";",",","all") />
								<cfset local.replyToEmail = trim(listFirst(local.thisStr.sendFrom,";")) />
							<cfelseif find(",",local.thisStr.sendFrom)>
								<cfset local.replyToEmail = trim(listFirst(local.thisStr.sendFrom,",")) />
							<cfelse>
								<cfset local.replyToEmail = trim(local.thisStr.sendFrom) />
							</cfif>
							<cfif NOT len(local.replyToEmail) or NOT isValid("regex",local.replyToEmail,application.regEx.email)>
								<cfthrow />
							</cfif>
							<cfcatch type="any">
								<cfset local.replyToEmail = application.objSiteInfo.getSiteInfo(local.qryGetSiteInfo.sitecode).supportProviderEmail>
							</cfcatch>
						</cftry> 	

						<cfset local.toEmailList = "">
						<cftry>
							<cfif len(trim(local.thisClientEmail)) and isValid("regex",local.thisClientEmail,application.regEx.email)>
								<cfset local.toEmailList = local.thisClientEmail>
							<cfelse>
								<cfthrow />
							</cfif>
							<cfif len(trim(local.emailAll)) and isValid("regex",local.emailAll,application.regEx.email)>
								<cfset local.toEmailList = ListAppend(local.toEmailList, local.emailAll)>
							<cfelse>
								<cfthrow />								
							</cfif>
							<cfcatch type="any">
								<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
							</cfcatch>							
						</cftry>

						<cfset local.thisStr.callUID = local.qryGetCaseData.callUID />
						<cfset local.thisStr.emailContent = local.qryGetCaseData.emailContent />
						<cfset local.thisStr.thisClientID = local.thisClientID />
						<cfset local.emailContent = getSurveyContent(thisStr=local.thisStr) />						
			
						<cfset local.sednEmailStr = local.thisStr.objAdminReferrals.sendReferralEmail(
							referralID=local.qryGetSiteInfo.referralID,
							siteid=local.qryGetSiteInfo.siteID,
							recordedByMemberID=0,
							messageContent=local.emailContent,
							contentTitle="Client Survey Email",
							fromName=local.fromName,
							fromEmail=local.fromEmail,
							replyToEmail=local.replyToEmail,
							subject=local.qryGetSiteInfo.orgname & " " & local.qryGetCaseData.emailSubject,
							refMemberID=local.qryGetSiteInfo.clientFeeMemberID,
							refMemberName=local.qryGetCaseData.clientName, 
							refMemberEmail=local.toEmailList,
							messageTypeCode="CLIENTSURVEYS")>					
							
						<cfset local.referralEmailSent = 1 />
						<cfcatch type="any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
						</cfcatch>
					</cftry>
				</cfoutput>	<!---// cfoutput group="firstName" --->
			
			</cfif>		<!--- //if len(local.thisClientEmail) --->		
			
			<cfif local.referralEmailSent>	
				<cfoutput group="clientReferralID">								
					<cfset updateDetailEmailStatus(itemID=local.itemID, clientReferralID=clientReferralID) />									
				</cfoutput>
			</cfif>																				
				
		</cfoutput>	<!---// cfoutput query="local.qryGetCaseData" group="itemID" --->

	</cffunction>		
	
	<cffunction name="doSendCaseEmail" access="private" output="false" returntype="void">
		<cfargument name="qryGetCaseData" type="query" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="thisStr" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.qryGetCaseData = arguments.qryGetCaseData>
		<cfset local.itemID = arguments.itemID>
		<cfset local.thisStr = arguments.thisStr>
		<cfset local.qryGetSiteInfo = local.thisStr.qryGetSiteInfo>
		<cfset local.caseEmailSent = 0 />
		<cfset local.thisHostname = local.thisStr.thisHostname>	

		<!--- loop per case --->				
		<cfloop query="local.qryGetCaseData">

			<cfset local.thisClientID = val(local.qryGetCaseData.clientID) />
			<cfif val(local.qryGetCaseData.clientParentID)>
				<cfset local.thisClientID = val(local.qryGetCaseData.clientParentID)/>
			</cfif>

			<cfset local.thisClientEmail = trim(local.qryGetCaseData.email) />
			<cfif not len(local.thisClientEmail)>
				<cfset local.thisClientEmail = trim(local.qryGetCaseData.repEmail) />
			</cfif>
			
			<cfset local.caseEmailSent = 0 />			

			<cfif len(local.thisClientEmail)>
			
				<cftry>
					<cfset local.fromName = local.qryGetSiteInfo.orgname>
					<cfset local.fromEmail = '<EMAIL>'>
					<cfset local.emailAll = local.thisStr.sendFrom>
					<cfset local.replyToEmail = "">
					<cftry>
						<cfif find(";",local.thisStr.sendFrom)>
							<cfset local.emailAll = replace(local.thisStr.sendFrom,";",",","all") />
							<cfset local.replyToEmail = trim(listFirst(local.thisStr.sendFrom,";")) />
						<cfelseif find(",",local.thisStr.sendFrom)>
							<cfset local.replyToEmail = trim(listFirst(local.thisStr.sendFrom,",")) />
						<cfelse>
							<cfset local.replyToEmail = trim(local.thisStr.sendFrom) />
						</cfif>
						<cfif NOT len(local.replyToEmail) or NOT isValid("regex",local.replyToEmail,application.regEx.email)>
							<cfthrow />
						</cfif>
						<cfcatch type="any">
							<cfset local.replyToEmail = application.objSiteInfo.getSiteInfo(local.qryGetSiteInfo.sitecode).supportProviderEmail>
						</cfcatch>
					</cftry> 	

					<cfset local.toEmailList = "">
					<cftry>
						<cfif len(trim(local.thisClientEmail)) and isValid("regex",local.thisClientEmail,application.regEx.email)>
							<cfset local.toEmailList = local.thisClientEmail>
						<cfelse>
							<cfthrow />
						</cfif>
						<cfif len(trim(local.emailAll)) and isValid("regex",local.emailAll,application.regEx.email)>
							<cfset local.toEmailList = ListAppend(local.toEmailList, local.emailAll)>
						<cfelse>
							<cfthrow />								
						</cfif>
						<cfcatch type="any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>							
					</cftry>

					<cfset local.thisStr.callUID = local.qryGetCaseData.callUID />
					<cfset local.thisStr.emailContent = local.qryGetCaseData.emailContent />
					<cfset local.thisStr.thisClientID = local.thisClientID />
					<cfset local.emailContent = getSurveyContent(thisStr=local.thisStr) />				
		
 					<cfset local.sednEmailStr = local.thisStr.objAdminReferrals.sendReferralEmail(
						referralID=local.qryGetSiteInfo.referralID,
						siteid=local.qryGetSiteInfo.siteID,
						recordedByMemberID=0,
						messageContent=local.emailContent,
						contentTitle="Client Survey Email",
						fromName=local.fromName,
						fromEmail=local.fromEmail,
						replyToEmail=local.replyToEmail,
						subject=local.qryGetSiteInfo.orgname & " " & local.qryGetCaseData.emailSubject,
						refMemberID=local.qryGetSiteInfo.clientFeeMemberID,
						refMemberName=local.qryGetCaseData.clientName, 
						refMemberEmail=local.toEmailList,
						messageTypeCode="CLIENTSURVEYS")>
							
					<cfset local.caseEmailSent = 1 />
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
					</cfcatch>
				</cftry>
				
 			</cfif>
	
			<cfif local.caseEmailSent>								
				<cfset updateDetailEmailStatus(itemID=local.itemID, clientReferralID=local.qryGetCaseData.clientReferralID) />
			</cfif>		
			
		</cfloop>	<!--- //loop query="local.qryGetCaseData | Retained  --->

	</cffunction>
	
	<cffunction name="getSurveyContent" output="false" returntype="string" hint="">
		<cfargument name="thisStr" type="struct" required="true">		
		
		<cfset var local = structNew()>
		<cfset local.thisStr = arguments.thisStr>
		<cfset local.qryGetSiteInfo = local.thisStr.qryGetSiteInfo>
		
		<cfset local.siteID = local.qryGetSiteInfo.siteID>
		<cfset local.orgID = local.qryGetSiteInfo.orgID>
		<cfset local.sitename = local.qryGetSiteInfo.sitename>
		<cfset local.siteCode = local.qryGetSiteInfo.siteCode>
		<cfset local.orgcode = local.qryGetSiteInfo.orgcode>
		<cfset local.showCurrencyType = local.qryGetSiteInfo.showCurrencyType>
		<cfset local.defaultCurrencyType = 	application.objSiteInfo.getSiteInfo(local.siteCode).defaultCurrencyType>
		<cfset local.mainhostname = application.objSiteInfo.getSiteInfo(local.siteCode).mainhostname>
		<cfset local.scheme = application.objSiteInfo.getSiteInfo(local.siteCode).scheme>
		
 		<cfset local.objAdminReferrals	= local.thisStr.objAdminReferrals>
		<cfset local.objReferrals = CreateObject("component","model.referrals.referrals")>
		<cfset local.objAppBaseLink = CreateObject('component', 'model.apploader')>
		<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=local.qryGetSiteInfo.defaultTimeZoneID)>		
		
 		<cfset local.callUID = local.thisStr.callUID />
		<cfset local.referralID = local.qryGetSiteInfo.referralID>
		<cfset local.applicationInstanceID = local.qryGetSiteInfo.applicationInstanceID>

		<cfset local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=local.orgID,callUID=local.callUID)>
		<cfset local.thisClientID = val(local.qryGetReferralData.clientParentID)>
		<cfif (not local.thisClientID)>
			<cfset local.thisClientID = val(local.qryGetReferralData.clientID)>
		</cfif>
		<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.qryGetReferralData.clientID)>
		<cfset local.questionAnswerPath = local.objAdminReferrals.getSearchXMLByClientID(local.thisClientID)>
		<cfset local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID)>
		<cfset local.memberLocalLink = "/?" & local.appBaseLink >
		<cfset local.memberExternalLink = local.scheme & "://" & local.mainhostname & local.memberLocalLink>
		
		<cfset local.renderedEmailContent = ''>		
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=local.siteid)>
		<cfset local.thisPostTypeFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=local.siteID, resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.arrThisPostTypeFields = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren>
		<cfset local.arrClientRefCustomFields = local.objReferrals.getClientReferralCustomFieldWithData(itemID=local.qryGetReferralData.clientReferralID, itemType='ClientRefCustom', arrResourceFields=local.arrThisPostTypeFields, objCustomFields=local.objResourceCustomFields)>
		<cfloop query="local.qryGetReferralData">
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />
			<cfset local.panelid1 = "" />
			
			<cfloop query="local.qryGetReferralFilterData">
				<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
					<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
						<cfcase value="panelid1">
							<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
							<cfset local.panelid1 = local.qryGetPanelInfo.name />
						</cfcase>
						<cfcase value="subpanelid1">
							<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
								<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
								<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
							</cfloop>
						</cfcase>
					</cfswitch>
				</cfif>
			</cfloop>
			<cfset local.referraldata = { 
				referralID=local.qryGetSiteInfo.referralID
				,siteID = local.siteID
				,sitename = local.sitename
				,orgId = local.orgID
				,orgcode = local.orgcode
				,siteCode = local.siteCode
				,clientID = local.thisClientID
				,callUID = local.callUID
				,clientFirstName = local.qryGetReferralData.firstName
				,clientLastName = local.qryGetReferralData.lastName
				,clientFullName = local.qryGetReferralData.firstName &' '&local.qryGetReferralData.middleName &' '&local.qryGetReferralData.lastName
				,memberFirstName = local.qryGetReferralData.memFirstName
				,memberLastName = local.qryGetReferralData.memLastname
				,memberFullName = local.qryGetReferralData.memFirstName &' '&local.qryGetReferralData.memMiddleName &' '&local.qryGetReferralData.memLastname
				,memberMemberNumber = local.qryGetReferralData.memberNumber
				,primaryPanelName = local.panelid1
				,clientEmail = local.qryGetReferralData.email
				,clientHomePhone= local.qryGetReferralData.homePhone
				,clientCellPhone= local.qryGetReferralData.cellPhone
				,clientAlternatePhone= local.qryGetReferralData.alternatePhone
				,clientBusiness = local.qryGetReferralData.businessName
				,clientAddress1 = local.qryGetReferralData.address1
				,clientAddress2 = local.qryGetReferralData.address2
				,clientCity = local.qryGetReferralData.city
				,clientState = local.qryGetReferralData.clientState
				,clientZipCode = local.qryGetReferralData.postalCode
				,representativeId= local.qryGetReferralData.repID
				,repFirstName= local.qryGetReferralData.repFirstName
				,repLastName= local.qryGetReferralData.repLastName
				,repEmail= local.qryGetReferralData.repEmail
				,repHomePhone= local.qryGetReferralData.repHomePhone
				,repCellPhone= local.qryGetReferralData.repCellPhone
				,repAlternatePhone= local.qryGetReferralData.repAlternatePhone
				,interviewerName= local.qryGetReferralData.interviewerName
				,clientReferralID= local.qryGetReferralData.clientReferralID
				,referralDate=  dateFormat(local.qryGetReferralData.clientReferralDate,"mm/dd/yyyy") 
				,regTimeZoneName= local.regTimeZoneName
				,refIssueDesc= local.qryGetReferralData.issueDesc
				,memberExternalLink= local.memberExternalLink
				,questionAnswerPath = local.questionAnswerPath
				,transactionDate = ''
				,showCurrencyType = local.showCurrencyType 
				,defaultCurrencyType = local.defaultCurrencyType
			}>
			<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
				<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
					<cfoutput>
					<cfif structKeyExists(local.thisField, "value")>
						<cfif isArray(local.thisField.value)>
							<cfloop array="#local.thisField.value#" index="local.thisItem">
								#local.thisItem#
							</cfloop>
						<cfelse>
							#local.thisField.value#
						</cfif>
					</cfif>
					</cfoutput>
				</cfsavecontent>
			</cfloop>
			<cfset local.content = local.thisStr.emailContent>
			<cfset StructInsert(local.referraldata,'strClientReferralGrid','',true)>
			<cfset StructInsert(local.referraldata,'strClientPaymentTable','',true)>
			<cfset local.strClientReferralGrid = local.objAdminReferrals.prepareClientReferralGrid(referraldata = local.referraldata)>
			<cfset local.referraldata.strClientReferralGrid = local.strClientReferralGrid>			
			<cfset local.strArgs = { content=local.content, referraldata=local.referraldata, orgcode=local.orgcode, sitecode=local.siteCode}>			
			<cfset local.renderedEmailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>			
			<cfbreak>
		</cfloop>

		<cfreturn local.renderedEmailContent>		
	</cffunction>	
	
</cfcomponent>