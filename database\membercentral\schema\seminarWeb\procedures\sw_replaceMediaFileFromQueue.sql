ALTER PROC dbo.sw_replaceMediaFileFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL
		DROP TABLE #tmpEnrollments;
	IF OBJECT_ID('tempdb..#tmpEnrollmentLogAccessFiles') IS NOT NULL
		DROP TABLE #tmpEnrollmentLogAccessFiles;
	CREATE TABLE #tmpEnrollments (enrollmentID int PRIMARY KEY, completed bit);
	CREATE TABLE #tmpEnrollmentLogAccessFiles (fileLogAccessID int PRIMARY KEY, logAccessID int, enrollmentID int, accessDetails varchar(max), newAccessDetails varchar(max));

	declare @statusGrabbed int, @statusProcessing int, @itemStatus int, @orgID int, @siteID int, @participantID int,
		@seminarID int, @titleID int, @oldFileID int, @newFileID int, @oldVideoDurationInSeconds int, @newVideoDurationInSeconds int,
		@recordedByMemberID int, @oldVideoAccessDetailsLength int, @newVideoAccessDetailsLength int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SWReplaceMediaFile', @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SWReplaceMediaFile', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;
	
	select @orgID = qid.orgID, @siteID = qid.siteID, @seminarID = qid.seminarID, @participantID = qid.participantID, @titleID = qid.titleID, 
		@oldFileID = qid.oldFileID, @newFileID = qid.newFileID, @recordedByMemberID = qid.recordedByMemberID,
		@itemStatus = qid.statusID, @oldVideoDurationInSeconds = fo.duration, @newVideoDurationInSeconds = fn.duration
	from platformQueue.dbo.queue_SWReplaceMediaFile AS qid
	inner join dbo.tblFiles as fo on fo.fileID = qid.oldFileID
	inner join dbo.tblFiles as fn on fn.fileID = qid.newFileID
	where qid.itemID = @itemID;

	-- if itemID is not grabbedForProcessing, kick out now
	IF @itemStatus <> @statusGrabbed
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_SWReplaceMediaFile
	SET statusID = @statusProcessing,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- accessDetails value will be like 11000, where each 1 is 15 seconds
	INSERT INTO #tmpEnrollmentLogAccessFiles (fileLogAccessID, logAccessID, enrollmentID, accessDetails)
	SELECT af.fileLogAccessID, af.logAccessID, e.enrollmentID, af.accessDetails
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblLogAccessSWOD AS a ON a.enrollmentID = e.enrollmentID
	INNER JOIN dbo.tblLogAccessSWODFiles AS af ON af.logAccessID = a.logAccessID
	INNER JOIN dbo.tblFiles AS f ON f.fileID = af.fileID
	WHERE e.seminarID = @seminarID
	AND e.participantID = @participantID
	AND e.isActive = 1
	AND f.fileID = @oldFileID;

	-- these enrollments have completed viewing the media file in full.
	INSERT INTO #tmpEnrollments (enrollmentID, completed)
	SELECT DISTINCT enrollmentID, 1
	FROM #tmpEnrollmentLogAccessFiles
	WHERE RIGHT(accessDetails,1) = 1;

	-- enrollments not completed
	INSERT INTO #tmpEnrollments (enrollmentID, completed)
	SELECT DISTINCT enrollmentID, 0
	FROM #tmpEnrollmentLogAccessFiles
		EXCEPT
	SELECT enrollmentID, 0
	FROM #tmpEnrollments
	WHERE completed = 1;


	SET @oldVideoAccessDetailsLength = LEN(REPLICATE(1,CEILING(CAST(@oldVideoDurationInSeconds AS decimal)/15)));
	SET @newVideoAccessDetailsLength = LEN(REPLICATE(1,CEILING(CAST(@newVideoDurationInSeconds AS decimal)/15)));


	-- update newAccessDetails
	IF @oldVideoAccessDetailsLength = @newVideoAccessDetailsLength
		UPDATE #tmpEnrollmentLogAccessFiles
		SET newAccessDetails = accessDetails;
	ELSE
		UPDATE laf
		SET laf.newAccessDetails = CASE WHEN tmp.completed = 1 THEN 
										CASE WHEN @newVideoAccessDetailsLength > @oldVideoAccessDetailsLength 
											THEN LEFT(laf.accessDetails,@oldVideoAccessDetailsLength) + RIGHT(laf.accessDetails,@newVideoAccessDetailsLength - @oldVideoAccessDetailsLength)
											ELSE LEFT(laf.accessDetails,@newVideoAccessDetailsLength)
										END
									ELSE
										CASE WHEN @newVideoAccessDetailsLength > @oldVideoAccessDetailsLength 
											THEN LEFT(laf.accessDetails,@oldVideoAccessDetailsLength) + REPLICATE(0,@newVideoAccessDetailsLength - @oldVideoAccessDetailsLength)
											ELSE LEFT(laf.accessDetails,@newVideoAccessDetailsLength)
										END
									END
		FROM #tmpEnrollmentLogAccessFiles AS laf
		INNER JOIN #tmpEnrollments AS tmp ON tmp.enrollmentID = laf.enrollmentID
		WHERE ISNULL(laf.accessDetails,'') <> '';


	BEGIN TRAN;
		IF EXISTS (SELECT 1 FROM #tmpEnrollmentLogAccessFiles) BEGIN
			UPDATE laf
			SET laf.fileID = @newFileID,
				laf.accessDetails = tmp.newAccessDetails
			FROM dbo.tblLogAccessSWODFiles AS laf
			INNER JOIN #tmpEnrollmentLogAccessFiles AS tmp ON tmp.fileLogAccessID = laf.fileLogAccessID;

			-- audit log
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			VALUES ('{ "c":"auditLog", "d": {
				"AUDITCODE":"SW",
				"ORGID":' + cast(@orgID as varchar(10)) + ',
				"SITEID":' + cast(@siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('File [' + cast(@newFileID as varchar(10)) + '] carried the registrant progress of the old file [' + CAST(@oldFileID AS varchar(10)) + '].'),'"','\"') + '" } }');
		END
	
		DELETE FROM platformQueue.dbo.queue_SWReplaceMediaFile
		WHERE itemID = @itemID;
	COMMIT TRAN;

	on_done:

	IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL
		DROP TABLE #tmpEnrollments;
	IF OBJECT_ID('tempdb..#tmpEnrollmentLogAccessFiles') IS NOT NULL
		DROP TABLE #tmpEnrollmentLogAccessFiles;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
