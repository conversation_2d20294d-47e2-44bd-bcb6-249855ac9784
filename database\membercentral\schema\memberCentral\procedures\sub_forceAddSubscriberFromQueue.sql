ALTER PROC dbo.sub_forceAddSubscriberFromQueue
@itemID int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAddOnSubs') IS NOT NULL 
		DROP TABLE #tmpAddOnSubs;
	CREATE TABLE #tmpAddOnSubs(subscriberID int, PCFree bit);

	DECLARE @queueTypeID int, @statusReady int, @statusProcessing int, @statusNotify int, @itemStatus int, 
		@siteID int, @orgID int, @subscriberID int, @subscriptionID int, @recordedByMemberID int, @memberID int, 
		@rootSubscriptionID int, @subscriptionName varchar(300), @isRenewalRate bit, @rootFrequencyID int, 
		@typeID int, @typeName varchar(100), @rateID int, @resultMessage varchar(max), @rateAmt decimal(18,2), 
		@numInstallments int, @rfid int, @rateGLAccountID int, @parentSubscriberID int, @parentSubscriptionID int,
		@addonID int, @maxAllowed smallint, @useAcctCodeInSet bit, @PCNum smallint, @PCPctOffEach smallint,
		@numFree int, @addonSubsCount int, @rateTotal decimal(18,2), @pcFree int, @GLAccountID int, @status varchar(1),
		@subStartDate datetime, @subEndDate datetime, @graceEndDate dateTime, @activationOptionCode varchar(1),
		@newSubscriberID int, @allowRateGLAccountOverride int, @paidStatusID int, @rootSubscriberID int,
		@recogStartDate datetime, @recogEndDate datetime;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='subscriptionForceAdd', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusNotify OUTPUT;

	-- if itemID is not readyToProcess, kick out now
	select @itemStatus = statusID, @subscriberID = subscriberID, @subscriptionID = subscriptionID, @recordedByMemberID = recordedByMemberID 
	from platformQueue.dbo.queue_subscriptionForceAdd 
	where itemID = @itemID;
	
	IF @itemStatus <> @statusReady OR @subscriberID is null
		GOTO on_done;

	select @siteID = s.siteID, @orgID = s.orgID, @rootSubscriptionID = subs.subscriptionID, @subscriptionName = subsToAdd.subscriptionName, 
		@typeID = tToAdd.typeID, @typeName = tToAdd.typeName, @memberID = mActive.memberID, @rateID = r.rateID, 
		@isRenewalRate = r.isRenewalRate, @rootFrequencyID = rf.frequencyID
	from dbo.sub_subscribers as ss 
	inner join dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join dbo.sub_rates as r on r.rateID = rf.rateID
	inner join dbo.ams_members as m on m.memberID = ss.memberID
	inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	inner join dbo.sub_subscriptions as subs on subs.subscriptionID = ss.subscriptionID
	inner join dbo.sub_types as t on t.typeID = subs.typeID
	inner join dbo.sites as s on s.siteID = t.siteID
	inner join dbo.sub_subscriptions as subsToAdd on subsToAdd.subscriptionID = @subscriptionID
	inner join dbo.sub_types as tToAdd on subsToAdd.typeID = tToAdd.typeID
	where ss.subscriberID = @subscriberID;

	-- set to processingSubscriber
	UPDATE platformQueue.dbo.queue_subscriptionForceAdd
	SET statusID = @statusProcessing
	WHERE itemID = @itemID;
	
	-- check if already added
	IF EXISTS (
		select ss.subscriberID
		from dbo.sub_subscribers ss
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
			and ss.subscriptionID = @subscriptionID
			and ss.rootSubscriberID = @subscriberID
		inner join dbo.sub_statuses st on st.statusID = ss.statusID 
		where st.statusCode not in ('D','X')) 
	BEGIN
		SET @resultMessage = 'Already Has Subscription';
		GOTO on_done;
	END

	-- most exclusive rate
	select top 1 @rfid = rf.rfid, @rateAmt = rf.rateAmt, @numInstallments = rf.numInstallments, @rateGLAccountID = r.GLAccountID
	from dbo.fn_sub_getMostExclusiveRateInfo(@memberID, @subscriptionID, @isRenewalRate, @qualifyFID) as ri
	inner join dbo.sub_rates r on r.rateID = ri.rateID
	inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
	inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		-- addon frequencies must match root or be the system rate (Full)
		and (f.frequencyID = @rootFrequencyID or f.isSystemRate = 1)
	inner join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid and rfmp.status = 'A' 
	order by 
		-- favor rate that matches rootSub rate; else favor system rate (Full)
		case when f.frequencyID = @rootFrequencyID then 1 else 2 end,
		case when f.isSystemRate = 1 then 1 else 2 end;

	IF @rfid IS NULL BEGIN
		SET @resultMessage = 'No Rate';
		GOTO on_done;
	END

	-- add on
	select top 1 @parentSubscriberID = rss.subscriberID, @parentSubscriptionID = sa.subscriptionID,
		@addonID = sa.addonID, @maxAllowed = sa.maxAllowed, @useAcctCodeInSet = sa.useAcctCodeInSet,
		@PCNum = sa.PCNum, @PCPctOffEach = sa.PCPctOffEach
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID, @subscriberID) as rss
	inner join dbo.fn_sub_getSubscriptionTreeOrder(@rootSubscriptionID) as sto on sto.parentSubscriptionID = rss.subscriptionID
		and sto.subscriptionID = @subscriptionID 
	inner join dbo.sub_addons as sa on sto.addonID = sa.addonID
	inner join dbo.sub_subscriptionsets as ssets on ssets.setID = sa.childSetID 
		and ssets.subscriptionID = @subscriptionID
	inner join dbo.sub_sets subsets on subsets.setID = ssets.setID
		and subsets.[status] <> 'D'
	inner join dbo.sub_subscribers ss on ss.subscriberID = rss.subscriberID
	inner join dbo.sub_statuses st on st.statusID = ss.statusID
		and st.statusCode <> 'D';

	IF @addonID IS NULL BEGIN
		SET @resultMessage = 'Missing Parent';
		GOTO on_done;
	END

	-- add on subs
	INSERT INTO #tmpAddOnSubs (subscriberID, PCFree)
	select s.subscriberID, s.PCFree
	from dbo.sub_subscribers as s
	inner join dbo.sub_subscriptionsets as ss on ss.subscriptionID = s.subscriptionID
	inner join dbo.sub_addons as sa on sa.childSetID = ss.setID
		and sa.addonID = @addonID
	where s.parentSubscriberID = @parentSubscriberID;

	set @addonSubsCount = @@ROWCOUNT;

	select @numFree = count(*) 
	from #tmpAddOnSubs
	where PCFree = 1;

	IF ISNULL(@maxAllowed,0) > 0 AND @maxAllowed <= @addonSubsCount BEGIN
		SET @resultMessage = 'Maximum Reached';
		GOTO on_done;
	END

	-- from getRateToUse (useRates=1, numPaymentsToUse=1)
	SET @rateTotal = @rateAmt * @numInstallments;
	SET @rateTotal = @rateTotal - (@rateTotal * (@PCPctOffEach * 0.01));
		
	-- free addon sub
	IF @PCNum > @numFree
		SET @pcFree = 1;
	ELSE
		SET @pcFree = 0;

	select @GLAccountID = subs.GLAccountID, @allowRateGLAccountOverride = subs.allowRateGLAccountOverride, @activationOptionCode = ao.subActivationCode
	from dbo.sub_subscriptions subs
	inner join dbo.sub_activationOptions ao on ao.subActivationID = subs.subActivationID
	where subscriptionID = @subscriptionID;
		
	IF (@allowRateGLAccountOverride = 1) AND (@rateGLAccountID is not null)
		select @GLAccountID = @rateGLAccountID;

	-- get certain values from the subscriber
	select @status = st.statusCode, @subStartDate = s.subStartDate, @subEndDate = s.subEndDate, 
		@graceEndDate = s.graceEndDate, @rootSubscriberID = s.rootSubscriberID
	from dbo.sub_subscribers s
	inner join dbo.sub_statuses st on st.statusID = s.statusID
	where s.subscriberID = @parentSubscriberID;
	
	select @paidStatusID = statusID
	from dbo.sub_paymentStatuses
	where statusCode = 'P';

	select top 1 @recogStartDate = r.recogAFStartDate, @recogEndDate = r.recogAFEndDate
	from dbo.sub_rates as r
	inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
	where rf.rfid = @RFID;

	-- if blank, default to sub dates
	IF @recogStartDate is null 	
		set @recogStartDate = @subStartDate;
	IF @recogEndDate is null 	
		set @recogEndDate = @subEndDate;

	-- recognition start date cannot be before sub start date
	IF @recogStartDate < @subStartDate
		set @recogStartDate = @subStartDate;

	-- recognition end date cannot be after sub end date
	IF @recogEndDate > @subEndDate
		set @recogEndDate = @subEndDate;

	-- recognition start date must be before recognition end date
	IF @recogStartDate > @recogEndDate
		set @recogEndDate = @recogStartDate;

	BEGIN TRAN;
		EXEC dbo.sub_addSubscriber @orgID=@orgID, @memberID=@memberID, @subscriptionID=@subscriptionID,
			@parentSubscriberID=@parentSubscriberID, @RFID=@RFID, @GLAccountID=@GLAccountID, @status=@status, 
			@subStartDate=@subStartDate, @subEndDate=@subEndDate, @graceEndDate=@graceEndDate, 
			@recogStartDate=@recogStartDate, @recogEndDate=@recogEndDate, @pcfree=@pcFree, @activationOptionCode=@activationOptionCode,
			@recordedByMemberID=@recordedByMemberID, @bypassQueue=1, @subscriberID=@newSubscriberID OUTPUT;
		IF @newSubscriberID = 0 
			RAISERROR('Unable to add subscriber.',16,1);

		SET @resultMessage = 'Added';

		IF @activationOptionCode = 'N'
			UPDATE dbo.sub_subscribers
			SET paymentStatusID = @paidStatusID
			WHERE subscriberID = @newSubscriberID;

		UPDATE dbo.sub_subscribers
		SET lastPrice = @rateTotal
		WHERE subscriberID = @newSubscriberID;

		EXEC dbo.sub_fixSubscriberTreeOrder @rootSubscriberID=@rootSubscriberID;
	COMMIT TRAN;

	-- add to group cache queue
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
	SELECT distinct orgID, @memberID, conditionID
	from dbo.ams_virtualGroupConditions
	where orgID = @orgID
	and fieldCode = 'sub_entry';

	EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';
	
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;

	on_done:

	UPDATE platformQueue.dbo.queue_subscriptionForceAdd
	SET statusID = @statusNotify,
		resultMessage = @resultMessage
	WHERE itemID = @itemID;

	IF OBJECT_ID('tempdb..#tmpAddOnSubs') IS NOT NULL 
		DROP TABLE #tmpAddOnSubs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
