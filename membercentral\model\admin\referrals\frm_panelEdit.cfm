<cfsavecontent variable="local.pageJS">
<cfoutput>
	<style type="text/css">
		##frmPanel .dateField{max-width: 150px;}
		.dateFieldIcon{padding-top: 7px;padding-bottom: 7px;}
		##frmPanel .input-group-append {height: 30px;margin-left: -2px;}
		##panelTreeSection h6{font-family: "Trebuchet MS", Arial, Helvetica, Geneva, sans-serif;font-size: 1.2em;line-height: 1.1em;margin: 0 0 12px 0;color: ##0E568D;border-bottom: 1px solid ##0E568D;}
		##panelTreeSection .panelQuestionTree{width:100%;}
		.errDivWrap li{list-style : none;}
	</style>
	<script type="text/javascript">
		var #ToScript(this.link.permissionsGridShow,"mca_perms_link")#
		
		function addFeeStructureRow(){
			$('##feeStructureTbl').show();
			$('##addRowBtn').hide();

			let panelFeeRowIndex = Number($('##panelFeeRowIndex').val()) + 1;
			let panelFeeTemplateSource = $('##panelFeeTemplate').html();
			var panelFeeTemplate = Handlebars.compile(panelFeeTemplateSource);
			$('##panelFeeRowContainer').append(panelFeeTemplate({ x:panelFeeRowIndex, isfeestructtotals:$('##panelFeeStructureTypeID').is(':checked') }));
			setFeeStructureChange();
			$('##panelFeeRowIndex').val(panelFeeRowIndex);
		}
		function removeFeeStructureRow(x){
			$('##panelFeeRow'+x).remove();
			setFeeStructureChange();
			if ($('##panelFeeRowContainer tr').length == 0){
				$('##feeStructureTbl').hide();
				$('##addRowBtn').show();
				$('##panelFeeRowIndex').val(0);
			}
		}
		function setFeeStructureChange() {
			if ($('##panelFeeStructureTypeID').is(':checked')) {
				$('.panelRangeInitPct').addClass('d-none');
				$('.rangeInitialChargeFeePercent').val(0);
			} else {
				$('.panelRangeInitPct').removeClass('d-none');
			}
			$('##feeStructureChanged').val(1);
		}	
		/* gl account selector functions */
		function selectGLAccount(a) {
			let title = '<span id="topModalTitle">Select a Revenue Account</span>';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: title,
				iframe: true,
				contenturl: '#buildLinkToTool(toolType="GLAccountSelector",mca_ta="showSelector")#&mode=direct&selectFN=parent.selectGLAccountResultp&glatid=3',
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: false
				}
			});
		}
		function selectGLAccountResultp(objGL) { selectGLAccountResult('p',objGL); }
		function closePanelBox() { MCModalUtils.hideModal(); }
		function selectGLAccountResult(a,objGL) {
			if (objGL.thepathexpanded.length > 0) {
				$('##panelGLAccountPath').html(objGL.thepathexpanded + '&nbsp; &nbsp; (<span class="text-danger font-weight-bold">Remember to save!</span>)');
				$('##panelGLAccountID').val(objGL.glaccountid);
				closePanelBox();
			} else { 
				var msg = '<div style="margin:10px;">';
				msg = '<h4>Error selecting GL Account</h4>';
				msg = '<div>There was a problem selecting the Override Panel Revenue Account.<br/>';
				msg = 'Try again; if the issue persists, contact MemberCentral for assistance.</div></div>';
				$('##MCModalBody').html(msg);
			}
		}
		function selectGLAccountClientFees(a) {
			let title = '<span id="topModalTitle">Select a Revenue Account</span>';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: title,
				iframe: true,
				contenturl: '#buildLinkToTool(toolType="GLAccountSelector",mca_ta="showSelector")#&mode=direct&selectFN=parent.selectGLAccountClientFeesResultp&glatid=3',
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: false
				}
			});
		}
		function selectGLAccountClientFeesResultp(objGL) { selectGLAccountClientFeesResult('p',objGL); }
		function selectGLAccountClientFeesResult(a,objGL) {
			if (objGL.thepathexpanded.length > 0) {
				$('##panelClientFeeGLAccountPath').html(objGL.thepathexpanded + '&nbsp; &nbsp; (<span class="text-danger font-weight-bold">Remember to save!</span>)');
				$('##panelClientFeeGLAccountID').val(objGL.glaccountid);
				MCModalUtils.hideModal();
			} else { 
				var msg = '<div style="margin:10px;">';
				msg += '<h4>Error selecting GL Account</h4>';
				msg += '<div>There was a problem selecting the Override Client Fees Revenue Account.<br/>';
				msg += 'Try again; if the issue persists, contact MemberCentral for assistance.</div></div>';
				$('##MCModalBody').html(msg);
			}
		}		
		function clearGLAccount(a) {
			$('##panelGLAccountPath').html('(No override; uses store\'s designated GL Account.)&nbsp; &nbsp; (<span class="text-danger font-weight-bold">Remember to save!</span>)');
			$('##panelGLAccountID').val(0);
		}	
		function clearGLAccountClientFees(a) {
			$('##panelClientFeeGLAccountPath').html('(No override; uses store\'s designated GL Account.)&nbsp; &nbsp; (<span class="text-danger font-weight-bold">Remember to save!</span>)');
			$('##panelClientFeeGLAccountID').val(0);
		}			
		function panelPermClose(){closePanelBox();}
		function chkPanelName() 	{
			$('.errDivWrap').addClass('d-none');
			var chkResult = function(r) {
				err = '';
				if (r.success && r.success.toLowerCase() == 'true') {
					let isUniqueName = r.uniquename && r.uniquename.toString().toLowerCase() == 'true';
					let isUniqueShortDesc = r.uniqueshortdesc && r.uniqueshortdesc.toString().toLowerCase() == 'true'
					if (isUniqueName && isUniqueShortDesc){
						var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
						var submitForm = true;
						var prevAmount = 0;
						var errMessage = "";
						panelName = $('##frmPanel input[name=panelName]').val().trim();
						panelShortDesc = $('##frmPanel input[name=panelShortDesc]').val().trim();
						
						if(panelName == ''){
							err += '<li>Enter the panel name.</li>';
							submitForm = false;	
						}
						
						if(panelShortDesc == ''){
							err += '<li>Enter the Code.</li>';
							submitForm = false;	
						}
						
						document.getElementById('panelReferralAmount').value = formatCurrency(document.getElementById('panelReferralAmount').value);
						
						<cfif not local.referralFeeStructureID>
							if ($('##frmPanel input[name*="cumulativeChargedAmount"]').length == 0){
								err += '<li>Enter a Fee Structure.</li>';
								submitForm = false;								
							}
						</cfif>

						$('##frmPanel .cumulativeChargedAmount').each(function(i){
							$(this).val( $(this).val().replace(/\$|,|%/g,''));
							if( $.trim($(this).val()).length > 0 && !amountRegex.test($.trim($(this).val())) ) {
								err += '<li>Enter a valid value for Cumulative Charged Amount. Only positive amounts are allowed.';
								submitForm = false;
							}
							if ($.trim($(this).val()).length == 0 )
								$(this).val("0");	

							if(parseFloat($(this).val()) <= prevAmount && i > 0){
								err += "<li>Amounts in Cumulative Charged Amount fields must be entered in ascending order and cannot have the same value as the previous row.</li>";
								submitForm = false;
							}	

							prevAmount = parseFloat($(this).val());	
						});

						$('##frmPanel .cumulativeFeePercent').each(function(){		
							$(this).val( $(this).val().replace(/\$|,|%/g,''));
							if( $.trim($(this).val()).length > 0 && !amountRegex.test($.trim($(this).val())) ) {
								err += '<li>Enter a valid number for Fee %. Only positive numbers are allowed.</li>';
								submitForm = false;
							}
							if ($.trim($(this).val()).length == 0 )
								$(this).val("0");							
						});

						$('##frmPanel .rangeInitialChargeFeePercent').each(function(){
							$(this).val( $(this).val().replace(/\$|,|%/g,''));
							if( $.trim($(this).val()).length > 0 && !amountRegex.test($.trim($(this).val())) ) {
								err += '<li>Enter a valid number for Range Initial Charge Fee %. Only positive numbers are allowed.</li>';
								submitForm = false;
							}
							if ($.trim($(this).val()).length == 0 )
								$(this).val("0");									
						});						
					} else {
						if (!isUniqueName) {
							err += '<li>The Panel name entered is already in use. Please enter a new Panel Name.</li>';
						}
						if (!isUniqueShortDesc) {
							err += '<li>The Code entered is already in use. Please enter a new Code.</li>';
						}
						submitForm = false;
					}
					
					if (submitForm){
						document.frmPanel.submit();
					}
					else if (err != ""){
						$('##err_div').html(err);
						$('.errDivWrap').removeClass('d-none');
						$("html, body").animate({ scrollTop: 0 }, "slow");
						$('##saveBtn').prop('disabled',false);
					}
				} else {
					$('##saveBtn').prop('disabled',false);
				}
			};

			if (_CF_checkfrmPanel(document.frmPanel)){
				$('##saveBtn').prop('disabled',true);
				var objParams = { referralID:$('##referralID').val(), panelID:$('##panelID').val(), panelName:$('##panelName').val(), panelShortDesc: $('##panelShortDesc').val().trim() };
				TS_AJX('ADMINREFERRALS','checkPanelName',objParams,chkResult,chkResult,10000,chkResult);
			}
		}

		$(function(){
			mca_setupMultipleDatePickerFields($('##frmPanel'),'dateControl');
			mca_setupCalendarIcons('frmPanel');
						
			<cfif not local.qryGetPanelFeeStructureLevels.recordCount>
				$('##feeStructureTbl').hide();
				$('##addRowBtn').show();
			<cfelse>
				$('##feeStructureTbl').show();
				$('##addRowBtn').hide();
			</cfif>	
			
			$.ajax({
				url: '#this.link.getPanelRelatedQuestionsContent#',
				type: 'POST',
				data: {panelID:#local.panelID#},
				dataType:'html',
				success: function(r) {
					$('##panelTreeSection').html(r);

					$('div.panelQuestionTree').each(function(){
						var _i = 1 ;
						$(this).children('div.panelQuestionHolder').each(function(){
							$(this).css('padding-left', 10*_i+'px');
							_i++;
						});
					});
				},
				error: function(r){
					$('##panelTreeSection').html('<p>Could not load the content.</p>');
				} 
			});
		});
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageJS#">

<cfoutput>
<cfform name="frmPanel"  id="frmPanel" action="#local.formLink#" method="POST">
	<cfinput type="hidden" name="referralID"  id="referralID" value="#this.referralID#" />
	<cfinput type="hidden" name="panelID"  id="panelID" value="#local.panelID#" />
	<cfinput type="hidden" name="panelParentID"  id="panelParentID" value="#local.panelParentID#" />
	<cfinput type="hidden" name="panelUID"  id="panelUID" value="#local.panelUID#" />
	<cfinput type="hidden" name="isActive"  id="isActive" value="1" />
	<cfinput type="hidden" name="feeStructureChanged"  id="feeStructureChanged" value="0" />
	<cfinput type="hidden" name="feConfirmReferralContentID" id="feConfirmReferralContentID" value="#local.feConfirmReferralContentID#">
	<cfinput type="hidden" name="feReviewSubmissionContentID" id="feReviewSubmissionContentID" value="#local.feReviewSubmissionContentID#">
	
	<div class="col-sm-12 col-xl-12">
		<div class="form-group row align-items-center errDivWrap d-none">
			<div class="col-sm-12  col-xl-12">
				<div class="mb-1 alert alert-danger alert-dismissible" role="alert">
					<span id="err_div"></span>
					<button type="button" class="close" aria-label="Close" data-hide="alert">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelName" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Name: *</label>
			<div class="col-sm-8  col-xl-5">
				<cfinput name="panelName"  class="form-control form-control-sm" id="panelName"  type="text"  maxlength="255" value="#local.panelName#"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelShortDesc" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Code: *</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="panelShortDesc"  class="form-control form-control-sm" id="panelShortDesc"  type="text"  maxlength="255" value="#local.panelShortDesc#"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelLongDesc" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Full Description:</label>
			<div class="col-sm-8 col-xl-5">
				<textarea name="panelLongDesc" id="panelLongDesc" rows="5" class="form-control form-control-sm">#ReReplace(local.panelLongDesc, "<[^<|>]+?>", "","ALL")#</textarea>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelInternalNotes" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Internal Notes:</label>
			<div class="col-sm-8 col-xl-5">
				<textarea name="panelInternalNotes" id="panelInternalNotes" rows="5" class="form-control form-control-sm">#ReReplace(local.panelInternalNotes, "<[^<|>]+?>", "","ALL")#</textarea>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelDateCreated" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Date Created:</label>
			<div class="col-sm-8 col-xl-5 d-flex">
				<input class="dateField form-control form-control-sm dateControl" type="text" name="panelDateCreated" id="panelDateCreated" value="#DateFormat(local.panelDateCreated, "m/d/yyyy")#" autocomplete="off" >
				<div class="input-group-append align-middle rounded-0">
					<span class="dateFieldIcon input-group-text cursor-pointer calendar-button rounded-0" data-target="md_8243"><i class="fa-solid fa-calendar"></i></span>
				</div>
				<button type="button" class="btnClearDateCF btn btn-pill btn-secondary btn-sm ml-2 py-0" name="btnClearDate" onclick="mca_clearDateRangeField('panelDateCreated')">clear</button>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelDateCommitteeApproved" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Date Approved by Committee:</label>
			<div class="col-sm-8 col-xl-5 d-flex">
				<input class="dateField form-control form-control-sm dateControl" type="text" name="panelDateCommitteeApproved" id="panelDateCommitteeApproved" value="#DateFormat(local.panelDateCommitteeApproved, "m/d/yyyy")#" autocomplete="off" >
				<div class="input-group-append align-middle rounded-0">
					<span class="dateFieldIcon input-group-text cursor-pointer calendar-button rounded-0" data-target="md_8243"><i class="fa-solid fa-calendar"></i></span>
				</div>
				<button type="button" class="btnClearDateCF btn btn-pill btn-secondary btn-sm ml-2 py-0" name="btnClearDate" onclick="mca_clearDateRangeField('panelDateCommitteeApproved')">clear</button>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelDateBoardApproved" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Date Approved by Board:</label>
			<div class="col-sm-8 col-xl-5 d-flex">
				<input class="dateField form-control form-control-sm dateControl" type="text" name="panelDateBoardApproved" id="panelDateBoardApproved" value="#DateFormat(local.panelDateBoardApproved, "m/d/yyyy")#" autocomplete="off" >
				<div class="input-group-append align-middle rounded-0">
					<span class="dateFieldIcon input-group-text cursor-pointer calendar-button rounded-0" data-target="md_8243"><i class="fa-solid fa-calendar"></i></span>
				</div>
				<button type="button" class="btnClearDateCF btn btn-pill btn-secondary btn-sm ml-2 py-0" name="btnClearDate" onclick="mca_clearDateRangeField('panelDateBoardApproved')">clear</button>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelDateBoardNotified" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Date State Board Notified of Panel:</label>
			<div class="col-sm-8 col-xl-5 d-flex">
				<input class="dateField form-control form-control-sm dateControl" type="text" name="panelDateBoardNotified" id="panelDateBoardNotified" value="#DateFormat(local.panelDateBoardNotified, "m/d/yyyy")#" autocomplete="off" >
				<div class="input-group-append align-middle rounded-0">
					<span class="dateFieldIcon input-group-text cursor-pointer calendar-button rounded-0" data-target="md_8243"><i class="fa-solid fa-calendar"></i></span>
				</div>
				<button type="button" class="btnClearDateCF btn btn-pill btn-secondary btn-sm ml-2 py-0" name="btnClearDate" onclick="mca_clearDateRangeField('panelDateBoardNotified')">clear</button>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelDateReviewed" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Date for Review of Panel:</label>
			<div class="col-sm-8 col-xl-5 d-flex">
				<input class="dateField form-control form-control-sm dateControl" type="text" name="panelDateReviewed" id="panelDateReviewed" value="#DateFormat(local.panelDateReviewed, "m/d/yyyy")#" autocomplete="off" >
				<div class="input-group-append align-middle rounded-0">
					<span class="dateFieldIcon input-group-text cursor-pointer calendar-button rounded-0" data-target="md_8243"><i class="fa-solid fa-calendar"></i></span>
				</div>
				<button type="button" class="btnClearDateCF btn btn-pill btn-secondary btn-sm ml-2 py-0" name="btnClearDate" onclick="mca_clearDateRangeField('panelDateReviewed')">clear</button>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelSendMail" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Send Referral E-mail or Post Mail to Clients:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="panelSendMail" id="panelSendMail" required="false" type="checkbox" value="1" checked="#local.panelSendMail#" class="form-check-input m-0"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelMaxNumMembers" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Max ## of Members in Panel:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="panelMaxNumMembers"  id="panelMaxNumMembers" required="false" type="text" maxlength="4" value="#local.panelMaxNumMembers#" class="form-control form-control-sm"/>
			</div>
		</div>
		<div class="form-group row">
			<input type="hidden" name="panelFeeRowIndex" id="panelFeeRowIndex" value="#local.qryGetPanelFeeStructureLevels.recordCount#">
			<label class="col-sm-3 col-md-3 col-xl-2 col-form-label align-self-center">Referral Fee Structure:</label>
			<div class="col-sm-7">
				<button type="button" id="addRowBtn" class="btn btn-success btn-sm" onclick="addFeeStructureRow();">Add Fee</button>
				<div id="feeStructureTbl" class="card card-box my-3 p-4 shadow-none">
					<div class="form-group row">
						<label for="panelFeeStructureTypeID" class="col-sm-auto pr-2">Recalculate Fees Due based on Total Fees Reported</label>
						<div class="col px-2">
							<div class="form-check">
								<input type="checkbox" name="panelFeeStructureTypeID" id="panelFeeStructureTypeID" value="#local.totalFeeStructureTypeID#" class="form-check-input" <cfif local.isFeeStructureTotals>checked="checked"</cfif> onchange="setFeeStructureChange();">
								<label class="form-check-label" for="panelFeeStructureTypeID">Yes</label>
							</div>
						</div>
					</div>
					<hr class="mt-0">
					<div class="text-right">
						<a href="##" title="Add a new fee structure level." onclick="addFeeStructureRow();return false;">Add</a>
					</div>
					<table class="table table-sm table-striped">
						<thead class="font-size-sm">
							<tr>
								<th style="width:33%;">Cumulative Charged Amount</th>
								<th>Fee %</th>
								<th class="panelRangeInitPct<cfif local.isFeeStructureTotals> d-none</cfif>">Range Initial Charge Fee %</th>
								<th></th>
							</tr>
						</thead>
						<tbody id="panelFeeRowContainer" class="font-size-sm">
						<cfloop query="local.qryGetPanelFeeStructureLevels">
							<tr id="panelFeeRow#local.qryGetPanelFeeStructureLevels.currentrow#">
								<td>
									<input type="text" name="cumulativeChargedAmount#local.qryGetPanelFeeStructureLevels.currentrow#" id="cumulativeChargedAmount#local.qryGetPanelFeeStructureLevels.currentrow#" maxlength="10" value="#numberFormat(local.qryGetPanelFeeStructureLevels.cumulativeChargedAmount,"__.__")#" class="feeStructField form-control form-control-sm cumulativeChargedAmount" onChange="setFeeStructureChange();" style="width:150px;">
								</td>
								<td>
									<input type="text" name="cumulativeFeePercent#local.qryGetPanelFeeStructureLevels.currentrow#" id="cumulativeFeePercent#local.qryGetPanelFeeStructureLevels.currentrow#" maxlength="10" value="#local.qryGetPanelFeeStructureLevels.cumulativeFeePercent#"  class="feeStructField form-control form-control-sm cumulativeFeePercent" onChange="setFeeStructureChange();" style="width:150px;">
								</td>
								<td class="panelRangeInitPct<cfif local.isFeeStructureTotals> d-none</cfif>">
									<input type="text" name="rangeInitialChargeFeePercent#local.qryGetPanelFeeStructureLevels.currentrow#" id="rangeInitialChargeFeePercent#local.qryGetPanelFeeStructureLevels.currentrow#" maxlength="10" value="#local.qryGetPanelFeeStructureLevels.rangeInitialChargeFeePercent#"  class="feeStructField form-control form-control-sm rangeInitialChargeFeePercent" onChange="setFeeStructureChange();" style="width:150px;">
								</td>
								<td class="text-right">
									<a href="##" title="Remove this fee structure level." onclick="removeFeeStructureRow(#local.qryGetPanelFeeStructureLevels.currentrow#);return false;"><i class="fa-solid fa-trash-can text-danger"></i></a>
								</td>
							</tr>
						</cfloop>
						</tbody>
					</table>
				</div>
				<input type="hidden" name="panelReferralFeePercent"  id="panelReferralFeePercent" required="false" size="10" maxlength="5" value="#local.panelReferralFeePercent#">
			</div>
		</div>

		<div class="form-group row align-items-center ">
			<label for="panelDeductExpenseDesc" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Deductible Expenses Description:</label>
			<div class="col-sm-8 col-xl-5">
				<textarea name="panelDeductExpenseDesc" id="panelDeductExpenseDesc" rows="5" class="form-control form-control-sm">#ReReplace(local.panelDeductExpenseDesc, "<[^<|>]+?>", "","ALL")#</textarea>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="ovFeeStructure" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Override Honoring Point-in-Time Referral Fees:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="ovFeeStructure" id="ovFeeStructure" required="false" type="checkbox" value="1" checked="#local.ovFeeStructure#" class="form-check-input m-0"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelReferralAmount" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Lawyer Referral Fixed Amount:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="panelReferralAmount"  id="panelReferralAmount" required="false" type="text" maxlength="10" value="#DollarFormat(local.panelReferralAmount)#" class="form-control form-control-sm"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label  class="col-sm-3 col-md-3 col-xl-2 col-form-label">Panel Revenue Override:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput type="hidden" name="panelGLAccountID" id="panelGLAccountID" value="#val(local.panelGLAccountID)#" />
				<span id="panelGLAccountPath"><cfif len(local.panelGLAccountPath)>#local.panelGLAccountPath#<cfelse>(No override; uses referral's designated GL Account.)</cfif></span>
				<br/><a href="javascript:selectGLAccount('p');">Choose GL Account</a> &nbsp; &bull; &nbsp; <a href="javascript:clearGLAccount('p');">Clear Selected GL Account</a>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelClientReferralAmount" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Client Fees Amount:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="panelClientReferralAmount"  id="panelClientReferralAmount" required="false" type="text" maxlength="10" value="#DollarFormat(local.panelClientReferralAmount)#" class="form-control form-control-sm"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label  class="col-sm-3 col-md-3 col-xl-2 col-form-label">Client Fees Revenue Override:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput type="hidden" name="panelClientFeeGLAccountID" id="panelClientFeeGLAccountID" value="#val(local.panelClientFeeGLAccountID)#" />
				<span id="panelClientFeeGLAccountPath"><cfif len(local.panelClientFeeGLAccountPath)>#local.panelClientFeeGLAccountPath#<cfelse>(No override; uses referral's designated GL Account.)</cfif></span>
				<br/><a href="javascript:selectGLAccountClientFees('p');">Choose GL Account</a> &nbsp; &bull; &nbsp; <a href="javascript:clearGLAccountClientFees('p');">Clear Selected GL Account</a>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="panelSendReceiptEmail" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Send Receipt E-mail to Clients:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="panelSendReceiptEmail" id="panelSendReceiptEmail" required="false" type="checkbox" value="1" checked="#local.panelSendReceiptEmail#" class="form-check-input m-0"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="allowPanelMgmt" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Display Panel in Referral Center:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="allowPanelMgmt" id="allowPanelMgmt" required="false" type="checkbox" value="1" checked="#local.allowPanelMgmt#" class="form-check-input m-0"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="feAllowClientReferral" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Allow Member Referral in Front-End:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="feAllowClientReferral" id="feAllowClientReferral" required="false" type="checkbox" value="1" checked="#local.feAllowClientReferral#" class="form-check-input m-0"/>
			</div>
		</div>
		<div class="form-group row align-items-center ">
			<label for="feDspClientReferral" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Display Panel in Front-End?:</label>
			<div class="col-sm-8 col-xl-5">
				<cfinput name="feDspClientReferral" id="feDspClientReferral" required="false" type="checkbox" value="1" checked="#local.feDspClientReferral#" class="form-check-input m-0"/>
			</div>
		</div>
		<cfif this.feApplicationInstanceID>
			<div class="form-group mt-2">#application.objWebEditor.showContentBoxWithLinks(fieldname='feConfirmReferralContent', fieldlabel='Front-End Confirmation Text:', contentID=local.feConfirmReferralContentID, content=local.feConfirmReferralContent, allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
			</div>
			<div class="form-group mt-2">#application.objWebEditor.showContentBoxWithLinks(fieldname='feReviewSubmissionContent', fieldlabel='Review Submission Text:', contentID=local.feReviewSubmissionContentID, content=local.feReviewSubmissionContent, allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
			</div>
		</cfif>
		<cfif local.panelID>
			<div class="form-group row align-items-center ">
				<label for="panelStatusID" class="col-sm-3 col-md-3 col-xl-2 col-form-label">Status:</label>
				<div class="col-sm-8 col-xl-5">
					<select name="panelStatusID" id="panelStatusID" class="form-control form-control-sm" <cfif local.panelStatusName EQ 'Deleted'>disabled</cfif>>
						<cfloop query="local.qryGetPanelStatuses">
						<option value="#local.qryGetPanelStatuses.panelStatusID#" <cfif local.panelStatusID EQ local.qryGetPanelStatuses.panelStatusID>selected</cfif>>#local.qryGetPanelStatuses.statusName#</option>
						</cfloop>
					</select>
				</div>
			</div>
		</cfif>
		<cfif not local.panelID or local.panelStatusName EQ 'Deleted'>
			<cfinput type="hidden" name="panelStatusID" id="panelStatusID" value="#local.panelStatusID#" />
		</cfif>
		<cfif local.panelID>
			<div class="form-group row align-items-center ">
				<label  class="col-sm-3 col-md-3 col-xl-2 col-form-label">Permissions:</label>
				<div class="col-sm-8 col-xl-5">
					<a id="siteResourceID" href="javascript:mca_showPermissions(#local.siteResourceID#,'#replace(replace(local.panelName,chr(34),'','ALL'),"'","","ALL")#');">Edit Permissions</a>
				</div>
			</div>
		</cfif>
		<div class="form-group row align-items-right" <cfif local.panelStatusName EQ 'Deleted'>d-none</cfif>>
			<div class="col-sm-12 col-xl-12 text-right">
				<button name="cancelBtn" id="cancelBtn" type="button" onClick="parent.location='#this.link.managePanel#';" class="btn btn-sm btn-secondary">Cancel</button>
				<button name="saveBtn" id="saveBtn" type="button" onClick="chkPanelName();" class="btn btn-sm btn-primary">Save Panel</button>
			</div>
		</div>
		<div class="form-group row align-items-right">
			<div class="col-sm-12 col-xl-12 text-left">
				* indicates a required field
			</div>
		</div>
	</div>
</cfform>
<div class="col-sm-12 col-xl-12 mt-5">
	<h4>#local.panelName# : Questions</h4>
	<div id="panelTreeSection" class="row col-12 mt-3">
	</div>
</div>
<script id="panelFeeTemplate" type="text/x-handlebars-template">
	<tr id="panelFeeRow{{x}}">
		<td class="font-size-sm">
			<input type="text" name="cumulativeChargedAmount{{x}}" id="cumulativeChargedAmount{{x}}" size="10" maxlength="10" value="0" class="feeStructField form-control form-control-sm cumulativeChargedAmount" onChange="setFeeStructureChange();" style="width:150px;">
		</td>
		<td class="font-size-sm">
			<input type="text" name="cumulativeFeePercent{{x}}" id="cumulativeFeePercent{{x}}" size="10" maxlength="10" value="0"  class="feeStructField form-control form-control-sm cumulativeFeePercent" onChange="setFeeStructureChange();" style="width:150px;">
		</td>
		<td class="font-size-sm panelRangeInitPct{{##if isfeestructtotals}} d-none{{/if}}">
			<input type="text" name="rangeInitialChargeFeePercent{{x}}"  id="rangeInitialChargeFeePercent{{x}}" size="10" maxlength="10" value="0" class="feeStructField form-control form-control-sm rangeInitialChargeFeePercent" onChange="setFeeStructureChange();" style="width:150px;">
		</td>
		<td class="text-right">
			<a href="##" title="Remove this fee structure level." onclick="removeFeeStructureRow({{x}});return false;"><i class="fa-solid fa-trash-can text-danger"></i></a>
		</td>
	</tr>
</script>
</cfoutput>