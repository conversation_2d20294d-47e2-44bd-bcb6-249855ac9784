ALTER PROC dbo.queue_AcctOption1Import_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='importAcctOpt1', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	CREATE TABLE #tmpNotifyItemGroupUID (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier, itemGroupUIDStr char(36) PRIMARY KEY);
	CREATE TABLE #tmpNotify2 (itemGroupUID uniqueidentifier PRIMARY KEY, siteID int, recordedByMemberID int);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_acctOption1Import
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_acctOption1Import
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotifyItemGroupUID
	FROM dbo.queue_acctOption1Import as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotify2;
		GOTO on_done;
	END

	-- get distinct list of groupUIDs
	insert into #tmpNotify
	select distinct itemGroupUID, replace(cast(itemGroupUID as char(36)),'-','') as itemGroupUIDStr
	from #tmpNotifyItemGroupUID;

	-- return itemGroupUIDs that can be marked as done
	insert into #tmpNotify2
	select distinct tmpN.itemGroupUID, qid.siteID, qid.recordedByMemberID
	from #tmpNotify as tmpN
	inner join dbo.queue_acctOption1Import as qid on qid.itemGroupUID = tmpN.itemGroupUID
	order by tmpN.itemGroupUID;

	select tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from #tmpNotify2 as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1)
		and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1)
		and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1)
		and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
