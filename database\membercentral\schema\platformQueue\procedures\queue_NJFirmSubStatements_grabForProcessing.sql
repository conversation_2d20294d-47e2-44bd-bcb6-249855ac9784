ALTER PROC dbo.queue_NJFirmSubStatements_grabForProcessing
@batchSize int,
@filePath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @readyToProcessStatusID int, @grabbedForProcessingStatusID int, @itemGroupUID uniqueIdentifier;
	EXEC dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus='grabbedForProcessing', @queueStatusID=@grabbedForProcessingStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_queueItems') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_queueItems;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firms') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firms;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firmChild') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firmChild;
	CREATE TABLE #tmpNJFirmSubStatement_queueItems (itemID int);
	CREATE TABLE #tmpNJFirmSubStatement_firms (itemID int, itemGroupUID uniqueidentifier, firmMemberID int, firmMemberNumber varchar(50), 
		firmFirstName varchar(75), firmLastName varchar(75), firmCompany varchar(200), firmAddress1 varchar(100), firmAddress2 varchar(100), 
		firmCity varchar(75), firmStateCode varchar(4), firmPostalCode varchar(25), firmCountry varchar(100), firmPhone varchar(40), 
		firmFax varchar(40), firmPastDues decimal(18,2), firmDues decimal(18,2), firmGrandTotalDues decimal(18,2),
		xmlConfigParam varchar(max), xmlFirm varchar(max));
	CREATE TABLE #tmpNJFirmSubStatement_firmChild (detailID int, itemID int, firmChildMemberID int, firmChildMemberNumber varchar(50), 
		firmChildFirstName varchar(75), firmChildLastName varchar(75), firmChildDues decimal(18,2));

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @grabbedForProcessingStatusID,
		qi.dateUpdated = GETDATE()
		OUTPUT inserted.itemID INTO #tmpNJFirmSubStatement_queueItems
	FROM dbo.queue_NJFirmSubStatements AS qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID
		FROM dbo.queue_NJFirmSubStatements AS qi2
		WHERE qi2.statusID = @readyToProcessStatusID
		ORDER BY qi2.dateAdded, qi2.itemID
	) AS batch ON batch.itemID = qi.itemID
	WHERE qi.statusID = @readyToProcessStatusID;

	INSERT INTO #tmpNJFirmSubStatement_firms (itemID, itemGroupUID, firmMemberID, firmMemberNumber, 
		firmFirstName, firmLastName, firmCompany, firmAddress1, firmAddress2, firmCity, firmStateCode, 
		firmPostalCode, firmCountry, firmPhone, firmFax, firmPastDues, firmDues, firmGrandTotalDues,
		xmlConfigParam)
	SELECT qi.itemID, qi.itemGroupUID, mFirmActive.memberID, mFirmActive.memberNumber, mFirmActive.firstName, 
		mFirmActive.lastName, mFirmActive.company, qi.firmAddress1, qi.firmAddress2, qi.firmCity, qi.firmStateCode, 
		qi.firmPostalCode, qi.firmCountry, qi.firmPhone, qi.firmFax, qi.firmPastDues, qi.firmDues, qi.firmGrandTotalDues, 
		qi.xmlConfigParam
	FROM #tmpNJFirmSubStatement_queueItems AS tmp
	INNER JOIN dbo.queue_NJFirmSubStatements AS qi ON qi.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.ams_members AS mFirm ON mFirm.memberID = qi.firmMemberID
	INNER JOIN membercentral.dbo.ams_members AS mFirmActive ON mFirmActive.memberID = mFirm.activeMemberID;

	INSERT INTO #tmpNJFirmSubStatement_firmChild (detailID, itemID, firmChildMemberID, firmChildMemberNumber, firmChildFirstName, 
		firmChildLastName, firmChildDues)
	SELECT qid.detailID, qid.itemID, mFirmChildActive.memberID, mFirmChildActive.memberNumber, mFirmChildActive.firstName, 
		mFirmChildActive.lastName, qid.firmChildDues
	FROM #tmpNJFirmSubStatement_firms AS tmp
	INNER JOIN dbo.queue_NJFirmSubStatementsDetail AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.ams_members AS mFirmChild ON mFirmChild.memberID = qid.firmChildMemberID
	INNER JOIN membercentral.dbo.ams_members AS mFirmChildActive ON mFirmChildActive.memberID = mFirmChild.activeMemberID;

	-- firms xml. casting to varchar(max) because it speed up final data query return
	UPDATE tmp
	SET tmp.xmlFirm = firms.firmXML
	FROM #tmpNJFirmSubStatement_firms AS tmp
	cross apply (
		select cast(isnull((		
			select firm.firmMemberID as '@firmmemberid',
				firm.firmMemberNumber as '@firmmembernumber' ,
				firm.firmFirstname as '@firmfirstname' ,
				firm.firmLastname as '@firmlastname' ,
				firm.firmCompany as '@firmcompany' ,
				firm.firmAddress1 as '@firmaddress1' ,
				firm.firmAddress2 as '@firmaddress2' ,
				'' as '@firmaddress3' ,
				firm.firmCity as '@firmcity' ,
				firm.firmStateCode as '@firmstate' ,
				firm.firmPostalCode as '@firmpostalcode',
				firm.firmCountry as '@firmcountry',
				firm.firmPhone as '@firmphone',
				firm.firmfax as '@firmfax',
				firm.firmPastDues as '@pastdues',  
				firm.firmDues as '@firmdues',
				firm.firmGrandTotalDues as '@grandtotal',
				( select
					member.firmChildMemberID as '@childmemberID',
					member.firmChildMemberNumber as '@childmembernumber',
					firm.firmMemberID as '@firmmemberid',
					member.firmChildFirstName as '@firstname',
					member.firmChildLastName as '@lastname',
					'' as '@prefix',
					'' as '@suffix',
					member.firmChildLastName + ', ' + member.firmChildFirstName + ' (' + member.firmChildMemberNumber + ')' as '@namestring',
					'' as '@company',
					'' as '@address1' ,
					'' as '@address2' ,
					'' as '@address3' ,
					'' as '@city' ,
					'' as '@state' ,
					'' as '@postalcode',
					'' as '@country',
					member.firmChildDues as '@dues'
					from #tmpNJFirmSubStatement_firmChild as member
					where member.itemID = firm.itemID
					order by member.firmChildLastName, member.firmChildFirstName, member.firmChildMemberID
					for xml path('member'), type
				)
			from #tmpNJFirmSubStatement_firms as firm
			where firm.itemID = tmp.itemID
			for xml path('company'), root('companies'), type
		),'<companies/>') as varchar(max)) as firmXML
	) as firms;

	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs;
	select distinct itemGroupUID, membercentral.dbo.fn_createDirectory(@filePath + cast(itemGroupUID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpNJFirmSubStatement_firms
	where membercentral.dbo.fn_DirectoryExists(@filePath + cast(itemGroupUID as varchar(50))) = 0;

	-- final data
	select itemID, itemGroupUID, firmMemberID, firmMemberNumber, firmFirstName, firmLastName, firmCompany, xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePath + cast(itemGroupUID as varchar(50)) + '\' + cast(itemID as varchar(10)) + '.xml',xmlFirm,1) as writeFileResult
	from #tmpNJFirmSubStatement_firms
	order by itemID;

	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_queueItems') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_queueItems;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firms') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firms;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firmChild') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firmChild;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
