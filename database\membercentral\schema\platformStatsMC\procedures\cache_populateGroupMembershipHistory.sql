ALTER PROC dbo.cache_populateGroupMembershipHistory
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @statusProcessing int, @startDate datetime = getdate();;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='trackGrpMembershipHistory', @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='trackGrpMembershipHistory', @queueStatus='Processing', @queueStatusID=@statusProcessing OUTPUT;

	-- update status
	UPDATE platformQueue.dbo.queue_trackGrpMembershipHistory
	SET statusID = @statusProcessing
	WHERE orgID = @orgID
	AND statusID = @statusReady;

	IF @@ROWCOUNT = 0
		RETURN 0;

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpActiveGroups') IS NOT NULL 
		DROP TABLE #tmpActiveGroups;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipStatus') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipStatus;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipChanges') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipChanges;
	CREATE TABLE #tmpActiveGroups (groupID int PRIMARY KEY);
	CREATE TABLE #tmpCacheGrpMembershipStatus (memberID int, groupID int, lastChangeDate date, lastStatusID tinyint,
		INDEX IX_tmpCacheGrpMembershipStatus__memberID__Includes2 (memberID) INCLUDE (groupID, lastStatusID));
	CREATE TABLE #tmpCacheGrpMembershipChanges (memberID int, groupID int, statusID tinyint);

	INSERT INTO #tmpActiveGroups (groupID)
	SELECT groupID
	FROM membercentral.dbo.ams_groups
	WHERE orgID = @orgID
	AND [status] = 'A';

	INSERT INTO #tmpCacheGrpMembershipStatus (memberID, groupID, lastChangeDate)
	SELECT mgh.memberID, mgh.groupID, MAX(mgh.changeDate)
	FROM dbo.cache_members_groups_history AS mgh
	INNER JOIN #tmpActiveGroups AS g ON g.groupID = mgh.groupID
	WHERE mgh.orgID = @orgID
	GROUP BY mgh.memberID, mgh.groupID;

	-- update status
	UPDATE tmp
	SET tmp.lastStatusID = mgh.statusID
	FROM #tmpCacheGrpMembershipStatus AS tmp
	INNER JOIN dbo.cache_members_groups_history AS mgh ON mgh.orgID = @orgID
		AND mgh.memberID = tmp.memberID
		AND mgh.groupID = tmp.groupID
		AND mgh.changeDate = tmp.lastChangeDate;

	INSERT INTO #tmpCacheGrpMembershipChanges (memberID, groupID, statusID)
	SELECT mg.memberID, mg.groupID, 1
	FROM #tmpActiveGroups AS g
	INNER JOIN membercentral.dbo.cache_members_groups AS mg ON mg.orgID = @orgID
		AND mg.groupID = g.groupID
		AND mg.memberID > 0
	LEFT OUTER JOIN #tmpCacheGrpMembershipStatus AS tmp ON tmp.groupID = mg.groupID
		AND tmp.memberID = mg.memberID
	WHERE tmp.memberID IS NULL
	OR tmp.lastStatusID = 0;

	INSERT INTO #tmpCacheGrpMembershipChanges (memberID, groupID, statusID)
	SELECT tmp.memberID, tmp.groupID, 0
	FROM #tmpCacheGrpMembershipStatus AS tmp
	LEFT OUTER JOIN membercentral.dbo.cache_members_groups AS mg ON mg.orgID = @orgID
		AND mg.groupID = tmp.groupID
		AND mg.memberID = tmp.memberID
	WHERE tmp.lastStatusID = 1
	AND mg.autoID IS NULL

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	BEGIN TRAN;
		-- new history entries
		INSERT INTO dbo.cache_members_groups_history (orgID, memberID, groupID, changeDate, statusID)
		SELECT @orgID, memberID, groupID, GETDATE(), statusID
		FROM #tmpCacheGrpMembershipChanges;

		DELETE FROM platformQueue.dbo.queue_trackGrpMembershipHistory
		WHERE orgID = @orgID;

		INSERT INTO platformStatsMC.dbo.cache_members_groups_historyLog (orgID, dateStarted, timeMS)
		VALUES (@orgID, @startDate, datediff(ms,@startDate,getdate()));

	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpActiveGroups') IS NOT NULL 
		DROP TABLE #tmpActiveGroups;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipStatus') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipStatus;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipChanges') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipChanges;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
