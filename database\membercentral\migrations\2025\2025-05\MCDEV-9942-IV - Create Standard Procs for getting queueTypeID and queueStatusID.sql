use platformQueue
GO

ALTER PROC dbo.queue_webhooks_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = GETDATE();
	EXEC dbo.queue_getQueueTypeID @queueType='webhook', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#webhookPriority') IS NOT NULL
		DROP TABLE #webhookPriority;
	CREATE TABLE #webhookPriority (itemID int, SQSQueueName varchar(80), totalQueued int, minutesInQueue int, [priority] int);

	INSERT INTO #webhookPriority (itemID, SQSQueueName, minutesInQueue)
	SELECT itemID, SQSQueueName, minsInQueue = DATEDIFF(minute,dateUpdated,@nowDate)
	FROM dbo.queue_webhook
	WHERE statusID IN (@statusReady,@statusGrabbed);

	UPDATE hp 
	SET hp.totalQueued = tmp.totalQueued
	FROM #webhookPriority AS hp
	INNER JOIN (
		SELECT SQSQueueName, COUNT(*) AS totalQueued
		FROM #webhookPriority
		GROUP BY SQSQueueName
	) AS tmp ON tmp.SQSQueueName = hp.SQSQueueName;

	UPDATE #webhookPriority 
	SET [priority] = CASE 
			WHEN totalQueued = 1 THEN -100
			WHEN minutesInQueue > 360 THEN (totalQueued / 50) + 1
			WHEN minutesInQueue > 90 THEN (totalQueued / 25) - 10
			WHEN minutesInQueue > 30 THEN (totalQueued / 25) + 2
			WHEN totalQueued < 500 THEN (totalQueued / 25)
			ELSE (totalQueued / 25) + 10
		END;

	 -- delete rows where the priority hasn't changed
	DELETE tmp
	FROM #webhookPriority AS tmp
	INNER JOIN dbo.queue_webhook AS qid ON tmp.itemID = qid.itemID
	WHERE tmp.[priority] = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = tmp.[priority]
	FROM dbo.queue_webhook AS qid
	INNER JOIN #webhookPriority AS tmp ON tmp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#webhookPriority') IS NOT NULL
		DROP TABLE #webhookPriority;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_webhooks_grabForProcessing
@batchSize int,
@maxEntriesPerSQSQueueName int = 100

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='webhook', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpWebhooks') IS NOT NULL
		DROP TABLE #tmpWebhooks;
	CREATE TABLE #tmpWebhooks (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries. also limiting the max items grabbed per SQSQueueName
	WITH rankedQueueEntries AS (
		SELECT itemID, ROW_NUMBER() OVER (PARTITION BY SQSQueueName ORDER BY queuePriority, dateAdded, itemID) AS rankedNum
		FROM dbo.queue_webhook
		WHERE statusID = @statusReady
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpWebhooks
	FROM dbo.queue_webhook AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) qw.itemID 
		FROM dbo.queue_webhook as qw
		INNER JOIN rankedQueueEntries as cteR on cteR.itemID = qw.itemID
			AND cteR.rankedNum <= @maxEntriesPerSQSQueueName
		WHERE qw.statusID = @statusReady
		ORDER BY qw.queuePriority, qw.dateAdded, qw.itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM #tmpWebhooks;

	SELECT qid.itemID, s.sitecode, qid.siteID, qid.webhookID, qid.webhookURL, qid.payloadMessage, qid.SQSQueueName, @totalItemCount as totalItemCount
	FROM #tmpWebhooks AS tmp
	INNER JOIN dbo.queue_webhook AS qid ON qid.itemID = tmp.itemID
	inner join membercentral.dbo.sites s on s.siteID = qid.siteID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpWebhooks') IS NOT NULL
		DROP TABLE #tmpWebhooks;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_webhook_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='webhook', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- webhook / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_webhook WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_webhook
		SET statusID = @readyStatusID,
			dateUpdated = GETDATE()
		WHERE statusID = @grabProcessingStatusID
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'webhook Queue Issue';
		SET @errorSubject = 'webhook queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;

		EXEC membercentral.dbo.sched_resumeTask @name='Send Webhook Data to SQS', @engine='BERLinux';
	END

	-- webhook / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -20, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_webhook WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_webhook
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'webhook Queue Issue';
		SET @errorSubject = 'webhook queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		
		EXEC membercentral.dbo.sched_resumeTask @name='Send Webhook Data to SQS', @engine='BERLinux';
	END

	-- webhook catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_webhook WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'webhook Queue Issue';
		SET @errorSubject = 'webhook queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_tsSubscriberDepos_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @processingStatusID int, @readyStatusID int, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @itemAsStr varchar(60), @xmlMessage xml;
	EXEC dbo.queue_getQueueTypeID @queueType='tsSubscriberDepos', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;

	-- tsSubscriberDepos / processingItem autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_tsSubscriberDepos WHERE statusID = @processingStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_tsSubscriberDepos
		set statusID = @readyStatusID, 
			dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID 
		and dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_tsSubscriberDepos_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'tsSubscriberDepos Queue Issue';
		SET @errorSubject = 'tsSubscriberDepos queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- tsSubscriberDepos catchall
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_tsSubscriberDepos WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'tsSubscriberDepos Queue Issue';
		SET @errorSubject = 'tsSubscriberDepos queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_TSPurchaseCreditsNotify_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='TSPurchaseCreditsNotify', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_TSPurchaseCreditsNotify as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_TSPurchaseCreditsNotify
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.depomemberdataID, qid.FirstName, qid.LastName, qid.Email
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_TSPurchaseCreditsNotify AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_TSPurchaseCreditsNotify_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @issueCount int, @timeToUse datetime, @queueTypeID int, @grabProcessingStatusID int, @readyStatusID int,
		@processingStatusID int, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='TSPurchaseCreditsNotify', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;
	
	-- queue_TSPurchaseCreditsNotify / grabbedForProcessing autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_TSPurchaseCreditsNotify WHERE statusID = @grabProcessingStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_TSPurchaseCreditsNotify
		set statusID = @readyStatusID,
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID
		and dateUpdated < @timeToUse;

		SET @errorTitle = 'TSPurchaseCreditsNotify Queue Issue';
		SET @errorSubject = 'TSPurchaseCreditsNotify queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- queue_TSPurchaseCreditsNotify / processingItem autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_TSPurchaseCreditsNotify WHERE statusID = @processingStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_TSPurchaseCreditsNotify
		set statusID = @readyStatusID,
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID
		and dateUpdated < @timeToUse;

		SET @errorTitle = 'TSPurchaseCreditsNotify Queue Issue';
		SET @errorSubject = 'TSPurchaseCreditsNotify queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- queue_TSPurchaseCreditsNotify catchall
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_TSPurchaseCreditsNotify WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'TSPurchaseCreditsNotify Queue Issue';
		SET @errorSubject = 'TSPurchaseCreditsNotify queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_TSDepoDocumentReport_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='TSDepoDocumentReport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_TSDepoDocumentReport as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_TSDepoDocumentReport
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.depomemberdataID, qid.FirstName, qid.LastName, qid.Email
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_TSDepoDocumentReport AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_TSDepoDocumentBuyReport_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='TSDepoDocumentBuyReport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_TSDepoDocumentBuyReport as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_TSDepoDocumentBuyReport
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.depomemberdataID, qid.FirstName, qid.LastName, qid.Email
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_TSDepoDocumentBuyReport AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;
	
	UPDATE dmd
	SET dmd.lastTSDepoDocumentBuyReportSent = getdate()
	FROM trialsmith.dbo.depoMemberData dmd
	INNER JOIN dbo.queue_TSDepoDocumentBuyReport AS qid ON qid.depomemberdataID = dmd.depomemberdataID
	INNER JOIN  #tmpQueueItems AS tmp ON tmp.itemID = qid.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SWVideoPreview_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='SWVideoPreview', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpVideoPreviews') IS NOT NULL 
		DROP TABLE #tmpVideoPreviews;
	CREATE TABLE #tmpVideoPreviews (itemID int PRIMARY KEY);

	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed
		OUTPUT inserted.itemID
		INTO #tmpVideoPreviews
	from dbo.queue_SWVideoPreview as qi
	inner join (
		select top(@BatchSize) itemID
		from dbo.queue_SWVideoPreview
		where statusID = @statusReady
		order by dateAdded, itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	select qidd.itemID, qidd.previewID, qidd.seminarID, qidd.titleID, qidd.baseFileID, qidd.timeCodeStart, qidd.timeCodeEnd, 
		qidd.dateAdded, isnull(sP.participantID,tP.participantID) as participantID, isnull(sP.orgcode,tP.orgcode) as participantOrgCode
	from #tmpVideoPreviews as qid
	inner join dbo.queue_SWVideoPreview as qidd on qidd.itemID = qid.itemID
	left outer join seminarWeb.dbo.tblSeminars as s 
			inner join seminarWeb.dbo.tblParticipants as sP on sP.participantID = s.participantID
		on s.seminarID = qidd.seminarID
	left outer join seminarWeb.dbo.tblTitles as t 
			inner join seminarWeb.dbo.tblParticipants as tP on tP.participantID = t.participantID
		on t.titleID = qidd.titleID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpVideoPreviews') IS NOT NULL 
		DROP TABLE #tmpVideoPreviews;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

ALTER PROC dbo.queue_SWReplaceMediaFile_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSWQueueItems') IS NOT NULL
		DROP TABLE #tmpSWQueueItems;
	CREATE TABLE #tmpSWQueueItems (itemID int);

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @environmentName varchar(50), @environmentID int;
	EXEC dbo.queue_getQueueTypeID @queueType='SWReplaceMediaFile', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpSWQueueItems
	FROM dbo.queue_SWReplaceMediaFile AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_SWReplaceMediaFile
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT DISTINCT qid.itemID, qid.seminarID, qid.titleID, qid.oldFileID, qid.newFileID
	FROM #tmpSWQueueItems as tmp
	INNER JOIN dbo.queue_SWReplaceMediaFile as qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpSWQueueItems') IS NOT NULL
		DROP TABLE #tmpSWQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SWLWebinarReminder_grabForProcessing
@batchCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='SWLWebinarReminder', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded where run date passed
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_SWLWebinarReminder AS qid
	INNER JOIN (
		SELECT TOP (@batchCount) itemID
		FROM dbo.queue_SWLWebinarReminder
		WHERE statusID = @statusReady
		AND runDate <= GETDATE()
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.participantID, qid.seminarID, qid.matchingTimeFrame, qid.runDate,
		qid.isRegistrantInstructionsEnabled, qid.isSpeakerInstructionsEnabled, qid.isWebinarMaterialEnabled 
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_SWLWebinarReminder AS qid ON qid.itemID = tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionRenewEmails_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @environmentName varchar(50), @environmentID int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenewEmails', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	SELECT @environmentName = tier FROM membercentral.dbo.fn_getServerSettings();
	SELECT @environmentID = environmentID FROM membercentral.dbo.platform_environments where environmentName = @environmentName;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;
	CREATE TABLE #tmpSubscribers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpSubscribers
	FROM dbo.queue_subscriptionRenewEmails AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_subscriptionRenewEmails
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	select distinct qid.itemID, qid.orgID, qid.siteID, s.subscriberID, m.activeMemberID as memberID, subs.renewEmailTemplateID,
		se.siteID, se.siteCode, se.subscriptionsSiteResourceID, sh.hostname as mainHostname,
		se.useRemoteLogin, se.siteName, se.defaultLanguageId,
		n.supportProviderEmail, n.emailFrom as networkEmailFrom, oi.organizationName as orgName
	FROM #tmpSubscribers as tmp
	INNER JOIN dbo.queue_subscriptionRenewEmails as qid ON qid.itemID = tmp.itemID
	inner join membercentral.dbo.sub_subscribers as s on s.orgID = qid.orgID
		and s.subscriberID = qid.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
	inner join membercentral.dbo.sites as se on se.siteID = qid.siteID
	inner join membercentral.dbo.siteHostnames as sh on sh.siteID = se.siteID
	inner join membercentral.dbo.siteEnvironments senv on senv.siteID=sh.siteID
		and senv.environmentID = @environmentID
		and senv.mainHostnameID = sh.hostNameID
	inner join membercentral.dbo.networkSites as ns on ns.siteID = se.siteID
		and ns.isLoginNetwork = 1
	inner join membercentral.dbo.networks as n on n.networkID = ns.networkID
	inner join membercentral.dbo.organizations as o on o.orgid = se.orgid
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID
		and oi.orgIdentityID = o.defaultOrgIdentityID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionRenew_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpqueue_subscriptionRenew') IS NOT NULL 
		DROP TABLE #tmpqueue_subscriptionRenew;
	CREATE TABLE #tmpqueue_subscriptionRenew (itemID int, recordedByMemberID int, orgID int, siteID int, 
		subscriberID int, rescindDate datetime, overrideStartDate datetime);

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize int, @batchUID uniqueidentifier, @itemsToProcess int = 0;
	DECLARE @taskParams TABLE (batchSize int, batchUID uniqueidentifier, requestedBatchSize int);
	
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenew', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- CAUTION:  Raising the batch size may cause maximum recursion issues.
	SET @batchSize = 100;
	SET @batchUID = NEWID();

	INSERT INTO @taskParams (batchSize, batchUID, requestedBatchSize) 
	VALUES (0, @batchUID, @batchSize);

	SELECT @itemsToProcess = COUNT(*)
	FROM dbo.queue_subscriptionRenew
	WHERE statusID = @statusReady

	IF @itemsToProcess > 0 BEGIN
		-- dequeue
		;WITH subsRenewBatch AS (
			select top 1 itemID, itemGroupUID, treeSize as totalSubs
			from dbo.queue_subscriptionRenew
			where statusID = @statusReady
			order by queuePriority
				union all
			select tmp.itemID, tmp.itemGroupUID, tmp.totalSubs
			from (
				select qi.itemID, qi.itemGroupUID, qi.treeSize + rb.totalSubs AS totalSubs,
					ROW_NUMBER() OVER (ORDER BY qi.queuePriority, qi.itemID) as rowNum
				from dbo.queue_subscriptionRenew as qi
				inner join subsRenewBatch as rb on rb.itemGroupUID = qi.itemGroupUID
				where qi.statusID = @statusReady
				and qi.itemID > rb.itemID
			) tmp
			where tmp.rowNum = 1
			and tmp.totalSubs <= @batchSize
		)
		UPDATE qid WITH (UPDLOCK, READPAST)
		SET qid.statusID = @statusGrabbed,
			qid.batchUID = @batchUID,
			qid.dateUpdated = GETDATE()
			OUTPUT inserted.itemID, inserted.recordedByMemberID, inserted.orgID, inserted.siteID, 
				inserted.subscriberID, inserted.rescindDate, inserted.overrideStartDate
			INTO #tmpqueue_subscriptionRenew
		FROM dbo.queue_subscriptionRenew as qid
		INNER JOIN subsRenewBatch as tmp on tmp.itemID = qid.itemID
		WHERE qid.statusID = @statusReady;

		UPDATE @taskParams SET batchSize = @@ROWCOUNT;
	END

	-- final data
	select qid.itemID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.subscriberID, qid.rescindDate, qid.overrideStartDate
	from #tmpqueue_subscriptionRenew as qid;

	-- qryTaskParams
	select batchSize, batchUID, requestedBatchSize
	from @taskParams;

	IF OBJECT_ID('tempdb..#tmpqueue_subscriptionRenew') IS NOT NULL 
		DROP TABLE #tmpqueue_subscriptionRenew;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionActiveEmails_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionActiveEmails', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;
	CREATE TABLE #tmpSubscribers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpSubscribers
	FROM dbo.queue_subscriptionActiveEmails AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_subscriptionActiveEmails
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	select distinct qid.itemID, qid.orgID, qid.siteID, s.subscriberID, m.activeMemberID as memberID, subs.emailTemplateID,
		et.emailFromName, et.emailFrom, et.subjectLine, se.siteCode, se.useRemoteLogin, se.siteName, se.defaultLanguageId,
		n.supportProviderEmail, n.emailFrom as networkEmailFrom, oi.organizationName as orgName
	FROM #tmpSubscribers as tmp
	INNER JOIN dbo.queue_subscriptionActiveEmails as qid ON qid.itemID = tmp.itemID
	inner join membercentral.dbo.sub_subscribers as s on s.orgID = qid.orgID
		and s.subscriberID = qid.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.et_emailTemplates as et on et.templateID = subs.emailTemplateID
	inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
	inner join membercentral.dbo.sites as se on se.siteID = qid.siteID
	inner join membercentral.dbo.networkSites as ns on ns.siteID = se.siteID
		and ns.isLoginNetwork = 1
	inner join membercentral.dbo.networks as n on n.networkID = ns.networkID
	inner join membercentral.dbo.organizations as o on o.orgid = se.orgid
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID
		and oi.orgIdentityID = o.defaultOrgIdentityID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sendgridSuppressions_grabForProcessing
@batchCount_singleEmail int = 30,
@batchCount_subUser int = 5

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	DECLARE @queueTypeID int, @readyToProcessStatusID int, @grabbedForProcessingStatusID int, @itemGroupUID uniqueIdentifier;
	EXEC dbo.queue_getQueueTypeID @queueType='SendgridSuppressions', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@grabbedForProcessingStatusID OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @grabbedForProcessingStatusID,
		qi.dateUpdated = GETDATE()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_sendgridSuppressions AS qi
	INNER JOIN (
		SELECT top(@batchCount_subUser) qi2.itemID
		FROM dbo.queue_sendgridSuppressions AS qi2
		WHERE qi2.statusID = @readyToProcessStatusID
		AND qi2.runAfter < GETDATE()
		AND qi2.emailAddress is null
		ORDER BY qi2.runAfter, qi2.dateAdded, qi2.itemID

		union

		SELECT top(@batchCount_singleEmail) qi2.itemID
		FROM dbo.queue_sendgridSuppressions AS qi2
		WHERE qi2.statusID = @readyToProcessStatusID
		AND qi2.runAfter < GETDATE()
		AND qi2.emailAddress is not null
		ORDER BY qi2.runAfter, qi2.dateAdded, qi2.itemID
	) AS batch ON batch.itemID = qi.itemID
	WHERE qi.statusID = @readyToProcessStatusID;

	SELECT qi.itemID, qi.itemGroupUID, qi.siteID, qi.subuserID, ss.username AS subuserName,
		qi.emailAddress, qi.referencedRecipientID, qi.dateAdded, qi.dateUpdated, qi.runAfter
	FROM dbo.queue_sendgridSuppressions AS qi
	INNER JOIN #tmpQueueItems AS tmp ON tmp.itemID = qi.itemID
	INNER JOIN platformmail.dbo.sendgrid_subusers AS ss ON ss.subuserID = qi.subuserID
	ORDER BY qi.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

ALTER PROC dbo.queue_schedReportChangeNotify_grabForProcessing
@batchCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='schedReportChangeNotify', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = GETDATE()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_schedReportChangeNotify AS qid
	INNER JOIN (
		SELECT TOP (@batchCount) itemID
		FROM dbo.queue_schedReportChangeNotify
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.reportID, qid.actorMemberID, qid.changeSection, s.siteCode, s.orgID, r.reportName,
		tt.toolType, tt.toolDesc, mActive.firstName AS actorFirstName, mActive.lastName AS actorLastName, me.email AS actorEmail
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_schedReportChangeNotify AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.rpt_SavedReports AS r ON r.reportID = qid.reportID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = r.siteID
	INNER JOIN membercentral.dbo.admin_toolTypes AS tt ON tt.toolTypeID = r.toolTypeID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = qid.actorMemberID
	INNER JOIN membercentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.organizations AS o ON o.orgID = mActive.orgID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = o.orgID AND metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = o.orgID 
		AND metagt.emailTagTypeID = metag.emailTagTypeID
		AND metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID = o.orgID
		AND me.memberID = metag.memberID
		AND me.emailTypeID = metag.emailTypeID;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sageCCCIMTest_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='sageCCCIMTest', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_sageCCCIMTest as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_sageCCCIMTest
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID,qid.profileID,qid.recordedByMemberID
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_sageCCCIMTest AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_refreshMemberPhoto_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='refreshMemberPhoto', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmprefreshMemberPhotos') IS NOT NULL
		DROP TABLE #tmprefreshMemberPhotos;
	CREATE TABLE #tmprefreshMemberPhotos (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmprefreshMemberPhotos
	FROM dbo.queue_refreshMemberPhoto AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_refreshMemberPhoto
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID,qid.orgID, qid.statusID
	FROM #tmprefreshMemberPhotos AS tmp
	INNER JOIN dbo.queue_refreshMemberPhoto AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmprefreshMemberPhotos') IS NOT NULL
		DROP TABLE #tmprefreshMemberPhotos;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_refreshDashboardObjects_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='refreshDashboardObjects', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpDashboardObjects') IS NOT NULL
		DROP TABLE #tmpDashboardObjects;
	CREATE TABLE #tmpDashboardObjects (itemID int);

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = GETDATE()
		OUTPUT inserted.itemID
		INTO #tmpDashboardObjects
	FROM dbo.queue_refreshDashboardObjects as qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_refreshDashboardObjects
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT DISTINCT qid.itemID, qid.siteID, qid.siteCode, qid.objectID, qid.objectTypeCode
	FROM #tmpDashboardObjects AS tmp
	INNER JOIN dbo.queue_refreshDashboardObjects AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpDashboardObjects') IS NOT NULL
		DROP TABLE #tmpDashboardObjects;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_pubSyndIssueDist_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='pubSyndIssueDist', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpIssues') IS NOT NULL
		DROP TABLE #tmpIssues;
	CREATE TABLE #tmpIssues (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpIssues
	FROM dbo.queue_pubSyndIssueDist as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_pubSyndIssueDist
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	-- return issue info without depending much on publication table since they may be altered after queuing and queue items will be orphaned
	SELECT DISTINCT qid.itemID, qid.parentSiteID, qid.parentPublicationID, qid.parentPublicationIssueID,
		qid.childSiteID, qid.childPublicationID, s.siteCode AS childSiteCode, qid.enteredByMemberID
	FROM #tmpIssues AS tmp
	INNER JOIN dbo.queue_pubSyndIssueDist AS qid ON qid.itemID = tmp.itemID
	LEFT OUTER JOIN membercentral.dbo.pub_publications AS p
		INNER JOIN membercentral.dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
		INNER JOIN membercentral.dbo.sites AS s ON s.siteID = ai.siteID
		ON p.publicationID = qid.childPublicationID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpIssues') IS NOT NULL
		DROP TABLE #tmpIssues;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_processPreApproveDepoDocs_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize tinyint = 45;
	EXEC dbo.queue_getQueueTypeID @queueType='processPreApproveDepoDocs', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_processPreApproveDepoDocs as qi
	INNER JOIN (
		SELECT top (@batchSize) qi2.itemID 
		FROM dbo.queue_processPreApproveDepoDocs as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoDocumentID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_processPreApproveDepoDocs as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

ALTER PROC dbo.queue_payTSBalance_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 60, @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='PayTSBalance', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL 
		DROP TABLE #tmpPayments;
	CREATE TABLE #tmpPayments (itemID int);

	-- dequeue in order of dateAdded. get @batchsize payments
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpPayments
	FROM dbo.queue_payTSBalance as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_payTSBalance as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qi.itemID, qi.depoMemberDataID, qi.payProfileID, qi.paymentAmount
	from #tmpPayments as tmp
	inner join dbo.queue_payTSBalance as qi on qi.itemID = tmp.itemID
	order by qi.itemID;

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL 
		DROP TABLE #tmpPayments;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_payInvoices_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 40, @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='payInvoices', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL 
		DROP TABLE #tmpPayments;
	IF OBJECT_ID('tempdb..#tmpInvStateZipForTax') IS NOT NULL 
		DROP TABLE #tmpInvStateZipForTax;
	IF OBJECT_ID('tempdb..#tmpReturn') IS NOT NULL 
		DROP TABLE #tmpReturn;
	CREATE TABLE #tmpPayments (itemID int, orgID int, merchantProfileID int, memberPaymentProfileID int);
	CREATE TABLE #tmpInvStateZipForTax (invoiceID int, stateIDForTax int, zipForTax varchar(25));

	-- dequeue in order of dateAdded. get @batchsize payments
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.orgID, inserted.MPProfileID, inserted.memberPaymentProfileID 
		INTO #tmpPayments (itemID, orgID, merchantProfileID, memberPaymentProfileID)
	FROM dbo.queue_payInvoices as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_payInvoices as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- return payment and invoice information
	-- left outer amount in case the invoice has been acted upon since it was added to the queue
	-- status of mp_profile doesnt matter here - if not active, payment will fail.
	select qid.itemID, o.orgID, qid.memberPaymentProfileID, qidd.invoiceid, 
		qidd.payProcessFee, qidd.processFeePercent, mpp.customerProfileID, 
		mpp.paymentProfileID, b.routingNumber, b.accountNumber, b.acctType, s.siteID, mp.gatewayID, g.gatewayType, mp.profileID, 
		m.activeMemberID as memberID, o.useBatches, s.sitename, mp.profileCode, 
		mp.processFeeDonationRenevueGLAccountID as processFeeRevGlAccountID, 
		mp.processFeeDonationRevTransDesc as processFeeRevTransDesc,
		cast(mpp.otherFields as varchar(4000)) as otherFields,
		mpp.surchargeEligible, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID, mp.processingFeeLabel,
		o.orgcode + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvDueAmount,
		cast(0 as decimal(18,2)) as additionalPaymentFee, cast(0 as decimal(18,2)) as paymentAmount, cast(0 as tinyint) as paymentFeeTypeID
	into #tmpReturn
	from #tmpPayments as qid
	inner join dbo.queue_payInvoicesDetail as qidd on qidd.itemID = qid.itemID
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = qidd.invoiceID
	inner join membercentral.dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = qid.memberPaymentProfileID
	inner join membercentral.dbo.mp_profiles as mp on mp.profileID = qid.merchantProfileID
	inner join membercentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
	inner join membercentral.dbo.sites as s on s.siteID = mp.siteID
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.ams_members as m on m.memberid = mpp.memberid
	left outer join membercentral.dbo.tr_invoiceTransactions as it on it.orgID = o.orgID and it.invoiceID = i.invoiceid
	left outer join membercentral.dbo.tr_bankAccounts as b on b.MPPPayProfileID = mpp.payProfileID
	group by qid.itemID, o.orgID, qid.memberPaymentProfileID, qidd.invoiceid, 
		qidd.payProcessFee, qidd.processFeePercent, mpp.customerProfileID, 
		mpp.paymentProfileID, b.routingNumber, b.accountNumber, b.acctType, s.siteID, mp.gatewayID, g.gatewayType, mp.profileID,
		m.activeMemberID, o.useBatches, s.sitename, mp.profileCode, 
		mp.processFeeDonationRenevueGLAccountID, mp.processFeeDonationRevTransDesc, 
		cast(mpp.otherFields as varchar(4000)), o.orgcode + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber),
		mpp.surchargeEligible, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID, mp.processingFeeLabel
	order by qid.itemID;

	-- processing fees
	UPDATE #tmpReturn
	SET additionalPaymentFee = ROUND(InvDueAmount * processFeePercent / 100,2),
		paymentFeeTypeID = 1
	WHERE payProcessFee = 1
	AND processFeePercent > 0
	AND processFeeRevGlAccountID IS NOT NULL;

	-- surcharge
	UPDATE #tmpReturn
	SET additionalPaymentFee = ROUND(InvDueAmount * surchargePercent / 100,2),
		paymentFeeTypeID = 2
	WHERE enableSurcharge = 1
	AND surchargePercent > 0
	AND surchargeRevenueGLAccountID IS NOT NULL
	AND surchargeEligible = 1;


	-- get payment amounts in the final total
	update tmp
	set tmp.paymentAmount = innersum.paymentAmount
	from #tmpReturn as tmp
	inner join (
		select itemID, sum(InvDueAmount + isnull(additionalPaymentFee,0)) as paymentAmount
		from #tmpReturn
		group by itemID
	) as innersum on innersum.itemID = tmp.itemID;

	-- update queue_payInvoices for the notification report
	update qid
	set qid.paymentAmount = innersum.paymentAmount
	from dbo.queue_payInvoices as qid
	inner join (
		select distinct itemID, paymentAmount
		from #tmpReturn
	) as innersum on innersum.itemID = qid.itemID;

	update qidd
	set qidd.invoiceDueAmount = tmp.InvDueAmount,
		qidd.additionalPaymentFee = tmp.additionalPaymentFee
	from dbo.queue_payInvoicesDetail as qidd
	inner join #tmpReturn as tmp on tmp.itemID = qidd.itemID and tmp.invoiceID = qidd.invoiceID;

	INSERT INTO #tmpInvStateZipForTax (invoiceID, stateIDForTax, zipForTax)
	SELECT tmp.invoiceID, MIN(ts.stateIDForTax), MIN(ts.zipForTax)
	FROM #tmpReturn AS tmp
	INNER JOIN membercentral.dbo.tr_invoiceTransactions AS it ON it.orgID = tmp.orgID
		AND it.invoiceID = tmp.invoiceID
	INNER JOIN membercentral.dbo.tr_transactionSales AS ts ON ts.orgID = tmp.orgID
		AND ts.transactionID = it.transactionID
	GROUP BY tmp.invoiceID;

	-- final data
	select tmp.itemID, tmp.orgID, tmp.siteID, tmp.memberPaymentProfileID, tmp.invoiceid, tmp.customerProfileID, 
		tmp.paymentProfileID, tmp.routingNumber, tmp.accountNumber, tmp.acctType, tmp.gatewayID, tmp.gatewayType, 
		tmp.profileID, tmp.memberID, tmp.useBatches, tmp.sitename, tmp.profileCode, tmp.otherFields, tmp.invoiceNumber, 
		tmp.InvDueAmount, tmp.additionalPaymentFee, tmp.paymentAmount, tmp.processFeePercent, tmp.processFeeRevGlAccountID, 
		tmp.processFeeRevTransDesc,tmp.enableSurcharge, tmp.surchargePercent, tmp.surchargeRevenueGLAccountID, 
		tmp.paymentFeeTypeID, sz.stateIDForTax, sz.zipForTax, tmp.surchargeEligible, 
		case tmp.paymentFeeTypeID when 1 then tmp.processingFeeLabel when 2 then 'Surcharge' else null end as additionalFeeLabel
	from #tmpReturn AS tmp
	left outer join #tmpInvStateZipForTax AS sz ON sz.invoiceID = tmp.invoiceID
	order by tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL 
		DROP TABLE #tmpPayments;
	IF OBJECT_ID('tempdb..#tmpInvStateZipForTax') IS NOT NULL 
		DROP TABLE #tmpInvStateZipForTax;
	IF OBJECT_ID('tempdb..#tmpReturn') IS NOT NULL 
		DROP TABLE #tmpReturn;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_moveGroupUsage_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='moveGroupUsage', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpmoveGroupUsage') IS NOT NULL
		DROP TABLE #tmpmoveGroupUsage;
	CREATE TABLE #tmpmoveGroupUsage (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpmoveGroupUsage
	FROM dbo.queue_moveGroupUsage AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_moveGroupUsage
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.orgID, qid.siteID, qid.groupID, qid.moveToGroupID, qid.recordedByMemberID, qid.statusID
	FROM #tmpmoveGroupUsage AS tmp
	INNER JOIN dbo.queue_moveGroupUsage AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpmoveGroupUsage') IS NOT NULL
		DROP TABLE #tmpmoveGroupUsage;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillTSRoyalty_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 100, @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSRoyalty', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	CREATE TABLE #tmpStatements (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpStatements
	FROM dbo.queue_monthBillTSRoyalty as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillTSRoyalty as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qi.itemID, qi.orgCode, oi.organizationName as orgName, qi.DepoSalePCT, qi.DepoContribAMT, qi.SubSalePCT, qi.eclipsMonthAMT, 
		qi.DepoSales, qi.DepoSpecialSales, qi.DepoContributions, qi.SubscriptionSales, qi.DepoSalesRoyalty, 
		qi.DepoSpecialSalesRoyalty, qi.DepoContributionsRoyalty, qi.SubscriptionSalesRoyalty, qi.eclipsRoyalty, 
		qi.TotalRoyalty, bp.EOMPeriod
	from #tmpStatements as tmp
	inner join dbo.queue_monthBillTSRoyalty as qi on qi.itemID = tmp.itemID
	inner join trialsmith.dbo.billingPeriods as bp on bp.periodID = qi.billingPeriodID
	inner join membercentral.dbo.sites as s on s.siteCode = qi.orgCode
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	order by qi.itemID;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillTSA_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 60, @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSA', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	CREATE TABLE #tmpStatements (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpStatements
	FROM dbo.queue_monthBillTSA as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillTSA as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qi.itemID, qi.depomemberDataID, qi.transStartDate, qi.transEndDate, qi.statementDate
	from #tmpStatements as tmp
	inner join dbo.queue_monthBillTSA as qi on qi.itemID = tmp.itemID
	order by qi.itemID;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillSW_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 100, @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillSW', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	CREATE TABLE #tmpStatements (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpStatements
	FROM dbo.queue_monthBillSW as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillSW as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qi.itemID, qi.runID, qi.participantID
	from #tmpStatements as tmp
	inner join dbo.queue_monthBillSW as qi on qi.itemID = tmp.itemID
	order by qi.itemID;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillEmailOrg_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 60, @queueTypeID int, @statusReady int, @statusGrabbed int,
		@applicationTypeID int = membercentral.dbo.fn_getApplicationTypeIDFromName('BuyNow'),
		@siteID int = membercentral.dbo.fn_getSiteIDFromSiteCode('MC'), @settingsXML xml;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillEmailOrg', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	SELECT @settingsXML = settingsXML
	FROM membercentral.dbo.cms_applicationTypeSettings
	WHERE siteID = @siteID
	AND applicationTypeID = @applicationTypeID;

	SELECT @settingsXML = ISNULL(@settingsXML,'<settings />');

	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	CREATE TABLE #tmpOrgs (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpOrgs
	FROM dbo.queue_monthBillEmailOrg as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillEmailOrg as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qieo.itemID, bp.EOMPeriod, qieo.siteCode, oi.organizationName as orgName, mcb.depoMemberDataID, mcb.billingEmail, 
		qisw.folderPath as swFolderPath, qisw.[filename] as swFileName, qisw.isPayable as swIsPayable, 
		qisw.payableAmount as swPayableAmount, qitsr.folderPath as tsrFolderPath, qitsr.[filename] as tsrFileName,
		qitsr.TotalRoyalty as tsrPayableAmount, qitsa.folderPath as tsFolderPath, qitsa.[filename] as tsFileName, 
		qitsa.balanceForward, qitsa.charges, qitsa.credits, qitsa.balanceEnd, qitsa.payLinkDirect, qitsa.payLinkCode, 
		d.sourceID,
		case 
		when exists (
			select dd.depoMemberDataID
			from trialsmith.dbo.depoMemberData as dd
			INNER JOIN trialsmith.dbo.ccMemberPaymentProfiles as TSCC on TSCC.depomemberdataID = dd.depomemberdataID 
				and TSCC.orgcode = 'TS'
				and TSCC.declined = 0
			where dd.depoMemberDataID = mcb.depoMemberDataID
			and dd.paymenttype = 'C'
			) then 1 
		else 0 
		end as willChargeCC,
		(select top 1 m.memberID 
			from membercentral.dbo.ams_members as m 
			where m.orgID = 1 
			and m.status = 'A' 
			and m.memberNumber = 'TSDEPOID_' + CAST(mcb.depoMemberDataID as varchar(10))) as MCMemberID
	from #tmpOrgs as tmp
	inner join dbo.queue_monthBillEmailOrg as qieo on qieo.itemID = tmp.itemID
	inner join trialsmith.dbo.memberCentralBilling as mcb on mcb.orgCode = qieo.siteCode
	inner join trialsmith.dbo.billingPeriods as bp on bp.periodID = qieo.billingPeriodID
	inner join membercentral.dbo.sites as s on s.siteCode = qieo.siteCode
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	left outer join dbo.queue_monthBillSW as qisw on qisw.billingPeriodID = qieo.billingPeriodID and qisw.siteCode = qieo.siteCode
	left outer join dbo.queue_monthBillTSRoyalty as qitsr on qitsr.billingPeriodID = qieo.billingPeriodID and qitsr.orgCode = qieo.siteCode
	left outer join dbo.queue_monthBillTSA as qitsa on qitsa.billingPeriodID = qieo.billingPeriodID and qitsa.depoMemberDataID = mcb.depoMemberDataID and qitsa.isOrg = 1
	left outer join trialsmith.dbo.depoMemberData as d on d.depomemberdataID = mcb.depoMemberDataID
	order by tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillEmailIndiv_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 60, @queueTypeID int, @statusReady int, @statusGrabbed int,
		@applicationTypeID int = membercentral.dbo.fn_getApplicationTypeIDFromName('BuyNow'),
		@siteID int = membercentral.dbo.fn_getSiteIDFromSiteCode('MC'), @settingsXML xml;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillEmailIndiv', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	SELECT @settingsXML = settingsXML
	FROM membercentral.dbo.cms_applicationTypeSettings
	WHERE siteID = @siteID
	AND applicationTypeID = @applicationTypeID;

	SELECT @settingsXML = ISNULL(@settingsXML,'<settings />');

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	CREATE TABLE #tmpMembers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpMembers
	FROM dbo.queue_monthBillEmailIndiv as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillEmailIndiv as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qieo.itemID, bp.EOMPeriod, d.FirstName, d.lastName, d.sourceID, qitsa.balanceForward, qitsa.charges, 
		qitsa.credits, qitsa.balanceEnd, qitsa.folderPath as tsFolderPath, qitsa.[filename] as tsFileName,
		qitsa.payLinkDirect, qitsa.payLinkCode,
		case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as billingEmail1,
		case when d.BillingContactEmail is not null and d.BillingContactEmail <> '' and d.BillingContactEmail <> d.Email then d.BillingContactEmail else null end as billingEmail2,
		case 
		when d.paymentType = 'C' AND exists (select depoMemberDataID from trialsmith.dbo.ccMemberPaymentProfiles WHERE depomemberdataID = qieo.depomemberdataID and orgcode = 'TS' and declined = 0) then 1 
		else 0 
		end as willChargeCC,
		(select top 1 m.memberID 
			from membercentral.dbo.ams_members as m 
			where m.orgID = 1 
			and m.status = 'A' 
			and m.memberNumber = 'TSDEPOID_' + CAST(qieo.depoMemberDataID as varchar(10))) as MCMemberID
	from #tmpMembers as tmp
	inner join dbo.queue_monthBillEmailIndiv as qieo on qieo.itemID = tmp.itemID
	inner join trialsmith.dbo.billingPeriods as bp on bp.periodID = qieo.billingPeriodID
	inner join trialsmith.dbo.depoMemberData as d on d.depomemberdataID = qieo.depoMemberDataID
	inner join dbo.queue_monthBillTSA as qitsa on qitsa.billingPeriodID = qieo.billingPeriodID and qitsa.depoMemberDataID = qieo.depoMemberDataID and qitsa.isOrg = 0
	order by tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_memberPhotoThumb_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='memberPhotoThumb', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpThumbs') IS NOT NULL 
		DROP TABLE #tmpThumbs;
	CREATE TABLE #tmpThumbs (itemID int PRIMARY KEY);

	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpThumbs
	from dbo.queue_memberPhotoThumb as qi
	inner join (
		select top(@BatchSize) itemID
		from dbo.queue_memberPhotoThumb
		where statusID = @statusReady
		order by dateAdded, itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	select qidd.itemID, qidd.orgcode, qidd.memberid, qidd.membernumber, qidd.width, qidd.height
	from #tmpThumbs as qid
	inner join dbo.queue_memberPhotoThumb as qidd on qidd.itemID = qid.itemID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpThumbs') IS NOT NULL 
		DROP TABLE #tmpThumbs;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

ALTER PROC dbo.queue_sitemaps_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='sitemaps', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpSitesToRun') IS NOT NULL 
		DROP TABLE #tmpSitesToRun;
	CREATE TABLE #tmpSitesToRun (itemID int, siteID int);

	-- dequeue in order of dateAdded.
	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.siteID
		INTO #tmpSitesToRun
	from dbo.queue_sitemaps as qi
	inner join (
		select top(1) qi2.itemID 
		from dbo.queue_sitemaps as qi2
		where qi2.statusID = @statusReady
		order by qi2.dateAdded, qi2.itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	select itemID, siteID
	from #tmpSitesToRun;

	IF OBJECT_ID('tempdb..#tmpSitesToRun') IS NOT NULL 
		DROP TABLE #tmpSitesToRun;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_screenshots_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchstartdate datetime = getdate();
	EXEC dbo.queue_getQueueTypeID @queueType='screenshots', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpScreenShots') IS NOT NULL
		DROP TABLE #tmpScreenShots;
	CREATE TABLE #tmpScreenShots (itemID int);

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = @batchstartdate
		OUTPUT inserted.itemID
		INTO #tmpScreenShots
	FROM dbo.queue_screenshots as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_screenshots
		WHERE statusID = @statusReady and nextAttemptDate < @batchstartdate
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	select distinct qid.itemID, qid.siteID, o.orgCode, s.siteCode, cv.rawContent as htmlContent, qid.viewportWidth, qid.viewportHeight, 
        qid.deviceScaleFactor, qid.fullPage, qid.featureImageConfigID, qid.referenceType, qid.referenceID, qid.enteredByMemberID
	from #tmpScreenShots as tmp
	inner join dbo.queue_screenshots as qid on qid.itemID = tmp.itemID
	inner join membercentral.dbo.sites as s on s.siteID = qid.siteID
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qid.contentVersionID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpScreenShots') IS NOT NULL
		DROP TABLE #tmpScreenShots;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_payInvoices_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='payInvoices', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_payInvoices
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_payInvoices
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_payInvoices as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- get system account
	declare @enteredByMemberID int;
	select @enteredByMemberID = membercentral.dbo.fn_ams_getMCSystemMemberID();

	-- return report information
	-- status of mp_profile is not important here
	select tmpN.itemGroupUID, qid.itemID, m2.memberID, s.siteID, o.orgID, 
		m2.lastname + ', ' + m2.firstname as memberName, m2.membernumber, m2.company, 
		qid.paymentAmount, qidd.invoiceDueAmount, qidd.additionalPaymentFee, o.orgcode + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
		mpp.detail as payMethodDetail, mp.profileName as payProfileName, qid.paymentErrorMessage, oi.organizationName as orgName, h.datePaid,
		m4.memberid as recordedByMemberID, s.siteCode, ip.profileName as invoiceProfileName, i.dateDue,
		case when qid.recordedByMemberID = @enteredByMemberID then o.accountingEmail else m4e.email end as accountingEmail,
		case when qid.recordedByMemberID = @enteredByMemberID then 1 else 0 end as isSystemGenerated,
		case when qidd.additionalPaymentFee > 0 and mp.enableProcessingFeeDonation = 1 then mp.processingFeeLabel 
			when  qidd.additionalPaymentFee > 0 and mp.enableSurcharge = 1 then 'Surcharge'
		end as additionalFeeLabel
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.queue_payInvoices as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN platformQueue.dbo.queue_payInvoicesDetail as qidd ON qidd.itemID = qid.itemID
	INNER JOIN membercentral.dbo.tr_invoices as i on i.invoiceID = qidd.invoiceID
	INNER JOIN membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = i.assignedToMemberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = qid.memberPaymentProfileID
	INNER JOIN membercentral.dbo.mp_profiles as mp on mp.profileID = qid.MPProfileID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = mp.siteID
	INNER JOIN membercentral.dbo.organizations as o on o.orgID = qid.orgID
	INNER JOIN membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	LEFT OUTER JOIN membercentral.dbo.tr_paymentHistory as h on h.historyID = qid.paymentHistoryID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as m4e on m4e.orgID = m4.orgID
		and m4e.memberID = metag.memberID
		and m4e.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when isnull(qid.paymentErrorMessage,'') <> '' then 1 else 2 end, m2.lastname, m2.firstname, m2.membernumber, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_triggerMCGCache
@runImmediately bit,
@type varchar(26)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- temp table needs to be there
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NULL
		RAISERROR('holding table #tblMCQRun does not exist.',16,1);

	-- if no rows, no need to continue
	IF (SELECT count(*) from #tblMCQRun) = 0 GOTO on_done;

	DECLARE @orgID int;
	select top 1 @orgID = orgID from #tblMCQRun;

	-- remove deleted members from the table. 
	-- This can happen in member merges where this proc is triggered when updating the merged accounts. This code is a safeguard to try to remove them from processing.
	DELETE tmp
	FROM #tblMCQRun as tmp
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID = @orgID 
		AND m.memberID = tmp.memberID 
		AND m.status = 'D';

	-- if no rows, no need to continue
	IF (SELECT count(*) from #tblMCQRun) = 0 GOTO on_done;

	-- valid type
	IF @type NOT IN ('ConditionsOnly','ConditionsOnlyNonImm','GroupsOnly','ConditionsAndGroups','ConditionsAndGroupsChanged')
		RAISERROR('invalid trigger type.',16,1);

	-- construct XML message
	DECLARE @xmlMessage xml;
	select @xmlMessage = isnull((
		select 'memberConditions' as t, @type as pt, tmp.orgID as o, 
			(
			select distinct memberID as m
			from #tblMCQRun
			where memberID is not null
			FOR XML PATH(''), TYPE
			),
			(
			select distinct conditionID as c
			from #tblMCQRun
			where conditionID is not null
			FOR XML PATH(''), TYPE
			)
		from #tblMCQRun as tmp
		group by tmp.orgID
		FOR XML RAW('mc'), TYPE
	),'<mc/>');


	-- log the message and add logID to the xml
	declare @triggerLogID bigint, @dateTriggered datetime = getdate();

	INSERT INTO platformStatsMC.dbo.cache_conditionsTriggerLog (dateTriggered, runImmediately, processType, xmlData)
	VALUES (@dateTriggered, @runImmediately, @type, @xmlMessage);

	select @triggerLogID = SCOPE_IDENTITY();

	SET @xmlMessage.modify('insert attribute x {sql:variable("@triggerLogID")} into (/*)[1]');
	SET @xmlMessage.modify('insert attribute dt {sql:variable("@dateTriggered")} into (/*)[1]');


	-- if runImmediately = 0 we use service broker
	IF @runImmediately = 0
		EXEC dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

	-- Else, we process cache immediately
	ELSE BEGIN
		
		DECLARE @itemGroupUID uniqueidentifier = NEWID(), @dateAdded datetime = getdate(), @queueTypeID int, @readyQueueStatusID int, 
			@notReadyQueueStatusID int, @memberConditionsQueueTypeID int, @memberConditionsReadyQueueStatusID int;
		EXEC dbo.queue_getQueueTypeID @queueType='memberGroups', @queueTypeID=@queueTypeID OUTPUT;
		EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;
		EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='notReady', @queueStatusID=@notReadyQueueStatusID OUTPUT;
		EXEC dbo.queue_getQueueTypeID @queueType='memberConditions', @queueTypeID=@memberConditionsQueueTypeID OUTPUT;
		EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@memberConditionsQueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;

		IF OBJECT_ID('tempdb..#tmpMCQCondCacheChanges') IS NOT NULL 
			DROP TABLE #tmpMCQCondCacheChanges;
		CREATE TABLE #tmpMCQCondCacheChanges (memberID int, conditionID int, isAdded bit);

		IF @type = 'ConditionsOnly' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=0;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'ConditionsOnlyNonImm' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=1;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'GroupsOnly' BEGIN
			IF EXISTS (select 1 from #tblMCQRun where memberID is null)
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select @itemGroupUID, @triggerLogID, @orgID, m.memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from membercentral.dbo.ams_members as m
				where m.orgID = @orgID
				and m.status <> 'D';
			ELSE 
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberGroupsFromQueue @orgID=@orgID, @itemGroupUID=@itemGroupUID, @runImmediately=1,
				@optimizeQueue=0, @dateTriggered=@dateTriggered;
			EXEC dbo.queue_MemberGroups_clearDone @orgID=@orgID, @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'ConditionsAndGroups' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=0;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			set @dateAdded = getdate();

			IF EXISTS (select 1 from #tblMCQRun where memberID is null)
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select @itemGroupUID, @triggerLogID, @orgID, m.memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from membercentral.dbo.ams_members as m
				where m.orgID = @orgID
				and m.status <> 'D';
			ELSE 
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberGroupsFromQueue @orgID=@orgID, @itemGroupUID=@itemGroupUID, @runImmediately=1, 
				@optimizeQueue=0, @dateTriggered=@dateTriggered;
			EXEC dbo.queue_MemberGroups_clearDone @orgID=@orgID, @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'ConditionsAndGroupsChanged' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=0;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			set @dateAdded = getdate();

			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
			from #tmpMCQCondCacheChanges;

			EXEC membercentral.dbo.ams_processMemberGroupsFromQueue @orgID=@orgID, @itemGroupUID=@itemGroupUID, @runImmediately=1, 
				@optimizeQueue=0, @dateTriggered=@dateTriggered;
			EXEC dbo.queue_MemberGroups_clearDone @orgID=@orgID, @itemGroupUID=@itemGroupUID;
		END

		IF OBJECT_ID('tempdb..#tmpMCQCondCacheChanges') IS NOT NULL 
			DROP TABLE #tmpMCQCondCacheChanges;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_TaskImport_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='TaskImport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyTasks') IS NOT NULL
		DROP TABLE #tmpNotifyTasks;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotifyTasks (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, siteID int, recordedByMemberID int, project varchar(200), prospectName varchar(250));

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	-- if none found, no need to continue. return empty query.
	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotifyTasks;
		GOTO on_done;
	END

	-- events in groupUID
	insert into #tmpNotifyTasks (itemGroupUID, itemUID, siteID, recordedByMemberID, project, prospectName)
	select itemGroupUID, itemUID, siteID, recordedByMemberID, projectContent.contentTitle, 
		mActive.firstName + ' ' + mActive.lastName + ' (' + mActive.memberNumber + ')'
	from (
		select qid.itemGroupUID, qid.itemUID, qid.siteID, qid.recordedByMemberID, dc.columnname, qid.columnValueInteger
		from (select distinct itemGroupUID from #tmpNotify) as tmpN
		inner join dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
		inner join dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID and dc.columnname in ('MCProjectID','MCProspectMemberID')
	) as tmp
	PIVOT (min(columnValueInteger) for columnname in (MCProjectID,MCProspectMemberID)) as pvt
	inner join memberCentral.dbo.tasks_projects as p on p.projectID = pvt.MCProjectID
	inner join memberCentral.dbo.ams_members as m on m.memberID = pvt.MCProspectMemberID
	inner join memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	cross apply memberCentral.dbo.fn_getContent(p.projectContentID,1) as projectContent;	
	
	-- return notifying details
	select distinct tmpN.itemGroupUID, mActive.memberID as recordedByMemberID, me.email as reportEmail, 
		s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, 
		tmpN.project, tmpN.prospectName
	from #tmpNotifyTasks as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = mActive.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = mActive.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = mActive.orgID 
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, tmpN.prospectName, tmpN.project;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyTasks') IS NOT NULL
		DROP TABLE #tmpNotifyTasks;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SWReplaceMediaFile_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusWaiting int, @statusReady int;
	EXEC dbo.queue_getQueueTypeID @queueType='SWReplaceMediaFile', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingToProcess', @queueStatusID=@statusWaiting OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	-- look at anything waitingToProcess to see if any are readyToProcess
	UPDATE qi
	SET qi.statusID = @statusReady,
		qi.dateUpdated = getdate()
	FROM dbo.queue_SWReplaceMediaFile as qi
	INNER JOIN seminarWeb.dbo.tblFiles AS f ON f.fileID = qi.newFileID
		AND f.duration IS NOT NULL
	WHERE qi.statusID = @statusWaiting;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_swodCertificate_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='swodCertificate', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_swodCertificate
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_swodCertificate
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_swodCertificate as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_swlCertificate_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='swlCertificate', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;
	
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_swlCertificate
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_swlCertificate
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_swlCertificate as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionRenew_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = getdate();;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenew', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#subsRenewPriority') IS NOT NULL 
		DROP TABLE #subsRenewPriority;
	CREATE TABLE #subsRenewPriority (itemID int, itemGroupUID uniqueidentifier, totalQueued int, minutesInQueue int, priority int);

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	INSERT INTO #subsRenewPriority (itemID, itemGroupUID, minutesInQueue)
	select itemID, itemGroupUID, minsInQueue = datediff(minute,dateUpdated,@nowDate)
	from dbo.queue_subscriptionRenew
	where statusID in (@statusReady,@statusGrabbed);

	update rp 
	set rp.totalQueued = temp.totalQueued
	from #subsRenewPriority as rp
	inner join (
		select itemGroupUID, count(*) as totalQueued
		from #subsRenewPriority
		group by itemGroupUID
	) as temp on temp.itemGroupUID = rp.itemGroupUID;

	update temp 
	set priority = 
			case 
				when totalQueued = 1 then -100
				when minutesInQueue > 360 then (totalQueued / 50) + 1
				when minutesInQueue > 90 then (totalQueued / 25) - 10
				when minutesInQueue > 30 then (totalQueued / 25) + 2
				when totalQueued < 500 then (totalQueued / 25)
				else (totalQueued / 25) + 10
			end
	from #subsRenewPriority as temp;

	 -- delete rows where the priority hasn't changed
	delete temp
	from #subsRenewPriority as temp
	inner join dbo.queue_subscriptionRenew as qid on temp.itemID = qid.itemID
	where temp.priority = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = temp.priority
	FROM dbo.queue_subscriptionRenew as qid
	INNER JOIN #subsRenewPriority as temp on temp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#subsRenewPriority') IS NOT NULL 
		DROP TABLE #subsRenewPriority;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionRenew_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenew', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	INSERT INTO #tmpNotify (itemGroupUID)
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID = @statusReady
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID <> @statusReady;

	-- mark as grabbed
	UPDATE queueSR
	SET queueSR.statusID = @statusGrabbed,
		queueSR.dateUpdated = getdate()
	FROM dbo.queue_subscriptionRenew as queueSR
	INNER JOIN #tmpNotify as tmp on tmp.itemGroupUID = queueSR.itemGroupUID
	WHERE queueSR.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, qid.recordedByMemberID, qid.orgID, me.email as reportEmail, s.siteName, s.siteCode
	from #tmpNotify as tmpN
	INNER JOIN dbo.queue_subscriptionRenew as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	-- return report information for failures
	select tmpN.itemGroupUID, qid.rescindDate, qid.errorMessage, qid.wddxMessages
	from #tmpNotify as tmpN
	INNER JOIN dbo.queue_subscriptionRenew as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	order by tmpN.itemGroupUID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionRenew_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='subscriptionRenew', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpHoldingReprocessConditionsBySubscriberBulk (orgID int INDEX IX_tmpHolding_orgID, memberID int, subTypeID int, subscriptionID int);

	-- which itemGroupUIDs are all done
	INSERT INTO #tmpitemGroupUIDs (itemGroupUID)
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0 
		GOTO on_done;

	WITH allSubscribers AS (
		SELECT qi.siteID, qi.subscriberID
		FROM dbo.queue_subscriptionRenew as qi
		INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID
			union all
		SELECT alls.siteID, s.subscriberID
		FROM membercentral.dbo.sub_subscribers as s
		INNER JOIN allSubscribers as alls on alls.subscriberID = s.parentSubscriberID
	)
	INSERT INTO #tmpHoldingReprocessConditionsBySubscriberBulk (orgID, memberID, subTypeID, subscriptionID)
	SELECT DISTINCT s.orgID, m.activeMemberID, ssub.typeID, ssub.subscriptionID
	FROM allSubscribers as tmp
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmp.siteID
	INNER JOIN membercentral.dbo.sub_subscribers as sub on sub.orgID = s.orgID and sub.subscriberID = tmp.subscriberID
	INNER JOIN membercentral.dbo.sub_subscriptions as ssub on ssub.orgID = s.orgID and ssub.subscriptionID = sub.subscriptionID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = sub.memberID;

	-- call the bulk queue processing
	EXEC membercentral.dbo.sub_reprocessConditionsBySubscriberBulk;

	-- delete queue items
	DELETE queueSR
	FROM dbo.queue_subscriptionRenew as queueSR
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = queueSR.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionOffers_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = getdate();;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionOffers', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#subsOffersPriority') IS NOT NULL 
		DROP TABLE #subsOffersPriority;
	CREATE TABLE #subsOffersPriority (itemID int, itemGroupUID uniqueidentifier, totalQueued int, minutesInQueue int, priority int);

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	INSERT INTO #subsOffersPriority (itemID, itemGroupUID, minutesInQueue)
	select itemID, itemGroupUID, minsInQueue = datediff(minute,dateUpdated,@nowDate)
	from dbo.queue_subscriptionOffers
	where statusID in (@statusReady,@statusGrabbed);

	update rp 
	set rp.totalQueued = temp.totalQueued
	from #subsOffersPriority as rp
	inner join (
		select itemGroupUID, count(*) as totalQueued
		from #subsOffersPriority
		group by itemGroupUID
	) as temp on temp.itemGroupUID = rp.itemGroupUID;

	update temp 
	set priority = 
			case 
				when totalQueued = 1 then -100
				when minutesInQueue > 360 then (totalQueued / 50) + 1
				when minutesInQueue > 90 then (totalQueued / 25) - 10
				when minutesInQueue > 30 then (totalQueued / 25) + 2
				when totalQueued < 500 then (totalQueued / 25)
				else (totalQueued / 25) + 10
            end
	from #subsOffersPriority as temp;

	 -- delete rows where the priority hasn't changed
	delete temp
	from #subsOffersPriority as temp
	inner join dbo.queue_subscriptionOffers as qid on temp.itemID = qid.itemID
	where temp.priority = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = temp.priority
	FROM dbo.queue_subscriptionOffers as qid
	INNER JOIN #subsOffersPriority as temp on temp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#subsOffersPriority') IS NOT NULL 
		DROP TABLE #subsOffersPriority;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionOffers_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionOffers', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionOffers
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionOffers
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = GETDATE()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionOffers as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, sites.siteCode, sites.siteName
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionOffers as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as st on st.typeID = sub.typeID
	INNER JOIN membercentral.dbo.sites on sites.siteID = st.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID 
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when errorMessage is null then 0 else 1 end desc, subscriberName, subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionInactivate_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionInactivate', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionInactivate
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionInactivate
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionInactivate as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, 
		m4.firstname as reportFirstName, m4.lastname as reportLastName, m4.memberNumber as reportMemberNumber, 
		sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionInactivate as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sites on sites.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberID = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgId = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when qid.errorMessage is null then 0 else 1 end desc, subscriberName, sub.subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionForceAdd_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionForceAdd', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionForceAdd
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionForceAdd
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionForceAdd as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, sub.subscriptionName,
		subAdded.subscriptionID as newSubscriptionID, subAdded.subscriptionName as newSubscriptionName, tAdded.typeID as newTypeID, tAdded.typeName as newTypeName,
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		qid.resultMessage, qid.subscriberID as rootSubscriberID,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID,  m4.firstname as reportFirstName, m4.lastname as reportLastName, 
		m4.memberNumber as reportMemberNumber, sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionForceAdd as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as st on st.typeID = sub.typeID
	INNER JOIN membercentral.dbo.sub_subscriptions as subAdded on subAdded.subscriptionID = qid.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as tAdded on tAdded.typeID = subAdded.typeID
	INNER JOIN membercentral.dbo.sites on sites.siteID = st.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID 
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, subscriberName, subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionForceAdd_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='subscriptionForceAdd', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;
	
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_subscriptionForceAdd
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionForceAdd
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0 
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_subscriptionForceAdd as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionDelete_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionDelete', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionDelete
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionDelete
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionDelete as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, 
		m4.firstname as reportFirstName, m4.lastname as reportLastName, m4.memberNumber as reportMemberNumber, 
		sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionDelete as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sites on sites.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when qid.errorMessage is null then 0 else 1 end desc, subscriberName, sub.subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionDelete_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpHoldingReprocessConditionsBySubscriberBulk (orgID int INDEX IX_tmpHolding_orgID, memberID int, subTypeID int, subscriptionID int);

	DECLARE @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionDelete', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;
	
	-- which itemGroupUIDs are all done
	INSERT INTO #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_subscriptionDelete
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionDelete
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;

	WITH allSubscribers AS (
		SELECT qi.siteID, qi.subscriberID
		FROM dbo.queue_subscriptionDelete as qi
		INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID
			UNION ALL
		SELECT alls.siteID, s.subscriberID
		FROM membercentral.dbo.sub_subscribers as s
		INNER JOIN allSubscribers as alls on alls.subscriberID = s.parentSubscriberID
	)
	INSERT INTO #tmpHoldingReprocessConditionsBySubscriberBulk (orgID, memberID, subTypeID, subscriptionID)
	SELECT DISTINCT s.orgID, m.activeMemberID, ssub.typeID, ssub.subscriptionID
	FROM allSubscribers as tmp
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmp.siteID
	INNER JOIN membercentral.dbo.sub_subscribers as sub on sub.orgID = s.orgID and sub.subscriberID = tmp.subscriberID
	INNER JOIN membercentral.dbo.sub_subscriptions as ssub on ssub.orgID = s.orgID and ssub.subscriptionID = sub.subscriptionID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = sub.memberID;

	-- call the bulk queue processing
	EXEC membercentral.dbo.sub_reprocessConditionsBySubscriberBulk;

	DELETE qi
	from dbo.queue_subscriptionDelete as qi
	inner join #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionBilled_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionBilled', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionBilled
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionBilled
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionBilled as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, 
		m4.firstname as reportFirstName, m4.lastname as reportLastName, m4.memberNumber as reportMemberNumber, 
		sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionBilled as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites on sites.siteID = qid.siteID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.orgID = sites.orgID and s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when qid.errorMessage is null then 0 else 1 end desc, subscriberName, sub.subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionBilled_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpHoldingReprocessConditionsBySubscriberBulk (orgID int INDEX IX_tmpHolding_orgID, memberID int, subTypeID int, subscriptionID int);

	DECLARE @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionBilled', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	-- which itemGroupUIDs are all done
	INSERT INTO #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_subscriptionBilled
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionBilled
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;

	WITH allSubscribers AS (
		SELECT qi.siteID, qi.subscriberID
		FROM dbo.queue_subscriptionBilled as qi
		INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID
			UNION ALL
		SELECT alls.siteID, s.subscriberID
		FROM membercentral.dbo.sub_subscribers as s
		INNER JOIN allSubscribers as alls on alls.subscriberID = s.parentSubscriberID
	)
	INSERT INTO #tmpHoldingReprocessConditionsBySubscriberBulk (orgID, memberID, subTypeID, subscriptionID)
	SELECT DISTINCT s.orgID, m.activeMemberID, ssub.typeID, ssub.subscriptionID
	FROM allSubscribers as tmp
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmp.siteID
	INNER JOIN membercentral.dbo.sub_subscribers as sub on sub.orgID = s.orgID and sub.subscriberID = tmp.subscriberID
	INNER JOIN membercentral.dbo.sub_subscriptions as ssub on ssub.orgID = s.orgID and ssub.subscriptionID = sub.subscriptionID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = sub.memberID;

	-- call the bulk queue processing
	EXEC membercentral.dbo.sub_reprocessConditionsBySubscriberBulk;

	DELETE qi
	from dbo.queue_subscriptionBilled as qi
	inner join #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionForceAdd_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionForceAdd', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;
	
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_subscriptionForceAdd
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionForceAdd
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0 
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_subscriptionForceAdd as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionRenew_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionRenew', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpHoldingReprocessConditionsBySubscriberBulk (orgID int INDEX IX_tmpHolding_orgID, memberID int, subTypeID int, subscriptionID int);

	-- which itemGroupUIDs are all done
	INSERT INTO #tmpitemGroupUIDs (itemGroupUID)
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionRenew
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0 
		GOTO on_done;

	WITH allSubscribers AS (
		SELECT qi.siteID, qi.subscriberID
		FROM dbo.queue_subscriptionRenew as qi
		INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID
			union all
		SELECT alls.siteID, s.subscriberID
		FROM membercentral.dbo.sub_subscribers as s
		INNER JOIN allSubscribers as alls on alls.subscriberID = s.parentSubscriberID
	)
	INSERT INTO #tmpHoldingReprocessConditionsBySubscriberBulk (orgID, memberID, subTypeID, subscriptionID)
	SELECT DISTINCT s.orgID, m.activeMemberID, ssub.typeID, ssub.subscriptionID
	FROM allSubscribers as tmp
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmp.siteID
	INNER JOIN membercentral.dbo.sub_subscribers as sub on sub.orgID = s.orgID and sub.subscriberID = tmp.subscriberID
	INNER JOIN membercentral.dbo.sub_subscriptions as ssub on ssub.orgID = s.orgID and ssub.subscriptionID = sub.subscriptionID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = sub.memberID;

	-- call the bulk queue processing
	EXEC membercentral.dbo.sub_reprocessConditionsBySubscriberBulk;

	-- delete queue items
	DELETE queueSR
	FROM dbo.queue_subscriptionRenew as queueSR
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = queueSR.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_swlCertificate_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='swlCertificate', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;
	
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_swlCertificate
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_swlCertificate
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_swlCertificate as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_swodCertificate_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='swodCertificate', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_swodCertificate
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_swodCertificate
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_swodCertificate as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionAdd_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpHoldingReprocessConditionsBySubscriberBulk (orgID int INDEX IX_tmpHolding_orgID, memberID int, subTypeID int, subscriptionID int);

	DECLARE @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='addSubscribers', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	-- which itemGroupUIDs are all done
	INSERT INTO #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_subscriptionAdd
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_subscriptionAdd
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;

	WITH allSubscribers AS (
		SELECT qi.siteID, qid.subscriberID
		FROM dbo.queue_subscriptionAdd as qi
		INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID
		INNER JOIN dbo.queue_subscriptionAddDetail as qid on qid.itemUID = qi.itemUID
			UNION ALL
		SELECT alls.siteID, s.subscriberID
		FROM membercentral.dbo.sub_subscribers as s
		INNER JOIN allSubscribers as alls on alls.subscriberID = s.parentSubscriberID
	)
	INSERT INTO #tmpHoldingReprocessConditionsBySubscriberBulk (orgID, memberID, subTypeID, subscriptionID)
	SELECT DISTINCT s.orgID, m.activeMemberID, ssub.typeID, ssub.subscriptionID
	FROM allSubscribers as tmp
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmp.siteID
	INNER JOIN membercentral.dbo.sub_subscribers as sub on sub.orgID = s.orgID and sub.subscriberID = tmp.subscriberID
	INNER JOIN membercentral.dbo.sub_subscriptions as ssub on ssub.orgID = s.orgID and ssub.subscriptionID = sub.subscriptionID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = sub.memberID;

	-- call the bulk queue processing
	EXEC membercentral.dbo.sub_reprocessConditionsBySubscriberBulk;

	BEGIN TRAN;
		DELETE qid
		from dbo.queue_subscriptionAddDetail as qid
		inner join dbo.queue_subscriptionAdd as qi on qi.itemUID = qid.itemUID
		inner join #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

		DELETE qi
		from dbo.queue_subscriptionAdd as qi
		inner join #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpHoldingReprocessConditionsBySubscriberBulk') IS NOT NULL 
		DROP TABLE #tmpHoldingReprocessConditionsBySubscriberBulk;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sendgridSubuserDomainAuth_clearDone
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusDone int, @subuserID int;
	EXEC dbo.queue_getStatusIDbyType @queueType='SendgridSubuserDomainAuth', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	select @subuserID = subUserID 
	from dbo.queue_SendgridSubuserDomainAuth
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	DELETE from dbo.queue_SendgridSubuserDomainAuth
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	-- Remove the sub user from queue_sendgridSubuserCreate if this was a subuser ALTER PROCess.
	DELETE from dbo.queue_sendgridSubuserCreate
	WHERE subUserID = @subuserID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sendgridSubuserCreate_clearDone
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='SendgridSubuserCreate', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	DELETE from dbo.queue_sendgridSubuserCreate
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillEmailOrg_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	declare @tblBillingPeriods TABLE (billingPeriodID int);
	EXEC dbo.queue_getStatusIDbyType @queueType='monthBillEmailOrg', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	INSERT INTO @tblBillingPeriods (billingPeriodID)
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailOrg
	where statusID = @statusDone
		except
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailOrg
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;
		
	BEGIN TRAN;
		delete q
		from platformQueue.dbo.queue_monthBillSW as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;
		
		delete q
		from platformQueue.dbo.queue_monthBillTSRoyalty as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;

		delete q
		from platformQueue.dbo.queue_monthBillTSA as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID
		where q.isOrg = 1;

		delete q
		from platformQueue.dbo.queue_monthBillEmailOrg as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillEmailIndiv_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	declare @tblBillingPeriods TABLE (billingPeriodID int);
	EXEC dbo.queue_getStatusIDbyType @queueType='monthBillEmailIndiv', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	INSERT INTO @tblBillingPeriods (billingPeriodID)
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailIndiv
	where statusID = @statusDone
		except
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailIndiv
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;
		
	BEGIN TRAN;
		delete q
		from platformQueue.dbo.queue_monthBillTSA as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID
		where q.isOrg = 0;

		delete q
		from platformQueue.dbo.queue_monthBillEmailIndiv as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importAuthCIM_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='importAuthCIM', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_importAuthCIM
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_importAuthCIM
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_importAuthCIM as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_EventsImport_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpItemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpItemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpEVQueueItems') IS NOT NULL 
		DROP TABLE #tmpEVQueueItems;
	CREATE TABLE #tmpItemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpEVQueueItems (siteID int, eventID int);

	declare @importEVStatusDone int, @evSearchTxtStatusReady int, @siteID int, @itemGroupUID uniqueidentifier, @xmlMessage xml;
	
	EXEC dbo.queue_getStatusIDbyType @queueType='importEvents', @queueStatus='done', @queueStatusID=@importEVStatusDone OUTPUT;
	EXEC dbo.queue_getStatusIDbyType @queueType='eventSearchText', @queueStatus='readyToProcess', @queueStatusID=@evSearchTxtStatusReady OUTPUT;

	INSERT INTO #tmpItemGroupUIDs (itemGroupUID)
	select distinct qid.itemGroupUID
	from dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	where qi.queueStatusID = @importEVStatusDone
		except
	select distinct qid.itemGroupUID
	from dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	where qi.queueStatusID <> @importEVStatusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- existing events
	INSERT INTO #tmpEVQueueItems (siteID, eventID)
	SELECT DISTINCT ev.siteID, ev.eventID
	FROM dbo.tblQueueItems AS qi
	INNER JOIN dbo.tblQueueItemData AS qid ON qid.itemUID = qi.itemUID
	INNER JOIN #tmpItemGroupUIDs AS tmp ON tmp.itemGroupUID = qid.itemGroupUID
	INNER JOIN dbo.tblQueueTypeDataColumns AS dc ON dc.columnID = qid.columnID
		AND dc.columnName = 'MCEventID'
	INNER JOIN membercentral.dbo.ev_events AS ev ON ev.siteID = qid.siteID
		AND ev.eventID = qid.columnValueInteger;

	-- new events
	INSERT INTO #tmpEVQueueItems (siteID, eventID)
	SELECT DISTINCT ev.siteID, ev.eventID
	FROM dbo.tblQueueItems AS qi
	INNER JOIN dbo.tblQueueItemData AS qid ON qid.itemUID = qi.itemUID
	INNER JOIN #tmpItemGroupUIDs AS tmp ON tmp.itemGroupUID = qid.itemGroupUID
	INNER JOIN dbo.tblQueueTypeDataColumns AS dc ON dc.columnID = qid.columnID
		AND dc.columnName = 'EventCode'
	INNER JOIN membercentral.dbo.ev_events AS ev ON ev.siteID = qid.siteID
		AND ev.reportCode = qid.columnValueString
		EXCEPT
	SELECT siteID, eventID
	FROM #tmpEVQueueItems;

	SELECT @siteID = MIN(siteID) FROM #tmpEVQueueItems;
	WHILE @siteID IS NOT NULL BEGIN
		SELECT @itemGroupUID = NULL, @xmlMessage = NULL;

		SET @itemGroupUID = NEWID();

		-- refreshCalendarEventsCache
		EXEC membercentral.dbo.ev_refreshCalendarEventsCache @siteID=@siteID;

		-- refreshCalendarEventsCategoryIDList
		EXEC membercentral.dbo.ev_refreshCalendarEventsCategoryIDList @siteID=@siteID;

		-- queue update search text
		INSERT INTO dbo.queue_eventSearchText (itemGroupUID, siteID, eventID, dateAdded, dateUpdated, statusID)
		SELECT @itemGroupUID, tmp.siteID, tmp.eventID, GETDATE(), GETDATE(), @evSearchTxtStatusReady
		FROM #tmpEVQueueItems AS tmp
		LEFT OUTER JOIN dbo.queue_eventSearchText AS qi ON qi.eventID = tmp.eventID
			AND qi.siteID = tmp.siteID
		WHERE tmp.siteID = @siteID
		AND qi.itemID IS NULL;

		SELECT @xmlMessage = ISNULL((
			SELECT 'eventSearchTextLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

		SELECT @siteID = MIN(siteID) FROM #tmpEVQueueItems WHERE siteID > @siteID;
	END

	BEGIN TRAN;
		DELETE from dbo.tblQueueItems
		where itemUID in (
			select qi.itemUID
			FROM dbo.tblQueueItems as qi
			inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
			INNER JOIN #tmpItemGroupUIDs as tmp on tmp.itemGroupUID = qid.itemGroupUID
			WHERE qi.queueStatusID = @importEVStatusDone
		);

		DELETE from dbo.tblQueueItemData
		where itemUID not in (select itemUID from dbo.tblQueueItems);
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpItemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpItemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpEVQueueItems') IS NOT NULL 
		DROP TABLE #tmpEVQueueItems;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_EventCertificate_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='eventCertificate', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_eventCertificate
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_eventCertificate
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_eventCertificate as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionAdd_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='addSubscribers', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionAdd
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionAdd
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = GETDATE()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionAdd as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteID, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.membernumber, mActive.memberID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionAdd as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	-- return report information for failures
	select tmpN.itemGroupUID, 
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		qid.treeCode, sub.subscriptionName,
		RANK() OVER (ORDER BY tmpN.itemGroupUID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')', qid.treecode) as rowID1,
		ROW_NUMBER() OVER (PARTITION BY tmpN.itemGroupUID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')', qid.itemUID ORDER BY qidd.rowID) as rowID2
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionAdd as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN dbo.queue_subscriptionAddDetail as qidd on qidd.itemUID = qid.itemUID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = qidd.subscriptionID
	WHERE qidd.subscriberID is null
	order by rowID1, rowID2;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionActivate_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionActivate', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionActivate
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionActivate
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionActivate as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, 
		m4.firstname as reportFirstName, m4.lastname as reportLastName, m4.memberNumber as reportMemberNumber, 
		sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionActivate as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sites on sites.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when qid.errorMessage is null then 0 else 1 end desc, subscriberName, sub.subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionAccept_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = getdate();;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionAccept', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#subsAcceptedPriority') IS NOT NULL 
		DROP TABLE #subsAcceptedPriority;
	CREATE TABLE #subsAcceptedPriority (itemID int, itemGroupUID uniqueidentifier, totalQueued int, minutesInQueue int, priority int);

	INSERT INTO #subsAcceptedPriority (itemID, itemGroupUID, minutesInQueue)
	select itemID, itemGroupUID, minsInQueue = datediff(minute,dateUpdated,@nowDate)
	from dbo.queue_subscriptionAccept
	where statusID in (@statusReady,@statusGrabbed);

	update rp 
	set rp.totalQueued = temp.totalQueued
	from #subsAcceptedPriority as rp
	inner join (
		select itemGroupUID, count(*) as totalQueued
		from #subsAcceptedPriority
		group by itemGroupUID
	) as temp on temp.itemGroupUID = rp.itemGroupUID;

	update temp 
	set priority = 
			case 
				when totalQueued = 1 then -100
				when minutesInQueue > 360 then (totalQueued / 50) + 1
				when minutesInQueue > 90 then (totalQueued / 25) - 10
				when minutesInQueue > 30 then (totalQueued / 25) + 2
				when totalQueued < 500 then (totalQueued / 25)
				else (totalQueued / 25) + 10
            end
	from #subsAcceptedPriority as temp;

	 -- delete rows where the priority hasn't changed
	delete temp
	from #subsAcceptedPriority as temp
	inner join dbo.queue_subscriptionAccept as qid on temp.itemID = qid.itemID
	where temp.priority = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = temp.priority
	FROM dbo.queue_subscriptionAccept as qid
	INNER JOIN #subsAcceptedPriority as temp on temp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#subsAcceptedPriority') IS NOT NULL 
		DROP TABLE #subsAcceptedPriority;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_subscriptionAccept_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionAccept', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionAccept
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionAccept
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = GETDATE()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionAccept as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errorMessage, sub.subscriptionName, s.subscriberID, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, m4.firstname as reportFirstName, m4.lastname as reportLastName, 
		m4.memberNumber as reportMemberNumber, sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionAccept as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as st on st.typeID = sub.typeID
	INNER JOIN membercentral.dbo.sites on sites.siteID = st.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when errorMessage is null then 0 else 1 end desc, subscriberName, subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sendgridSubuserDomainAuth_setStatus
@itemID int,
@statusCode varchar(30)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @subuserQueueStatusID int;
	EXEC dbo.queue_getStatusIDbyType @queueType='SendgridSubuserDomainAuth', @queueStatus=@statusCode, @queueStatusID=@subuserQueueStatusID OUTPUT;

	UPDATE dbo.queue_SendgridSubuserDomainAuth
	SET statusID = @subuserQueueStatusID,
		dateUpdated = getdate()
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sendgridSubuserDomainAuth_markSubuserBatch
@batchSize int,
@restrictToSubuserID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubusers') IS NOT NULL 
		DROP TABLE #tmpSubusers;
	IF OBJECT_ID('tempdb..#tmpDomainValidation') IS NOT NULL 
		DROP TABLE #tmpDomainValidation;
	IF OBJECT_ID('tempdb..#tmpBrandValidation') IS NOT NULL 
		DROP TABLE #tmpBrandValidation;

	CREATE TABLE #tmpSubusers (itemID int, batchUID uniqueidentifier, subuserID int);
	CREATE TABLE #tmpDomainValidation (itemID int, batchUID uniqueidentifier, subuserID int);
	CREATE TABLE #tmpBrandValidation (itemID int, batchUID uniqueidentifier, subuserID int);

	DECLARE @batchSizeMultiplier int = 1, @realbatchSize int, @batchUID uniqueidentifier, @processingStatusID int, 
		@recipientID int, @scheduledStatusID int, @scheduledJobsFound bit = 0, @futureDatedQueuedMessagesFound bit = 0,
		@queueTypeID int, @statusReady int, @statusGrabbed int, @statusWaitingDomain int, @statusWaitingBrand int, 
		@statusGrabbedDomain int, @statusGrabbedBrand int, @statusDone int, @threadCount int = 1,
		@subuserActive int, @subuserWaitingDomain int; 
	DECLARE @taskParams TABLE (threadCount int, batchUID uniqueidentifier, requestedBatchSize int, batchSizeMultiplier int);

    set @batchUID = newID();
    set @realbatchSize = @batchSize * @batchSizeMultiplier;

	INSERT INTO @taskParams (threadCount, batchUID, requestedBatchSize, batchSizeMultiplier) 
	VALUES (@threadCount, @batchUID, @batchSize, @batchSizeMultiplier);

	select @subuserActive = subuserStatusID from platformmail.dbo.sendgrid_subuserStatuses where status = 'Active';
	select @subuserWaitingDomain = subuserStatusID from platformmail.dbo.sendgrid_subuserStatuses where status = 'Awaiting Domain Authentication';

	EXEC dbo.queue_getQueueTypeID @queueType='SendgridSubuserDomainAuth', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingForSubuserCreation', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForCreation', @queueStatusID=@statusGrabbed OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingForDomainValidation', @queueStatusID=@statusWaitingDomain OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForDomainValidation', @queueStatusID=@statusGrabbedDomain OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingForBrandingValidation', @queueStatusID=@statusWaitingBrand OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForBrandingValidation', @queueStatusID=@statusGrabbedBrand OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Done', @queueStatusID=@statusDone OUTPUT;

	-- dequeue in order of dateAdded. 
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subUserID
		INTO #tmpSubusers
	FROM dbo.queue_SendgridSubuserDomainAuth as qi
	INNER JOIN (
		SELECT top(@realbatchSize) qi2.itemID 
		from dbo.queue_SendgridSubuserDomainAuth as qi2
		WHERE qi2.statusID = @statusReady
		and qi2.subUserID in (
			select su.subUserID from platformmail.dbo.sendgrid_subusers su 
			where su.subuserID = qi2.subUserID
			and su.statusID in (@subuserActive, @subuserWaitingDomain)
		)		
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- return subuser information
	select qid.itemID, qid.batchUID, qid.subuserID, ss.siteID, ss.firstname, ss.lastname, ss.email, ss.username, ss.password
	from #tmpSubusers as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	order by ss.subuserID;

	-- return subuser domain information
	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendingHostname, ssd.linkBrandHostname
	from #tmpSubusers as qid
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;

	-- return subuser waiting domain validation
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbedDomain,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subUserID
		INTO #tmpDomainValidation
	FROM dbo.queue_SendgridSubuserDomainAuth as qi
	WHERE qi.statusID = @statusWaitingDomain;

	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendgrid_domain_id, ss.username
	from #tmpDomainValidation as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;

	-- return subuser waiting link brand validation
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbedBrand,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subuserID
		INTO #tmpBrandValidation
	FROM dbo.queue_SendgridSubuserDomainAuth as qi
	WHERE qi.statusID = @statusWaitingBrand;

	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendgrid_linkbrand_id, ss.username
	from #tmpBrandValidation as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;

	-- return qryDone
	select itemID, batchUID, subuserID
	from dbo.queue_SendgridSubuserDomainAuth
	WHERE statusID = @statusDone;
	
	-- qryTaskParams
	select threadCount, batchUID, requestedBatchSize, batchSizeMultiplier
	from @taskParams;

	on_done:
	IF OBJECT_ID('tempdb..#tmpSubusers') IS NOT NULL 
		DROP TABLE #tmpSubusers;
	IF OBJECT_ID('tempdb..#tmpDomainValidation') IS NOT NULL 
		DROP TABLE #tmpDomainValidation;
	IF OBJECT_ID('tempdb..#tmpBrandValidation') IS NOT NULL 
		DROP TABLE #tmpBrandValidation;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sendgridSubuserCreate_setStatus
@itemID int,
@statusCode varchar(30)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @subuserQueueStatusID int;
	EXEC dbo.queue_getStatusIDbyType @queueType='SendgridSubuserCreate', @queueStatus=@statusCode, @queueStatusID=@subuserQueueStatusID OUTPUT;

	UPDATE dbo.queue_sendgridSubuserCreate
	SET statusID = @subuserQueueStatusID,
		dateUpdated = getdate()
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_sendgridSubuserCreate_markSubuserBatch
@batchSize int,
@restrictToSubuserID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubusers') IS NOT NULL 
		DROP TABLE #tmpSubusers;
	IF OBJECT_ID('tempdb..#tmpDomainValidation') IS NOT NULL 
		DROP TABLE #tmpDomainValidation;
	IF OBJECT_ID('tempdb..#tmpBrandValidation') IS NOT NULL 
		DROP TABLE #tmpBrandValidation;

	CREATE TABLE #tmpSubusers (itemID int, batchUID uniqueidentifier, subuserID int);
	CREATE TABLE #tmpDomainValidation (itemID int, batchUID uniqueidentifier, subuserID int);
	CREATE TABLE #tmpBrandValidation (itemID int, batchUID uniqueidentifier, subuserID int);

	DECLARE @batchSizeMultiplier int = 1, @realbatchSize int, @batchUID uniqueidentifier, @processingStatusID int, 
		@recipientID int, @scheduledStatusID int, @scheduledJobsFound bit = 0, @futureDatedQueuedMessagesFound bit = 0,
		@queueTypeID int, @statusReady int, @statusGrabbed int, @statusWaitingDomain int, @statusWaitingBrand int, 
		@statusGrabbedDomain int, @statusGrabbedBrand int, @statusDone int, @threadCount int = 1; 
	DECLARE @taskParams TABLE (threadCount int, batchUID uniqueidentifier, requestedBatchSize int, batchSizeMultiplier int);

    set @batchUID = newID();
    set @realbatchSize = @batchSize * @batchSizeMultiplier;

	INSERT INTO @taskParams (threadCount, batchUID, requestedBatchSize, batchSizeMultiplier) 
	VALUES (@threadCount, @batchUID, @batchSize, @batchSizeMultiplier);

	EXEC dbo.queue_getQueueTypeID @queueType='SendgridSubuserCreate', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToCreate', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForCreation', @queueStatusID=@statusGrabbed OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingForDomainValidation', @queueStatusID=@statusWaitingDomain OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForDomainValidation', @queueStatusID=@statusGrabbedDomain OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingForBrandingValidation', @queueStatusID=@statusWaitingBrand OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForBrandingValidation', @queueStatusID=@statusGrabbedBrand OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Done', @queueStatusID=@statusDone OUTPUT;

	-- dequeue in order of dateAdded. 
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subUserID
		INTO #tmpSubusers
	FROM dbo.queue_sendgridSubuserCreate as qi
	INNER JOIN (
		SELECT top(@realbatchSize) qi2.itemID 
		from dbo.queue_sendgridSubuserCreate as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- return subuser information
	select qid.itemID, qid.batchUID, qid.subuserID, ss.siteID, ss.firstname, ss.lastname, ss.email, ss.username, ss.password
	from #tmpSubusers as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	order by ss.subuserID;

	-- return subuser domain information
	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendingHostname, ssd.linkBrandHostname
	from #tmpSubusers as qid
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;

	-- return subuser waiting domain validation
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbedDomain,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subUserID
		INTO #tmpDomainValidation
	FROM dbo.queue_sendgridSubuserCreate as qi
	WHERE qi.statusID = @statusWaitingDomain;

	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendgrid_domain_id, ss.username
	from #tmpDomainValidation as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;
	
	-- return subuser waiting link brand validation
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbedBrand,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subuserID
		INTO #tmpBrandValidation
	FROM dbo.queue_sendgridSubuserCreate as qi
	WHERE qi.statusID = @statusWaitingBrand;

	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendgrid_linkbrand_id, ss.username
	from #tmpBrandValidation as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;

	-- return qryDone
	select itemID, batchUID, subuserID
	from dbo.queue_sendgridSubuserCreate
	WHERE statusID = @statusDone;
	
	-- qryTaskParams
	select threadCount, batchUID, requestedBatchSize, batchSizeMultiplier
	from @taskParams;

	on_done:
	IF OBJECT_ID('tempdb..#tmpSubusers') IS NOT NULL 
		DROP TABLE #tmpSubusers;
	IF OBJECT_ID('tempdb..#tmpDomainValidation') IS NOT NULL 
		DROP TABLE #tmpDomainValidation;
	IF OBJECT_ID('tempdb..#tmpBrandValidation') IS NOT NULL 
		DROP TABLE #tmpBrandValidation;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_schedReportChangeNotify_addEntry
@reportID int,
@actorMemberID int,
@changeSection varchar(100) = NULL

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @queueStatusID int, @nowDate datetime = GETDATE();

	IF EXISTS (SELECT 1 FROM membercentral.dbo.rpt_scheduledReports WHERE reportID = @reportID AND enteredByMemberID <> @actorMemberID) BEGIN
		EXEC dbo.queue_getStatusIDbyType @queueType='schedReportChangeNotify', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;
		
		INSERT INTO dbo.queue_schedReportChangeNotify (reportID, actorMemberID, changeSection, statusID, dateAdded, dateUpdated)
		SELECT @reportID, @actorMemberID, @changeSection, @queueStatusID, @nowDate, @nowDate
			EXCEPT
		SELECT reportID, actorMemberID, changeSection, @queueStatusID, @nowDate, @nowDate
		FROM dbo.queue_schedReportChangeNotify;

		EXEC membercentral.dbo.sched_resumeTask @name='Scheduled Report Change Notification', @engine='MCLuceeLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_paperStatements_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int, @downloadModeColumnID int;
	EXEC dbo.queue_getQueueTypeID @queueType='PaperStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	select @downloadModeColumnID=columnID
	from dbo.tblQueueTypeDataColumns
	where queueTypeID = @queueTypeID and columnname = 'downloadmode'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, orgID int, siteID int, recordedByMemberID int, downloadMode varchar(50), PRIMARY KEY (siteID, orgID,itemGroupUID,itemUID));

	-- dequeue. 
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID, qid.itemUID, s.orgID, s.siteID, qid.recordedByMemberID, qid.columnValueString
		INTO #tmpNotify (itemGroupUID, itemUID, orgID, siteID, recordedByMemberID, downloadMode)
	FROM (
		select distinct qid2.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid2 
			on qid2.itemUID = qi.itemUID
			and qid2.columnID =  @downloadModeColumnID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid2.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid2 
			on qid2.itemUID = qi.itemUID
			and qid2.columnID =  @downloadModeColumnID
		where qi.queueStatusID <> @statusReady
		) itemGroupUIDs
	INNER JOIN platformQueue.dbo.tblQueueItemData as qid 
		on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		and qid.columnID =  @downloadModeColumnID
	inner join platformQueue.dbo.tblQueueItems as qi
		on qid.itemUID = qi.itemUID
		and qi.queueStatusID = @statusReady
	inner join membercentral.dbo.sites s 
		on s.siteID = qid.siteID;

	-- return itemGroupUIDs that can be marked as done
	select ig.itemGroupUID, me.email as reportEmail, s.siteName, s.siteID, s.siteCode, o.orgCode, mActive.memberID, mActive.firstname, mActive.lastname, mActive.memberNumber, ig.downloadMode
	from #tmpNotify as ig
	INNER JOIN membercentral.dbo.sites s 
		on ig.siteID = s.siteID
	INNER JOIN membercentral.dbo.organizations o 
		on s.orgID = o.orgID
	INNER JOIN membercentral.dbo.ams_members as m 
		on m.orgID in (ig.orgID,1) 
		and m.memberID = ig.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	group by ig.itemGroupUID, me.email, s.siteName, s.siteID, s.siteCode, o.orgCode, mActive.memberID, mActive.firstname, mActive.lastname, mActive.memberNumber, ig.downloadMode
	order by ig.itemGroupUID;


	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_NJFirmSubStatements_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int, @siteID int, @orgID int;
	EXEC dbo.queue_getQueueTypeID @queueType='NJFirmSubStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	SELECT @siteID = siteID, @orgID = orgID
	FROM membercentral.dbo.sites
	WHERE siteCode = 'NJ';

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_NJFirmSubStatements
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_NJFirmSubStatements
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = GETDATE()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_NJFirmSubStatements as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	SELECT DISTINCT tmpN.itemGroupUID, me.email as reportEmail, s.siteID, s.siteName, s.siteCode, o.orgID, o.orgCode,
		mActive.memberID, mActive.firstname, mActive.lastname, mActive.memberNumber
	FROM (SELECT DISTINCT itemGroupUID FROM #tmpNotify) as tmpN
	INNER JOIN dbo.queue_NJFirmSubStatements as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = @siteID
	INNER JOIN membercentral.dbo.organizations as o on o.orgID = s.orgID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (@orgID,1)
		and m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	ORDER BY tmpN.itemGroupUID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillEmailOrg_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @BillqueueTypeID int, @BillstatusWaiting int, @BillstatusReady int, @SWqueueTypeID int, @SWstatusDone int, 
		@TSRqueueTypeID int, @TSRstatusDone int, @TSAqueueTypeID int, @TSAstatusDone int, @billItemID int, 
		@checkbillingPeriodID int, @checkisOrg bit;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillEmailOrg', @queueTypeID=@BillqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillSW', @queueTypeID=@SWqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSRoyalty', @queueTypeID=@TSRqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSA', @queueTypeID=@TSAqueueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='waitingToProcess', @queueStatusID=@BillstatusWaiting OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@BillstatusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@SWqueueTypeID, @queueStatus='done', @queueStatusID=@SWstatusDone OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@TSRqueueTypeID, @queueStatus='done', @queueStatusID=@TSRstatusDone OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@TSAqueueTypeID, @queueStatus='done', @queueStatusID=@TSAstatusDone OUTPUT;

	-- look at anything waitingToProcess to see if any are readyToProcess
	select @checkbillingPeriodID = MIN(billingPeriodID) from dbo.queue_monthBillEmailOrg where statusID = @BillstatusWaiting;
	while @checkbillingPeriodID is not null begin

		IF NOT EXISTS (select top 1 itemID from dbo.queue_monthBillSW where billingPeriodID = @checkbillingPeriodID and statusID <> @SWstatusDone)
		AND NOT EXISTS (select top 1 itemID from dbo.queue_monthBillTSRoyalty where billingPeriodID = @checkbillingPeriodID and statusID <> @TSRstatusDone)
		AND NOT EXISTS (select top 1 itemID from dbo.queue_monthBillTSA where billingPeriodID = @checkbillingPeriodID and isOrg = 1 and statusID <> @TSAstatusDone)
			UPDATE dbo.queue_monthBillEmailOrg
			SET statusID = @BillstatusReady,
				dateUpdated = getdate()
			WHERE billingPeriodID = @checkbillingPeriodID;

		select @checkbillingPeriodID = MIN(billingPeriodID) from dbo.queue_monthBillEmailOrg where statusID = @BillstatusWaiting and billingPeriodID > @checkbillingPeriodID;
	end

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_monthBillEmailIndiv_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @BillqueueTypeID int, @BillstatusWaiting int, @BillstatusReady int, @TSAqueueTypeID int, @TSAstatusDone int, 
		@billItemID int, @checkbillingPeriodID int;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillEmailIndiv', @queueTypeID=@BillqueueTypeID OUTPUT;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillTSA', @queueTypeID=@TSAqueueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='waitingToProcess', @queueStatusID=@BillstatusWaiting OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@BillqueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@BillstatusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@TSAqueueTypeID, @queueStatus='done', @queueStatusID=@TSAstatusDone OUTPUT;

	-- look at anything waitingToProcess to see if any are readyToProcess
	UPDATE mbei
	SET mbei.statusID = @BillstatusReady,
		mbei.dateUpdated = getdate()
	FROM dbo.queue_monthBillEmailIndiv as mbei
	INNER JOIN dbo.queue_monthBillTSA as tsa on tsa.billingPeriodID = mbei.billingPeriodID
		and tsa.depomemberDataID = mbei.depoMemberDataID
		and tsa.statusID = @TSAstatusDone
		and tsa.isOrg = 0
	WHERE mbei.statusID = @BillstatusWaiting;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberUpdate_markMemberDone
@jobID int,
@jobMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @memImportStatusDone int, @type varchar(30), @runImmediately bit;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='MemberImport', @queueStatus='done', @queueStatusID=@memImportStatusDone OUTPUT;
	
	-- mark member as done
	UPDATE dbo.memimport_members
	SET queueStatusID = @memImportStatusDone
	WHERE memberID = @jobMemberID;

	-- if there are no more members to process, job is done and can be removed.
	IF EXISTS (
		select distinct jobID
		from dbo.memimport_members
		where jobID = @jobID
		and queueStatusID = @memImportStatusDone
			except
		select distinct jobID
		from dbo.memimport_members
		where jobID = @jobID
		and queueStatusID <> @memImportStatusDone
	) BEGIN

		-- send to membermergematch queue
		DECLARE @itemGroupUID uniqueidentifier = NEWID(), @memMergeMatchStatusReady int, @xmlMessage xml;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberMergeMatch', @queueStatus='readyToProcess', @queueStatusID=@memMergeMatchStatusReady OUTPUT;

		INSERT INTO dbo.queue_memberMergeMatch (itemGroupUID, orgID, memberID, dateAdded, dateUpdated, statusID)
		SELECT @itemGroupUID, imp.orgID, ISNULL(imp.actualMemberID,imp.newMemberID), GETDATE(), GETDATE(), @memMergeMatchStatusReady
		FROM dbo.memimport_members AS imp
		WHERE imp.jobID = @jobID
		AND (imp.firstNameChanged = 1
			OR imp.lastNameChanged = 1
			OR EXISTS(SELECT 1 FROM dbo.memimport_memberEmails AS me WHERE me.memberID = imp.memberID)
			OR EXISTS(SELECT 1 FROM dbo.memimport_memberProfessionalLicenses AS mpl WHERE mpl.memberID = imp.memberID)
		);

		IF @@ROWCOUNT > 0 BEGIN
			SELECT @xmlMessage = ISNULL((
				SELECT 'memberMergeMatchLoad' AS t, cast(@itemGroupUID as varchar(36)) AS u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');

			EXEC dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		END

		/* ***************************** */
		/* process conditions and groups */
		/* ***************************** */
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		SELECT @type = MIN(type) FROM dbo.memimport_conditionsToRun WHERE jobID = @jobID;
		WHILE @type IS NOT NULL BEGIN
			SET @runImmediately = 0;

			SELECT TOP 1 @runImmediately = runImmediately
			FROM dbo.memimport_conditionsToRun
			WHERE jobID = @jobID
			AND type = @type;

			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT DISTINCT orgID, memberID, conditionID
			FROM dbo.memimport_conditionsToRun
			WHERE jobID = @jobID
			AND type = @type;

			IF @@ROWCOUNT > 0
				EXEC dbo.queue_triggerMCGCache @runImmediately=@runImmediately, @type=@type;

			TRUNCATE TABLE #tblMCQRun;

			SELECT @type = MIN(type) FROM dbo.memimport_conditionsToRun WHERE jobID = @jobID AND type > @type;
		END

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;

		-- populate the log table for logging of these subprocs. we dont know the logID so just put 0.
		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
		CREATE TABLE #tmpPMILogID (logID int PRIMARY KEY);
		INSERT INTO #tmpPMILogID (logID) VALUES (0);
		
		EXEC membercentral.dbo.ams_importPartialMemberData_cancel @jobID=@jobID;

		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberConditions_load
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @itemGroupUID uniqueidentifier = NEWID(), @orgID int, @processType varchar(30), @xmldataFinal xml,
		@dateAdded datetime = getdate(), @triggerLogID bigint, @readyQueueStatusID int, 
		@notReadyQueueStatusID int, @memberConditionsReadyQueueStatusID int;

	SELECT @orgID = @xmlMessage.value('(/mc/@o)[1]','int'), 
		@processType = @xmlMessage.value('(/mc/@pt)[1]','varchar(30)'),
		@triggerLogID = @xmlMessage.value('(/mc/@x)[1]','bigint');

	select @xmldataFinal = isnull((
		select @processType as t, @orgID as o, @itemGroupUID as u, @triggerLogID as x
		FOR XML RAW('mc'), TYPE
	),'<mc/>');		

	IF OBJECT_ID('tempdb..#tmpsbQueueM') IS NOT NULL 
		DROP TABLE #tmpsbQueueM;
	IF OBJECT_ID('tempdb..#tmpsbQueueC') IS NOT NULL 
		DROP TABLE #tmpsbQueueC;
	IF OBJECT_ID('tempdb..#tblMCQRunMCL') IS NOT NULL 
		DROP TABLE #tblMCQRunMCL;
	CREATE TABLE #tmpsbQueueM (memberID int);
	CREATE TABLE #tmpsbQueueC (conditionID int);
	CREATE TABLE #tblMCQRunMCL (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tmpsbQueueM (memberID)
	select M.member.value('.','int')
	from @xmlMessage.nodes('/mc/m') as M(member);

	INSERT INTO #tmpsbQueueC (conditionID)
	select C.condition.value('.','int')
	from @xmlMessage.nodes('/mc/c') as C(condition);

	insert into #tblMCQRunMCL (orgID, memberID, conditionID)
	select o.orgID, nullIf(tmpM.memberid,0), nullIf(tmpC.conditionID,0)
	from (select @orgID as orgID) as o
	outer apply #tmpsbQueueM as tmpM
	outer apply #tmpsbQueueC as tmpC;

	IF @processType = 'ConditionsOnly' OR @processType = 'ConditionsOnlyNonImm' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberConditions', @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;
		
		insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
		select distinct @itemGroupUID, @processType, @triggerLogID, @orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
		from #tblMCQRunMCL;

		EXEC dbo.queue_MemberConditions_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	IF @processType = 'GroupsOnly' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberGroups', @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

		IF EXISTS (select 1 from #tblMCQRunMCL where memberID is null)
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
			from membercentral.dbo.ams_members
			where orgID = @orgID
			and status <> 'D';
		ELSE 
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
			from #tblMCQRunMCL;

		select @xmldataFinal = isnull((
			select @orgID as o, @itemGroupUID as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');	

		EXEC dbo.queue_MemberGroups_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	IF @processType = 'ConditionsAndGroups' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberGroups', @queueStatus='notReady', @queueStatusID=@notReadyQueueStatusID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberConditions', @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;

		insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
		select distinct @itemGroupUID, @processType, @triggerLogID, @orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
		from #tblMCQRunMCL;

		IF EXISTS (select 1 from #tblMCQRunMCL where memberID is null)
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @notReadyQueueStatusID
			from membercentral.dbo.ams_members
			where orgID = @orgID
			and status <> 'D';
		ELSE 
			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @notReadyQueueStatusID
			from #tblMCQRunMCL;

		EXEC dbo.queue_MemberConditions_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	IF @processType = 'ConditionsAndGroupsChanged' BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberConditions', @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;
		
		insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
		select distinct @itemGroupUID, @processType, @triggerLogID, @orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
		from #tblMCQRunMCL;

		EXEC dbo.queue_MemberConditions_sendMessage @xmlMessage=@xmldataFinal;
		GOTO on_done;
	END

	on_done:
	IF OBJECT_ID('tempdb..#tmpsbQueueM') IS NOT NULL 
		DROP TABLE #tmpsbQueueM;
	IF OBJECT_ID('tempdb..#tmpsbQueueC') IS NOT NULL 
		DROP TABLE #tmpsbQueueC;
	IF OBJECT_ID('tempdb..#tblMCQRunMCL') IS NOT NULL 
		DROP TABLE #tblMCQRunMCL;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberConditions_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
	@xmldata xml, @itemGroupUID uniqueidentifier, @ErrorMessage nvarchar(2048),
	@xmldataFinal xml, @clearDone bit, @processType varchar(30), @orgID int,
	@triggerLogID bigint, @queueTypeID int, @readyQueueStatusID int, @notReadyQueueStatusID int;

WHILE 1 = 1
BEGIN TRY

	SELECT @DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @processType = NULL, 
		@orgID = NULL, @itemGroupUID = NULL, @ErrorMessage = NULL, @xmldataFinal = NULL, @clearDone = 0, @triggerLogID = NULL;

	-- initially set to snapshot. this allows us to go in and out throughout this request
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	BEGIN TRANSACTION;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		WAITFOR (
			RECEIVE TOP (1) @DialogHandle = conversation_handle,
							@MessageType = message_type_name,
							@MessageBody = message_body
			FROM dbo.MemberConditionsQueue
		), TIMEOUT 1000;

		IF @DialogHandle IS NULL BEGIN
			COMMIT TRANSACTION;
			BREAK;
		END

		IF @MessageType = N'PlatformQueue/GeneralXMLRequest' BEGIN
			BEGIN TRY
				SET @xmldata = cast(@MessageBody as xml);

				SELECT @orgID = @xmldata.value('(/mc/@o)[1]','int'), 
					@processType = @xmldata.value('(/mc/@t)[1]','varchar(30)'),
					@itemGroupUID = @xmldata.value('(/mc/@u)[1]','uniqueidentifier'),
					@triggerLogID = @xmldata.value('(/mc/@x)[1]','bigint');

				IF @itemGroupUID is not null AND EXISTS (SELECT 1 FROM dbo.queue_memberConditions WHERE itemGroupUID = @itemGroupUID) BEGIN
					
					SELECT @xmldataFinal = isnull((
						select @orgID as o, @itemGroupUID as u
						FOR XML RAW('mc'), TYPE
					),'<mc/>');	

					IF OBJECT_ID('tempdb..#tmpMCQCondCacheChanges') IS NOT NULL 
						DROP TABLE #tmpMCQCondCacheChanges;
					IF OBJECT_ID('tempdb..#tmpMCQCondCacheMemChangedMCA') IS NOT NULL 
						DROP TABLE #tmpMCQCondCacheMemChangedMCA;
					CREATE TABLE #tmpMCQCondCacheChanges (memberID int, conditionID int, isAdded bit);
					CREATE TABLE #tmpMCQCondCacheMemChangedMCA (memberID int);

					EXEC dbo.queue_getQueueTypeID @queueType='memberGroups', @queueTypeID=@queueTypeID OUTPUT;
					EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;
					EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='notReady', @queueStatusID=@notReadyQueueStatusID OUTPUT;
					
					IF @processType = 'ConditionsOnly' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=0;
						SET @clearDone = 1;
					END

					IF @processType = 'ConditionsOnlyNonImm' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=1;
						SET @clearDone = 1;
					END

					IF @processType = 'ConditionsAndGroups' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=0;

						UPDATE dbo.queue_memberGroups
						SET statusID = @readyQueueStatusID,
							dateUpdated = getdate()
						WHERE orgID = @orgID
						AND itemGroupUID = @itemGroupUID
						AND statusID = @notReadyQueueStatusID;

						EXEC dbo.queue_MemberGroups_sendMessage @xmlMessage=@xmldataFinal;
						SET @clearDone = 1;
					END

					IF @processType = 'ConditionsAndGroupsChanged' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=0;

						-- safety check: add any members that are missing system groups
						declare @publicGroupID int, @guestsGroupID int, @usersGroupID int, @dateAdded datetime = getdate();
						select @publicGroupID = groupID from membercentral.dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Public' AND [status] = 'A';
						select @guestsGroupID = groupID from membercentral.dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Guests' AND [status] = 'A';
						select @usersGroupID = groupID from membercentral.dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Users' AND [status] = 'A';

						insert into #tmpMCQCondCacheMemChangedMCA (memberID)
						select distinct memberID 
						from #tmpMCQCondCacheChanges;

						insert into #tmpMCQCondCacheMemChangedMCA (memberID)
						select memberID
						from (
							select memberID
							from membercentral.dbo.ams_members
							where orgID = @orgID
							and [status] <> 'D'
								except
							select mg.memberID
							from membercentral.dbo.cache_members_groups as mg
							where mg.groupID = @publicGroupID
						) as missingPublic
							union
						select memberID
						from (
							select memberID
							from membercentral.dbo.ams_members
							where orgID = @orgID				
							and memberTypeID = 2
							and [status] <> 'D'
								except
							select mg.memberID
							from membercentral.dbo.cache_members_groups as mg
							where mg.groupID = @usersGroupID
						) as missingUsers
							union
						select memberID
						from (
							select memberID
							from membercentral.dbo.ams_members
							where orgID = @orgID
							and memberTypeID = 1
							and [status] <> 'D'
								except
							select mg.memberID
							from membercentral.dbo.cache_members_groups as mg
							where mg.groupID = @guestsGroupID
						) as missingGuests;

						insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
						select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
						from #tmpMCQCondCacheMemChangedMCA;

						EXEC dbo.queue_MemberGroups_sendMessage @xmlMessage=@xmldataFinal;
						SET @clearDone = 1;
					END

					IF OBJECT_ID('tempdb..#tmpMCQCondCacheMemChangedMCA') IS NOT NULL 
						DROP TABLE #tmpMCQCondCacheMemChangedMCA;
				END

				END CONVERSATION @DialogHandle;
			END TRY		
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET @ErrorMessage = N'queue_MemberConditions_Activated - ' + ERROR_MESSAGE();
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END CATCH
		END
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @DialogHandle;
			END 
			ELSE BEGIN
				IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
					SET @xmldata = cast(@MessageBody as xml);						
					with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
					select @ErrorMessage = N'queue_MemberConditions_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
				ELSE BEGIN
					SET @ErrorMessage = N'queue_MemberConditions_Activated - Unexpected message type received: ' + @MessageType; 
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
			END
		END

	IF @@trancount > 0	
		COMMIT TRANSACTION;

	IF @clearDone = 1
		EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH

IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
GO

ALTER PROC dbo.queue_importSWODPrograms_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='importSWODPrograms', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyPrograms') IS NOT NULL
		DROP TABLE #tmpNotifyPrograms;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotifyPrograms (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, siteID int,
		recordedByMemberID int, ProgramCode varchar(15), SeminarTitle varchar(200));

	-- dequeue
	; WITH itemGroupUIDs AS (
		SELECT DISTINCT qid.itemGroupUID
		FROM dbo.tblQueueItems as qi
		INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		WHERE qi.queueStatusID = @statusReady
			EXCEPT
		SELECT DISTINCT qid.itemGroupUID
		FROM dbo.tblQueueItems as qi
		INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		WHERE qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	WHERE qi.queueStatusID = @statusReady;

	-- if none found, no need to continue. return empty query.
	IF @@ROWCOUNT = 0 BEGIN
		SELECT * FROM #tmpNotifyPrograms;
		GOTO on_done;
	END

	-- programs in groupUID
	INSERT INTO #tmpNotifyPrograms (itemGroupUID, itemUID, siteID, recordedByMemberID, ProgramCode, SeminarTitle)
	SELECT itemGroupUID, itemUID, siteID, recordedByMemberID, ProgramCode, SeminarTitle
	FROM (
		SELECT qid.itemGroupUID, qid.itemUID, qid.siteID, qid.recordedByMemberID, dc.columnname, qid.columnValueString
		FROM (SELECT DISTINCT itemGroupUID FROM #tmpNotify) as tmpN
		INNER JOIN dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
		INNER JOIN dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			AND dc.columnname in ('SeminarTitle','ProgramCode')
	) AS tmp
	PIVOT (MIN(columnValueString) for columnname in (ProgramCode,SeminarTitle)) as pvt;

	SELECT DISTINCT tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode,
		mActive.firstName, mActive.lastName, mActive.memberNumber, mActive.memberID,
		tmpN.ProgramCode, tmpN.SeminarTitle, seminar.seminarID as MCSeminarID
	FROM #tmpNotifyPrograms as tmpN
	INNER JOIN seminarweb.dbo.tblSeminars as seminar ON seminar.programCode = tmpN.ProgramCode
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID
		AND metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		AND me.memberID = metag.memberID
		AND me.emailTypeID = metag.emailTypeID
	ORDER BY tmpN.itemGroupUID, tmpN.SeminarTitle, tmpN.ProgramCode;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyPrograms') IS NOT NULL
		DROP TABLE #tmpNotifyPrograms;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importPagesJSON_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='importPagesJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;
	CREATE TABLE #tmpImportPagesJSON (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportPagesJSON
	FROM dbo.queue_importPagesJSON AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_importPagesJSON
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM #tmpImportPagesJSON;

	SELECT DISTINCT qid.itemID, qid.itemGroupUID, qid.siteID, qid.submittedMemberID, qid.pageJSON, qid.errorMessage, @totalItemCount as totalItemCount
	FROM #tmpImportPagesJSON AS tmp
	INNER JOIN dbo.queue_importPagesJSON AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importPagesJSON_grabForNotifying

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='importPagesJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;
	CREATE TABLE #tmpImportPagesJSON (itemID int);

	WITH processedJobs AS (
		SELECT qi.itemID 
		FROM dbo.queue_importPagesJSON AS qi
		WHERE qi.statusID = @statusReady
		AND NOT EXISTS (
			SELECT 1
			FROM dbo.queue_importPagesJSON AS tmp
			WHERE tmp.itemGroupUID = qi.itemGroupUID
			AND tmp.statusID <> @statusReady
		)
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportPagesJSON
	FROM dbo.queue_importPagesJSON AS qid
	INNER JOIN processedJobs AS batch ON batch.itemID = qid.itemID;

	SELECT DISTINCT qid.itemGroupUID, qid.itemID, qid.siteID, qid.submittedMemberID, qid.pageJSON, qid.errorMessage, mActive.firstName + ' ' + mActive.lastName as memberName, me.email AS memberEmail
	FROM #tmpImportPagesJSON AS tmp
	INNER JOIN dbo.queue_importPagesJSON AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.orgID in (s.orgID,1) and m.memberID = qid.submittedMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID = m.orgID and me.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	ORDER BY qid.itemGroupUID, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importPages_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='importPages', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	INSERT INTO #tmpNotify (itemGroupUID)
	select distinct itemGroupUID
	from dbo.queue_importPages
	where statusID = @statusReady
		except
	select distinct itemGroupUID
	from dbo.queue_importPages
	where statusID <> @statusReady;

	-- mark as grabbed
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
	FROM dbo.queue_importPages as qi
	INNER JOIN #tmpNotify as tmp on tmp.itemGroupUID = qi.itemGroupUID
	WHERE qi.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	SELECT DISTINCT tmpN.itemGroupUID, qid.pageName, ps.sectionName, s.siteName, s.siteCode,
		m.activeMemberID as runByMemberID, m.firstName, m.lastName, m.memberNumber, me.email as reportEmail
	FROM #tmpNotify as tmpN
	INNER JOIN dbo.queue_importPages as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.cms_pageSections as ps on ps.sectionID = qid.sectionID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = qid.runByMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1) and metag.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1) 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	ORDER BY tmpN.itemGroupUID, qid.pageName, ps.sectionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importBlogEntryJSON_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='importBlogEntryJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;
	CREATE TABLE #tmpImportBlogEntryJSON (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportBlogEntryJSON
	FROM dbo.queue_importBlogEntryJSON AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_importBlogEntryJSON
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM dbo.queue_importBlogEntryJSON;

	SELECT DISTINCT qid.itemID, qid.itemGroupUID, qid.siteID, qid.blogID, qid.submittedMemberID, qid.blogEntryJSON, qid.errorMessage, @totalItemCount as totalItemCount
	FROM #tmpImportBlogEntryJSON AS tmp
	INNER JOIN dbo.queue_importBlogEntryJSON AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importBlogEntryJSON_grabForNotifying

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @communityResourceTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='importBlogEntryJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;
	CREATE TABLE #tmpImportBlogEntryJSON (itemID int);

	WITH processedJobs AS (
		SELECT qi.itemID 
		FROM dbo.queue_importBlogEntryJSON AS qi
		WHERE qi.statusID = @statusReady
		AND NOT EXISTS (
			SELECT 1
			FROM dbo.queue_importBlogEntryJSON AS tmp
			WHERE tmp.itemGroupUID = qi.itemGroupUID
			AND tmp.statusID <> @statusReady
		)
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportBlogEntryJSON
	FROM dbo.queue_importBlogEntryJSON AS qid
	INNER JOIN processedJobs AS batch ON batch.itemID = qid.itemID;

	SELECT @communityResourceTypeID = membercentral.dbo.fn_getResourceTypeID('Community');

	SELECT DISTINCT qid.itemGroupUID, qid.itemID, qid.siteID, qid.blogID, qid.submittedMemberID, qid.blogEntryJSON, qid.errorMessage,
		mActive.firstName + ' ' + mActive.lastName as memberName, me.email AS memberEmail,
		blogName = ai.applicationInstanceName + CASE WHEN communityInstances.applicationInstanceName IS NOT NULL THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END
	FROM #tmpImportBlogEntryJSON AS tmp
	INNER JOIN dbo.queue_importBlogEntryJSON AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.bl_blog AS b ON b.blogID = qid.blogID
	INNER JOIN membercentral.dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID
		AND ai.siteID = qid.siteID
	INNER JOIN membercentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
	INNER JOIN membercentral.dbo.cms_siteResources AS parentResource ON parentResource.siteID = qid.siteID
		AND parentResource.siteResourceID = sr.parentSiteResourceID
	LEFT OUTER JOIN membercentral.dbo.cms_siteResources AS grandparentResource
		INNER JOIN membercentral.dbo.cms_applicationInstances AS communityInstances ON communityInstances.siteResourceID = grandParentResource.siteResourceID
		ON grandparentResource.siteID = qid.siteID
			AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
			AND grandparentResource.resourceTypeID = @communityResourceTypeID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = qid.submittedMemberID
	INNER JOIN membercentral.dbo.ams_members AS mActive on mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID = m.orgID and me.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = me.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = metag.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	ORDER BY qid.itemGroupUID, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importAuthCIM_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusProcessing int;
	EXEC dbo.queue_getQueueTypeID @queueType='importAuthCIM', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCOF') IS NOT NULL 
		DROP TABLE #tmpAuthCIMCOF;
	CREATE TABLE #tmpAuthCIMCOF (itemID int);

	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusProcessing,
		qi.dateUpdated = GETDATE()
		OUTPUT inserted.itemID INTO #tmpAuthCIMCOF
	FROM dbo.queue_importAuthCIM AS qi
	INNER JOIN (
		SELECT top 1 qi2.itemID
		FROM dbo.queue_importAuthCIM AS qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) AS batch ON batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	select qid.itemID, qid.profileID, qid.memberID, m.membernumber, m.lastname, m.firstname, qid.FirstNameOnCard, 
		qid.LastNameOnCard, qid.CardNumber, qid.Expiration, qid.BillingAddress, qid.BillingCity, qid.BillingState, 
		qid.BillingZIP, qid.BillingCountry, qid.NickName
	from #tmpAuthCIMCOF as qi
	INNER JOIN dbo.queue_importAuthCIM as qid ON qid.itemID = qi.itemID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.memberID;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCOF') IS NOT NULL 
		DROP TABLE #tmpAuthCIMCOF;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importAuthCIM_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='importAuthCIM', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	;WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_importAuthCIM
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_importAuthCIM
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_importAuthCIM as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemID, qid.errResult, m2.activeMemberID as recordedByMemberID, me.email as reportEmail, s.sitecode, s.siteName,
		mActive.lastname + ', ' + mActive.firstname + isnull(' ' + nullif(mActive.middlename,''),'') + ' (' + mActive.membernumber + ')' as memberName,
		mActive.membernumber, mActive.company, qid.profileID, mp.profileCode, 'XXXX' + right(qid.cardNumber,4) as cardNumber
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_importAuthCIM as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.mp_profiles as mp on mp.siteID = s.siteID and mp.profileID = qid.profileID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = qid.memberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID in (s.orgID,1) and m2.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1) and metag.memberID = m2.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1) and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when errResult is null then 0 else 1 end desc, memberName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_goDaddyOrders_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @statusReady int;
	EXEC dbo.queue_getStatusIDbyType @queueType='goDaddyOrders', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	SELECT TOP (@batchSize) itemID, orderID, dateAdded, dateUpdated
	FROM dbo.queue_goDaddyOrders
	WHERE statusID = @statusReady
	ORDER BY itemID;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_firmSubStatements_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='FirmSubStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt 
		on metagt.orgID =  mActive.orgID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag 
		on metag.orgID = mActive.orgID 
		and metag.emailTagTypeID = metagt.emailTagTypeID
		and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = mActive.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_featuredImages_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchStartDate datetime = getdate();
	EXEC dbo.queue_getQueueTypeID @queueType='featuredImages', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpFeaturedImages') IS NOT NULL
		DROP TABLE #tmpFeaturedImages;
	CREATE TABLE #tmpFeaturedImages (itemID int);

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = @batchStartDate
		OUTPUT inserted.itemID
		INTO #tmpFeaturedImages
	FROM dbo.queue_featuredImages as qid
	INNER JOIN (
		SELECT TOP (@BatchSize) itemID 
		FROM dbo.queue_featuredImages
		WHERE statusID = @statusReady
		AND nextAttemptDate < @batchStartDate
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	-- return featured images info without depending much on featuredImage tables since they may be altered after queuing and queue items will be orphaned
	select distinct qid.itemID, qid.featureImageID, qid.featureImageSizeID, s.siteID, o.orgID, s.siteCode, o.orgCode,
		qid.enteredByMemberID, fi.originalWidth, fi.originalHeight, fi.fileExtension as origFileExtension,
		fics.width, fics.height, fics.fileExtension, m.methodType, m.horizontalAlign, m.verticalAlign
	from #tmpFeaturedImages as tmp
	inner join dbo.queue_featuredImages as qid on qid.itemID = tmp.itemID
	left outer join membercentral.dbo.cms_featuredImages as fi
		inner join membercentral.dbo.cms_featuredImageUsages as fiu on fiu.featureImageID = fi.featureImageID
		inner join membercentral.dbo.cms_featuredImageConfigs as fic on fic.featureImageConfigID = fiu.featureImageConfigID
		inner join membercentral.dbo.sites as s on s.siteID = fi.siteID
		inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
		on fi.featureImageID = qid.featureImageID
	inner join membercentral.dbo.cms_featuredImageConfigSizes as fics on fics.featureImageSizeID = qid.featureImageSizeID
	INNER JOIN membercentral.dbo.cms_featuredImageConfigSizeMethods as m on m.methodID = fics.methodID
	order by qid.itemID;


	IF OBJECT_ID('tempdb..#tmpFeaturedImages') IS NOT NULL
		DROP TABLE #tmpFeaturedImages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_exportPagesJSON_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='exportPagesJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;
	CREATE TABLE #tmpExportPagesJSON (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpExportPagesJSON
	FROM dbo.queue_exportPagesJSON AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_exportPagesJSON
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM #tmpExportPagesJSON;

	SELECT DISTINCT qid.itemID, qid.itemGroupUID, qid.siteID, qid.submittedMemberID, qid.pageID, @totalItemCount as totalItemCount
	FROM #tmpExportPagesJSON AS tmp
	INNER JOIN dbo.queue_exportPagesJSON AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_exportPagesJSON_grabForNotifying

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='exportPagesJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;
	CREATE TABLE #tmpExportPagesJSON (itemID int);

	WITH processedJobs AS (
		SELECT qi.itemID 
		FROM dbo.queue_exportPagesJSON AS qi
		WHERE qi.statusID = @statusReady
		AND NOT EXISTS (
			SELECT 1
			FROM dbo.queue_exportPagesJSON AS tmp
			WHERE tmp.itemGroupUID = qi.itemGroupUID
			AND tmp.statusID <> @statusReady
		)
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpExportPagesJSON
	FROM dbo.queue_exportPagesJSON AS qid
	INNER JOIN processedJobs AS batch ON batch.itemID = qid.itemID;

	SELECT DISTINCT qid.itemGroupUID, qid.itemID, qid.siteID, qid.submittedMemberID, qid.pageJSON, mActive.firstName + ' ' + mActive.lastName as memberName, me.email AS memberEmail
	FROM #tmpExportPagesJSON AS tmp
	INNER JOIN dbo.queue_exportPagesJSON AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.orgID in (s.orgID,1) and m.memberID = qid.submittedMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID in (s.orgID,1) and me.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID in (s.orgID,1) and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID in (s.orgID,1) and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	ORDER BY qid.itemGroupUID, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_EventsImport_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='importEvents', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyEvents') IS NOT NULL
		DROP TABLE #tmpNotifyEvents;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotifyEvents (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, siteID int, recordedByMemberID int, EventCode varchar(15), EventTitle varchar(200));

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	-- if none found, no need to continue. return empty query.
	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotifyEvents;
		GOTO on_done;
	END

	-- events in groupUID
	insert into #tmpNotifyEvents (itemGroupUID, itemUID, siteID, recordedByMemberID, EventCode, EventTitle)
	select itemGroupUID, itemUID, siteID, recordedByMemberID, EventCode, EventTitle
	from (
		select qid.itemGroupUID, qid.itemUID, qid.siteID, qid.recordedByMemberID, dc.columnname, qid.columnValueString
		from (select distinct itemGroupUID from #tmpNotify) as tmpN
		inner join dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
		inner join dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('eventTitle','eventCode')
	) as tmp
	PIVOT (min(columnValueString) for columnname in (EventCode,EventTitle)) as pvt;

	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID, 
		tmpN.EventCode, tmpN.eventTitle
	from #tmpNotifyEvents as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1) and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1) and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, tmpN.eventTitle, tmpN.EventCode;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyEvents') IS NOT NULL
		DROP TABLE #tmpNotifyEvents;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_dispActionsProf_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- BatchSize is limited to 80 here because of the 2100 mssql cfqueryparam limit using this data
	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize int = 80;
	EXEC dbo.queue_getQueueTypeID @queueType='dispActionsProf', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_dispActionsProf as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_dispActionsProf
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.jsonDoc
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_dispActionsProf AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_dispActionsDoc_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- BatchSize is limited to 200 here because of the 2100 mssql cfqueryparam limit using this data
	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize int = 200;
	EXEC dbo.queue_getQueueTypeID @queueType='dispActionsDoc', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_dispActionsDoc as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_dispActionsDoc
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.jsonDoc
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_dispActionsDoc AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_depoDocumentsUnzip_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='depoDocumentsUnzip', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_depoDocumentsUnzip as qi
	INNER JOIN (
		SELECT top 1 qi2.itemID 
		FROM dbo.queue_depoDocumentsUnzip as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoMemberDataID, qid.pathToZip, qid.[State], qid.DepoAmazonBucks,
		qid.DepoAmazonBucksFullName, qid.DepoAmazonBucksEmail, qid.uploadSourceID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_depoDocumentsUnzip as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

ALTER PROC dbo.queue_depoDocumentsAttach_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize tinyint = 100;
	EXEC dbo.queue_getQueueTypeID @queueType='depoDocumentsAttach', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_depoDocumentsAttach as qi
	INNER JOIN (
		SELECT top (@batchSize) qi2.itemID 
		FROM dbo.queue_depoDocumentsAttach as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoDocumentID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_depoDocumentsAttach as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

ALTER PROC dbo.queue_depoCrawlerIndex_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='depoCrawlerIndex', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int, nameID int);

	-- dequeue in order of dateAdded
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.nameID
		INTO #tmpQueueItem
	FROM dbo.queue_depoCrawlerIndex AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID
		FROM dbo.queue_depoCrawlerIndex
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT tmp.itemID, sei.nameID, sei.firstname, sei.lastname
	FROM #tmpQueueItem AS tmp
	INNER JOIN search.dbo.tblSearchEngineIndex as sei on sei.nameID = tmp.nameID
	ORDER BY tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_deleteCardsOnFile_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='deleteCardsOnFile', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_deleteCardsOnFile as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_deleteCardsOnFile
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.payProfileID, qid.profileID, qid.memberID, qid.customerProfileID, qid.paymentProfileID, 
		qid.recordedByMemberID
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_deleteCardsOnFile AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_ContributionsImport_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='importContributions', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyContributions') IS NOT NULL
		DROP TABLE #tmpNotifyContributions;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotifyContributions (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, siteID int, recordedByMemberID int, programName varchar(200));

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	-- if none found, no need to continue. return empty query.
	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotifyContributions;
		GOTO on_done;
	END

	-- contributions in groupUID
	insert into #tmpNotifyContributions (itemGroupUID, itemUID, siteID, recordedByMemberID, programName)
	select itemGroupUID, itemUID, siteID, recordedByMemberID, programName
	from (
		select qid.itemGroupUID, qid.itemUID, qid.siteID, qid.recordedByMemberID, dc.columnname, qid.columnValueInteger
		from (select distinct itemGroupUID from #tmpNotify) as tmpN
		inner join dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
		inner join dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID and dc.columnname = 'programID'
	) as tmp
	PIVOT (min(columnValueInteger) for columnname in (programID)) as pvt
	inner join membercentral.dbo.cp_programs as cp on cp.programID = pvt.programID;

	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, 
		mActive.lastname, mActive.memberNumber, mActive.memberID, tmpN.programName
	from #tmpNotifyContributions as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1) and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1) and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, tmpN.programName;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyContributions') IS NOT NULL
		DROP TABLE #tmpNotifyContributions;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_completeSeminarReminder_grabForProcessing
@batchCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='completeSeminarReminder', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_completeSeminarReminder AS qid
	INNER JOIN (
		SELECT TOP (@batchCount) itemID
		FROM dbo.queue_completeSeminarReminder
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.orgcode, qid.memberID, qid.email, qid.firstName, qid.lastName, qid.overrideEmail, 
		qid.EmailFrom, qid.TimeLapse, qid.CatalogURL, qid.seminarID, qid.seminarName, qid.dateEnrolled, 
		qid.supportPhone, qid.supportEmail, qid.lastdatetoComplete, qid.emailOptionID, qid.emailsubject,
		qid.orgIdentityID
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_completeSeminarReminder AS qid ON qid.itemID = tmp.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_badgePrinting_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='badgePrinting', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpRegBadges') IS NOT NULL
		DROP TABLE #tmpRegBadges;
	CREATE TABLE #tmpRegBadges (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpRegBadges
	FROM dbo.queue_badgePrinting AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_badgePrinting
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, s.sitecode, qid.siteID, qid.registrantID, qid.deviceID, etc.rawContent AS templateContent
	FROM #tmpRegBadges AS tmp
	INNER JOIN dbo.queue_badgePrinting AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.badge_templates AS bt ON bt.templateID = qid.templateID
	CROSS APPLY membercentral.dbo.fn_getContent(bt.contentID,1) AS etc
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpRegBadges') IS NOT NULL
		DROP TABLE #tmpRegBadges;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_authorizeCCCIMTest_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = GETDATE();
	EXEC dbo.queue_getQueueTypeID @queueType='authorizeCCCIMTest', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- Dequeue in order of nextAttemptDate (instead of dateAdded), ensuring only items where nextAttemptDate < GETDATE() are selected
	UPDATE qid WITH (UPDLOCK, READPAST)
    SET qid.statusID = @statusGrabbed,
        qid.dateUpdated = @nowDate
    OUTPUT inserted.itemID
    INTO #tmpQueueItems
    FROM dbo.queue_authorizeCCCIMTest AS qid
    INNER JOIN (
        SELECT TOP (@batchSize) itemID 
        FROM dbo.queue_authorizeCCCIMTest
        WHERE statusID = @statusReady 
		AND nextAttemptDate < @nowDate
        ORDER BY nextAttemptDate, itemID
    ) AS batch ON batch.itemID = qid.itemID
    WHERE qid.statusID = @statusReady;
	
	SELECT qid.itemID, qid.profileID, qid.recordedByMemberID, qid.attemptNum, qid.nextAttemptDate
    FROM #tmpQueueItems AS tmp
    INNER JOIN dbo.queue_authorizeCCCIMTest AS qid ON qid.itemID = tmp.itemID
    ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_authCIMCustMP_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='authCIMCustMP', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustMPs') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustMPs;
	CREATE TABLE #tmpAuthCIMCustMPs (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpAuthCIMCustMPs
	FROM dbo.queue_authCIMCustMP AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_authCIMCustMP
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.profileID, qid.gatewayUsername, qid.gatewayPassword
	FROM #tmpAuthCIMCustMPs AS tmp
	INNER JOIN dbo.queue_authCIMCustMP AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustMPs') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustMPs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_authCIMCustDelete_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='authCIMCustDelete', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustomers') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustomers;
	CREATE TABLE #tmpAuthCIMCustomers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpAuthCIMCustomers
	FROM dbo.queue_authCIMCustDelete AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_authCIMCustDelete
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.profileID, qid.gatewayUsername, qid.gatewayPassword, qid.customerProfileID
	FROM #tmpAuthCIMCustomers AS tmp
	INNER JOIN dbo.queue_authCIMCustDelete AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustomers') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustomers;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_authCIMCustCheck_load
@profileID int,
@gatewayUsername varchar(50),
@gatewayPassword varchar(75),
@customerProfileID varchar(50)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int;
	EXEC dbo.queue_getStatusIDbyType @queueType='authCIMCustCheck', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM dbo.queue_authCIMCustCheck WHERE profileID = @profileID AND customerProfileID = @customerProfileID) BEGIN
		INSERT INTO dbo.queue_authCIMCustCheck (profileID, gatewayUsername, gatewayPassword, customerProfileID, statusID, dateAdded, dateUpdated)
		VALUES (@profileID, @gatewayUsername, @gatewayPassword, @customerProfileID, @statusReady, GETDATE(), GETDATE());

		EXEC membercentral.dbo.sched_resumeTask @name='Process Authorize CIM Customers Check Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_authCIMCustCheck_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='authCIMCustCheck', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustomers') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustomers;
	CREATE TABLE #tmpAuthCIMCustomers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpAuthCIMCustomers
	FROM dbo.queue_authCIMCustCheck AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_authCIMCustCheck
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.profileID, qid.gatewayUsername, qid.gatewayPassword, qid.customerProfileID
	FROM #tmpAuthCIMCustomers AS tmp
	INNER JOIN dbo.queue_authCIMCustCheck AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustomers') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustomers;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_affiniPayCCTest_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='affiniPayCCTest', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_affiniPayCCTest as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_affiniPayCCTest
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID,qid.profileID,qid.recordedByMemberID
	FROM #tmpQueueItems AS tmp
	INNER JOIN dbo.queue_affiniPayCCTest AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_AcctOption2Import_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @orgID int, @recordedByMemberID int, 
		@itemGroupID uniqueidentifier = NEWID();
	EXEC dbo.queue_getQueueTypeID @queueType='importAcctOpt2', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	CREATE TABLE #tmpNotifyItemGroupUID (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier, itemGroupUIDStr char(36) PRIMARY KEY);
	CREATE TABLE #tmpNotify2 (itemGroupUID uniqueidentifier PRIMARY KEY, orgID int, siteID int, recordedByMemberID int);

	-- dequeue 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_acctOption2Import
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_acctOption2Import
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotifyItemGroupUID
	FROM dbo.queue_acctOption2Import as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotify2;
		GOTO on_done;
	END

	-- get distinct list of groupUIDs
	insert into #tmpNotify
	select distinct itemGroupUID, replace(cast(itemGroupUID as char(36)),'-','') as itemGroupUIDStr
	from #tmpNotifyItemGroupUID;

	insert into #tmpNotify2
	select distinct tmpN.itemGroupUID, qid.orgID, qid.siteID, qid.recordedByMemberID
	from #tmpNotify as tmpN
	inner join dbo.queue_acctOption2Import as qid on qid.itemGroupUID = tmpN.itemGroupUID
	order by tmpN.itemGroupUID;

	-- queue the posting of the batches
	select top 1 @orgID=orgID, @recordedByMemberID=recordedByMemberID
	from #tmpNotify2;

	INSERT INTO dbo.queue_batchPost (itemGroupID, orgID, batchID, addedByMemberID, dateAdded, dateUpdated)
	select distinct @itemGroupID, @orgID, b.batchID, @recordedByMemberID, getdate(), getdate()
	FROM membercentral.dbo.tr_batches as b
	inner join #tmpNotify as tmpN on b.batchCode = convert(char(8),b.depositDate,112) + tmpN.itemGroupUIDStr
	where b.orgID = @orgID
	and b.statusID <> 4;

	-- send message to service broker to create all the individual messages
	DECLARE @xmlMessage xml;
	select @xmlMessage = isnull((
		select 'batchPostLoad' as t, cast(@itemGroupID as varchar(60)) as u
		FOR XML RAW('mc'), TYPE
	),'<mc/>');
	EXEC dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

	-- return itemGroupUIDs that can be marked as done
	select tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from #tmpNotify2 as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (@orgID,1) and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (@orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (@orgID,1) and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (@orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (@orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_AcctOption1Import_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='importAcctOpt1', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	CREATE TABLE #tmpNotifyItemGroupUID (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier, itemGroupUIDStr char(36) PRIMARY KEY);
	CREATE TABLE #tmpNotify2 (itemGroupUID uniqueidentifier PRIMARY KEY, siteID int, recordedByMemberID int);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_acctOption1Import
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_acctOption1Import
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotifyItemGroupUID
	FROM dbo.queue_acctOption1Import as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotify2;
		GOTO on_done;
	END

	-- get distinct list of groupUIDs
	insert into #tmpNotify
	select distinct itemGroupUID, replace(cast(itemGroupUID as char(36)),'-','') as itemGroupUIDStr
	from #tmpNotifyItemGroupUID;

	-- return itemGroupUIDs that can be marked as done
	insert into #tmpNotify2
	select distinct tmpN.itemGroupUID, qid.siteID, qid.recordedByMemberID
	from #tmpNotify as tmpN
	inner join dbo.queue_acctOption1Import as qid on qid.itemGroupUID = tmpN.itemGroupUID
	order by tmpN.itemGroupUID;

	select tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from #tmpNotify2 as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1)
		and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1)
		and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1)
		and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_acctIssuesReport_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='acctIssuesReport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_acctIssuesReport AS qid
	INNER JOIN (
		SELECT TOP 1 itemID
		FROM dbo.queue_acctIssuesReport
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	-- org info
	SELECT qid.itemID, qid.orgID, o.orgcode, oi.organizationName as orgName, o.accountingEmail, s.sitecode
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.organizations AS o ON o.orgID = qid.orgID
	INNER JOIN membercentral.dbo.orgIdentities AS oi ON oi.orgID = o.orgID 
		AND oi.orgIdentityID = o.defaultOrgIdentityID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = o.defaultSiteID;

	-- open invoices
	SELECT qid.itemID, qid.orgID, ri.dateDue, ri.invoiceNumber, ri.memberID, 
		ri.firstName, ri.lastName, ri.memberNumber, ri.hasCard, ri.invDue
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_openInv AS ri ON ri.orgID = qid.orgID;

	-- non posted batches
	SELECT qid.itemID, qid.orgID, ri.batchID, ri.status, ri.batchName, 
		ri.depositDate, ri.profileName
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_nonPostedBatches AS ri ON ri.orgID = qid.orgID;

	-- out of order invoices
	SELECT qid.itemID, qid.orgID, ri.memberID, ri.memberName, ri.memberNumber
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_outOfOrderInv AS ri ON ri.orgID = qid.orgID;

	-- inv prof alloc violations
	SELECT qid.itemID, qid.orgID, ri.dateRecorded, ri.memberName, ri.memberNumber, ri.allocAmount,
		ri.detail, ri.invoiceNumber, ri.invoiceProfileName, ri.payProfileName
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_invProfAllocViolations AS ri ON ri.orgID = qid.orgID;

	-- flagged transactions
	SELECT qid.itemID, qid.orgID, ri.dateRecorded, ri.message, ri.memberID, ri.memberName
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_flaggedTransactions AS ri ON ri.orgID = qid.orgID;


	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.job_emailExtMergeCode_grabForProcessing
@jobUID uniqueIdentifier OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @recipientIDDataColumnID int, @messageIDDataColumnID int, 
		@batchSize int, @itemsToProcess int =0;
	EXEC dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	select @recipientIDDataColumnID = columnID from dbo.tblQueueTypeDataColumns where queueTypeID = @queueTypeID and columnName = 'MCRecipientID';
	select @messageIDDataColumnID = columnID from dbo.tblQueueTypeDataColumns where queueTypeID = @queueTypeID and columnName = 'MCMessageID';

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	CREATE TABLE #tmpRecipients (itemUID uniqueidentifier, jobUID uniqueidentifier, recipientID int, messageID int, recordedByMemberID int, siteID int, INDEX tmpRecipients_siteID_messageID_recipientID (siteID, messageID, recipientID));

	SET @jobUID = NEWID();
	SET @batchsize = 2500;

	declare @processRecipientThreadCount int = 8; 
	declare @filterMergeCodesThreadCount int = 2; 
	declare @recipientsPerMiniBatch int = 150; 
	

	declare @taskParams TABLE (processRecipientThreadCount int, filterMergeCodesThreadCount int, recipientsPerMiniBatch int, requestedBatchSize int)
	insert into @taskParams (processRecipientThreadCount,filterMergeCodesThreadCount, recipientsPerMiniBatch, requestedBatchSize) values (@processRecipientThreadCount, @filterMergeCodesThreadCount, @recipientsPerMiniBatch, @batchSize)

	select @itemsToProcess=count(*)
	from dbo.tblQueueItems as qi2
	WHERE qi2.queueStatusID = @statusReady

	IF @itemsToProcess > 0 BEGIN

		-- dequeue by queue priority
		UPDATE qi WITH (UPDLOCK, READPAST)
		SET qi.queueStatusID = @statusGrabbed,
			qi.dateUpdated = getdate(),
			qi.jobUID = @jobUID,
			qi.jobDateStarted = getdate()
			OUTPUT	inserted.itemUID, inserted.jobUID, temp.recipientID, 
					temp.messageID, temp.recordedByMemberID, temp.siteID
			INTO #tmpRecipients
		FROM dbo.tblQueueItems as qi
		INNER JOIN (
			SELECT top(@BatchSize) qi2.itemUID, mrh2.recipientID, m2.messageID, qid2.recordedByMemberID, m2.siteID
			from dbo.tblQueueItems as qi2
			INNER JOIN dbo.tblQueueItemData as qid2 on qi2.itemUID = qid2.itemUID 
				and qid2.columnID = @recipientIDDataColumnID
				and qi2.queueStatusID = @statusReady
			inner join platformMail.dbo.email_messageRecipientHistory as mrh2 
				on mrh2.emailStatusID = 1
				and mrh2.siteID = qid2.siteID
				and mrh2.recipientID = qid2.columnValueInteger
				AND mrh2.batchID is null
			INNER JOIN platformMail.dbo.email_messages as m2 on m2.siteID = mrh2.siteID
				AND m2.status = 'A'
				AND m2.messageID = mrh2.messageID
			ORDER BY mrh2.queuePriority
		) as temp on temp.itemUID = qi.itemUID;
	END

	-- return recipients
	select tmp.itemUID, tmp.jobUID, mrh.recipientID, em.messageID, em.orgIdentityID, mActive.memberID as recipientMemberID, mActive.memberNumber,
		mrh.toEmail as recipientEmail, s.siteID, s.siteCode, o.orgID, o.orgCode, mt.messageTypeCode
	from #tmpRecipients as tmp
	inner join platformMail.dbo.email_messages as em 
		on em.siteID = tmp.siteID
		and em.messageID = tmp.messageID
		and em.[status] = 'A'
	inner join memberCentral.dbo.sites as s on s.siteID = em.siteID
	inner join platformMail.dbo.email_messageRecipientHistory as mrh 
		on mrh.siteID = em.siteID
		and mrh.messageID = em.messageID
		and mrh.recipientID = tmp.recipientID
	inner join platformMail.dbo.email_messageTypes as mt on mt.messageTypeID = em.messageTypeID
	inner join memberCentral.dbo.organizations as o on o.orgID = s.orgID
	inner join memberCentral.dbo.ams_members as m on (m.orgID = o.orgID or m.orgID = 1) and m.memberID = mrh.memberID
	inner join memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID;

	-- return recipient data
	select tmp.itemUID, tmp.recipientID, tmp.messageID, dc.columnID, dc.columnName, qid.dataKey, qid.columnValueBit, qid.columnvalueDate, 
		qid.columnValueDecimal2, qid.columnValueInteger, qid.columnValueString, qid.columnValueText, qid.columnValueXML
	from #tmpRecipients as tmp
	inner join dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	where dc.columnID not in (@recipientIDDataColumnID,@messageIDDataColumnID);
	
	-- qryTaskParams
	select processRecipientThreadCount, filterMergeCodesThreadCount, recipientsPerMiniBatch, requestedBatchSize
	from @taskParams tp

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.job_CLEReport_grabForNotification
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='caCLECredits', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct top(@BatchSize) qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1)
		AND m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1)
		AND mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1)
		AND metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.job_CallSheets_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='callSheets', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemUID uniqueidentifier);

	-- dequeue. 
	; WITH itemUIDs AS (
		select distinct c.itemUID
		from platformQueue.dbo.queue_callSheets as c
		inner join platformQueue.dbo.queue_callSheetsDetail as cd on cd.itemUID = c.itemUID
		where cd.statusID = @statusReady
			except
		select distinct c.itemUID
		from platformQueue.dbo.queue_callSheets as c
		inner join platformQueue.dbo.queue_callSheetsDetail as cd on cd.itemUID = c.itemUID
		where cd.statusID <> @statusReady
	)

	UPDATE cd WITH (UPDLOCK, READPAST)
	SET statusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT itemUIDs.itemUID
		INTO #tmpNotify
	FROM platformQueue.dbo.queue_callSheetsDetail as cd
	inner join itemUIDs on itemUIDs.itemUID = cd.itemUID
	where cd.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	select distinct cd.itemUID, me.email as reportEmail, s.siteName, s.siteCode,
		mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from (select distinct itemUID from #tmpNotify) as tmpN
	inner join platformQueue.dbo.queue_callSheetsDetail as cd on cd.itemUID = tmpN.itemUID
	inner join platformQueue.dbo.queue_callSheets as c on c.itemUID = cd.itemUID
	inner join membercentral.dbo.ams_members as m on m.orgID in (c.orgID,1)
		and m.memberID = c.recordedByMemberID
	inner join membercentral.dbo.ams_members as mActive on mActive.orgID in (c.orgID,1)
		and mActive.memberID = m.activeMemberID
	inner join membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (c.orgID,1)
		and metag.memberID = mActive.memberID
	inner join membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (c.orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	inner join membercentral.dbo.ams_memberEmails as me on me.orgID in (c.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	inner join membercentral.dbo.sites as s on s.siteID = c.siteID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

