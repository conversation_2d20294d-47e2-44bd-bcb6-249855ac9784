<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.processQueueResult = processQueue()>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cfset local.objPublication = createObject("component","model.admin.publications.publication")>
		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_pubSyndIssueDist_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="20">
				<cfprocresult name="local.qryIssues" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryIssues.recordCount>

			<cfloop query="local.qryIssues">
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='pubSyndIssueDist', @queueStatus='processing', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_pubSyndIssueDist
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryIssues.itemID#">;
				</cfquery>

				<cfquery name="local.qryPublicationIssue" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT p.publicationID, ai.siteID
					FROM dbo.pub_publications AS p
					INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
						AND ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.parentSiteID#">
						AND p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.parentPublicationID#">
					INNER JOIN dbo.pub_volumes AS v ON v.publicationID = p.publicationID
					INNER JOIN dbo.pub_issues AS i ON i.volumeID = v.volumeID
						AND i.issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.parentPublicationIssueID#">
					INNER JOIN dbo.pub_publications AS child ON ISNULL(child.parentPublicationID,0) = p.publicationID
						AND child.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.childPublicationID#">
					INNER JOIN dbo.cms_applicationInstances AS ai_c ON ai_c.applicationInstanceID = child.applicationInstanceID
						AND ai_c.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.childSiteID#">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfif local.qryPublicationIssue.recordCount>
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_copyParentIssueToChild">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.parentSiteID#">				
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.parentPublicationID#">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.parentPublicationIssueID#">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.childSiteID#">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.childPublicationID#">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.qryIssues.enteredByMemberID#">
						<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="local.childPublicationIssueID">
					</cfstoredproc>

					<cfset local.retStruct = local.objPublication.sendIssueApprovalRequest(siteCode=local.qryIssues.childSiteCode, issueID=local.childPublicationIssueID,
						publicationID=local.qryIssues.childPublicationID, emailMode="childIssueDistribution", actorMemberID=local.qryIssues.enteredByMemberID)>
					<cfset removeQueueItem(itemID=local.qryIssues.itemID)>
				<cfelse>
					<!--- queue entry no longer valid --->
					<cfset removeQueueItem(itemID=local.qryIssues.itemID)>
				</cfif>
			</cfloop>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeQueueItem" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">

		<cfquery name="local.qryRemoveFromQueue" datasource="#application.dsn.platformQueue.dsn#">
			DELETE FROM dbo.queue_pubSyndIssueDist
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
		</cfquery>
	</cffunction>

</cfcomponent>