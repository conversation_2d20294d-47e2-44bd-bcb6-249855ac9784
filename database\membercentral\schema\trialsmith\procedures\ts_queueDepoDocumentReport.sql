ALTER PROC dbo.ts_queueDepoDocumentReport
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	CREATE TABLE #tmpDepoMembers (depomemberdataID int PRIMARY KEY);

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	SET @itemCount = 0;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='TSDepoDocumentReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	-- get depoMembers whose depositions were approved in the previous week
	INSERT INTO #tmpDepoMembers
	SELECT DISTINCT doc.DepomemberdataID 
	FROM dbo.depoDocuments doc 
	INNER JOIN dbo.depoDocumentStatusHistory docSH ON docSH.documentID = doc.DocumentID
	INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = docSH.statusID
		AND ds.statusName = 'Approved'
	WHERE docSH.dateEntered BETWEEN DATEADD(DAY,-7,@nowDate) and @nowDate;
	
	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- populate queue
	INSERT INTO platformQueue.dbo.queue_TSDepoDocumentReport (depomemberdataID, FirstName, LastName, Email, statusID, dateAdded, dateUpdated)
	SELECT d.depomemberdataID, d.FirstName, d.LastName, d.Email, @statusReady, GETDATE(), GETDATE()
	FROM #tmpDepoMembers AS tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID
	LEFT OUTER JOIN platformQueue.dbo.queue_TSDepoDocumentReport AS qi ON qi.depomemberdataID = tmp.depomemberdataID
	WHERE d.Email <> ''
	AND d.optOutTSMarketing = 0
	AND qi.itemID IS NULL;
	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='TrialSmith Depo Document report', @engine='MCLuceeLinux';

	on_done:
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
