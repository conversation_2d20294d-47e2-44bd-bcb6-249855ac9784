<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.processQueueResult = processQueue(strTask=arguments.strTask, batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.totalItemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "totalItemCount":0 }>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_importPagesJSON_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryPageJSONEntries" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.totalItemCount = val(local.qryPageJSONEntries.totalItemCount)>

			<cfloop query="local.qryPageJSONEntries">
				<cfset updateQueueStatus(itemIDList=local.qryPageJSONEntries.itemID, queueStatus="Processing")>
				<cfset local.strPageData = deserializeJSON(local.qryPageJSONEntries.pageJSON)>
				<cfset processPageJSON(itemID=local.qryPageJSONEntries.itemID, strPageData=local.strPageData)>
			</cfloop>

			<cfset sendEmailReportsForProcessedEntries(strTask=arguments.strTask)>

			<cfset deleteCompletedQueueEntries()>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="validateAndPrepPageJSON" access="private" output="false" returntype="array">
		<cfargument name="strPageData" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.arrErrors = []>

		<cfif StructIsEmpty(arguments.strPageData)>
			<cfset arrayAppend(local.arrErrors, "Empty JSON.")>
		<cfelse>
			<cfset local.arrRequiredFields = ["pageName","siteResourceType","status"]>
			<cfloop array="#local.arrRequiredFields#" item="local.thisField">
				<cfif not structKeyExists(arguments.strPageData, local.thisField) or not len(arguments.strPageData[local.thisField])>
					<cfset arrayAppend(local.arrErrors, "No value found for [#local.thisField#].")>
				</cfif>
			</cfloop>
			<cfif len(arguments.strPageData.pageName) gt 50>
				<cfset arrayAppend(local.arrErrors, "[pageName] has #len(arguments.strPageData.pageName)# characters. Max length is 50: #arguments.strPageData.pageName#")>
			</cfif>
		</cfif>

		<cfif NOT ArrayLen(local.arrErrors)>
			<cfif not structKeyExists(arguments.strPageData,"inheritPlacements")>
				<cfset arguments.strPageData.inheritPlacements = 1>
			</cfif>
			<cfif not structKeyExists(arguments.strPageData,"modeOverride")>
				<cfset arguments.strPageData.modeOverride = "">
			</cfif>
			<cfif not structKeyExists(arguments.strPageData,"templateOverride")>
				<cfset arguments.strPageData.templateOverride = "">
			</cfif>
			<cfif not structKeyExists(arguments.strPageData,"templateOverrideMobile")>
				<cfset arguments.strPageData.templateOverrideMobile = "">
			</cfif>
			<cfif not structKeyExists(arguments.strPageData,"applyNoIndex")>
				<cfset arguments.strPageData.applyNoIndex = 0>
			</cfif>
			<cfif not structKeyExists(arguments.strPageData,"applyNoFollow")>
				<cfset arguments.strPageData.applyNoFollow = 0>
			</cfif>
			<cfif not structKeyExists(arguments.strPageData,"applyNoArchive")>
				<cfset arguments.strPageData.applyNoArchive = 0>
			</cfif>
			<cfif not structKeyExists(arguments.strPageData,"dateUnavailable") or arguments.strPageData.applyNoIndex eq 1>
				<cfset arguments.strPageData.dateUnavailable = "">
			</cfif>

			<cfif not structKeyExists(arguments.strPageData,"languages") or not isStruct(arguments.strPageData.languages) or structIsEmpty(arguments.strPageData.languages)>
				<cfset arrayAppend(local.arrErrors, "Invalid or empty value found for languages.")>
			<cfelse>
				<cfloop collection="#arguments.strPageData.languages#" item="local.thisLanguageKey">
					<cfset local.thisLanguage = arguments.strPageData.languages[local.thisLanguageKey]>
					<cfif not structKeyExists(local.thisLanguage, "languageCode")>
						<cfset arrayAppend(local.arrErrors, "No value found for [languageCode] within languages [#local.thisLanguageKey#].")>
					</cfif>
					<cfif not structKeyExists(local.thisLanguage, "pageTitle")>
						<cfset arrayAppend(local.arrErrors, "No value found for [pageTitle] within languages [#local.thisLanguageKey#].")>
					</cfif>
					<cfif len(local.thisLanguage.pageTitle) gt 200>
						<cfset arrayAppend(local.arrErrors, "#local.thisLanguageKey# pageTitle has #len(local.thisLanguage.pageTitle)# characters. Max length is 200: #local.thisLanguage.pageTitle#")>
					</cfif>
					<cfif not structKeyExists(local.thisLanguage, "keywords")>
						<cfset local.thisLanguage.keywords = "">
					</cfif>
					<cfif not structKeyExists(local.thisLanguage, "description")>
						<cfset local.thisLanguage.description = "">
					</cfif>
					<cfif len(local.thisLanguage.description) gt 400>
						<cfset arrayAppend(local.arrErrors, "#local.thisLanguageKey# description has #len(local.thisLanguage.description)# characters. Max length is 400: #local.thisLanguage.description#")>
					</cfif>

				</cfloop>
			</cfif>

			<cfif not structKeyExists(arguments.strPageData,"pageZones") or not isStruct(arguments.strPageData.pageZones) or structIsEmpty(arguments.strPageData.pageZones)>
				<cfset arrayAppend(local.arrErrors, "Invalid or empty value found for pageZones.")>
			<cfelse>
				<cfloop collection="#arguments.strPageData.pageZones#" item="local.thisZoneName">
					<cfset local.thisZone = arguments.strPageData.pageZones[local.thisZoneName]>

					<cfloop array="#local.thisZone.content#" item="local.thisContent">
						<cfif not structKeyExists(local.thisZone, "zoneName")>
							<cfset arrayAppend(local.arrErrors, "No value found for zoneName within pageZone [#local.thisZoneName#].")>
						</cfif>
						<cfif not structKeyExists(local.thisZone, "isHTML")>
							<cfset local.thisZone.isHTML = 1>
						</cfif>
						<cfif not structKeyExists(local.thisZone, "uid")>
							<cfset local.thisZone.uid = "">
						</cfif>

						<cfif not structKeyExists(local.thisContent, "permissions") or not isArray(local.thisContent.permissions)>
							<cfset local.thisContent.permissions = []>
						<cfelse>
							<cfloop array="#local.thisContent.permissions#" item="local.thisPermission">
								<cfif not structKeyExists(local.thisPermission, "functionName")>
									<cfset arrayAppend(local.arrErrors, "No value found for [functionName] within pageZone [#local.thisZoneName#] content permissions.")>
								</cfif>
								<cfif not structKeyExists(local.thisPermission, "allowed")>
									<cfset arrayAppend(local.arrErrors, "No value found for [allowed] within pageZone [#local.thisZoneName#] content permissions.")>
								</cfif>
								<cfif not structKeyExists(local.thisPermission, "groupUIDs") or NOT isArray(local.thisPermission.groupUIDs)>
									<cfset arrayAppend(local.arrErrors, "No valid value found for [groupUIDs] within pageZone [#local.thisZoneName#] content permissions.")>
								</cfif>
							</cfloop>
						</cfif>

						<cfif not structKeyExists(local.thisContent, "languages") or not isStruct(local.thisContent.languages) or structIsEmpty(local.thisContent.languages)>
							<cfset arrayAppend(local.arrErrors, "Invalid or empty value found for languages within pageZone [#local.thisZoneName#] content.")>
						<cfelse>
							<cfloop collection="#local.thisContent.languages#" item="local.thisLanguageKey">
								<cfset local.thisLanguage = local.thisContent.languages[local.thisLanguageKey]>

								<cfif not structKeyExists(local.thisLanguage, "languageCode")>
									<cfset arrayAppend(local.arrErrors, "No value found for [languageCode] within pageZone [#local.thisZoneName#] content language [#local.thisLanguageKey#].")>
								</cfif>
								<cfif not structKeyExists(local.thisLanguage, "contentTitle")>
									<cfset arrayAppend(local.arrErrors, "No value found for [contentTitle] within pageZone [#local.thisZoneName#] content language [#local.thisLanguageKey#].")>
								</cfif>
								<cfif len(local.thisLanguage.contentTitle) gt 200>
									<cfset arrayAppend(local.arrErrors, "#local.thisLanguageKey# contentTitle within pageZone [#local.thisZoneName#] has #len(local.thisLanguage.contentTitle)# characters. Max length is 200: #local.thisLanguage.contentTitle#")>
								</cfif>
								<cfif not structKeyExists(local.thisLanguage, "description")>
									<cfset local.thisLanguage.description = "">
								</cfif>
								<cfif len(local.thisLanguage.description) gt 400>
									<cfset arrayAppend(local.arrErrors, "#local.thisLanguageKey# description within pageZone [#local.thisZoneName#] has #len(local.thisLanguage.description)# characters. Max length is 400: #local.thisLanguage.description#")>
								</cfif>
								<cfif not structKeyExists(local.thisLanguage, "rawContent")>
									<cfset local.thisLanguage.rawContent = "">
								</cfif>
							</cfloop>
						</cfif>
					</cfloop>
				</cfloop>
			</cfif>
		</cfif>

		<cfreturn local.arrErrors>
	</cffunction>

	<cffunction name="processPageJSON" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="strPageData" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.arrErrors = validateAndPrepPageJSON(strPageData=arguments.strPageData)>

		<cfif NOT ArrayLen(local.arrErrors)>
			<cftry>
				<cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##mc_pagesJSONImport') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_languages') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_languages;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_zoneContent') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_zoneContent;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_zoneContentPermissions') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_zoneContentPermissions;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_zoneContentLanguages') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_zoneContentLanguages;

						DECLARE @itemID int, @success bit, @errorMessage varchar(max), @importEntryID int, @zoneContentID int;
						SET @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
						
						CREATE TABLE ##mc_pagesJSONImport(importEntryID int IDENTITY(1,1), itemID int, siteResourceType varchar(50), pageName varchar(50),
							sectionPathExpanded varchar(max), status varchar(100), quickLink varchar(1000), inheritPlacements bit, modeOverride varchar(30), 
							templateOverride varchar(100), templateOverrideMobile varchar(100), applyNoIndex bit, applyNoFollow bit, applyNoArchive bit, 
							dateUnavailable varchar(100));
						CREATE TABLE ##mc_pagesJSONImport_languages(autoID int IDENTITY(1,1), importEntryID int, languageCode varchar(5),
							pageTitle varchar(200), keywords varchar(400), description varchar(400));
						CREATE TABLE ##mc_pagesJSONImport_zoneContent(zoneContentID int IDENTITY(1,1), importEntryID int, zoneName varchar(30), isHTML bit, [uid] varchar(36));
						CREATE TABLE ##mc_pagesJSONImport_zoneContentPermissions(autoID int IDENTITY(1,1), zoneContentID int, functionName varchar(200), allowed bit, groupUIDs varchar(max));
						CREATE TABLE ##mc_pagesJSONImport_zoneContentLanguages(autoID int IDENTITY(1,1), zoneContentID int, languageCode varchar(5),
							contentTitle varchar(200), description varchar(400), rawContent varchar(max));

						INSERT INTO ##mc_pagesJSONImport(itemID, siteResourceType, pageName, sectionPathExpanded, status, quickLink, inheritPlacements,
							modeOverride, templateOverride, templateOverrideMobile, applyNoIndex, applyNoFollow, applyNoArchive, dateUnavailable)
						VALUES (
							@itemID,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.siteResourceType#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.pageName#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.sectionPathExpanded#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.status#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.quickLink#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strPageData.inheritPlacements#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.modeOverride#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.templateOverride#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.templateOverrideMobile#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strPageData.applyNoIndex#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strPageData.applyNoFollow#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strPageData.applyNoArchive#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strPageData.dateUnavailable#">
						);
						SELECT @importEntryID = SCOPE_IDENTITY();

						<cfloop collection="#arguments.strPageData.languages#" item="local.thisLanguageKey">
							<cfset local.thisLanguage = arguments.strPageData.languages[local.thisLanguageKey]>
							INSERT INTO ##mc_pagesJSONImport_languages(importEntryID, languageCode, pageTitle, keywords, description)
							VALUES (
								@importEntryID,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.languageCode#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.pageTitle#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.keywords#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.description#">
							);
						</cfloop>

						<cfloop collection="#arguments.strPageData.pageZones#" item="local.thisZoneName">
							<cfset local.thisZone = arguments.strPageData.pageZones[local.thisZoneName]>

							<cfloop array="#local.thisZone.content#" item="local.thisContent">
								INSERT INTO ##mc_pagesJSONImport_zoneContent(importEntryID, zoneName, isHTML, [uid])
								VALUES (
									@importEntryID,
									<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisZone.zoneName#">,
									<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisContent.isHTML#">,
									<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisContent.uid#">
								);
								SELECT @zoneContentID = SCOPE_IDENTITY();

								<cfloop array="#local.thisContent.permissions#" item="local.thisPermission">
									INSERT INTO ##mc_pagesJSONImport_zoneContentPermissions(zoneContentID, functionName, allowed, groupUIDs)
									VALUES (
										@zoneContentID,
										<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisPermission.functionName#">,
										<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisPermission.allowed#">,
										<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.thisPermission.groupUIDs)#">
									);
								</cfloop>

								<cfloop collection="#local.thisContent.languages#" item="local.thisLanguageKey">
									<cfset local.thisLanguage = local.thisContent.languages[local.thisLanguageKey]>
									INSERT INTO ##mc_pagesJSONImport_zoneContentLanguages(zoneContentID, languageCode, contentTitle, description, rawContent)
									VALUES (
										@zoneContentID,
										<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.languageCode#">,
										<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.contentTitle#">,
										<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.description#">,
										<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLanguage.rawContent#">
									);
								</cfloop>
							</cfloop>
						</cfloop>

						-- import page from json
						BEGIN TRY
							SET @success = NULL;
							EXEC dbo.cms_importPageJSON @itemID=@itemID, @success=@success OUTPUT;
						END TRY
						BEGIN CATCH
							SET @success = 0;
							SET @errorMessage = error_message();
						END CATCH

						select @success as success, @errorMessage as errorMessage;
						
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_languages') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_languages;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_zoneContent') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_zoneContent;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_zoneContentPermissions') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_zoneContentPermissions;
						IF OBJECT_ID('tempdb..##mc_pagesJSONImport_zoneContentLanguages') IS NOT NULL
							DROP TABLE ##mc_pagesJSONImport_zoneContentLanguages;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfif local.qryImport.recordCount and val(local.qryImport.success) eq 0 and len(local.qryImport.errorMessage)>
					<cfset arrayAppend(local.arrErrors, "There was a problem importing the page JSON:" & local.qryImport.errorMessage)>
				</cfif>
			<cfcatch type="Any">
				<cfset arrayAppend(local.arrErrors, "There was a problem importing the page JSON: " & cfcatch.message & (len(cfcatch.detail) ? " " & cfcatch.detail : ""))>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfif ArrayLen(local.arrErrors)>
			<cfquery name="local.updateErrorMessage" datasource="#application.dsn.platformQueue.dsn#">
				UPDATE dbo.queue_importPagesJSON
				SET errorMessage = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#ArrayToList(local.arrErrors,"^--^")#">
				WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
			</cfquery>
		</cfif>

		<cfset updateQueueStatus(itemIDList=arguments.itemID, queueStatus="ReadyToNotify")>
	</cffunction>

	<cffunction name="sendEmailReportsForProcessedEntries" access="private" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">

		<cfset var local = structNew()>

		<cfstoredproc procedure="queue_importPagesJSON_grabForNotifying" datasource="#application.dsn.platformQueue.dsn#">
			<cfprocresult name="local.qryNotifyEntries" resultset="1">
		</cfstoredproc>

		<cfoutput query="local.qryNotifyEntries" group="itemGroupUID">
			<cfset local.arrPageNames = []>
			<cfset local.arrPageErrors = []>
			<cfset local.arrOtherErrorMsgs = []>
			<cfset local.itemIDList = "">

			<cftry>
				<cfoutput>
					<cfset local.thisPageName = "">
					<cfif isJSON(local.qryNotifyEntries.pageJSON)>
						<cfset local.strPageData = deserializeJSON(local.qryNotifyEntries.pageJSON)>
						<cfif structKeyExists(local.strPageData, "pageName")>
							<cfset local.thisPageName = local.strPageData.pageName>
						</cfif>
					</cfif>
					<cfif len(local.qryNotifyEntries.errorMessage)>
						<cfif len(local.thisPageName)>
							<cfset arrayAppend(local.arrPageErrors, {
								pageName: local.thisPageName,
								errMsgs: ListToArray(local.qryNotifyEntries.errorMessage,"^--^",false,true)
							})>
						<cfelse>
							<cfset arrayAppend(local.arrOtherErrorMsgs, ListToArray(local.qryNotifyEntries.errorMessage,"^--^",false,true), "true")>
						</cfif>
					<cfelse>
						<cfset ArrayAppend(local.arrPageNames, local.thisPageName)>
					</cfif>

					<cfset local.itemIDList = listAppend(local.itemIDList, local.qryNotifyEntries.itemID)>
				</cfoutput>

				<cfset arraysort(local.arrPageNames,"textnocase")>

				<cfsavecontent variable="local.notificationMessage">
					<div>
						<p>Hi #local.qryNotifyEntries.memberName#:</p>
						<p>We have completed processing your JSON files for Pages Import.</p>
						<cfif arrayLen(local.arrPageErrors) or arrayLen(local.arrOtherErrorMsgs)>
							<p>The following errors occurred while attempting to import pages from JSON.</p>
							<cfif arrayLen(local.arrPageErrors)>
								<cfloop array="#local.arrPageErrors#" item="local.thisPageError">
									<div>Page Name: <b>#local.thisPageError.pageName#</b></div>
									<ul style="margin-bottom:8px;">
										<cfloop array="#local.thisPageError.errMsgs#" item="local.thisErrMsg">
											<li>#local.thisErrMsg#</li>
										</cfloop>
									</ul>
								</cfloop>
							</cfif>
							<cfif arrayLen(local.arrOtherErrorMsgs)>
								<cfif arrayLen(local.arrPageErrors)>
									<div>Errors from files with no pageName in the JSON:</div>
								</cfif>
								<ul style="margin-bottom:8px;">
									<cfloop array="#local.arrOtherErrorMsgs#" item="local.thisErrMsg">
										<li>#local.thisErrMsg#</li>
									</cfloop>
								</ul>
							</cfif>
						</cfif>
						<cfif arrayLen(local.arrPageNames)>
							<p>The following pages were imported successfully.</p>
							<ul>
								<cfloop array="#local.arrPageNames#" item="local.thisPageName">
									<li>#local.thisPageName#</li>
								</cfloop>
							</ul>
						</cfif>
					</div>
				</cfsavecontent>

				<cfif len(local.qryNotifyEntries.memberEmail)>

					<cfset local.sitecode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.qryNotifyEntries.siteID)>
					<cfset local.siteinfo = application.objSiteInfo.mc_siteInfo[local.sitecode]>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo['MC'].networkEmailFrom},
						emailto=[{ name=local.qryNotifyEntries.memberName, email=local.qryNotifyEntries.memberEmail }],
						emailreplyto="",
						emailsubject="JSON Pages Import Report for #local.siteinfo.mainHostname# [JobID: #local.qryNotifyEntries.itemGroupUID#]",
						emailtitle="JSON Pages Import Report for #local.siteinfo.mainHostname#",
						emailhtmlcontent=local.notificationMessage,
						siteID=application.objSiteInfo.mc_siteInfo['MC'].siteID,
						memberID=local.qryNotifyEntries.submittedMemberID,
						messageTypeID=arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="SCHEDTASK"),
						sendingSiteResourceID=application.objSiteInfo.mc_siteInfo['MC'].siteSiteResourceID
					)>
				</cfif>

				<cfset updateQueueStatus(itemIDList=local.itemIDList, queueStatus="Done")>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStruct.success = false>
			</cfcatch>
			</cftry>
		</cfoutput>
	</cffunction>

	<cffunction name="updateQueueStatus" access="private" output="false" returntype="void">
		<cfargument name="itemIDList" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @queueStatusID int;
				EXEC dbo.queue_getStatusIDbyType @queueType='importPagesJSON', @queueStatus=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueStatus#">, @queueStatusID=@queueStatusID OUTPUT;

				UPDATE dbo.queue_importPagesJSON
				SET statusID = @queueStatusID,
					dateUpdated = getdate()
				WHERE itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">);

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="deleteCompletedQueueEntries" access="private" output="false" returntype="void">
		<cfset var local = structNew()>

		<cfquery name="local.updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @statusDone int;
				EXEC dbo.queue_getStatusIDbyType @queueType='importPagesJSON', @queueStatus='Done', @queueStatusID=@statusDone OUTPUT;

				WITH completedJobs AS (
					SELECT qi.itemID 
					FROM dbo.queue_importPagesJSON AS qi
					WHERE qi.statusID = @statusDone
					AND NOT EXISTS (
						SELECT 1
						FROM dbo.queue_importPagesJSON AS tmp
						WHERE tmp.itemGroupUID = qi.itemGroupUID
						AND tmp.statusID <> @statusDone
					)
				)
				DELETE qid
				FROM dbo.queue_importPagesJSON AS qid
				INNER JOIN completedJobs AS batch ON batch.itemID = qid.itemID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		</cfquery>
	</cffunction>

</cfcomponent>