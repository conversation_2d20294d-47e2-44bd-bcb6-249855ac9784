ALTER PROC dbo.sw_deleteFileFormat
@fileID int,
@fileEXT varchar(100),
@fileMode varchar(1),
@objectKey varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @s3DeleteReadyStatusID int, @siteResourceID int, @formatsAvailable xml;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

	SET @fileEXT = LOWER(@fileEXT);
	SET @fileMode = UPPER(@fileMode);

	-- delete the node format from formatsAvailable field 
	SELECT @formatsAvailable = formatsAvailable FROM dbo.tblFiles WHERE fileID = @fileID;
	SET @formatsAvailable.modify('delete /formats/format[@ext=sql:variable("@fileEXT")][@accesstype=sql:variable("@fileMode")]');

	BEGIN TRAN;
		IF (SELECT tier FROM memberCentral.dbo.fn_getServerSettings()) = 'production'
		BEGIN
			INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
			VALUES (@s3DeleteReadyStatusID, 'seminarweb', @objectKey, GETDATE(), GETDATE());
		END

		UPDATE dbo.tblFiles
		SET formatsAvailable = @formatsAvailable
		WHERE fileID = @fileID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
