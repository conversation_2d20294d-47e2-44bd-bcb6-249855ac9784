ALTER PROC dbo.ts_addDepoDocumentToUnzipQueue
@pathToZip varchar(400),
@depomemberdataID int,
@docState varchar(10),
@DepoAmazonBucks bit = 0,
@DepoAmazonBucksFullName varchar(550),
@DepoAmazonBucksEmail varchar(255),
@uploadSourceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='depoDocumentsUnzip', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_depoDocumentsUnzip WHERE pathToZip = @pathToZip) BEGIN
		INSERT INTO platformQueue.dbo.queue_depoDocumentsUnzip (pathToZip, depomemberdataID, State, DepoAmazonBucks, 
			DepoA<PERSON>zonBucksFullName, DepoAmazonBucksEmail, dateAdded, dateUpdated, statusID, uploadSourceID)
		VALUES (@pathToZip, @depomemberdataID, @docState, @DepoAmazonBucks, @DepoAmazonBucksFullName, 
			@DepoAmazonBucksEmail, @nowDate, @nowDate, @statusReady, @uploadSourceID);

		EXEC membercentral.dbo.sched_resumeTask @name='Process Depo Documents Unzip Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
