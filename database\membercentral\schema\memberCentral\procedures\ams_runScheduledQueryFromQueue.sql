ALTER PROC dbo.ams_runScheduledQueryFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @queryID int, @queueTypeID int, @readyToProcessStatusID int, @processingItemStatusID int, 
		@environmentName varchar(50);
	SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='schQueries', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingItemStatusID OUTPUT;

	SELECT @siteID = siteID, @queryID = queryID
	FROM platformQueue.dbo.queue_schQueries
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @queryID IS NULL
		GOTO on_done;

	-- update queue item status
	UPDATE platformQueue.dbo.queue_schQueries
	SET statusID = @processingItemStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- run scheduled query
	EXEC dbo.ams_scheduledQueryImportMembers @queryID=@queryID, @siteID=@siteID, @environmentName=@environmentName;

	-- schedule next run date
	EXEC dbo.ams_setScheduledQueryNextRunDate @queryID=@queryID;

	-- delete queue item
	DELETE FROM platformQueue.dbo.queue_schQueries
	WHERE itemID = @itemID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
