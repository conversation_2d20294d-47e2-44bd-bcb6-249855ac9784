ALTER PROC dbo.queue_completeSeminarReminder_grabForProcessing
@batchCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='completeSeminarReminder', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_completeSeminarReminder AS qid
	INNER JOIN (
		SELECT TOP (@batchCount) itemID
		FROM dbo.queue_completeSeminarReminder
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.orgcode, qid.memberID, qid.email, qid.firstName, qid.lastName, qid.overrideEmail, 
		qid.EmailFrom, qid.TimeLapse, qid.CatalogURL, qid.seminarID, qid.seminarName, qid.dateEnrolled, 
		qid.supportPhone, qid.supportEmail, qid.lastdatetoComplete, qid.emailOptionID, qid.emailsubject,
		qid.orgIdentityID
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_completeSeminarReminder AS qid ON qid.itemID = tmp.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
