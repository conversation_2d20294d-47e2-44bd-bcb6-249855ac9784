CREATE PROC dbo.queue_evRegistrantDelete_download
@status varchar(60),
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max) = '';

	SELECT @selectsql = 'SELECT ''evRegistrantDelete'' as queueType, qs.queueStatus, qi.itemID, qi.itemGroupUID, qi.registrantID,
		qi.orgID, qi.siteID, qi.AROption, qi.cancellationFee, qi.GLAccountID, qi.deallocUsingPaidAmt, qi.dateAdded, qi.dateUpdated, qi.recordedByMemberID,
		ROW_NUMBER() OVER(order by qi.dateAdded, qi.itemID) as mcCSVorder
		*FROM* platformQueue.dbo.queue_evRegistrantDelete as qi
		INNER JOIN platformQueue.dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID and qs.queueStatus = ''' + @status + '''';

	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO