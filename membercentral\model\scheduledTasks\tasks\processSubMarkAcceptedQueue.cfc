<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="590">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="100" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>
		
		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.arrOffers = ArrayNew(1)>
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>
		<cfset local.objHistory = CreateObject('component', 'model.system.platform.history')>
		<cfset local.subRegObj = CreateObject("component","model.admin.subscriptions.subscriptionReg")>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<!--- reprioritize the queue --->
		<cftry>
			<cfstoredproc procedure="queue_subscriptionAccept_prioritize" datasource="#application.dsn.platformQueue.dsn#">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
		
		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_subscriptionAccept_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qrySubscribers" resultset="1">
			</cfstoredproc>
	
			<!--- loop per subscriber --->
			<cfloop query="local.qrySubscribers">
			
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_subscriptionAccept as qi
					INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">
					AND qs.queueStatus = 'grabbedForProcessing'
				</cfquery>
				<cfif local.checkItemID.itemCount>

					<cftry>
						<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @statusProcessing int;
							EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionAccept', @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;

							UPDATE dbo.queue_subscriptionAccept
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">;
						</cfquery>

						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptions">
							select subscriberID, memberID, memberName, subscriptionID, typeName, subscriptionName, 
									CONVERT(VARCHAR, subStartDate, 101) as startDate, CONVERT(VARCHAR, subEndDate, 101) as endDate
							from dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.siteID#">,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.subscriberID#">)
							where status <> 'D'
							order by thePath
						</cfquery>

						<cfif local.qrySubscriptions.recordCount gt 0>
							<cfset session.cfcuser.memberdata.memberid = local.qrySubscribers.recordedByMemberID>
							<!--- if requested, skipping email was handled when items were loaded into the queue, so we just pass false here--->
							<cfset local.acceptResult = local.subRegObj.autoAcceptSubscription(
								orgID=local.qrySubscribers.orgID, 
								siteID=local.qrySubscribers.siteID,
								siteCode=local.qrySubscribers.sitecode,
								memberID=local.qrySubscribers.memberID, 
								rootSubscriberID=local.qrySubscribers.subscriberID,
								skipEmailTemplateNotifications=false,
								bypassQueue=1)>
							<cfif local.acceptResult.success eq 0>
								<cfquery datasource="#application.dsn.platformQueue.dsn#" name="local.noRate">
									SET NOCOUNT ON;

									DECLARE @itemID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.itemID#">;

									<!--- deadlocks --->
									<cfif local.acceptResult.retCreate.keyExists("errcode") AND local.acceptResult.retCreate.errcode EQ 'SQLDEADLOCK'>
										DECLARE @statusReady int;
										EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionAccept', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

										UPDATE dbo.queue_subscriptionAccept
										SET statusID = @statusReady, 
											dateUpdated = GETDATE()
										WHERE itemID = @itemID;
									</cfif>

									UPDATE dbo.queue_subscriptionAccept
									SET errorMessage = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.acceptResult.retCreate.errReason# for #local.qrySubscriptions.subscriptionName#.">
									WHERE itemID = @itemID;
								</cfquery>

							<!--- charge amount and allocate to invoices if any --->
							<cfelseif val(local.qrySubscribers.amountToCharge) gt 0>
								<cfset local.totalAmtToCharge = val(local.qrySubscribers.amountToCharge)>
								<cfset local.useMID = local.qrySubscribers.memberID>

								<!--- get fields --->
								<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=local.qrySubscribers.siteID, profilecode=local.qrySubscribers.profilecode)>
								<cfset local.tmpFields = structNew()>
								<cfloop query="local.qryGatewayProfileFields">
									<cfif local.qryGatewayProfileFields.fieldType eq 'Check Number'>
										<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_',local.qrySubscribers.checkNumber)>
									<cfelse>
										<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_','')>
									</cfif>
								</cfloop>

								<!--- get info on file if applicable --->
								<cfif val(local.qrySubscribers.memberPayProfileID) gt 0>
									<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=local.qrySubscribers.memberPayProfileID, memberID=local.useMID, profileID=local.qrySubscribers.merchantProfileID)>
									<cfset structInsert(local.tmpFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
								</cfif>

								<!--- prepare fields for gateway and send --->
								<cfset local.strTemp = { orgID=local.qrySubscribers.orgID, siteid=local.qrySubscribers.siteID, 
														 profileCode=local.qrySubscribers.profileCode, assignedToMemberID=local.useMID, 
														 recordedByMemberID=local.qrySubscribers.recordedByMemberID, statsSessionID=0, 
														 x_amount=local.totalAmtToCharge, x_description='Payment for #local.qrySubscriptions.subscriptionName#' }>

								<cfif val(local.qrySubscribers.batchID) gt 0>
									<cfset structInsert(local.strTemp, "overrideBatchID", local.qrySubscribers.batchID)>
									<cfset structInsert(local.strTemp, "overrideTransactionDate", local.qrySubscribers.paymentDate)>
									<cfset structInsert(local.strTemp, "overrideAcceptPending", local.qrySubscribers.overridePendingPayment)>
								</cfif>

								<cfset structAppend(local.strTemp,local.tmpFields)>
								
								<cfif listFindNoCase("AuthorizeCCCIM",local.qrySubscribers.gatewayType)>
									<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
									<cfset QueryAddRow(local.qryLevel3Data, {
										"name": "Subscription Payment",
										"desc": "#local.qrySubscribers.sitecode# #local.qrySubscriptions.subscriptionName#",
										"itemPriceExcDiscount": local.totalAmtToCharge,
										"itemPriceIncDiscount": local.totalAmtToCharge,
										"discount": 0,
										"qty": 1,
										"total": local.totalAmtToCharge
									})>
									<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qrySubscribers.gatewayType)>
								</cfif>
								<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

								<!--- if payment not successful --->
								<cfif local.paymentResponse.responseCode is not 1>
									<cfset local.paymentFailMessage = local.paymentResponse.responseReasonText>
								<cfelse>
									<cfset local.paymentFailMessage = "">
								</cfif>

								<!--- allocate payment --->
								<cfif local.paymentResponse.mc_transactionID gt 0>
									<cftry>
										<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
											set nocount on;

											declare @orgID int, @siteID int, @subscriberID int;
											set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.orgID#">;
											set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.siteID#">;
											set @subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.subscriberID#">;

											IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
												DROP TABLE ##mcSubscribersForAcct;
											IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
												DROP TABLE ##mcSubscriberTransactions;

											CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
											CREATE TABLE ##mcSubscriberTransactions (subscriberID int, transactionID int INDEX IDX_mcSubscriberTransactions_transactionID, transactionIDForSubscriberSale int, statusID int, detail varchar(500), 
												amount decimal(18,2), dateRecorded datetime, transactionDate datetime, assignedToMemberID int, recordedByMemberID int,
												statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

											-- populate mcSubscriberTransactions
											INSERT INTO ##mcSubscribersForAcct (subscriberID)
											select ss.subscriberID
											FROM sub_subscribers ss
											WHERE orgID = @orgID and rootSubscriberID = @subscriberID;


											EXEC dbo.sub_getSubscriberTransactions_Full @orgID=@orgID;

											select it.invoiceID, sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) as amountDue
											from ##mcSubscriberTransactions as stFull
											inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = stFull.transactionID
											inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
											inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
											where invs.status in ('Closed','Delinquent')
											group by it.invoiceID
											having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;

											IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
												DROP TABLE ##mcSubscribersForAcct;
											IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
												DROP TABLE ##mcSubscriberTransactions;
										</cfquery>

										<cfset local.leftOver = local.totalAmtToCharge>

										<cfloop query="local.qryInvoices">
											<cfset local.thisAllocAmount = min(local.qryInvoices.amountDue,local.leftOver)>
											
											<cfset local.strACCTemp = { recordedOnSiteID=local.qrySubscribers.siteID, recordedByMemberID=local.useMID, 
																		statsSessionID=0, amount=local.thisAllocAmount, transactionDate=now(), 
																		paymentTransactionID=local.paymentResponse.mc_transactionID, 
																		invoiceID=local.qryInvoices.invoiceID }>
											<cfset local.strACCAllocate = local.objAccounting.allocateToInvoice(argumentcollection=local.strACCTemp)>
											<cfif local.strACCAllocate.rc is not 0>
												<cfthrow message="Failed to allocate payment to invoice">
											<cfelse>
												<cfset local.leftOver = local.leftOver - local.thisAllocAmount>
												<cfif local.leftOver is 0>
													<cfbreak>
												</cfif>
											</cfif>
										</cfloop>
									<cfcatch type="any">
										<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
										<cfset local.paymentFailMessage = cfcatch.message>
									</cfcatch>
									</cftry>
								</cfif>

								<cfif len(local.paymentFailMessage)>
									<cfquery name="local.qryRecordError" datasource="#application.dsn.platformQueue.dsn#">
										update dbo.queue_subscriptionAccept
										set errorMessage = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.paymentFailMessage#">
										where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.itemID#">
									</cfquery>
								</cfif>

							</cfif>
						</cfif>

					<cfcatch type="Any">
						<cfquery datasource="#application.dsn.platformQueue.dsn#" name="local.noRate">
							update dbo.queue_subscriptionAccept
							set errorMessage = <cfqueryparam cfsqltype="cf_sql_varchar" value="#cfcatch.message#">
							where itemID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qrySubscribers.itemID#">
						</cfquery>

						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>

					<!--- UPDATE STATUS --->
					<cfquery name="local.updateToReadyToNotify" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @queueTypeID int, @statusProcessing int, @statusReadyToNotify int;
						EXEC dbo.queue_getQueueTypeID @queueType='subscriptionAccept', @queueTypeID=@queueTypeID OUTPUT;
						EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;
						EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReadyToNotify OUTPUT;

						UPDATE dbo.queue_subscriptionAccept
						SET statusID = @statusReadyToNotify,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">
						AND statusID = @statusProcessing;
					</cfquery>
				</cfif>
			</cfloop>

			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_subscriptionAccept_grabForNotification" datasource="#application.dsn.platformQueue.dsn#">
					<cfprocresult name="local.qryNotifications" resultset="1">
				</cfstoredproc>
				<cfif local.qryNotifications.recordcount>
	
					<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;padding:2px;">
					<cfset local.thStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;border-bottom:1px solid ##333;padding:2px;">
					<cfset local.pageStyle = "width:600px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;">
					<cfset local.errStyle = "color:##f00;font-weight:bold;">
	
					<cfoutput query="local.qryNotifications" group="itemGroupUID">
						<cftry>
							<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
							<cfset local.thisSiteName = local.qryNotifications.siteName>
							<cfset local.thisSiteCode = local.qryNotifications.siteCode>
							<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryNotifications.sitecode)>
							<cfset local.arrSuccess = arrayNew(1)>
							<cfset local.arrFailure = arrayNew(1)>

							<cfoutput>
								<cfset local.errStruct = {
									memberID = local.qryNotifications.memberID,
									memberName = local.qryNotifications.subscriberName,
									rootSubscriberID = local.qryNotifications.subscriberID,
									errType = "",
									errMessage = ""
								}>
								<cfif len(local.qryNotifications.errorMessage)>
									<cfset local.errStruct.errType = "Error">
									<cfset local.errStruct.errMessage = local.qryNotifications.errorMessage>
									<cfset arrayAppend(local.arrFailure, local.errStruct)>
								<cfelse>
									<cfset local.errStruct.errType = "Marked Accepted">
									<cfset local.errStruct.errMessage = "Accepted #local.qryNotifications.subscriptionName#">
									<cfset arrayAppend(local.arrSuccess, local.errStruct)>
								</cfif>
							</cfoutput>

							<cfset local.resultMsg = "Subscription Mark Accepted Results:<br>#ArrayLen(local.arrSuccess)# successfully accepted<br>#ArrayLen(local.arrFailure)# errors<br>">
							
							<cfset local.objHistory.addSubOfferUpdateHistory(orgID=local.qryNotifications.orgID, 
								actorMemberID=local.qryNotifications.reportMemberID, mainMessage=local.resultMsg, 
								offers=local.arrSuccess)>

							<cfif len(local.thisReportEmail)>
								<cfsavecontent variable="local.thisEmailContent">
									<div style="font-family:arial;">
										<table style="border:0;padding:4px;width:100%;margin-bottom:10px;" cellspacing="0">
											<tr valign="top">
												<td colspan="2" style="width:120px;font-weight:bold;border-bottom:1px solid ##ccc; font-size:9pt;">
													#local.resultMsg#
													<br /><br />
													Requested by: #local.qryNotifications.reportfirstname# #local.qryNotifications.reportlastname# (#local.qryNotifications.reportmembernumber#) <br/>
													Email: <cfif len(local.qryNotifications.reportEmail)>#local.qryNotifications.reportEmail#<cfelse>(none)</cfif><br/>
												</td>
											</tr>
											<cfset local.currLoopMemberID = 0>
											<cfloop array="#local.arrFailure#" index="local.thisMessage">
												<cfif local.thisMessage.memberID neq local.currLoopMemberID>
													<tr valign="top">
														<td colspan="2" style="width:120px;font-weight:bold;border-bottom:1px solid ##ccc; font-size:9pt;">
															<br><nobr>#local.thisMessage.memberName#:</nobr>
															<cfset local.currLoopMemberID = local.thisMessage.memberID>
														</td>
													</tr>
												</cfif>
												<tr valign="top">
													<td style="width:2%">&nbsp;</td>
													<td style="border-bottom:1px solid ##ccc; font-size:9pt;">
														#local.thisMessage.errMessage#
													</td>
												</tr>
											</cfloop>
										</table>
									</div>
								</cfsavecontent>
								<cfset local.thisEmailContent = trim(replace(replace(replace(local.thisEmailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

								<cfscript>
									local.arrEmailTo = [];
									
									local.toEmailArr = listToArray(local.thisReportEmail,';');
									for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
										local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
									}
								</cfscript>
								<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name='MemberCentral', email='<EMAIL>' },
									emailto=local.arrEmailTo,
									emailreplyto="<EMAIL>",
									emailsubject="#local.thisSiteName# Mark Accepted Report" ,
									emailtitle="#local.thisSiteName# Mark Accepted Report",
									emailhtmlcontent=local.thisEmailContent,
									emailAttachments=[],
									siteID=local.mc_siteinfo.siteID,
									memberID=local.qryNotifications.reportMemberID,
									messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
									sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
								)>
							</cfif>
							
							<!--- ------------- --->
							<!--- UPDATE STATUS --->
							<!--- ------------- --->
							<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
								set nocount on;
		
								declare @newstatus int;
								select @newstatus = qs.queueStatusID 
									from dbo.tblQueueStatuses as qs
									inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
									where qt.queueType = 'subscriptionAccept'
									and qs.queueStatus = 'done';
								
								IF @newstatus is not null
									update dbo.queue_subscriptionAccept 
									set statusID = @newstatus,
										dateUpdated = getdate()
									where itemGroupUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryNotifications.itemGroupUID#">;
							</cfquery>
	
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
						</cftry>
						
					</cfoutput>
	
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>			

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are marked as done, after processing subscription conditions for them --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_subscriptionAccept_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>			

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_subscriptionAccept;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>