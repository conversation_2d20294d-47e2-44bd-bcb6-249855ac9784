ALTER PROC dbo.queue_SWReplaceMediaFile_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusWaiting int, @statusReady int;
	EXEC dbo.queue_getQueueTypeID @queueType='SWReplaceMediaFile', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingToProcess', @queueStatusID=@statusWaiting OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	-- look at anything waitingToProcess to see if any are readyToProcess
	UPDATE qi
	SET qi.statusID = @statusReady,
		qi.dateUpdated = getdate()
	FROM dbo.queue_SWReplaceMediaFile as qi
	INNER JOIN seminarWeb.dbo.tblFiles AS f ON f.fileID = qi.newFileID
		AND f.duration IS NOT NULL
	WHERE qi.statusID = @statusWaiting;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
