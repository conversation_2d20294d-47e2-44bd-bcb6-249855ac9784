ALTER PROC dbo.queue_depoCrawlerIndex_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='depoCrawlerIndex', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int, nameID int);

	-- dequeue in order of dateAdded
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.nameID
		INTO #tmpQueueItem
	FROM dbo.queue_depoCrawlerIndex AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID
		FROM dbo.queue_depoCrawlerIndex
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT tmp.itemID, sei.nameID, sei.firstname, sei.lastname
	FROM #tmpQueueItem AS tmp
	INNER JOIN search.dbo.tblSearchEngineIndex as sei on sei.nameID = tmp.nameID
	ORDER BY tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
