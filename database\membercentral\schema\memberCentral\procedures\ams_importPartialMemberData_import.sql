ALTER PROC dbo.ams_importPartialMemberData_import
@orgID int,
@importTitle varchar(200),
@runByMemberID int,
@dataFilename varchar(400),
@dataFileExt varchar(3),
@inactivateNonIncMembers bit,
@bypassListEmailUpdate bit,
@environmentName varchar(50),
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @queueTypeID int, @statusInserting int, @statusToVerify int, @statusToProcess int, @jobID int, @websiteTypeID int, 
		@websiteType varchar(20), @siteID int, @addressTagTypeID int, @addressTagType varchar(60), @emailTypeID int, 
		@emailType varchar(20), @addressTypeID int, @phoneTypeID int, @addressType varchar(20), @hasAttn bit, @hasAddress2 bit, 
		@hasAddress3 bit, @hasCounty bit, @phoneType varchar(20), @PLTypeID int, @PLType varchar(114), @dynSQL nvarchar(max), 
		@hasPrefix bit, @usePrefixList bit, @colList varchar(max), @selColList varchar(max), @defaultAddressTypeID int, 
		@updateFields xml, @updateMembers xml, @finalMSG varchar(max), @IDList varchar(max), @logID bigint, 
		@logProcStart datetime, @logSubProcStart datetime, @numSubmitted int, @emailTagTypeID int, @emailTagType varchar(60),
		@defaultEmailTypeID int;
	declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int);

	select @logID = logID, @numSubmitted = numSubmitted from #tmpPMILogID;

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL 
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#memimport_memberWebsites') IS NOT NULL 
		DROP TABLE #memimport_memberWebsites;
	IF OBJECT_ID('tempdb..#memimport_memberEmails') IS NOT NULL 
		DROP TABLE #memimport_memberEmails;
	IF OBJECT_ID('tempdb..#memimport_memberAddressTags') IS NOT NULL 
		DROP TABLE #memimport_memberAddressTags;
	IF OBJECT_ID('tempdb..#memimport_memberEmailTags') IS NOT NULL 
		DROP TABLE #memimport_memberEmailTags;
	IF OBJECT_ID('tempdb..#memimport_memberAddresses') IS NOT NULL 
		DROP TABLE #memimport_memberAddresses;
	IF OBJECT_ID('tempdb..#memimport_memberPhones') IS NOT NULL 
		DROP TABLE #memimport_memberPhones;
	IF OBJECT_ID('tempdb..#memimport_memberProfessionalLicenses') IS NOT NULL 
		DROP TABLE #memimport_memberProfessionalLicenses;
	IF OBJECT_ID('tempdb..#tmpPMI_str') IS NOT NULL 
		DROP TABLE #tmpPMI_str;
	IF OBJECT_ID('tempdb..#tmpPMI_dec') IS NOT NULL 
		DROP TABLE #tmpPMI_dec;
	IF OBJECT_ID('tempdb..#memimport_memberDataColumnValues') IS NOT NULL 
		DROP TABLE #memimport_memberDataColumnValues;
	IF OBJECT_ID('tempdb..#memimport_mongo') IS NOT NULL 
		DROP TABLE #memimport_mongo;
	IF OBJECT_ID('tempdb..#tmpInnerMULTIUnpvt') IS NOT NULL 
		DROP TABLE #tmpInnerMULTIUnpvt;
	IF OBJECT_ID('tempdb..#tmpInnerMULTI') IS NOT NULL 
		DROP TABLE #tmpInnerMULTI;
	IF OBJECT_ID('tempdb..#tmpInnerHoldMULTI') IS NOT NULL 
		DROP TABLE #tmpInnerHoldMULTI;
	IF OBJECT_ID('tempdb..#tmpQueueMembers') IS NOT NULL 
		DROP TABLE #tmpQueueMembers;
	IF OBJECT_ID('tempdb..#tmpMDCDef') IS NOT NULL 
		DROP TABLE #tmpMDCDef;
	IF OBJECT_ID('tempdb..#tmpIDs') IS NOT NULL 
		DROP TABLE #tmpIDs;

	CREATE TABLE #tblExistingMember (memberID int PRIMARY KEY, prefix varchar(50), firstName varchar(75),
		middleName varchar(25), lastName varchar(75), suffix varchar(50), professionalSuffix varchar(100),
		company varchar(200), memberTypeID int, recordTypeID int, [status] char(1), membernumber varchar(50));
	CREATE TABLE #memimport_memberWebsites (memberNumber varchar(50), websiteTypeID int, website varchar(400));
	CREATE TABLE #memimport_memberEmails (memberNumber varchar(50), emailTypeID int, email varchar(255));
	CREATE TABLE #memimport_memberEmailTags (memberNumber varchar(50), emailTagTypeID int, emailTagType varchar(60), emailTypeID int);
	CREATE TABLE #memimport_memberAddressTags (memberNumber varchar(50), addressTagTypeID int, addressTagType varchar(60), addressTypeID int);
	CREATE TABLE #memimport_memberAddresses (memberNumber varchar(50), addressTypeID int, attn varchar(100), address1 varchar(100),
		address2 varchar(100), address3 varchar(100), city varchar(75), stateID int, postalCode varchar(25), county varchar(50), 
		countryID int, attnChanged bit, address1Changed bit, address2Changed bit, address3Changed bit, cityChanged bit, 
		stateIDChanged bit, postalCodeChanged bit, countyChanged bit, countryIDChanged bit);
	CREATE TABLE #memimport_memberPhones (memberNumber varchar(50), phoneTypeID int, addressTypeID int, phone varchar(40));
	CREATE TABLE #memimport_memberProfessionalLicenses (memberNumber varchar(50), PLTypeID int, LicenseNumber varchar(200), 
		ActiveDate datetime, PLStatusID int);
	CREATE TABLE #tmpPMI_str (MCmemberID int, memberNumber varchar(50), areaID int, columnValueString varchar(255));
	CREATE TABLE #tmpPMI_dec (MCmemberID int, memberNumber varchar(50), areaID int, columnValueDecimal2 decimal(14,2));
	CREATE TABLE #memimport_memberDataColumnValues (MCMemberID int, memberNumber varchar(50), columnID int, columnValueString varchar(255), 
		columnValueDecimal2 decimal(14,2), columnValueInteger int, columnvalueDate date, columnValueBit bit, 
		columnValueContent varchar(max), actualValueID int, 
		INDEX IX_memimport_memberDataColumnValues NONCLUSTERED (columnID, columnValueString, memberNumber, columnValueDate));
	CREATE TABLE #memimport_mongo (memberID int, columnName sysname, change varchar(max));
	CREATE TABLE #tmpInnerMULTIUnpvt (MCMemberID int, memberNumber varchar(50), columnName varchar(128), columnValue varchar(max));
	CREATE TABLE #tmpInnerMULTI (MCMemberID int, memberNumber varchar(50), columnName varchar(128), columnMultiValue varchar(max));
	CREATE TABLE #tmpInnerHoldMULTI (MCMemberID int, memberNumber varchar(50), columnName varchar(128), columnMultiValue varchar(max));
	CREATE TABLE #tmpQueueMembers (queueMemberID int PRIMARY KEY, membernumber varchar(50) INDEX IX_queuemembers_membernumber);
	CREATE TABLE #tmpMDCDef (columnID int PRIMARY KEY, columnName varchar(128), dataTypeCode varchar(20), 
		displayTypeCode varchar(20), allowMultiple bit);
	CREATE TABLE #tmpIDs (listItem int PRIMARY KEY);

	BEGIN TRY
		EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='MemberImport', @queueTypeID=@queueTypeID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Inserting', @queueStatusID=@statusInserting OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToVerify', @queueStatusID=@statusToVerify OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusToProcess OUTPUT;

		select @siteID = dbo.fn_getDefaultSiteIDFromOrgID(@orgID);
		select @hasPrefix=hasPrefix, @usePrefixList=usePrefixList from dbo.organizations where orgID = @orgID;
		set @importResult = null;

		IF @runByMemberID is null
			select @runByMemberID = dbo.fn_ams_getMCSystemMemberID();

		-- add temporary membernumbers to those that are blank
		update #mc_PartialMemImport
		set membernumber = '|pmi|' + cast(NEWID() as varchar(36))
		where membernumber = '';

		SET @logProcStart = getdate();
		
		/* ************************************************** */
		/* determine what member data changed for new members */
		/* ************************************************** */
		update #mc_PartialMemImport set firstNameChanged = 1, lastNameChanged = 1, memberTypeIDChanged = 1, statusChanged = 1 where MCMemberID is null;
		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'prefix')
			update #mc_PartialMemImport set prefixChanged = 1 where MCMemberID is null and isnull(prefix,'') <> '';
		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'middlename')
			update #mc_PartialMemImport set middleNameChanged = 1 where MCMemberID is null and isnull(middlename,'') <> '';
		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'suffix')
			update #mc_PartialMemImport set suffixChanged = 1 where MCMemberID is null and isnull(suffix,'') <> '';
		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'professionalSuffix')
			update #mc_PartialMemImport set professionalSuffixChanged = 1 where MCMemberID is null and isnull(professionalSuffix,'') <> '';
		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'company')
			update #mc_PartialMemImport set companyChanged = 1 where MCMemberID is null and isnull(company,'') <> '';
		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'MCRecordType')
			update #mc_PartialMemImport set recordTypeIDChanged = 1 where MCMemberID is null and isnull(mcrecordtype,'') <> '';

		/* ******************************************************* */
		/* determine what member data changed for existing members */
		/* ******************************************************* */
		insert into #tblExistingMember (memberID, prefix, firstName, middleName, lastName, suffix, professionalSuffix, 
			company, memberTypeID, recordTypeID, [status], membernumber)
		select m.memberID, m.prefix, m.firstName, m.middleName, m.lastName, m.suffix, m.professionalSuffix, 
			m.company, m.memberTypeID, m.recordTypeID, m.[status], m.membernumber
		from #mc_PartialMemImport as tmp 
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmp.MCMemberID;
			
		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'prefix')
			update tmp 
			set tmp.prefixChanged = 
				case 
				when @hasPrefix = 1 and @usePrefixList = 1 and isnull(tmp.prefix,'') = isnull(m.prefix,'') then 0 
				when isnull(tmp.prefix,'') = isnull(m.prefix,'') COLLATE Latin1_General_CS_AS then 0 
				else 1 
				end
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID;

		update tmp 
		set tmp.firstNameChanged = 1
		from #mc_PartialMemImport as tmp 
		inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
		where tmp.firstname <> m.firstname COLLATE Latin1_General_CS_AS;

		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'middlename')
			update tmp 
			set tmp.middleNameChanged = 1
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
			where isnull(tmp.middleName,'') <> isnull(m.middleName,'') COLLATE Latin1_General_CS_AS;

		update tmp 
		set tmp.lastNameChanged = 1
		from #mc_PartialMemImport as tmp 
		inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
		where tmp.lastName <> m.lastName COLLATE Latin1_General_CS_AS;

		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'suffix')
			update tmp 
			set tmp.suffixChanged = 1
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
			where isnull(tmp.suffix,'') <> isnull(m.suffix,'') COLLATE Latin1_General_CS_AS;

		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'professionalSuffix')
			update tmp 
			set tmp.professionalSuffixChanged = 1
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
			where isnull(tmp.professionalSuffix,'') <> isnull(m.professionalSuffix,'') COLLATE Latin1_General_CS_AS;

		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'company')
			update tmp 
			set tmp.companyChanged = 1
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
			where isnull(tmp.company,'') <> isnull(m.company,'') COLLATE Latin1_General_CS_AS;

		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'MCAccountType')
			update tmp 
			set tmp.memberTypeIDChanged = 1
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
			where tmp.MCMemberTypeID <> m.memberTypeID;

		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'MCRecordType')
			update tmp 
			set tmp.recordTypeIDChanged = 1
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
			where isnull(tmp.MCRecordTypeID,0) <> isnull(m.recordTypeID,0);

		update tmp 
		set tmp.statusChanged = 1
		from #mc_PartialMemImport as tmp 
		inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
		where tmp.MCAccountStatus <> m.[status];

		IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = 'NewMemberNumber')
			update tmp 
			set tmp.memberNumberChanged = 1
			from #mc_PartialMemImport as tmp 
			inner join #tblExistingMember as m on m.memberID = tmp.MCMemberID
			where tmp.NewMemberNumber <> m.membernumber COLLATE Latin1_General_CS_AS;

		INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
		select @logID, 'ams_importPartialMemberData_import_m', datediff(ms,@logProcStart,getdate());
	
		/* ******************************* */
		/* determine what websites changed */
		/* ******************************* */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Websites';
		if @colList is not null BEGIN
			set @dynSQL = 'select tmp.memberNumber, pos.areaID, tmp.websiteValue COLLATE Latin1_General_CS_AS
				from (
					select memberNumber, websiteType, websiteValue
					from #mc_PartialMemImport
					unpivot (websiteValue for websiteType in (' + @colList + ')) u
					where mcmemberID is null 
					and websiteValue <> ''''
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.websiteType';
			INSERT INTO #memimport_memberWebsites (membernumber, websiteTypeID, website)
			EXEC(@dynSQL);			
			
			set @dynSQL = 'select tmp.memberNumber, pos.areaID, tmp.websiteValue COLLATE Latin1_General_CS_AS
				from (
					select MCMemberID, memberNumber, websiteType, websiteValue
					from #mc_PartialMemImport
					unpivot (websiteValue for websiteType in (' + @colList + ')) u
					where MCmemberID is not null
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.websiteType
				left outer join dbo.ams_memberWebsites as mw on mw.orgID = ' + cast(@orgID as varchar(10)) + ' and mw.memberID = tmp.MCMemberID and mw.websiteTypeID = pos.areaID
				where tmp.websiteValue <> '''' or mw.website <> ''''
					except
				select tmp.membernumber, mw.websiteTypeID, isnull(mw.website,'''')
				from #mc_PartialMemImport as tmp
				inner join dbo.ams_memberWebsites as mw on mw.orgID = ' + cast(@orgID as varchar(10)) + ' and mw.memberID = tmp.MCMemberID
				where mw.websiteTypeID in (' + @IDList + ')';
			INSERT INTO #memimport_memberWebsites (membernumber, websiteTypeID, website)
			EXEC(@dynSQL);

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mw', datediff(ms,@logProcStart,getdate());
		END

		/* ***************************** */
		/* determine what emails changed */
		/* ***************************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(met.emailType),
			@IDList = COALESCE(@IDList + ',', '') + cast(met.emailTypeID as varchar(10))
			from dbo.ams_memberEmailTypes as met
			inner join #tblActualImportCols as act on act.COLUMN_NAME = met.emailType
			where met.orgID = @orgID;
		if @colList is not null BEGIN
			set @dynSQL = 'select tmp.memberNumber, pos.areaID, tmp.emailValue COLLATE Latin1_General_CS_AS
				from (
					select memberNumber, emailType, emailValue
					from #mc_PartialMemImport
					unpivot (emailValue for emailType in (' + @colList + ')) u
					where mcmemberID is null 
					and emailValue <> ''''
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.emailType';
			INSERT INTO #memimport_memberEmails (membernumber, emailTypeID, email)
			EXEC(@dynSQL);	

			set @dynSQL = 'select tmp.memberNumber, pos.areaID, tmp.emailValue COLLATE Latin1_General_CS_AS
				from (
					select MCMemberID, memberNumber, emailType, emailValue
					from #mc_PartialMemImport
					unpivot (emailValue for emailType in (' + @colList + ')) u
					where MCmemberID is not null
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.emailType
				left outer join dbo.ams_memberEmails as me on me.orgID = ' + cast(@orgID as varchar(10)) + ' and me.memberID = tmp.MCMemberID and me.emailTypeID = pos.areaID
				where tmp.emailValue <> '''' or me.email <> ''''
					except
				select tmp.membernumber, me.emailTypeID, me.email
				from #mc_PartialMemImport as tmp
				inner join dbo.ams_memberEmails as me on me.orgID = ' + cast(@orgID as varchar(10)) + ' and me.memberID = tmp.MCMemberID
				where me.emailTypeID in (' + @IDList + ')';
			INSERT INTO #memimport_memberEmails (membernumber, emailTypeID, email)
			EXEC(@dynSQL);

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_me', datediff(ms,@logProcStart,getdate());
		END

		/* ********************************* */
		/* determine what email tags changed */
		/* ********************************* */
		SET @logProcStart = getdate();
		declare @tblEmailTags TABLE (emailTagTypeID int PRIMARY KEY, emailTagType varchar(60));
		insert into @tblEmailTags (emailTagTypeID, emailTagType)
		select tag.emailTagTypeID, tag.emailTagType
		from dbo.ams_memberEmailTagTypes as tag
		inner join #tblActualImportCols as act on act.COLUMN_NAME = tag.emailTagType + 'EmailType'
		WHERE tag.orgID = @orgID;

		select @emailTagTypeID = min(emailTagTypeID) FROM @tblEmailTags;
		while @emailTagTypeID is not null BEGIN
			set @emailTagType = null;
			select @emailTagType = emailTagType from @tblEmailTags where emailTagTypeID = @emailTagTypeID;

			set @dynSQL = ' select tmp.membernumber, ' + cast(@emailTagTypeID as varchar(10)) + ' as emailTagTypeID, ''' + @emailTagType + ''' COLLATE Latin1_General_CS_AS as emailTagType, met.emailTypeID 
				from #mc_PartialMemImport as tmp 
				inner join dbo.ams_memberEmailTypes as met on met.orgID = ' + cast(@orgID as varchar(10)) + ' and met.emailType = tmp.[' + @emailTagType + 'EmailType]
					except
				select tmp.membernumber, metg.emailTagTypeID, mett.emailTagType, metg.emailTypeID
				from dbo.ams_memberEmailTags as metg
				inner join dbo.ams_memberEmailTagTypes as mett on mett.orgID = ' + cast(@orgID as varchar(10)) + ' and mett.emailTagTypeID = metg.emailTagTypeID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = metg.memberID
				where metg.orgID = ' + cast(@orgID as varchar(10)) + ' 
				and metg.emailTagTypeID = ' + cast(@emailTagTypeID as varchar(10));
			INSERT INTO #memimport_memberEmailTags (memberNumber, emailTagTypeID, emailTagType, emailTypeID)
			EXEC(@dynSQL);
				
			select @emailTagTypeID = min(emailTagTypeID) FROM @tblEmailTags where emailTagTypeID > @emailTagTypeID
		END

		-- email tags for new members for email tags not specified in import file
		select @defaultEmailTypeID = emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeOrder = 1;

		INSERT INTO #memimport_memberEmailTags (memberNumber, emailTagTypeID, emailTagType, emailTypeID)
		select tmp.membernumber, mett.emailTagTypeID, mett.emailTagType, @defaultEmailTypeID
		from #mc_PartialMemImport as tmp
		inner join dbo.ams_memberEmailTagTypes as mett on mett.orgID = @orgID
		where tmp.MCMemberID is null
		and not exists (
			select 1 from #memimport_memberEmailTags where membernumber = tmp.membernumber and emailTagTypeID = mett.emailTagTypeID
		)
			except
		select memberNumber, emailTagTypeID, emailTagType, emailTypeID
		from #memimport_memberEmailTags;

		INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
		select @logID, 'ams_importPartialMemberData_import_met', datediff(ms,@logProcStart,getdate());

		/* *********************************** */
		/* determine what address tags changed */
		/* *********************************** */
		SET @logProcStart = getdate();
		declare @tblAddressTags TABLE (addressTagTypeID int PRIMARY KEY, addressTagType varchar(60));
		insert into @tblAddressTags (addressTagTypeID, addressTagType)
		select tag.addressTagTypeID, tag.addressTagType
		from dbo.ams_memberAddressTagTypes as tag
		inner join #tblActualImportCols as act on act.COLUMN_NAME = tag.addressTagType + 'AddressType'
		WHERE tag.orgID = @orgID;

		select @addressTagTypeID = min(addressTagTypeID) FROM @tblAddressTags;
		while @addressTagTypeID is not null BEGIN
			set @addressTagType = null;
			select @addressTagType = addressTagType from @tblAddressTags where addressTagTypeID = @addressTagTypeID;

			set @dynSQL = 'select tmp.membernumber, ' + cast(@addressTagTypeID as varchar(10)) + ' as addressTagTypeID, ''' + @addressTagType + ''' COLLATE Latin1_General_CS_AS as addressTagType, mat.addressTypeID 
				from #mc_PartialMemImport as tmp 
				inner join dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.[' + @addressTagType + 'AddressType]
					except
				select tmp.membernumber, matg.addressTagTypeID, matt.addressTagType, matg.addressTypeID
				from #mc_PartialMemImport as tmp
				inner join dbo.ams_memberAddressTags as matg on matg.orgID = ' + cast(@orgID as varchar(10)) + ' 
					and matg.memberID = tmp.MCMemberID
					and matg.addressTagTypeID = ' + cast(@addressTagTypeID as varchar(10)) + ' 
				inner join dbo.ams_memberAddressTagTypes as matt on matt.orgID = ' + cast(@orgID as varchar(10)) + ' 
					and matt.addressTagTypeID = matg.addressTagTypeID';
			INSERT INTO #memimport_memberAddressTags (memberNumber, addressTagTypeID, addressTagType, addressTypeID)
			EXEC(@dynSQL);
				
			select @addressTagTypeID = min(addressTagTypeID) FROM @tblAddressTags where addressTagTypeID > @addressTagTypeID
		END

		-- address tags for new members for address tags not specified in import file
		select @defaultAddressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1;

		INSERT INTO #memimport_memberAddressTags (memberNumber, addressTagTypeID, addressTagType, addressTypeID)
		select tmp.membernumber, matt.addressTagTypeID, matt.addressTagType, @defaultAddressTypeID
		from #mc_PartialMemImport as tmp
		inner join dbo.ams_memberAddressTagTypes as matt on matt.orgID = @orgID
		where tmp.MCMemberID is null
		and not exists (
			select 1 from #memimport_memberAddressTags where membernumber = tmp.membernumber and addressTagTypeID = matt.addressTagTypeID
		)
			except
		select memberNumber, addressTagTypeID, addressTagType, addressTypeID
		from #memimport_memberAddressTags;

		INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
		select @logID, 'ams_importPartialMemberData_import_mat', datediff(ms,@logProcStart,getdate());

		/* *************************************** */
		/* determine what addresses/phones changed */
		/* *************************************** */
		SET @logProcStart = getdate();
		IF EXISTS (
			select act.COLUMN_NAME 
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Addresses')
		BEGIN
			select @addressTypeID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID;
			while @addressTypeID is not null BEGIN
				select @addressType=null, @hasAttn=null, @hasAddress2=null, @hasAddress3=null, @hasCounty=null;

				select @addressType=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, @hasCounty=hasCounty
				from dbo.ams_memberAddressTypes 
				where addressTypeID = @addressTypeID;

				if EXISTS (
					select COLUMN_NAME
					from #tblActualImportCols 
					where COLUMN_NAME in (@addressType + '_attn', @addressType + '_address1', @addressType + '_address2', @addressType + '_address3', 
						@addressType + '_city', @addressType + '_stateprov', @addressType + '_postalcode', @addressType + '_country', @addressType + '_county')
				) BEGIN
					set @dynSQL = 'select tmp.membernumber, ' + cast(@addressTypeID as varchar(10)) + ' as addressTypeID';
					if @hasAttn = 1 and exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_attn')
						set @dynSQL = @dynSQL + ', isnull(tmp.[' + @addressType + '_attn],'''') COLLATE Latin1_General_CS_AS as attn';
					else 
						set @dynSQL = @dynSQL + ', '''' as attn';
					if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_address1')
						set @dynSQL = @dynSQL + ', isnull(tmp.[' + @addressType + '_address1],'''') COLLATE Latin1_General_CS_AS as address1';
					else
						set @dynSQL = @dynSQL + ', '''' as address1';
					if @hasAddress2 = 1 and exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_address2')
						set @dynSQL = @dynSQL + ', isnull(tmp.[' + @addressType + '_address2],'''') COLLATE Latin1_General_CS_AS as address2';
					else 
						set @dynSQL = @dynSQL + ', '''' as address2';
					if @hasAddress3 = 1 and exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_address3')
						set @dynSQL = @dynSQL + ', isnull(tmp.[' + @addressType + '_address3],'''') COLLATE Latin1_General_CS_AS as address3';
					else 
						set @dynSQL = @dynSQL + ', '''' as address3';
					if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_city')
						set @dynSQL = @dynSQL + ', isnull(tmp.[' + @addressType + '_city],'''') COLLATE Latin1_General_CS_AS as city';
					else
						set @dynSQL = @dynSQL + ', '''' as city';
					if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_stateprov')
						set @dynSQL = @dynSQL + ', s.stateID';
					else 
						set @dynSQL = @dynSQL + ', null as stateID';
					if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_postalcode')
						set @dynSQL = @dynSQL + ', isnull(tmp.[' + @addressType + '_postalcode],'''') COLLATE Latin1_General_CS_AS as postalCode';
					else
						set @dynSQL = @dynSQL + ', '''' as postalCode';
					if @hasCounty = 1 and exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_county')
						set @dynSQL = @dynSQL + ', isnull(tmp.[' + @addressType + '_county],'''') COLLATE Latin1_General_CS_AS as county';
					else 
						set @dynSQL = @dynSQL + ', '''' as county';
					if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_country')
						set @dynSQL = @dynSQL + ', c.countryID';
					else 
						set @dynSQL = @dynSQL + ', null as countryID';
					set @dynSQL = @dynSQL + ' from #mc_PartialMemImport as tmp';
					if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_country')
						set @dynSQL = @dynSQL + ' left outer join dbo.ams_countries as c on c.country = tmp.[' + @addressType + '_country]';

					if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_stateprov')
						and exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_country')
						set @dynSQL = @dynSQL + ' left outer join dbo.ams_states as s on s.code = tmp.[' + @addressType + '_stateprov] and s.countryID = c.countryID';

					else if exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_stateprov')
						set @dynSQL = @dynSQL + ' left outer join dbo.ams_states as s 
													inner join dbo.ams_memberAddresses as ma on ma.orgID = ' + cast(@orgID as varchar(10)) + '
														and ma.countryID = s.countryID
														and ma.addressTypeID = ' + cast(@addressTypeID as varchar(10)) + '
													inner join dbo.ams_countries as c on c.countryID = s.countryID
													on s.code = tmp.[' + @addressType + '_stateprov] and ma.memberID = tmp.MCMemberID';
					set @dynSQL = @dynSQL + ' 
							except
						select tmp.membernumber, ma.addressTypeID, isnull(ma.attn,''''), isnull(ma.address1,''''), isnull(ma.address2,''''), 
							isnull(ma.address3,''''), isnull(ma.city,''''), ma.stateID, isnull(ma.postalCode,''''), isnull(ma.county,''''), 
							ma.countryID
						from dbo.ams_memberAddresses as ma
						inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = ma.memberID
						where ma.orgID = ' + cast(@orgID as varchar(10)) + '
						and ma.addressTypeID = ' + cast(@addressTypeID as varchar(10));
 					insert into #memimport_memberAddresses (memberNumber, addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, county, countryID)
					exec(@dynSQL);

					-- missing address info updated for existing members
					update tmpAddress
					set tmpAddress.attn = case when @hasAttn = 1 and not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_attn') then isnull(ma.attn,'') else isnull(tmpAddress.attn,'') end,
						tmpAddress.address1 = case when not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_address1') then isnull(ma.address1,'') else isnull(tmpAddress.address1,'') end,
						tmpAddress.address2 = case when @hasAddress2 = 1 and not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_address2') then isnull(ma.address2,'') else isnull(tmpAddress.address2,'') end,
						tmpAddress.address3 = case when @hasAddress3 = 1 and not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_address3') then isnull(ma.address3,'') else isnull(tmpAddress.address3,'') end,
						tmpAddress.city = case when not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_city') then isnull(ma.city,'') else isnull(tmpAddress.city,'') end,
						tmpAddress.stateID = case when not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_stateprov') then ma.stateID else tmpAddress.stateID end,
						tmpAddress.postalCode = case when not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_postalCode') then isnull(ma.postalCode,'') else isnull(tmpAddress.postalCode,'') end,
						tmpAddress.county = case when @hasCounty = 1 and not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_county') then isnull(ma.county,'') else isnull(tmpAddress.county,'') end,
						tmpAddress.countryID = case when not exists (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_country') then ma.countryID else tmpAddress.countryID end
					from #memimport_memberAddresses as tmpAddress
					inner join #mc_PartialMemImport as tmp on tmp.memberNumber = tmpAddress.memberNumber
					inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
						and ma.memberID = tmp.MCMemberID
					where tmpAddress.addressTypeID = @addressTypeID
					and ma.addressTypeID = @addressTypeID;

					-- if we now have stateID but no countryID, then set the countryID anyway.
					update tmpAddress
					set tmpAddress.countryID = s.countryID
					from #memimport_memberAddresses as tmpAddress
					inner join dbo.ams_states as s on s.stateID = tmpAddress.stateID
					where tmpAddress.stateID is not null 
					and tmpAddress.countryID is null;

					-- determine what member address data changed for new members and existing members
					update tmpAddress
					set tmpAddress.attnChanged = case when @hasAttn = 1 and isnull(tmpAddress.attn,'') <> '' then 1 else 0 end,
						tmpAddress.address1Changed = case when isnull(tmpAddress.address1,'') <> '' then 1 else 0 end,
						tmpAddress.address2Changed = case when @hasAddress2 = 1 and isnull(tmpAddress.address2,'') <> '' then 1 else 0 end,
						tmpAddress.address3Changed = case when @hasAddress3 = 1 and isnull(tmpAddress.address3,'') <> '' then 1 else 0 end,
						tmpAddress.cityChanged = case when isnull(tmpAddress.city,'') <> '' then 1 else 0 end,
						tmpAddress.stateIDChanged = case when isnull(tmpAddress.stateID,0) <> 0 then 1 else 0 end,
						tmpAddress.postalCodeChanged = case when isnull(tmpAddress.postalCode,'') <> '' then 1 else 0 end,
						tmpAddress.countyChanged = case when isnull(tmpAddress.county,'') <> '' then 1 else 0 end,
						tmpAddress.countryIDChanged = case when isnull(tmpAddress.countryID,0) <> 0 then 1 else 0 end
					from #memimport_memberAddresses as tmpAddress
					inner join #mc_PartialMemImport as tmp on tmp.memberNumber = tmpAddress.memberNumber
					where tmp.MCMemberID is null
					and tmpAddress.addressTypeID = @addressTypeID;

					update tmpAddress
					set tmpAddress.attnChanged = case when isnull(tmpAddress.attn,'') COLLATE Latin1_General_CS_AS = isnull(ma.attn,'') then 0 else 1 end,
						tmpAddress.address1Changed = case when isnull(tmpAddress.address1,'') COLLATE Latin1_General_CS_AS = isnull(ma.address1,'') then 0 else 1 end,
						tmpAddress.address2Changed = case when isnull(tmpAddress.address2,'') COLLATE Latin1_General_CS_AS = isnull(ma.address2,'') then 0 else 1 end,
						tmpAddress.address3Changed = case when isnull(tmpAddress.address3,'') COLLATE Latin1_General_CS_AS = isnull(ma.address3,'') then 0 else 1 end,
						tmpAddress.cityChanged = case when isnull(tmpAddress.city,'') COLLATE Latin1_General_CS_AS = isnull(ma.city,'') then 0 else 1 end,
						tmpAddress.stateIDChanged = case when isnull(tmpAddress.stateID,0) = isnull(ma.stateID,0) then 0 else 1 end,
						tmpAddress.postalCodeChanged = case when isnull(tmpAddress.postalCode,'') COLLATE Latin1_General_CS_AS = isnull(ma.postalCode,'') then 0 else 1 end,
						tmpAddress.countyChanged = case when isnull(tmpAddress.county,'') COLLATE Latin1_General_CS_AS = isnull(ma.county,'') then 0 else 1 end,
						tmpAddress.countryIDChanged = case when isnull(tmpAddress.countryID,0) = isnull(ma.countryID,0) then 0 else 1 end
					from #memimport_memberAddresses as tmpAddress
					inner join #mc_PartialMemImport as tmp on tmp.memberNumber = tmpAddress.memberNumber
					left outer join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
						and ma.memberID = tmp.MCMemberID
						and ma.addressTypeID = @addressTypeID
					where tmpAddress.addressTypeID = @addressTypeID;
				END

				select @phoneTypeID = null;
				select @phoneTypeID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID;
				while @phoneTypeID is not null BEGIN
					set @phoneType = null;
					select @phoneType = phoneType from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID = @phoneTypeID;

					IF EXISTS (select COLUMN_NAME from #tblActualImportCols where COLUMN_NAME = @addressType + '_' + @phoneType) BEGIN
						set @dynSQL = 'select tmp.membernumber, ' + cast(@phoneTypeID as varchar(10)) + ' as phoneTypeID, ' + cast(@addressTypeID as varchar(10)) + ' as addressTypeID, isnull(tmp.[' + @addressType + '_' + @phoneType + '],'''') COLLATE Latin1_General_CS_AS as phone 
							from #mc_PartialMemImport as tmp 
							left outer join dbo.ams_memberAddresses as ma 
								inner join dbo.ams_memberPhones as mp on mp.orgID = ' + cast(@orgID as varchar(10)) + '
									and mp.memberID = ma.memberID
									and mp.phoneTypeID = ' + cast(@phoneTypeID as varchar(10)) + ' 
									and mp.addressID = ma.addressID
								on ma.orgID = ' + cast(@orgID as varchar(10)) + '
								and ma.memberID = tmp.MCMemberID
								and ma.addressTypeID = ' + cast(@addressTypeID as varchar(10)) + ' 
							where isnull(tmp.[' + @addressType + '_' + @phoneType + '],'''')  <> '''' or isnull(mp.phone,'''') <> '''' 
								except
							select tmp.membernumber, mp.phoneTypeID, ma.addressTypeID, isnull(mp.phone,'''')
							from dbo.ams_memberPhones as mp
							inner join dbo.ams_memberAddresses as ma on ma.orgID = ' + cast(@orgID as varchar(10)) + '
								and ma.addressID = mp.addressID
							inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = ma.memberID
							where mp.orgID = ' + cast(@orgID as varchar(10)) + '
							and mp.memberID = ma.memberID
							and mp.phoneTypeID = ' + cast(@phoneTypeID as varchar(10));
						insert into #memimport_memberPhones (membernumber, phoneTypeID, addressTypeID, phone)
						exec(@dynSQL);
					END

					select @phoneTypeID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID > @phoneTypeID;
				END

				select @addressTypeID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeID > @addressTypeID;
			END

			delete ma
			from #memimport_memberAddresses as ma
			where ma.attnChanged = 0
			and ma.address1Changed = 0
			and ma.address2Changed = 0
			and ma.address3Changed = 0
			and ma.cityChanged = 0
			and ma.stateIDChanged = 0
			and ma.postalCodeChanged = 0
			and ma.countyChanged = 0
			and ma.countryIDChanged = 0;

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_ma', datediff(ms,@logProcStart,getdate());
		END

		/* ******************************************** */
		/* determine what professional licenses changed */
		/* ******************************************** */
		SET @logProcStart = getdate();
		IF EXISTS (
			select act.COLUMN_NAME 
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Professional Licenses') 
		BEGIN
			declare @tblProLic TABLE (PLTypeID int PRIMARY KEY, PLName varchar(114));
			insert into @tblProLic (PLTypeID, PLName)
			select distinct mpl.PLTypeID, mpl.PLName
			from dbo.ams_memberProfessionalLicenseTypes as mpl
			inner join #tblActualImportCols as act on act.COLUMN_NAME in (mpl.PLName + '_licenseNumber',mpl.PLName + '_status',mpl.PLName + '_activeDate')
			WHERE mpl.orgID = @orgID;	
		
			select @PLTypeID = min(PLTypeID) from @tblProLic;
			while @PLTypeID is not null BEGIN
				set @plType = null;
				select @plType = PLName from @tblProLic where PLTypeID = @PLTypeID;
				
				set @dynSQL = ' select tmp.membernumber, ' + cast(@PLTypeID as varchar(10)) + ' as PLTypeID, tmp.[' + @plType + '_licenseNumber] COLLATE Latin1_General_CS_AS as LicenseNumber, 
						tmp.[' + @plType + '_activeDate] as ActiveDate, pls.PLStatusID 
					from #mc_PartialMemImport as tmp
					left outer join dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = tmp.[' + @PLType + '_status] and pls.orgID = ' + cast(@orgid as varchar(10)) + ' 
					left outer join dbo.ams_memberProfessionalLicenses as mpl on mpl.memberID = tmp.MCMemberID and mpl.PLTypeID = ' + cast(@PLTypeID as varchar(10)) + '
					where mpl.PLTypeID is not null or pls.PLStatusID is not null
						except
					select tmp.membernumber, mpl.PLTypeID, mpl.LicenseNumber, mpl.ActiveDate, mpl.PLstatusID
					from dbo.ams_memberProfessionalLicenses as mpl
					inner join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLstatusID = mpl.PLstatusID
					inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = mpl.memberID
					where mpl.PLTypeID = ' + cast(@PLTypeID as varchar(10));
				insert into #memimport_memberProfessionalLicenses (membernumber, PLTypeID, LicenseNumber, ActiveDate, PLStatusID)
				exec(@dynSQL);

				select @PLTypeID = min(PLTypeID) from @tblProLic where PLTypeID > @PLTypeID;
			END

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mpl', datediff(ms,@logProcStart,getdate());
		END

		/* ***************************************************************** */
		/* string columns supporting select, radio and checkbox displaytypes */
		/* ***************************************************************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(pos.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'STRING'
			and pos.MDdisplayTypeCode in ('SELECT','RADIO','CHECKBOX')
			and pos.allowMultiple = 0;
		if @colList is not null BEGIN
			-- unpivot was not returning columns with null values
			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.MCMemberID, tmp.memberNumber, pos.areaID, nullif(tmp.columnValueString,'''') as columnValueString
				from (
					select MCMemberID, memberNumber, columnName, columnValueString
					from #mc_PartialMemImport
					unpivot (columnValueString for columnName in (' + @colList + ')) u
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName;';
			insert into #tmpPMI_str (MCmemberID, membernumber, areaID, columnValueString)
			EXEC(@dynSQL);

			set @dynSQL = 'select tmp.memberNumber, tmp.areaID, tmp.columnValueString
				from #tmpPMI_str as tmp
				where tmp.columnValueString <> ''''
					union
				select tmp.memberNumber, tmp.areaID, tmp.columnValueString
				from #tmpPMI_str as tmp
				inner join dbo.ams_memberData as md on md.memberID = tmp.MCmemberID 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = tmp.areaID
				where mdcv.columnValueString <> ''''
					except
				select tmp.membernumber, mdcv.columnID, isnull(mdcv.columnValueString,'''')
				from dbo.ams_memberDataColumnValues as mdcv
				inner join dbo.ams_memberData as md on mdcv.valueID = md.valueID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID
				where mdcv.columnID in (' + @IDList + ');';
			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueString)
			EXEC(@dynSQL);

			TRUNCATE TABLE #tmpPMI_str;

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mdstrsrc', datediff(ms,@logProcStart,getdate());
		END

		/* **************************************************************************************** */
		/* string columns except select, radio and checkbox displaytypes - these are case sensitive */
		/* **************************************************************************************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(pos.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'STRING'
			and pos.MDdisplayTypeCode not in ('SELECT','RADIO','CHECKBOX')
			and pos.allowMultiple = 0;
		if @colList is not null BEGIN
			-- unpivot was not returning columns with null values
			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.MCMemberID, tmp.memberNumber, pos.areaID, nullif(tmp.columnValueString,'''') as columnValueString
				from (
					select MCMemberID, memberNumber, columnName, columnValueString
					from #mc_PartialMemImport
					unpivot (columnValueString for columnName in (' + @colList + ')) u
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName;';
			insert into #tmpPMI_str (MCmemberID, membernumber, areaID, columnValueString)
			EXEC(@dynSQL);

			set @dynSQL = 'select tmp.memberNumber, tmp.areaID, tmp.columnValueString COLLATE Latin1_General_CS_AS
				from #tmpPMI_str as tmp
				where tmp.columnValueString <> ''''
					union
				select tmp.memberNumber, tmp.areaID, tmp.columnValueString COLLATE Latin1_General_CS_AS
				from #tmpPMI_str as tmp
				inner join dbo.ams_memberData as md on md.memberID = tmp.MCmemberID 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = tmp.areaID
				where mdcv.columnValueString <> ''''
					except
				select tmp.membernumber, mdcv.columnID, isnull(mdcv.columnValueString,'''')
				from dbo.ams_memberDataColumnValues as mdcv
				inner join dbo.ams_memberData as md on mdcv.valueID = md.valueID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID
				where mdcv.columnID in (' + @IDList + ');';
			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueString)
			EXEC(@dynSQL);

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mdstrt', datediff(ms,@logProcStart,getdate());
		END

		/* ********************************************* */
		/* string columns which supports multiple values */
		/* ********************************************* */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'STRING'
			and pos.allowMultiple = 1;
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			INSERT INTO #tmpIDs (listItem)
			select distinct listItem
			from dbo.fn_intListToTableInline(@IDList,',');

			-- db values of existing members for multi-value string fields
			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			select tmp.MCMemberID, tmp.memberNumber, mdc.columnName, mdcv.columnValueString as columnMultiValue
			from #tmpIDs as li
			inner join dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and li.listItem = mdc.columnID
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
			inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID;

			TRUNCATE TABLE #tmpIDs;

			insert into #tmpInnerMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			select tmpMem.MCMemberID, tmpMem.memberNumber, tmpMem.columnName, 
					STUFF(( SELECT '|' + columnMultiValue
							FROM #tmpInnerHoldMULTI
							WHERE MCMemberID = tmpMem.MCMemberID
							and columnName = tmpMem.columnName
							ORDER BY columnMultiValue
							FOR XML PATH(''), type
						).value('.', 'varchar(max)'), 1, 1, '' ) AS columnValueString
			FROM #tmpInnerHoldMULTI as tmpMem
			group by tmpMem.MCMemberID, tmpMem.memberNumber, tmpMem.columnName;
			
			truncate table #tmpInnerHoldMULTI;

			-- new values of members (existing or new) in file
			set @dynSQL = 'select tmp.MCMemberID, tmp.memberNumber, tmp.columnName, dbo.fn_RegExReplace(columnValueString,''[^\x20-\x7E]'','''') as columnValueString
				from (
					select MCMemberID, memberNumber, columnName, columnValueString
					from #mc_PartialMemImport
					unpivot (columnValueString for columnName in (' + @colList + ')) u
				) as tmp
				where tmp.columnValueString <> ''''
				or exists (select top 1 MCMemberID from #tmpInnerMULTI where MCMemberID = tmp.MCMemberID and columnName = tmp.columnName)';
			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			exec(@dynSQL);
			
			insert into #tmpInnerMULTIUnpvt (MCMemberID, membernumber, columnName, columnValue)
			select MCMemberID, memberNumber, columnName, isnull(tbl.listItem,'')
			from #tmpInnerHoldMULTI
			outer apply dbo.fn_varcharListToTableInline(columnMultiValue,'|') as tbl;

			truncate table #tmpInnerHoldMULTI;

			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			SELECT MCMemberID, memberNumber, columnName, 
				STUFF(( SELECT '|' + columnValue
						FROM #tmpInnerMULTIUnpvt
						WHERE memberNumber = tmp.memberNumber
						and columnName = tmp.columnName
						ORDER BY columnValue
						FOR XML PATH(''), type
					).value('.', 'varchar(max)'), 1, 1, '' ) AS columnMultiValue
			FROM #tmpInnerMULTIUnpvt as tmp
			group by tmp.MCMemberID, tmp.memberNumber, tmp.columnName;

			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueString)
			select tmp.memberNumber, pos.areaID, nullif(tbl.listitem,'') as columnValueString
			from (
				select memberNumber, columnName, columnMultiValue from #tmpInnerHoldMULTI 
					except
				select memberNumber, columnName, columnMultiValue from #tmpInnerMULTI
			) tmp
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName		
			outer apply dbo.fn_varcharListToTableInline(tmp.columnMultiValue,'|') as tbl;

			truncate table #tmpInnerMULTI;
			truncate table #tmpInnerHoldMULTI;
			truncate table #tmpInnerMULTIUnpvt;

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mdstrmulti', datediff(ms,@logProcStart,getdate());
		END

		/* *************** */
		/* decimal columns */
		/* *************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'DECIMAL2'
			and pos.allowMultiple = 0;
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_PartialMemImport ALTER COLUMN ' + tbl.listitem + ' varchar(20) null'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.MCMemberID, tmp.memberNumber, pos.areaID, cast(nullif(tmp.columnValueDecimal2,'''') as decimal(14,2))
				from (
					select MCMemberID, memberNumber, columnName, columnValueDecimal2
					from #mc_PartialMemImport
					unpivot (columnValueDecimal2 for columnName in (' + @colList + ')) u
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName;';
			insert into #tmpPMI_dec (MCmemberID, membernumber, areaID, columnValueDecimal2)
			EXEC(@dynSQL);

			set @dynSQL = 'select tmp.memberNumber, tmp.areaID, tmp.columnValueDecimal2 
				from #tmpPMI_dec as tmp
				where tmp.columnValueDecimal2 is not null
					union
				select tmp.memberNumber, tmp.areaID, tmp.columnValueDecimal2 
				from #tmpPMI_dec as tmp
				inner join dbo.ams_memberData as md on md.memberID = tmp.MCmemberID 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = tmp.areaID
				where mdcv.columnValueDecimal2 is not null
					except
				select tmp.membernumber, mdcv.columnID, mdcv.columnValueDecimal2
				from dbo.ams_memberDataColumnValues as mdcv
				inner join dbo.ams_memberData as md on mdcv.valueID = md.valueID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID
				where mdcv.columnID in (' + @IDList + ');';
			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueDecimal2)
			EXEC(@dynSQL);

			TRUNCATE TABLE #tmpPMI_dec;

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mddec', datediff(ms,@logProcStart,getdate());
		END

		/* ********************************************** */
		/* decimal columns which supports multiple values */
		/* ********************************************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'DECIMAL2'
			and pos.allowMultiple = 1;
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			INSERT INTO #tmpIDs (listItem)
			select distinct listItem
			from dbo.fn_intListToTableInline(@IDList,',');

			-- db values of existing members for multi-value decimal fields
			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			select tmp.MCMemberID, tmp.memberNumber, mdc.columnName, mdcv.columnValueDecimal2 as columnMultiValue
			from dbo.ams_memberData as md
			inner join dbo.ams_memberDataColumnValues as mdcv on md.valueID = mdcv.valueID
			inner join dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdcv.columnID = mdc.columnID
			inner join #tmpIDs as li on li.listItem = mdc.columnID
			inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID;

			TRUNCATE TABLE #tmpIDs;

			insert into #tmpInnerMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			select tmpMem.MCMemberID, tmpMem.memberNumber, tmpMem.columnName, 
					STUFF(( SELECT '|' + columnMultiValue
							FROM #tmpInnerHoldMULTI
							WHERE MCMemberID = tmpMem.MCMemberID
							and columnName = tmpMem.columnName
							ORDER BY columnMultiValue
							FOR XML PATH(''), type
						).value('.', 'varchar(max)'), 1, 1, '' ) AS columnMultiValue
			FROM #tmpInnerHoldMULTI as tmpMem
			group by tmpMem.MCMemberID, tmpMem.memberNumber, tmpMem.columnName;

			truncate table #tmpInnerHoldMULTI;

			-- new values of members (existing or new) in file
			set @dynSQL = 'select tmp.MCMemberID, tmp.memberNumber, tmp.columnName, tmp.columnValueDecimal2
				from (
					select MCMemberID, memberNumber, columnName, columnValueDecimal2
					from #mc_PartialMemImport
					unpivot (columnValueDecimal2 for columnName in (' + @colList + ')) u
				) as tmp
				where tmp.columnValueDecimal2 <> ''''
				or exists (select top 1 MCMemberID from #tmpInnerMULTI where MCMemberID = tmp.MCMemberID and columnName = tmp.columnName)';
			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			exec(@dynSQL);
			
			insert into #tmpInnerMULTIUnpvt (MCMemberID, membernumber, columnName, columnValue)
			select MCMemberID, memberNumber, columnName, isnull(cast(tbl.listItem as varchar(16)),'')
			from #tmpInnerHoldMULTI
			outer apply dbo.fn_decimal2ListToTableInline(columnMultiValue,'|') as tbl;

			truncate table #tmpInnerHoldMULTI;

			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			SELECT MCMemberID, memberNumber, columnName, 
				STUFF(( SELECT '|' + columnValue
						FROM #tmpInnerMULTIUnpvt
						WHERE memberNumber = tmp.memberNumber
						and columnName = tmp.columnName
						ORDER BY columnValue
						FOR XML PATH(''), type
					).value('.', 'varchar(max)'), 1, 1, '' ) AS columnMultiValue
			FROM #tmpInnerMULTIUnpvt as tmp
			group by tmp.MCMemberID, tmp.memberNumber, tmp.columnName;

			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueDecimal2)
			select tmp.memberNumber, pos.areaID, tbl.listitem
			from (
				select memberNumber, columnName, columnMultiValue from #tmpInnerHoldMULTI
					except
				select memberNumber, columnName, columnMultiValue from #tmpInnerMULTI
			) tmp
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName		
			outer apply dbo.fn_decimal2ListToTableInline(tmp.columnMultiValue,'|') as tbl;

			truncate table #tmpInnerMULTI;
			truncate table #tmpInnerHoldMULTI;
			truncate table #tmpInnerMULTIUnpvt;

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mddecmulti', datediff(ms,@logProcStart,getdate());
		END

		/* *************** */
		/* integer columns */
		/* *************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'INTEGER'
			and pos.allowMultiple = 0;
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_PartialMemImport ALTER COLUMN ' + tbl.listitem + ' varchar(20) null'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.memberNumber, pos.areaID, cast(nullif(tmp.columnValueInteger,'''') as int) as columnValueInteger
				from (
					select MCMemberID, memberNumber, columnName, columnValueInteger
					from #mc_PartialMemImport
					unpivot (columnValueInteger for columnName in (' + @colList + ')) u
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName
				left outer join dbo.ams_memberData as md
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					on md.memberID = tmp.MCmemberID and mdcv.columnID = pos.areaID
				where tmp.columnValueInteger <> '''' or isnull(mdcv.columnValueInteger,'''') <> ''''
					except
				select tmp.membernumber, mdcv.columnID, mdcv.columnValueInteger
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID
				where mdcv.columnID in (' + @IDList + ')';
			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueInteger)
			EXEC(@dynSQL);

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mdint', datediff(ms,@logProcStart,getdate());
		END

		/* ********************************************** */
		/* integer columns which supports multiple values */
		/* ********************************************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'INTEGER'
			and pos.allowMultiple = 1;
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			INSERT INTO #tmpIDs (listItem)
			select distinct listItem
			from dbo.fn_intListToTableInline(@IDList,',');

			-- db values of existing members for multi-value integer fields
			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			select tmp.MCMemberID, tmp.memberNumber, mdc.columnName, mdcv.columnValueInteger as columnMultiValue
			from dbo.ams_memberData as md
			inner join dbo.ams_memberDataColumnValues as mdcv on md.valueID = mdcv.valueID
			inner join dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdcv.columnID = mdc.columnID
			inner join #tmpIDs as li on li.listItem = mdc.columnID
			inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID;

			TRUNCATE TABLE #tmpIDs;

			insert into #tmpInnerMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			select tmpMem.MCMemberID, tmpMem.memberNumber, tmpMem.columnName, 
					STUFF(( SELECT '|' + columnMultiValue
							FROM #tmpInnerHoldMULTI
							WHERE MCMemberID = tmpMem.MCMemberID
							and columnName = tmpMem.columnName
							ORDER BY columnMultiValue
							FOR XML PATH(''), type
						).value('.', 'varchar(max)'), 1, 1, '' ) AS columnMultiValue
			FROM #tmpInnerHoldMULTI as tmpMem
			group by tmpMem.MCMemberID, tmpMem.memberNumber, tmpMem.columnName;

			truncate table #tmpInnerHoldMULTI;

			-- new values of members (existing or new) in file
			set @dynSQL = 'select tmp.MCMemberID, tmp.memberNumber, tmp.columnName, tmp.columnValueInteger
				from (
					select MCMemberID, memberNumber, columnName, columnValueInteger
					from #mc_PartialMemImport
					unpivot (columnValueInteger for columnName in (' + @colList + ')) u
				) as tmp
				where tmp.columnValueInteger <> ''''
				or exists (select top 1 MCMemberID from #tmpInnerMULTI where MCMemberID = tmp.MCMemberID and columnName = tmp.columnName)';
			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			exec(@dynSQL);
			
			insert into #tmpInnerMULTIUnpvt (MCMemberID, membernumber, columnName, columnValue)
			select MCMemberID, memberNumber, columnName, isnull(cast(tbl.listItem as varchar(10)),'')
			from #tmpInnerHoldMULTI
			outer apply dbo.fn_intListToTableInline(columnMultiValue,'|') as tbl;

			truncate table #tmpInnerHoldMULTI;

			insert into #tmpInnerHoldMULTI (MCMemberID, membernumber, columnName, columnMultiValue)
			SELECT MCMemberID, memberNumber, columnName, 
				STUFF(( SELECT '|' + columnValue
						FROM #tmpInnerMULTIUnpvt
						WHERE memberNumber = tmp.memberNumber
						and columnName = tmp.columnName
						ORDER BY columnValue
						FOR XML PATH(''), type
					).value('.', 'varchar(max)'), 1, 1, '' ) AS columnMultiValue
			FROM #tmpInnerMULTIUnpvt as tmp
			group by tmp.MCMemberID, tmp.memberNumber, tmp.columnName;

			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueInteger)
			select tmp.memberNumber, pos.areaID, tbl.listitem
			from (
				select memberNumber, columnName, columnMultiValue from #tmpInnerHoldMULTI
					except
				select memberNumber, columnName, columnMultiValue from #tmpInnerMULTI
			) tmp
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName		
			outer apply dbo.fn_intListToTableInline(tmp.columnMultiValue,'|') as tbl;

			truncate table #tmpInnerMULTI;
			truncate table #tmpInnerHoldMULTI;
			truncate table #tmpInnerMULTIUnpvt;

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mdintmulti', datediff(ms,@logProcStart,getdate());
		END

		/* ************ */
		/* date columns */
		/* ************ */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'DATE';
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_PartialMemImport ALTER COLUMN ' + tbl.listitem + ' varchar(30) null'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.memberNumber, pos.areaID, cast(nullif(tmp.columnValueDate,'''') as date) as columnValueDate
				from (
					select MCMemberID, memberNumber, columnName, columnValueDate
					from #mc_PartialMemImport
					unpivot (columnValueDate for columnName in (' + @colList + ')) u
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName
				left outer join dbo.ams_memberData as md
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					on md.memberID = tmp.MCmemberID and mdcv.columnID = pos.areaID
				where tmp.columnValueDate <> '''' or isnull(mdcv.columnValueDate,'''') <> ''''
					except
				select tmp.membernumber, mdcv.columnID, mdcv.columnValueDate
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID
				where mdcv.columnID in (' + @IDList + ')';
			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueDate)
			EXEC(@dynSQL);

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mddate', datediff(ms,@logProcStart,getdate());
		END

		/* *********** */
		/* bit columns */
		/* *********** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'BIT';
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_PartialMemImport ALTER COLUMN ' + tbl.listitem + ' varchar(1) null'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.memberNumber, pos.areaID, cast(nullif(tmp.columnValueBit,'''') as bit) as columnValueBit
				from (
					select MCMemberID, memberNumber, columnName, columnValueBit
					from #mc_PartialMemImport
					unpivot (columnValueBit for columnName in (' + @colList + ')) u
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName
				left outer join dbo.ams_memberData as md
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					on md.memberID = tmp.MCmemberID and mdcv.columnID = pos.areaID
				where tmp.columnValueBit <> isnull(cast(mdcv.columnValueBit as varchar(1)),'''')
					except
				select tmp.membernumber, mdcv.columnID, mdcv.columnValueBit
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID
				where mdcv.columnID in (' + @IDList + ')';
			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueBit)
			EXEC(@dynSQL);

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mdbit', datediff(ms,@logProcStart,getdate());
		END

		/* ****************** */
		/* contentobj columns */
		/* ****************** */
		SET @logProcStart = getdate();
		select @colList = null, @IDList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(act.COLUMN_NAME),
			@IDList = COALESCE(@IDList + ',', '') + cast(pos.areaID as varchar(10))
			from #tblActualImportCols as act
			inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = act.COLUMN_NAME
			where pos.area = 'Custom Fields'
			and pos.MDdataTypeCode = 'CONTENTOBJ';
		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_PartialMemImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTableInline(@colList,',') as tbl;

			set @dynSQL = 'update #mc_PartialMemImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.memberNumber, pos.areaID, nullif(tmp.columnValueContent,'''') as columnValueContent
				from (
					select MCMemberID, memberNumber, columnName, columnValueContent
					from #mc_PartialMemImport
					unpivot (columnValueContent for columnName in (' + @colList + ')) u
				) as tmp
				inner join #tblPossibleImportCols as pos on pos.COLUMN_NAME = tmp.columnName
				left outer join dbo.ams_memberData as md
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					on md.memberID = tmp.MCMemberID and mdcv.columnID = pos.areaID
				where tmp.columnValueContent <> '''' 
				or (select isnull(rawContent,'''') 
					from dbo.fn_getContent((
						select contentID 
						FROM dbo.cms_content 
						where siteResourceID = mdcv.columnValueSiteResourceID
						and siteID = ' + cast(@siteID as varchar(10)) + ')
						, 1)
					) <> ''''
					except
				select tmp.membernumber, mdcv.columnID, content.rawContent as columnValueContent
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join #mc_PartialMemImport as tmp on tmp.MCMemberID = md.memberID
				cross apply dbo.fn_getContent((select contentID FROM dbo.cms_content where siteResourceID = mdcv.columnValueSiteResourceID 
												and siteID = ' + cast(@siteID as varchar(10)) + '), 1) as content
				where mdcv.columnID in (' + @IDList + ')';
			INSERT INTO #memimport_memberDataColumnValues (membernumber, columnID, columnValueContent)
			EXEC(@dynSQL);

			INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
			select @logID, 'ams_importPartialMemberData_import_mdcon', datediff(ms,@logProcStart,getdate());
		END

		-- update MCMemberID
		UPDATE tmp
		SET tmp.MCMemberID = imp.MCMemberID
		FROM #memimport_memberDataColumnValues AS tmp
		INNER JOIN #mc_PartialMemImport AS imp ON imp.memberNumber = tmp.memberNumber
		WHERE imp.MCMemberID IS NOT NULL;

		SET @logProcStart = getdate();

		-- get definition of the custom fields
		INSERT INTO #tmpMDCDef (columnID, columnName, dataTypeCode, displayTypeCode, allowMultiple)
		select pos.areaID, pos.COLUMN_NAME, pos.MDdataTypeCode, pos.MDdisplayTypeCode, pos.allowMultiple
		from (
			select distinct columnID from #memimport_memberDataColumnValues
		) as tmp
		inner join #tblPossibleImportCols as pos on pos.area = 'Custom Fields' and pos.areaID = tmp.columnID;

		-- update string columns supporting select, radio button and checkbox with correct case
		update tblVal
		set tblVal.columnValueString = mdcv.columnValueString
		from #memimport_memberDataColumnValues as tblVal
		inner join #tmpMDCDef as def on def.columnID = tblVal.columnID
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = def.columnID
		where def.dataTypeCode = 'STRING'
		and def.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
		and tblVal.columnValueString = mdcv.columnValueString
		and tblVal.columnValueString <> mdcv.columnValueString COLLATE Latin1_General_CS_AS;

		-- update actualValueID where it already exists
		update tblVal
		set tblVal.actualValueID = mdcv.valueID
		from #memimport_memberDataColumnValues as tblVal
		inner join #tmpMDCDef as def on def.columnID = tblVal.columnID
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = def.columnID
		where def.dataTypeCode = 'STRING'
		and tblVal.columnValueString = mdcv.columnValueString COLLATE Latin1_General_CS_AS;

		update tblVal
		set tblVal.actualValueID = mdcv.valueID
		from #memimport_memberDataColumnValues as tblVal
		inner join #tmpMDCDef as def on def.columnID = tblVal.columnID
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = def.columnID
		where def.dataTypeCode = 'DECIMAL2'
		and tblVal.columnValueDecimal2 = mdcv.columnValueDecimal2;

		update tblVal
		set tblVal.actualValueID = mdcv.valueID
		from #memimport_memberDataColumnValues as tblVal
		inner join #tmpMDCDef as def on def.columnID = tblVal.columnID
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = def.columnID
		where def.dataTypeCode = 'INTEGER'
		and tblVal.columnValueInteger = mdcv.columnValueInteger;

		update tblVal
		set tblVal.actualValueID = mdcv.valueID
		from #memimport_memberDataColumnValues as tblVal
		inner join #tmpMDCDef as def on def.columnID = tblVal.columnID
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = def.columnID
		where def.dataTypeCode = 'DATE'
		and tblVal.columnValueDate = mdcv.columnValueDate;

		update tblVal
		set tblVal.actualValueID = mdcv.valueID
		from #memimport_memberDataColumnValues as tblVal
		inner join #tmpMDCDef as def on def.columnID = tblVal.columnID
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = def.columnID
		where def.dataTypeCode = 'BIT'
		and tblVal.columnValueBit = mdcv.columnValueBit;

		INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
		select @logID, 'ams_importPartialMemberData_import_actval', datediff(ms,@logProcStart,getdate());

		SET @logProcStart = getdate();

		-- get fields and members of original import file so we can back up that data once accepted
		select @updateFields = isnull((select isnull(column_name,'') from #tblActualImportCols as c FOR XML AUTO, ELEMENTS, ROOT('cs')),'<cs/>');
		select @updateMembers = isnull((select isnull(membernumber,'') from #tblPartialImportMembers as m FOR XML AUTO, ELEMENTS, ROOT('ms')),'<ms/>');

		-- determine if we put the members in the queue
		UPDATE #mc_PartialMemImport
		SET putInQueue = 1
		WHERE MCMemberProtected = 0
		AND (prefixChanged = 1 
			or firstNameChanged = 1 
			or middleNameChanged = 1 
			or lastNameChanged = 1 
			or suffixChanged = 1 
			or professionalSuffixChanged = 1 
			or companyChanged = 1 
			or memberTypeIDChanged = 1
			or recordTypeIDChanged = 1
			or statusChanged = 1
			or memberNumberChanged = 1
			or memberNumber in (select memberNumber from #memimport_memberWebsites)
			or memberNumber in (select memberNumber from #memimport_memberEmails)
			or memberNumber in (select memberNumber from #memimport_memberEmailTags)
			or memberNumber in (select memberNumber from #memimport_memberAddressTags)
			or memberNumber in (select memberNumber from #memimport_memberAddresses)
			or memberNumber in (select memberNumber from #memimport_memberPhones)
			or memberNumber in (select memberNumber from #memimport_memberProfessionalLicenses)
			or memberNumber in (select memberNumber from #memimport_memberDataColumnValues));

		INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
		select @logID, 'ams_importPartialMemberData_import_prerecord', datediff(ms,@logProcStart,getdate());

		-- prep mongo changes
		IF EXISTS (select rowID from #mc_PartialMemImport where putInQueue = 1 and MCMemberID is not null)
			EXEC dbo.ams_importPartialMemberData_recordChanges @orgID=@orgID;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		SET @logProcStart = getdate();

		BEGIN TRAN;
			INSERT INTO platformQueue.dbo.memimport_jobs (orgID, inactivateNonIncMembers, bypassListEmailUpdate, dateEntered, statusID, updateFields, updateMembers)
				OUTPUT INSERTED.jobID, INSERTED.orgID, INSERTED.dateEntered, @runByMemberID, @importTitle, @numSubmitted
				INTO platformStatsMC.dbo.ams_memberImportHistory (jobID, orgID, dateEntered, runByMemberID, importTitle,numSubmitted)
			VALUES (@orgID, @inactivateNonIncMembers, @bypassListEmailUpdate, getdate(), @statusInserting, @updateFields, @updateMembers);
				set @jobID = SCOPE_IDENTITY();

			-- each member in the upload should have a row
			INSERT INTO platformQueue.dbo.memimport_members (orgID, prefix, firstName, middlename, lastName, suffix, company, professionalSuffix, memberNumber, 
				memberTypeID, recordTypeID, jobID, actualMemberID, [status], prefixChanged, firstNameChanged, middleNameChanged, lastNameChanged, suffixChanged, 
				professionalSuffixChanged, companyChanged, memberTypeIDChanged, recordTypeIDChanged, newMemberNumber, memberNumberChanged, statusChanged, queueStatusID)
				OUTPUT INSERTED.memberID, INSERTED.memberNumber
				INTO #tmpQueueMembers (queueMemberID, membernumber)
			select @orgID, prefix, firstName, middlename, lastName, suffix, company, professionalSuffix, memberNumber, MCMemberTypeID, MCRecordTypeID, @jobID, 
				MCMemberID, MCAccountStatus, prefixChanged, firstNameChanged, middleNameChanged, lastNameChanged, suffixChanged, professionalSuffixChanged, 
				companyChanged, memberTypeIDChanged, recordTypeIDChanged, newMemberNumber, memberNumberChanged, statusChanged, @statusToProcess
			from #mc_PartialMemImport
			where putInQueue = 1;
				
			-- websites
			INSERT INTO platformQueue.dbo.memimport_memberWebsites (memberID, websiteTypeID, website)
			select imp.queueMemberID, tmp.websiteTypeID, tmp.website
			from #memimport_memberWebsites as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber;

			-- emails
			INSERT INTO platformQueue.dbo.memimport_memberEmails (memberID, emailTypeID, email)
			select imp.queueMemberID, tmp.emailTypeID, tmp.email
			from #memimport_memberEmails as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber;

			-- email tags
			INSERT INTO platformQueue.dbo.memimport_memberEmailTags (memberID, emailTagTypeID, emailTagType, emailTypeID)
			select imp.queueMemberID, tmp.emailTagTypeID, tmp.emailTagType, tmp.emailTypeID
			from #memimport_memberEmailTags as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber;

			-- address tags
			INSERT INTO platformQueue.dbo.memimport_memberAddressTags (memberID, addressTagTypeID, addressTagType, addressTypeID)
			select imp.queueMemberID, tmp.addressTagTypeID, tmp.addressTagType, tmp.addressTypeID
			from #memimport_memberAddressTags as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber;

			-- addresses 
			insert into platformQueue.dbo.memimport_memberAddresses (memberID, addressTypeID, attn, address1, address2, address3, 
				city, stateID, postalCode, county, countryID, attnChanged, address1Changed, address2Changed, address3Changed, 
				cityChanged, stateIDChanged, postalCodeChanged, countyChanged, countryIDChanged)
			select imp.queueMemberID, tmp.addressTypeID, tmp.attn, tmp.address1, tmp.address2, tmp.address3, tmp.city, tmp.stateID, 
				tmp.postalCode, tmp.county, tmp.countryID, tmp.attnChanged, tmp.address1Changed, tmp.address2Changed, tmp.address3Changed, 
				tmp.cityChanged, tmp.stateIDChanged, tmp.postalCodeChanged, tmp.countyChanged, tmp.countryIDChanged
			from #memimport_memberAddresses as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber;

			-- phones
			insert into platformQueue.dbo.memimport_memberPhones (memberID, addressTypeID, phoneTypeID, phone)
			select imp.queueMemberID, tmp.addressTypeID, tmp.phoneTypeID, tmp.phone
			from #memimport_memberPhones as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber;

			-- professional licenses
			insert into platformQueue.dbo.memimport_memberProfessionalLicenses (memberID, PLTypeID, LicenseNumber, ActiveDate, PLStatusID)
			select imp.queueMemberID, tmp.PLTypeID, tmp.licenseNumber, tmp.ActiveDate, tmp.PLStatusID
			from #memimport_memberProfessionalLicenses as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber;

			-- member custom data
			insert into platformQueue.dbo.memimport_memberDataColumnValues (memberID, columnID, columnName, dataTypeCode, displayTypeCode, 
				allowMultiple, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, columnValueBit, 
				columnValueContent, actualValueID)
			select imp.queueMemberID, def.columnID, def.columnName, def.dataTypeCode, def.displayTypeCode, def.allowMultiple,
				tmp.columnValueString, tmp.columnValueDecimal2, tmp.columnValueInteger, tmp.columnvalueDate, 
				tmp.columnValueBit, tmp.columnValueContent, tmp.actualValueID
			from #memimport_memberDataColumnValues as tmp
			inner join #tmpQueueMembers as imp on imp.memberNumber = tmp.membernumber
			inner join #tmpMDCDef as def on def.columnID = tmp.columnID;

			-- swap out the temporary membernumbers
			UPDATE platformQueue.dbo.memimport_members
			set membernumber = ''
			where jobID = @jobID
			and actualMemberID is null
			and left(membernumber,5) = '|pmi|';

			-- any non-fatal errors
			insert into platformQueue.dbo.memimport_warnings (jobID, membernumber, firstname, lastname, errorMessage)
			select @jobID as jobID, m.membernumber, m.firstname, m.lastname, tmp.msg
			from #tblPartialImportWarnings as tmp
			left outer join #mc_PartialMemImport as m on m.rowID = tmp.importRowID;

			-- record changes for mongo
			INSERT INTO platformQueue.dbo.memimport_mongo (jobID, memberID, columnName, change)
			select @jobID as jobID, memberID, columnName, change
			from #memimport_mongo;

			-- update job to readytoverify
			update platformQueue.dbo.memimport_jobs
			set statusID = @statusToVerify
			where jobID = @jobID;
		COMMIT TRAN;

		INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
		select @logID, 'ams_importPartialMemberData_import_insert', datediff(ms,@logProcStart,getdate());

		-- save original doc and report sections
		IF @numSubmitted > 1
		BEGIN
			EXEC dbo.ams_importPartialMemberData_saveDoc @jobID=@jobID, @docType='data', @sourceFile=@dataFilename, @sourceFileExt=@dataFileExt;
			EXEC dbo.ams_importPartialMemberData_saveReports @jobID=@jobID, @environmentName=@environmentName;
		END
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		INSERT INTO #tblPartialImportErrors (msg) VALUES ('Unable to queue members for import.');
		INSERT INTO #tblPartialImportErrors (msg) VALUES (left(error_message(),600));
	END CATCH

	-- return the xml results
	select @importResult = (
		select getdate() as "@date", @jobID as "@jobID",
			(select count(queueMemberID) from #tmpQueueMembers) as "@memCount",

			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblPartialImportErrors
			order by msg
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL 
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#memimport_memberWebsites') IS NOT NULL 
		DROP TABLE #memimport_memberWebsites;
	IF OBJECT_ID('tempdb..#memimport_memberEmails') IS NOT NULL 
		DROP TABLE #memimport_memberEmails;
	IF OBJECT_ID('tempdb..#memimport_memberEmailTags') IS NOT NULL 
		DROP TABLE #memimport_memberEmailTags;
	IF OBJECT_ID('tempdb..#memimport_memberAddressTags') IS NOT NULL 
		DROP TABLE #memimport_memberAddressTags;
	IF OBJECT_ID('tempdb..#memimport_memberAddresses') IS NOT NULL 
		DROP TABLE #memimport_memberAddresses;
	IF OBJECT_ID('tempdb..#memimport_memberPhones') IS NOT NULL 
		DROP TABLE #memimport_memberPhones;
	IF OBJECT_ID('tempdb..#memimport_memberProfessionalLicenses') IS NOT NULL 
		DROP TABLE #memimport_memberProfessionalLicenses;
	IF OBJECT_ID('tempdb..#tmpPMI_str') IS NOT NULL 
		DROP TABLE #tmpPMI_str;
	IF OBJECT_ID('tempdb..#tmpPMI_dec') IS NOT NULL 
		DROP TABLE #tmpPMI_dec;
	IF OBJECT_ID('tempdb..#memimport_memberDataColumnValues') IS NOT NULL 
		DROP TABLE #memimport_memberDataColumnValues;
	IF OBJECT_ID('tempdb..#memimport_mongo') IS NOT NULL 
		DROP TABLE #memimport_mongo;
	IF OBJECT_ID('tempdb..#tmpInnerMULTI') IS NOT NULL 
		DROP TABLE #tmpInnerMULTI;
	IF OBJECT_ID('tempdb..#tmpInnerMULTIUnpvt') IS NOT NULL 
		DROP TABLE #tmpInnerMULTIUnpvt;
	IF OBJECT_ID('tempdb..#tmpInnerHoldMULTI') IS NOT NULL 
		DROP TABLE #tmpInnerHoldMULTI;
	IF OBJECT_ID('tempdb..#tmpQueueMembers') IS NOT NULL 
		DROP TABLE #tmpQueueMembers;
	IF OBJECT_ID('tempdb..#tmpMDCDef') IS NOT NULL 
		DROP TABLE #tmpMDCDef;
	IF OBJECT_ID('tempdb..#tmpIDs') IS NOT NULL 
		DROP TABLE #tmpIDs;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
