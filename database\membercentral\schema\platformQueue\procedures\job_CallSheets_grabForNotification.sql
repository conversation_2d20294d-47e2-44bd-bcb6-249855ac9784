ALTER PROC dbo.job_CallSheets_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='callSheets', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemUID uniqueidentifier);

	-- dequeue. 
	; WITH itemUIDs AS (
		select distinct c.itemUID
		from platformQueue.dbo.queue_callSheets as c
		inner join platformQueue.dbo.queue_callSheetsDetail as cd on cd.itemUID = c.itemUID
		where cd.statusID = @statusReady
			except
		select distinct c.itemUID
		from platformQueue.dbo.queue_callSheets as c
		inner join platformQueue.dbo.queue_callSheetsDetail as cd on cd.itemUID = c.itemUID
		where cd.statusID <> @statusReady
	)

	UPDATE cd WITH (UPDLOCK, READPAST)
	SET statusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT itemUIDs.itemUID
		INTO #tmpNotify
	FROM platformQueue.dbo.queue_callSheetsDetail as cd
	inner join itemUIDs on itemUIDs.itemUID = cd.itemUID
	where cd.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	select distinct cd.itemUID, me.email as reportEmail, s.siteName, s.siteCode,
		mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from (select distinct itemUID from #tmpNotify) as tmpN
	inner join platformQueue.dbo.queue_callSheetsDetail as cd on cd.itemUID = tmpN.itemUID
	inner join platformQueue.dbo.queue_callSheets as c on c.itemUID = cd.itemUID
	inner join membercentral.dbo.ams_members as m on m.orgID in (c.orgID,1)
		and m.memberID = c.recordedByMemberID
	inner join membercentral.dbo.ams_members as mActive on mActive.orgID in (c.orgID,1)
		and mActive.memberID = m.activeMemberID
	inner join membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (c.orgID,1)
		and metag.memberID = mActive.memberID
	inner join membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (c.orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	inner join membercentral.dbo.ams_memberEmails as me on me.orgID in (c.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	inner join membercentral.dbo.sites as s on s.siteID = c.siteID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
