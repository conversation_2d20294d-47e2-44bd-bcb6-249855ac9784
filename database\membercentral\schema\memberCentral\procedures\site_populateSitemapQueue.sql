ALTER PROC dbo.site_populateSitemapQueue
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='sitemaps', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	INSERT INTO platformQueue.dbo.queue_sitemaps (siteID, statusID, dateAdded, dateupdated)
	SELECT s.siteID, @statusReady, getdate(), getdate()
	from dbo.sites as s
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
	where NOT EXISTS (select siteID from platformQueue.dbo.queue_sitemaps where siteID = s.siteID);

	SET @itemCount = @@ROWCOUNT;

	-- resume task
	EXEC dbo.sched_resumeTask @name='Process Sitemaps Queue', @engine='MCLuceeLinux';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
