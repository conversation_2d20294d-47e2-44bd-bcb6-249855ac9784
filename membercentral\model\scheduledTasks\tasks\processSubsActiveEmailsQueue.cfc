<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_subscriptionActiveEmails_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qrySubscriberEmails" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qrySubscriberEmails.recordCount>

			<cfloop query="local.qrySubscriberEmails">
				<cfquery name="local.qryUpdateStatus" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionActiveEmails', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_subscriptionActiveEmails
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscriberEmails.itemID#">;
				</cfquery>

				<cfset local.errReason = ''>
				<cfset local.emailStatusCode = 'S'>
				<cfset local.offerMemberData = local.objSubs.getOfferMemberData(subscriberID=local.qrySubscriberEmails.subscriberID, emailTemplateID=local.qrySubscriberEmails.emailTemplateID)>

				<cfif isValid("regex",local.offerMemberData.toEmail,application.regEx.email)>
					<cfset local.replyto = trim(local.qrySubscriberEmails.emailFrom)>
					<cfif NOT len(local.replyto) OR NOT isValid("regex",local.replyto,application.regEx.email)>
						<cfset local.replyto = local.qrySubscriberEmails.supportProviderEmail>
					</cfif>

					<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name=local.qrySubscriberEmails.emailFromName, email=local.qrySubscriberEmails.networkEmailFrom },
							emailto=[{ name=local.offerMemberData.memberName, email=local.offerMemberData.toEmail }],
							emailreplyto=local.replyto,
							emailsubject=local.qrySubscriberEmails.subjectLine,
							emailtitle=local.qrySubscriberEmails.subjectLine,
							emailhtmlcontent=local.offerMemberData.templateDisp,
							emailAttachments=[],
							siteID=local.offerMemberData.siteID,
							memberID=local.offerMemberData.memberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SUBSWELCOME"),
							sendingSiteResourceID=local.offerMemberData.siteResourceID
					)>

					<cfif NOT local.strResult.success>
						<cfset local.errReason = local.strResult.err>
						<cfset local.emailStatusCode = 'E'>
					</cfif>
				<cfelse>
					<cfif len(local.offerMemberData.toEmail) eq 0>
						<cfset local.emailStatusCode = 'B'>
						<cfset local.toEmail = "blank email">
					<cfelse>
						<cfset local.emailStatusCode = 'I'>
						<cfset local.toEmail = local.offerMemberData.toEmail>
					</cfif>
					<cfset local.errReason = 'Invalid email address for member (#local.toEmail#).'>
				</cfif>

				<cfset local.actorMemberID = application.objCommon.getMCSystemMemberID()>

				<cfstoredproc procedure="ams_createEmailLogEntry" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySubscriberEmails.memberID#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySubscriberEmails.emailTemplateID#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.actorMemberID#">
					<cfprocparam cfsqltype="cf_sql_varchar" value="#local.emailStatusCode#">
					<cfprocparam cfsqltype="cf_sql_date" null="yes">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySubscriberEmails.subscriberID#">
					<cfprocparam cfsqltype="cf_sql_varchar" value="#local.errReason#">
				</cfstoredproc>

				<cfquery name="local.qryDeleteFromQueue" datasource="#application.dsn.platformQueue.dsn#">
					DELETE FROM dbo.queue_subscriptionActiveEmails
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscriberEmails.itemID#">
				</cfquery>
			</cfloop>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>