ALTER PROC dbo.queue_MemberConditions_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
	@xmldata xml, @itemGroupUID uniqueidentifier, @ErrorMessage nvarchar(2048),
	@xmldataFinal xml, @clearDone bit, @processType varchar(30), @orgID int,
	@triggerLogID bigint, @queueTypeID int, @readyQueueStatusID int, @notReadyQueueStatusID int;

WHILE 1 = 1
BEGIN TRY

	SELECT @DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @processType = NULL, 
		@orgID = NULL, @itemGroupUID = NULL, @ErrorMessage = NULL, @xmldataFinal = NULL, @clearDone = 0, @triggerLogID = NULL;

	-- initially set to snapshot. this allows us to go in and out throughout this request
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	BEGIN TRANSACTION;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		WAITFOR (
			RECEIVE TOP (1) @DialogHandle = conversation_handle,
							@MessageType = message_type_name,
							@MessageBody = message_body
			FROM dbo.MemberConditionsQueue
		), TIMEOUT 1000;

		IF @DialogHandle IS NULL BEGIN
			COMMIT TRANSACTION;
			BREAK;
		END

		IF @MessageType = N'PlatformQueue/GeneralXMLRequest' BEGIN
			BEGIN TRY
				SET @xmldata = cast(@MessageBody as xml);

				SELECT @orgID = @xmldata.value('(/mc/@o)[1]','int'), 
					@processType = @xmldata.value('(/mc/@t)[1]','varchar(30)'),
					@itemGroupUID = @xmldata.value('(/mc/@u)[1]','uniqueidentifier'),
					@triggerLogID = @xmldata.value('(/mc/@x)[1]','bigint');

				IF @itemGroupUID is not null AND EXISTS (SELECT 1 FROM dbo.queue_memberConditions WHERE itemGroupUID = @itemGroupUID) BEGIN
					
					SELECT @xmldataFinal = isnull((
						select @orgID as o, @itemGroupUID as u
						FOR XML RAW('mc'), TYPE
					),'<mc/>');	

					IF OBJECT_ID('tempdb..#tmpMCQCondCacheChanges') IS NOT NULL 
						DROP TABLE #tmpMCQCondCacheChanges;
					IF OBJECT_ID('tempdb..#tmpMCQCondCacheMemChangedMCA') IS NOT NULL 
						DROP TABLE #tmpMCQCondCacheMemChangedMCA;
					CREATE TABLE #tmpMCQCondCacheChanges (memberID int, conditionID int, isAdded bit);
					CREATE TABLE #tmpMCQCondCacheMemChangedMCA (memberID int);

					EXEC dbo.queue_getQueueTypeID @queueType='memberGroups', @queueTypeID=@queueTypeID OUTPUT;
					EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;
					EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='notReady', @queueStatusID=@notReadyQueueStatusID OUTPUT;
					
					IF @processType = 'ConditionsOnly' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=0;
						SET @clearDone = 1;
					END

					IF @processType = 'ConditionsOnlyNonImm' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=1;
						SET @clearDone = 1;
					END

					IF @processType = 'ConditionsAndGroups' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=0;

						UPDATE dbo.queue_memberGroups
						SET statusID = @readyQueueStatusID,
							dateUpdated = getdate()
						WHERE orgID = @orgID
						AND itemGroupUID = @itemGroupUID
						AND statusID = @notReadyQueueStatusID;

						EXEC dbo.queue_MemberGroups_sendMessage @xmlMessage=@xmldataFinal;
						SET @clearDone = 1;
					END

					IF @processType = 'ConditionsAndGroupsChanged' BEGIN
						EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=1, @includeNonImmediate=0;

						-- safety check: add any members that are missing system groups
						declare @publicGroupID int, @guestsGroupID int, @usersGroupID int, @dateAdded datetime = getdate();
						select @publicGroupID = groupID from membercentral.dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Public' AND [status] = 'A';
						select @guestsGroupID = groupID from membercentral.dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Guests' AND [status] = 'A';
						select @usersGroupID = groupID from membercentral.dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Users' AND [status] = 'A';

						insert into #tmpMCQCondCacheMemChangedMCA (memberID)
						select distinct memberID 
						from #tmpMCQCondCacheChanges;

						insert into #tmpMCQCondCacheMemChangedMCA (memberID)
						select memberID
						from (
							select memberID
							from membercentral.dbo.ams_members
							where orgID = @orgID
							and [status] <> 'D'
								except
							select mg.memberID
							from membercentral.dbo.cache_members_groups as mg
							where mg.groupID = @publicGroupID
						) as missingPublic
							union
						select memberID
						from (
							select memberID
							from membercentral.dbo.ams_members
							where orgID = @orgID				
							and memberTypeID = 2
							and [status] <> 'D'
								except
							select mg.memberID
							from membercentral.dbo.cache_members_groups as mg
							where mg.groupID = @usersGroupID
						) as missingUsers
							union
						select memberID
						from (
							select memberID
							from membercentral.dbo.ams_members
							where orgID = @orgID
							and memberTypeID = 1
							and [status] <> 'D'
								except
							select mg.memberID
							from membercentral.dbo.cache_members_groups as mg
							where mg.groupID = @guestsGroupID
						) as missingGuests;

						insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
						select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
						from #tmpMCQCondCacheMemChangedMCA;

						EXEC dbo.queue_MemberGroups_sendMessage @xmlMessage=@xmldataFinal;
						SET @clearDone = 1;
					END

					IF OBJECT_ID('tempdb..#tmpMCQCondCacheMemChangedMCA') IS NOT NULL 
						DROP TABLE #tmpMCQCondCacheMemChangedMCA;
				END

				END CONVERSATION @DialogHandle;
			END TRY		
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET @ErrorMessage = N'queue_MemberConditions_Activated - ' + ERROR_MESSAGE();
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END CATCH
		END
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @DialogHandle;
			END 
			ELSE BEGIN
				IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
					SET @xmldata = cast(@MessageBody as xml);						
					with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
					select @ErrorMessage = N'queue_MemberConditions_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
				ELSE BEGIN
					SET @ErrorMessage = N'queue_MemberConditions_Activated - Unexpected message type received: ' + @MessageType; 
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
			END
		END

	IF @@trancount > 0	
		COMMIT TRANSACTION;

	IF @clearDone = 1
		EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH

IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
GO
