ALTER PROC dbo.tr_populatecache_tr_ARByDayInvoiceProfileChanges

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	/* *********************** */
	/* Scheduled Task Handling */
	/* *********************** */
	declare @taskID int, @historyStatusID int, @taskHistoryID int, @ChangedAfterDate datetime;
	select @taskID = taskid FROM dbo.scheduledTasks WHERE taskCFC = 'sp tr_populatecache_tr_ARByDayInvoiceProfileChanges' and isRunning = 0;
	IF @taskID is null
		GOTO on_done;
	update dbo.scheduledTasks set isRunning = 1 where taskID = @taskID;

	SELECT @historyStatusID = statusTypeID FROM platformStatsMC.dbo.scheduledTaskStatusTypes WHERE statusName = 'Pending';
	INSERT INTO platformStatsMC.dbo.scheduledTaskHistory (taskid, statusTypeID, dateStarted, dateLastUpdated)		
	VALUES (@taskID, @historyStatusID, getdate(), getdate());
		SELECT @taskHistoryID = SCOPE_IDENTITY();

	EXEC dbo.sched_getLastRunDate @taskCFC='sp tr_populatecache_tr_ARByDayInvoiceProfileChanges', @defaultDate='8/25/2020', @lastDate=@ChangedAfterDate OUTPUT;


	declare @orgID int, @minRecalcDate date, @yesterday date, @oldestAllowedDate date;
	declare @tblOrgsToRun TABLE (orgID int, minRecalcDate date);
	SET @yesterday = DATEADD(DAY,-1,getdate());
	SET @oldestAllowedDate = DATEADD(YEAR,-2,@yesterday);
	SET @oldestAllowedDate = DATEFROMPARTS(YEAR(@oldestAllowedDate),MONTH(@oldestAllowedDate),1);
	

	insert into @tblOrgsToRun (orgID, minRecalcDate)
	select orgID, 
		minRecalcDate = case 
		when minRecalcDate < @oldestAllowedDate then @oldestAllowedDate
		else minRecalcDate
		end
	from (
		select orgID, min(minRecalcDate) as minRecalcDate
		from (
			-- invoices that changed statuses to or from open or were created since the date specified
			select i.orgID, min(i.datebilled) as minRecalcDate
			from dbo.tr_invoiceStatusHistory as ish
			inner join dbo.tr_invoices as i on i.invoiceID = ish.invoiceID 
			where ish.updateDate >= @ChangedAfterDate
			and (ish.oldStatusID = 1 or ish.statusID = 1)
			group by i.orgID
				union
			-- invoices changing the datebilled date since the date specified
			select i.orgID, min(i.datebilled) as minRecalcDate
			from dbo.tr_invoiceDateHistory as idh
			inner join dbo.tr_invoices as i on i.invoiceID = idh.invoiceID
			where idh.updateDate >= @ChangedAfterDate
			and idh.[type] = 'B'
			group by i.orgID
				union
			-- transactions created since the date specified
			select ownedByOrgID, min(transactionDate) as minRecalcDate
			from dbo.tr_transactions
			where dateRecorded >= @ChangedAfterDate
			group by ownedByOrgID
				union
			-- batches changing the depositDate date since the date specified
			select b.orgID, min(b.depositDate) as minRecalcDate
			from dbo.tr_batchDateHistory as bdh
			inner join dbo.tr_batches as b on b.batchID = bdh.batchID
			where bdh.updateDate >= @ChangedAfterDate
			and bdh.[type] = 'B'
			group by b.orgID
				union
			-- add in orgs that had no activity since their AR carries and we need to record it in the cache
			select orgID, @ChangedAfterDate as minRecalcDate
			from dbo.organizations
		) as tmp
		group by orgID
	) as outerTmp;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


	/* ****************************** */
	/* populate ARByDayInvProf queue  */
	/* ****************************** */
	DECLARE @queueStatusID int, @xmlMessage xml;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='ARByDayInvProf', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;
	
	INSERT INTO platformQueue.dbo.queue_ARByDayInvProf (orgID, arDate, statusID, dateAdded, dateUpdated)
	SELECT tmp.orgID, cal.caldate, @queueStatusID, getdate(), getdate()
	FROM @tblOrgsToRun as tmp
	CROSS APPLY (
		SELECT TOP (case when DATEDIFF(DAY, tmp.minRecalcDate, @yesterday) + 1 >=0 then DATEDIFF(DAY, tmp.minRecalcDate, @yesterday) + 1 else 0 end) caldate = DATEADD(DAY, ROW_NUMBER() OVER(ORDER BY a.object_id) - 1, tmp.minRecalcDate)
		FROM sys.all_objects a
        CROSS JOIN sys.all_objects b
	) as cal;

	IF @@ROWCOUNT > 0 BEGIN
		-- send message to service broker to create all the individual messages
		select @xmlMessage = isnull((
			select 'ARByDayInvProfLoad' as t, cast(NEWID() as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	END

	/* *********************** */
	/* Scheduled Task Handling */
	/* *********************** */
	EXEC dbo.sched_markAsFinished @taskID=@taskID, @historyID=@taskHistoryID, @isSuccess=1;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	IF @taskID is not null and @taskHistoryID is not null
		EXEC dbo.sched_markAsFinished @taskID=@taskID, @historyID=@taskHistoryID, @isSuccess=0;

	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
