ALTER PROC dbo.hooks_webhookListener_subscriptionStatusChange
@xmlData xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpWebhookSubs') IS NOT NULL 
		DROP TABLE #tmpWebhookSubs;
	CREATE TABLE #tmpWebhookSubs (subscriptionID int, rateID int, statusID int, paymentStatusID int);

	DECLARE @statusReady int, @siteID int, @orgID int, @webhookID int, @webhookURL varchar(400), 
		@eventID int, @subscriptionID int, @subscriberID int, @newStatusID int, @oldStatusID int, @memberID int, 
		@rateID int, @payloadXML xml, @SQSQueueName varchar(80), @subUID varchar(36), @oldStatusName varchar(50),
		@newStatusName varchar(50), @changeDate datetime, @paymentStatusID int, @payloadMessage varchar(max),
		@environmentName varchar(50), @nowDate datetime = GETDATE();

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='webhook', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	SELECT @eventID = eventID FROM dbo.hooks_events where [event] = 'subscriptionStatusChange';
	SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

	SELECT @siteID = @xmlData.value('(/wh/@s)[1]','int');
	SELECT @webhookID = @xmlData.value('(/wh/@w)[1]','int');	
	SELECT @payloadXML = @xmlData.query('wh/data');

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SELECT @memberID = @payloadXML.value('(/data/memberid)[1]', 'int');
	SELECT @subscriberID = @payloadXML.value('(/data/subscriberid)[1]', 'int');
	SELECT @subscriptionID = @payloadXML.value('(/data/subscriptionid)[1]', 'int');
	SELECT @newStatusID = @payloadXML.value('(/data/newstatusid)[1]', 'int');
	SELECT @oldStatusID = @payloadXML.value('(/data/oldstatusid)[1]', 'int');
	SELECT @changeDate = @payloadXML.value('(/data/changedate)[1]', 'datetime');

	SELECT @webhookURL = hookURL, @SQSQueueName = SQSQueueName FROM dbo.hooks_webhooks WHERE webhookID = @webhookID;

	SELECT @rateID = rf.rateID, @paymentStatusID = s.paymentStatusID
	FROM dbo.sub_subscribers AS s
	INNER JOIN dbo.sub_rateFrequencies AS rf ON rf.rfid = s.RFID
	WHERE s.subscriberID = @subscriberID
	AND s.orgID = @orgID;

	-- determine the subscription status change options
	INSERT INTO #tmpWebhookSubs (subscriptionID, rateID, statusID, paymentStatusID)
	SELECT DISTINCT s.subscriptionID, wss.rateID, wss.statusID, wss.paymentStatusID
	FROM dbo.hooks_webhooks_subscriptionStatusChange AS wss
	INNER JOIN dbo.sub_subscriptions AS s ON s.orgID = @orgID 
		AND s.typeID = wss.typeID
	WHERE wss.webhookID = @webhookID
	AND wss.subscriptionID IS NULL
		UNION
	SELECT DISTINCT s.subscriptionID, wss.rateID, wss.statusID, wss.paymentStatusID
	FROM dbo.hooks_webhooks_subscriptionStatusChange AS wss
	INNER JOIN dbo.sub_subscriptions AS s ON s.orgID = @orgID 
		AND s.subscriptionID = wss.subscriptionID
		AND s.typeID = wss.typeID
	WHERE wss.webhookID = @webhookID;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpWebhookSubs (subscriptionID, statusID, paymentStatusID)
		select distinct s.subscriptionID, wss.statusID, wss.paymentStatusID
		from dbo.sub_subscriptions as s
		inner join dbo.hooks_webhooks_subscriptionStatusChange as wss on wss.webhookID = @webhookID
		where s.orgID = @orgID
		and s.[status] = 'A';

	SELECT TOP 1 @subUID = s.[uid]
	FROM #tmpWebhookSubs AS tmp
	INNER JOIN dbo.sub_subscriptions AS s ON s.orgID = @orgID 
		AND s.subscriptionID = tmp.subscriptionID
		AND s.subscriptionID = @subscriptionID
	WHERE tmp.statusID = @newStatusID
	AND ISNULL(tmp.rateID,@rateID) = @rateID
	AND ISNULL(tmp.paymentStatusID,@paymentStatusID) = @paymentStatusID;

	IF LEN(@subUID) > 0 BEGIN
		SELECT @oldStatusName = statusName FROM dbo.sub_statuses WHERE statusID = @oldStatusID;
		SELECT @newStatusName = statusName FROM dbo.sub_statuses WHERE statusID = @newStatusID;

		SELECT @payloadMessage = '{ "mcwh_event":"subscriptionstatuschange", "mcwh_eventid":"' + CAST(NEWID() as varchar(36)) + '", "mcwh_env":"' + @environmentName + '", '+
			'"type": "' + t.typeName + '", "type_api_id": "' + CAST(t.[uid] AS varchar(36)) + '", "subscription": "' + sub.subscriptionName + '", ' +
			'"subscription_api_id": "' + CAST(sub.[uid] AS varchar(36)) + '", "rate": "' + r.rateName + '", "rate_api_id": "'+ CAST(r.[uid] AS varchar(36)) + '", ' +
			'"frequency": "' + f.frequencyName + '", "frequency_api_id": "' + CAST(f.[uid] AS varchar(36)) + '", "status": "' + @newStatusName + '", "formerstatus": "' + ISNULL(@oldStatusName,'') + '", ' +
			'"activationstatus": "' + pst.statusName + '", "startdate": "' + convert(varchar(10),s.subStartDate,101) + '", "enddate": "' + convert(varchar(10),s.subEndDate,101) + '", ' +
			'"graceenddate": "' + isnull(convert(varchar(10),s.graceEndDate,101),'') + '", "subscriber_id": "' + cast(s.subscriberID as varchar(10)) + '", "membernumber": "' + m.memberNumber + '", ' +
			'"changedate": "' + FORMAT(@changeDate,'MMMM, dd yyyy hh:mm:ss tt') + '", ' +
			'"parent_subscriber_id": "' + isnull(cast(s.parentSubscriberID as varchar(10)),'') + '", ' +
			'"x-parent-api-uri": "' + isnull('/v1/member/' + m.membernumber + '/subscription/' + cast(s.parentSubscriberID as varchar(10)),'') +'", ' +
			'"x-api-uri": "' + '/v1/member/' + m.membernumber + '/subscription/' + cast(s.subscriberID as varchar(10)) + '" }'
		FROM dbo.sub_subscribers as s
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = s.memberID and m.activeMemberID = @memberID
		INNER JOIN dbo.sub_subscriptions as sub on sub.orgID = @orgID and sub.subscriptionID = s.subscriptionID
		INNER JOIN dbo.sub_types as t on t.typeID = sub.typeID
		INNER JOIN dbo.sub_rateFrequencies as rf on rf.RFID = s.RFID
		INNER JOIN dbo.sub_rates as r on r.rateID = rf.rateID
		INNER JOIN dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		INNER JOIN dbo.sub_paymentStatuses as pst on pst.statusID = s.paymentStatusID
		WHERE s.orgID = @orgID
		AND s.subscriberID = @subscriberID;

		INSERT INTO platformQueue.dbo.queue_webhook (siteID, webhookID, webhookURL, eventID, payloadMessage, SQSQueueName, statusID, dateAdded, dateUpdated, nextAttemptDate)
		VALUES (@siteID, @webhookID, @webhookURL, @eventID, @payloadMessage, @SQSQueueName, @statusReady, @nowDate, @nowDate, @nowDate);
	END

	IF OBJECT_ID('tempdb..#tmpWebhookSubs') IS NOT NULL 
		DROP TABLE #tmpWebhookSubs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
