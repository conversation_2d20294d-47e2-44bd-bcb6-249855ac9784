ALTER PROC dbo.queue_screenshots_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchstartdate datetime = getdate();
	EXEC dbo.queue_getQueueTypeID @queueType='screenshots', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpScreenShots') IS NOT NULL
		DROP TABLE #tmpScreenShots;
	CREATE TABLE #tmpScreenShots (itemID int);

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = @batchstartdate
		OUTPUT inserted.itemID
		INTO #tmpScreenShots
	FROM dbo.queue_screenshots as qid
	INNER JOIN (
		SELECT top (@BatchSize) itemID 
		from dbo.queue_screenshots
		WHERE statusID = @statusReady and nextAttemptDate < @batchstartdate
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	select distinct qid.itemID, qid.siteID, o.orgCode, s.siteCode, cv.rawContent as htmlContent, qid.viewportWidth, qid.viewportHeight, 
        qid.deviceScaleFactor, qid.fullPage, qid.featureImageConfigID, qid.referenceType, qid.referenceID, qid.enteredByMemberID
	from #tmpScreenShots as tmp
	inner join dbo.queue_screenshots as qid on qid.itemID = tmp.itemID
	inner join membercentral.dbo.sites as s on s.siteID = qid.siteID
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qid.contentVersionID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpScreenShots') IS NOT NULL
		DROP TABLE #tmpScreenShots;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
