use trialsmith
GO

ALTER PROC dbo.up_monthEndBillingForMember
@depomemberdataID int,
@eomPeriod date

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @periodID int, @SOMClosedBillingPeriod date, @isOrg bit = 0, @sendEmail bit = 0;

	select @periodID = periodID, @SOMClosedBillingPeriod = DATEFROMPARTS(year(EOMPeriod),MONTH(EOMPeriod),1)
	from dbo.billingPeriods
	where EOMPeriod = @eomPeriod;

	-- DATES FOR REST OF PROCESS
	declare @BPStartD date, @BPStartDT datetime, @BPStartSDT smalldatetime, @BPEndD date, @BPEndDT datetime, @BPEndSDT smalldatetime;
	SET @BPStartD = @SOMClosedBillingPeriod;
	SET @BPStartDT = DATETIMEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(@BPStartD),0,0,0,0);
	SET @BPStartSDT = SMALLDATETIMEFROMPARTS(YEAR(@BPStartD),<PERSON>ONTH(@BPStartD),DAY(@BPStartD),0,0);
	SET @BPEndD = DATEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(EOMONTH(@BPStartD)));
	SET @BPEndDT = DATETIMEFROMPARTS(YEAR(@BPEndD),MONTH(@BPEndD),DAY(@BPEndD),23,59,59,997);
	SET @BPEndSDT = SMALLDATETIMEFROMPARTS(YEAR(@BPEndD),MONTH(@BPEndD),DAY(@BPEndD),23,59);

	-- is this an org record?
	IF EXISTS (select orgCode from dbo.memberCentralBilling where depoMemberDataID = @depomemberdataID)
		SET @isOrg = 1;

	IF @isOrg = 1 BEGIN
		IF OBJECT_ID('tempdb..#tmpSiteCodes') IS NOT NULL 
			DROP TABLE #tmpSiteCodes;
		CREATE TABLE #tmpSiteCodes (sitecode varchar(10));

		INSERT INTO #tmpSiteCodes (sitecode)
		SELECT orgCode
		from dbo.memberCentralBilling 
		where depoMemberDataID = @depomemberdataID;

		BEGIN TRAN;
			-- CHECK AND QUEUE THE ORG SW STATEMENTS FOR BILLING PERIOD
			DECLARE @SWrunID int;
			select @SWrunID = runID from platformStatsMC.dbo.sw_MonthlyBillingRun where activityReportMonth = @BPStartD;

			IF @SWrunID IS NOT NULL BEGIN
				DECLARE @SWstatusReady int;
				EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillSW', @queueStatus='readyToProcess', @queueStatusID=@SWstatusReady OUTPUT;

				-- join to sites because the orgemail processing requires it to be there
				insert into platformQueue.dbo.queue_monthBillSW (runID, participantID, siteCode, billingPeriodID, statusID)
				select distinct rd.runID, rd.participantID, rd.siteCode, @periodID, @SWstatusReady
				from platformStatsMC.dbo.sw_MonthlyBillingRunDetail as rd
				inner join membercentral.dbo.sites as s on s.siteCode = rd.siteCode
				inner join #tmpSiteCodes as tmp on tmp.siteCode = s.siteCode
				where rd.runID = @SWrunID;

				IF @@ROWCOUNT > 0 BEGIN
					SET @sendEmail = 1;
					EXEC membercentral.dbo.sched_resumeTask @name='Process SeminarWeb Statements Queue', @engine='MCLuceeLinux';
				END
			END


			-- CHECK AND QUEUE THE ORG TS ROYALTY STATEMENTS FOR BILLING PERIOD
			DECLARE @TSRrunID int;
			select @TSRrunID = runID from platformStatsMC.dbo.ts_MonthlyRoyaltyRun where reportEndDate = @BPEndD;

			IF @TSRrunID IS NOT NULL BEGIN
				DECLARE @TSRstatusReady int;
				EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillTSRoyalty', @queueStatus='readyToProcess', @queueStatusID=@TSRstatusReady OUTPUT;
				
				-- join to sites because the orgemail processing requires it to be there
				insert into platformQueue.dbo.queue_monthBillTSRoyalty (billingPeriodID, orgCode, DepoSalePCT, DepoContribAMT, SubSalePCT, 
					eclipsMonthAMT, DepoSales, DepoSpecialSales, DepoContributions, SubscriptionSales, DepoSalesRoyalty, DepoSpecialSalesRoyalty, 
					DepoContributionsRoyalty, SubscriptionSalesRoyalty, eclipsRoyalty, TotalRoyalty, statusID)
				SELECT @periodID, rd.orgcode, rd.DepoSalePCT, rd.DepoContribAMT, rd.SubSalePCT, rd.eclipsMonthAMT, rd.DepoSales, 
					rd.DepoSpecialSales, rd.DepoContributions, rd.SubscriptionSales, rd.DepoSalesRoyalty, rd.DepoSpecialSalesRoyalty, 
					rd.DepoContributionsRoyalty, rd.SubscriptionSalesRoyalty, rd.eclipsRoyalty, rd.TotalRoyalty, @TSRstatusReady
				FROM platformStatsMC.dbo.ts_MonthlyRoyaltyDetail as rd
				INNER JOIN membercentral.dbo.sites as s on s.siteCode = rd.orgCode
				INNER JOIN #tmpSiteCodes as tmpSC on tmpSC.siteCode = s.siteCode
				where rd.runID = @TSRrunID;

				IF @@ROWCOUNT > 0 BEGIN
					SET @sendEmail = 1;
					EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Royalty Statements Queue', @engine='MCLuceeLinux';
				END
			END


			-- QUEUE THE ORG TS STATEMENTS FOR BILLING PERIOD
			DECLARE @TSAstatusReady int;
			EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillTSA', @queueStatus='readyToProcess', @queueStatusID=@TSAstatusReady OUTPUT;
			
			IF OBJECT_ID('tempdb..#tmpOrgTSA') IS NOT NULL 
				DROP TABLE #tmpOrgTSA;
			CREATE TABLE #tmpOrgTSA (depomemberdataID int PRIMARY KEY, EOPBalance decimal(18,2), numInPeriod int);

			-- join to sites because the orgemail processing requires it to be there
			INSERT INTO #tmpOrgTSA (depomemberdataID, EOPBalance, numInPeriod)
			select mcb.depoMemberDataID, SUM(t.amountBilled+t.salesTaxAmount) as EOPBalance,
				SUM(case when t.DatePurchased between @BPStartSDT and @BPEndSDT then 1 else 0 end) as numInPeriod
			from dbo.memberCentralBilling as mcb
			inner join dbo.depoTransactions as t on t.depoMemberDataID = mcb.depoMemberDataID
				and t.DatePurchased <= @BPEndSDT
			inner join membercentral.dbo.sites as s on s.siteCode = mcb.orgCode
			where mcb.depoMemberDataID = @depomemberdataID
			group by mcb.depoMemberDataID;

			-- we only want statements when either have activity in billing month OR have a non-zero balance at end of month
			DELETE FROM #tmpOrgTSA
			WHERE EOPBalance = 0
			AND numInPeriod = 0;

			insert into platformQueue.dbo.queue_monthBillTSA (depomemberDataID, TransStartDate, TransEndDate, StatementDate, billingPeriodID, isOrg, statusID)
			select depomemberdataID, @BPStartSDT, @BPEndSDT, @BPEndSDT, @periodID, 1, @TSAstatusReady
			from #tmpOrgTSA;

			IF @@ROWCOUNT > 0 BEGIN
				SET @sendEmail = 1;
				EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Statements Queue', @engine='TSAdminLinux';
			END

			IF OBJECT_ID('tempdb..#tmpOrgTSA') IS NOT NULL 
				DROP TABLE #tmpOrgTSA;


			-- QUEUE THE EMAILING OF ORG STATEMENTS
			IF @sendEmail = 1 BEGIN
				DECLARE @EmailOrgStatusWaiting int;
				EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillEmailOrg', @queueStatus='waitingToProcess', @queueStatusID=@EmailOrgStatusWaiting OUTPUT;
				
				INSERT INTO platformQueue.dbo.queue_monthBillEmailOrg (billingPeriodID, siteCode, statusID)
				select @periodID, siteCode, @EmailOrgStatusWaiting
				from #tmpSiteCodes;

				IF @@ROWCOUNT > 0
					EXEC membercentral.dbo.sched_resumeTask @name='Process Monthly Billing Email Queue', @engine='MCLuceeLinux';
			END
		COMMIT TRAN;

	END ELSE BEGIN

		BEGIN TRAN;

			-- QUEUE THE TS STATEMENTS FOR BILLING PERIOD for INDIVIDUALS
			-- QUEUE THE EMAILING OF INDIV STATEMENTS
			DECLARE @TSAINDqueueTypeID int, @TSAINDstatusReady int, @EmailIndivQueueTypeID int, @EmailIndivStatusWaiting int;
			EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillTSA', @queueStatus='readyToProcess', @queueStatusID=@TSAINDstatusReady OUTPUT;
			EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillEmailIndiv', @queueStatus='waitingToProcess', @queueStatusID=@EmailIndivStatusWaiting OUTPUT;
			
			IF OBJECT_ID('tempdb..#tmpIndivTSA') IS NOT NULL 
				DROP TABLE #tmpIndivTSA;
			CREATE TABLE #tmpIndivTSA (depomemberdataID int PRIMARY KEY, paymentType char(1), EOPBalance decimal(18,2), 
				nonSWActivity int, email1 varchar(400), email2 varchar(400), firmPlanMasterDepoID int);

			-- nonSWActivity = if acctcode is not 7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012 (SW codes) and not 1000 ($0 basic plan fees that all sw accts have)
			INSERT INTO #tmpIndivTSA (depomemberdataID, paymentType, EOPBalance, nonSWActivity, email1, email2, firmPlanMasterDepoID)
			select d.depomemberdataID, d.paymenttype, ISNULL(SUM(t.amountBilled+t.salesTaxAmount),0) as EOPBalance, 
				SUM(case when t.DatePurchased between @BPStartSDT and @BPEndSDT and t.accountCode not in ('1000','7000','7001','7002','7003','7004','7005','7006','7007','7008','7009','7010','7011','7012') then 1 else 0 end) as nonSWActivity,
				case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as email1,
				nullIF(d.BillingContactEmail,'') as email2,
				(
					select fplMaster.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					INNER JOIN dbo.tlaFirmPlanLink AS fplMaster ON fplMaster.firmPlanID = fpl.firmPlanID
						and fplMaster.isMaster = 1
					where fpl.depoMemberDataID = d.depomemberdataID
					and fpl.isMaster = 0
				) as firmPlanMasterDepoID
			from dbo.depomemberdata as d
			INNER JOIN dbo.depoTransactions as t on t.depoMemberDataID = d.depoMemberDataID
				and t.DatePurchased <= @BPEndSDT
			where d.depomemberdataID = @depomemberdataID
			OR d.depomemberdataID in (
				select fpl.depomemberdataID
				from dbo.tlaFirmPlanLink as fpl
				INNER JOIN dbo.tlaFirmPlanLink AS fplMaster ON fplMaster.firmPlanID = fpl.firmPlanID
					and fplMaster.isMaster = 1
					and fplMaster.depoMemberDataID = @depomemberdataID
			)
			group by d.depomemberdataID, d.paymenttype, d.Email, d.BillingContactEmail;

			-- When Invoiced, we only want the ones with a EOP balance or non-swactivity in period and 0 balance
			-- When CreditCard, we only want the ones with non-swactivity in period and 0 balance
			delete from #tmpIndivTSA
			where (paymentType = 'I' AND NOT (EOPBalance > 0 OR (nonSWActivity > 0 AND EOPBalance = 0)))
			OR (paymentType = 'C' AND NOT (nonSWActivity > 0 AND EOPBalance = 0));

			-- delete the non-firm plans that dont have an email
			delete from #tmpIndivTSA
			where firmPlanMasterDepoID is null
			and email1 is null
			and email2 is null;

			-- add in the firm plan masters
			INSERT INTO #tmpIndivTSA (depomemberdataID, paymentType, EOPBalance, nonSWActivity, email1, email2, firmPlanMasterDepoID)
			select fpm.depomemberDataID, 'I', 0, 0, fpm.email1, fpm.email2, null
			from (
				select distinct d.depomemberdataID,  
					case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as email1,
					nullIF(d.BillingContactEmail,'') as email2
				from #tmpIndivTSA as tmp
				inner join dbo.depomemberdata as d on d.depomemberdataID = tmp.firmPlanMasterDepoID
				where tmp.firmPlanMasterDepoID is not null
			) as fpm
			where (fpm.email1 is not null OR fpm.email2 is not null)
			and NOT EXISTS (select top 1 depomemberdataID from #tmpIndivTSA where depomemberdataID = fpm.depomemberdataID);

			-- delete the firm plan members since the master may have been added
			delete from #tmpIndivTSA
			where firmPlanMasterDepoID is not null;

			insert into platformQueue.dbo.queue_monthBillTSA (depomemberDataID, TransStartDate, TransEndDate, StatementDate, billingPeriodID, isOrg, statusID)
				OUTPUT INSERTED.billingPeriodID, INSERTED.depoMemberDataID, @EmailIndivStatusWaiting
				INTO platformQueue.dbo.queue_monthBillEmailIndiv (billingPeriodID, depoMemberDataID, statusID)
			select depomemberdataID, @BPStartSDT, @BPEndSDT, @BPEndSDT, @periodID, 0, @TSAINDstatusReady
			from #tmpIndivTSA;

			IF @@ROWCOUNT > 0 BEGIN
				EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Statements Queue', @engine='TSAdminLinux';
				EXEC membercentral.dbo.sched_resumeTask @name='Process Monthly Billing Email Queue', @engine='MCLuceeLinux';
			END

			IF OBJECT_ID('tempdb..#tmpIndivTSA') IS NOT NULL 
				DROP TABLE #tmpIndivTSA;
		COMMIT TRAN;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.up_monthEndBilling

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- initially set to snapshot. this allows us to go in and out throughout this request
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	BEGIN TRAN;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		-- CLOSE BILLING PERIOD
		declare @SOMClosedBillingPeriod date, @periodID int;
		EXEC dbo.up_closeNextBillingPeriod @SOMClosedBillingPeriod=@SOMClosedBillingPeriod OUTPUT, @periodID=@periodID OUTPUT;


		-- DATES FOR REST OF PROCESS
		declare @BPStartD date, @BPStartDT datetime, @BPStartSDT smalldatetime, @BPEndD date, @BPEndDT datetime, @BPEndSDT smalldatetime;
		SET @BPStartD = @SOMClosedBillingPeriod;
		SET @BPStartDT = DATETIMEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(@BPStartD),0,0,0,0);
		SET @BPStartSDT = SMALLDATETIMEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(@BPStartD),0,0);
		SET @BPEndD = DATEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(EOMONTH(@BPStartD)));
		SET @BPEndDT = DATETIMEFROMPARTS(YEAR(@BPEndD),MONTH(@BPEndD),DAY(@BPEndD),23,59,59,997);
		SET @BPEndSDT = SMALLDATETIMEFROMPARTS(YEAR(@BPEndD),MONTH(@BPEndD),DAY(@BPEndD),23,59);

		DECLARE @applicationTypeID int = membercentral.dbo.fn_getApplicationTypeIDFromName('BuyNow'),
			@siteID int = membercentral.dbo.fn_getSiteIDFromSiteCode('MC'),
			@settingsXML xml;

		SELECT @settingsXML = settingsXML
		FROM membercentral.dbo.cms_applicationTypeSettings
		WHERE siteID = @siteID
		AND applicationTypeID = @applicationTypeID;

		SELECT @settingsXML = ISNULL(@settingsXML,'<settings />');


		-- QUEUE THE ORG SW STATEMENTS FOR BILLING PERIOD
		DECLARE @SWrunID int;
		select @SWrunID = runID from platformStatsMC.dbo.sw_MonthlyBillingRun where activityReportMonth = @BPStartD;

		IF @SWrunID IS NOT NULL BEGIN
			DECLARE @SWstatusReady int;
			EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillSW', @queueStatus='readyToProcess', @queueStatusID=@SWstatusReady OUTPUT;

			-- join to sites because the orgemail processing requires it to be there
			insert into platformQueue.dbo.queue_monthBillSW (runID, participantID, siteCode, billingPeriodID, statusID)
			select distinct rd.runID, rd.participantID, rd.siteCode, @periodID, @SWstatusReady
			from platformStatsMC.dbo.sw_MonthlyBillingRunDetail as rd
			inner join membercentral.dbo.sites as s on s.siteCode = rd.siteCode
			where rd.runID = @SWrunID;

			EXEC membercentral.dbo.sched_resumeTask @name='Process SeminarWeb Statements Queue', @engine='MCLuceeLinux';
		END


		-- GENERATE TS ROYALTY STATEMENT DATA
		-- we do this here instead of in a separate task on the 1st because we wanted the ability to modify depo transactions while the bp is still open
		-- the bp ending 10/31/2021 (and only that bp) needed to produce a combined royalty statement from July 1 - Oct 31.
		declare @TSRStartDate datetime = @BPStartDT;
		IF @BPEndD = '10/31/2021'
			SET @TSRStartDate = '7/1/2021';
		EXEC trialsmith.dbo.up_calculateMonthlyTSRoyalties @startDate=@TSRStartDate, @endDate=@BPEndDT;


		-- QUEUE THE ORG TS ROYALTY STATEMENTS FOR BILLING PERIOD
		DECLARE @TSRrunID int;
		select @TSRrunID = runID from platformStatsMC.dbo.ts_MonthlyRoyaltyRun where reportEndDate = @BPEndD;

		IF @TSRrunID IS NOT NULL BEGIN
			DECLARE @TSRstatusReady int;
			EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillTSRoyalty', @queueStatus='readyToProcess', @queueStatusID=@TSRstatusReady OUTPUT;

			-- join to sites because the orgemail processing requires it to be there
			insert into platformQueue.dbo.queue_monthBillTSRoyalty (billingPeriodID, orgCode, DepoSalePCT, DepoContribAMT, SubSalePCT, 
				eclipsMonthAMT, DepoSales, DepoSpecialSales, DepoContributions, SubscriptionSales, DepoSalesRoyalty, DepoSpecialSalesRoyalty, 
				DepoContributionsRoyalty, SubscriptionSalesRoyalty, eclipsRoyalty, TotalRoyalty, statusID)
			SELECT @periodID, rd.orgcode, rd.DepoSalePCT, rd.DepoContribAMT, rd.SubSalePCT, rd.eclipsMonthAMT, rd.DepoSales, 
				rd.DepoSpecialSales, rd.DepoContributions, rd.SubscriptionSales, rd.DepoSalesRoyalty, rd.DepoSpecialSalesRoyalty, 
				rd.DepoContributionsRoyalty, rd.SubscriptionSalesRoyalty, rd.eclipsRoyalty, rd.TotalRoyalty, @TSRstatusReady
			FROM platformStatsMC.dbo.ts_MonthlyRoyaltyDetail as rd
			INNER JOIN membercentral.dbo.sites as s on s.siteCode = rd.orgCode
			where rd.runID = @TSRrunID;

			EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Royalty Statements Queue', @engine='MCLuceeLinux';
		END


		-- QUEUE THE ORG TS STATEMENTS FOR BILLING PERIOD
		DECLARE @TSAstatusReady int;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillTSA', @queueStatus='readyToProcess', @queueStatusID=@TSAstatusReady OUTPUT;

		IF OBJECT_ID('tempdb..#tmpOrgTSA') IS NOT NULL 
			DROP TABLE #tmpOrgTSA;
		CREATE TABLE #tmpOrgTSA (depomemberdataID int PRIMARY KEY, EOPBalance decimal(18,2), numInPeriod int);

		-- join to sites because the orgemail processing requires it be there
		INSERT INTO #tmpOrgTSA (depomemberdataID, EOPBalance, numInPeriod)
		select mcb.depoMemberDataID, SUM(t.amountBilled+t.salesTaxAmount) as EOPBalance,
			SUM(case when t.DatePurchased between @BPStartSDT and @BPEndSDT then 1 else 0 end) as numInPeriod
		from dbo.memberCentralBilling as mcb
		inner join dbo.depoTransactions as t on t.depoMemberDataID = mcb.depoMemberDataID
			and t.DatePurchased <= @BPEndSDT
		inner join membercentral.dbo.sites as s on s.siteCode = mcb.orgCode
		where mcb.depoMemberDataID is not null
		group by mcb.depoMemberDataID;

		-- we only want statements when either have activity in billing month OR have a non-zero balance at end of month
		DELETE FROM #tmpOrgTSA
		WHERE EOPBalance = 0
		AND numInPeriod = 0;

		insert into platformQueue.dbo.queue_monthBillTSA (depomemberDataID, TransStartDate, TransEndDate, StatementDate, billingPeriodID, isOrg, statusID)
		select depomemberdataID, @BPStartSDT, @BPEndSDT, @BPEndSDT, @periodID, 1, @TSAstatusReady
		from #tmpOrgTSA;

		IF OBJECT_ID('tempdb..#tmpOrgTSA') IS NOT NULL 
			DROP TABLE #tmpOrgTSA;

		EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Statements Queue', @engine='TSAdminLinux';


		--- QUEUE THE CHARGING OF ORG CREDIT CARDS FOR BILLING PERIOD
		DECLARE @CCstatusReady int;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='payTSBalance', @queueStatus='readyToProcess', @queueStatusID=@CCstatusReady OUTPUT;
		
		IF OBJECT_ID('tempdb..#tmpOrgCC') IS NOT NULL 
			DROP TABLE #tmpOrgCC;
		CREATE TABLE #tmpOrgCC (depomemberdataID int PRIMARY KEY, EOPBalance decimal(18,2), payProfileID int);

		-- get orgs with an outstanding balance EOP, set to C, and a non-declined TS card on file
		INSERT INTO #tmpOrgCC (depomemberdataID, EOPBalance, payProfileID)
		select mcb.depoMemberDataID, SUM(t.amountBilled+t.salesTaxAmount) as EOPBalance, TSCC.payProfileID
		from dbo.memberCentralBilling as mcb
		INNER JOIN dbo.depoMemberData as d on d.depomemberdataID = mcb.depomemberdataID
			and d.paymenttype = 'C'
		INNER JOIN dbo.ccMemberPaymentProfiles as TSCC on TSCC.depomemberdataID = mcb.depomemberdataID 
			and TSCC.orgcode = 'TS'
			and TSCC.declined = 0
		INNER JOIN dbo.depoTransactions as t on t.depoMemberDataID = mcb.depoMemberDataID
			and t.DatePurchased <= @BPEndSDT
		where mcb.depoMemberDataID is not null
		group by mcb.depoMemberDataID, TSCC.payProfileID
		having SUM(t.amountBilled+t.salesTaxAmount) > 0;

		INSERT INTO platformQueue.dbo.queue_payTSBalance (depoMemberDataID, payProfileID, paymentAmount, statusID)
		SELECT depoMemberDataID, payProfileID, EOPBalance, @CCstatusReady
		FROM #tmpOrgCC;

		IF OBJECT_ID('tempdb..#tmpOrgCC') IS NOT NULL 
			DROP TABLE #tmpOrgCC;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Pay TS Balance Queue', @engine='MCLuceeLinux';


		-- QUEUE THE EMAILING OF ORG STATEMENTS
		DECLARE @EmailOrgStatusWaiting int;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillEmailOrg', @queueStatus='waitingToProcess', @queueStatusID=@EmailOrgStatusWaiting OUTPUT;

		INSERT INTO platformQueue.dbo.queue_monthBillEmailOrg (billingPeriodID, siteCode, statusID)
		select distinct billingPeriodID, siteCode, @EmailOrgStatusWaiting
		from platformQueue.dbo.queue_monthBillSW
		where billingPeriodID = @periodID
			union
		select distinct q.billingPeriodID, mcb.orgcode, @EmailOrgStatusWaiting
		from platformQueue.dbo.queue_monthBillTSA as q
		inner join dbo.memberCentralBilling as mcb on mcb.depoMemberDataID = q.depoMemberDataID
		where q.billingPeriodID = @periodID
			union
		select distinct billingPeriodID, orgCode, @EmailOrgStatusWaiting
		from platformQueue.dbo.queue_monthBillTSRoyalty
		where billingPeriodID = @periodID;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Monthly Billing Email Queue', @engine='MCLuceeLinux';


		-- QUEUE THE TS STATEMENTS FOR BILLING PERIOD for INDIVIDUALS
		-- QUEUE THE EMAILING OF INDIV STATEMENTS
		DECLARE @TSAINDstatusReady int, @EmailIndivStatusWaiting int;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillTSA', @queueStatus='readyToProcess', @queueStatusID=@TSAINDstatusReady OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='monthBillEmailIndiv', @queueStatus='waitingToProcess', @queueStatusID=@EmailIndivStatusWaiting OUTPUT;
		
		IF OBJECT_ID('tempdb..#tmpIndivTSA') IS NOT NULL 
			DROP TABLE #tmpIndivTSA;
		CREATE TABLE #tmpIndivTSA (depomemberdataID int PRIMARY KEY, paymentType char(1), EOPBalance decimal(18,2), 
			nonSWActivity int, email1 varchar(400), email2 varchar(400), firmPlanMasterDepoID int);

		-- nonSWActivity = if acctcode is not 7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012 (SW codes) and not 1000 ($0 basic plan fees that all sw accts have)
		INSERT INTO #tmpIndivTSA (depomemberdataID, paymentType, EOPBalance, nonSWActivity, email1, email2, firmPlanMasterDepoID)
		select d.depomemberdataID, d.paymenttype, ISNULL(SUM(t.amountBilled+t.salesTaxAmount),0) as EOPBalance, 
			SUM(case when t.DatePurchased between @BPStartSDT and @BPEndSDT and t.accountCode not in ('1000','7000','7001','7002','7003','7004','7005','7006','7007','7008','7009','7010','7011','7012') then 1 else 0 end) as nonSWActivity,
			case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as email1,
			nullIF(d.BillingContactEmail,'') as email2,
			(
				select fplMaster.depoMemberDataID
				from dbo.tlaFirmPlanLink as fpl
				INNER JOIN dbo.tlaFirmPlanLink AS fplMaster ON fplMaster.firmPlanID = fpl.firmPlanID
					and fplMaster.isMaster = 1
				where fpl.depoMemberDataID = d.depomemberdataID
				and fpl.isMaster = 0
			) as firmPlanMasterDepoID
		from dbo.depomemberdata as d
		INNER JOIN dbo.depoTransactions as t on t.depoMemberDataID = d.depoMemberDataID
			and t.DatePurchased <= @BPEndSDT
		LEFT OUTER JOIN dbo.memberCentralBilling as mcb on mcb.depoMemberDataID = d.depomemberdataID
		where mcb.orgCode is null
		group by d.depomemberdataID, d.paymenttype, d.Email, d.BillingContactEmail;

		-- When Invoiced, we only want the ones with a EOP balance or non-swactivity in period and 0 balance
		-- When CreditCard, we only want the ones with non-swactivity in period and 0 balance
		delete from #tmpIndivTSA
		where (paymentType = 'I' AND NOT (EOPBalance > 0 OR (nonSWActivity > 0 AND EOPBalance = 0)))
		OR (paymentType = 'C' AND NOT (nonSWActivity > 0 AND EOPBalance = 0));

		-- delete the non-firm plans that dont have an email
		delete from #tmpIndivTSA
		where firmPlanMasterDepoID is null
		and email1 is null
		and email2 is null;

		-- add in the firm plan masters
		INSERT INTO #tmpIndivTSA (depomemberdataID, paymentType, EOPBalance, nonSWActivity, email1, email2, firmPlanMasterDepoID)
		select fpm.depomemberDataID, 'I', 0, 0, fpm.email1, fpm.email2, null
		from (
			select distinct d.depomemberdataID,  
				case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as email1,
				nullIF(d.BillingContactEmail,'') as email2
			from #tmpIndivTSA as tmp
			inner join dbo.depomemberdata as d on d.depomemberdataID = tmp.firmPlanMasterDepoID
			where tmp.firmPlanMasterDepoID is not null
		) as fpm
		where (fpm.email1 is not null OR fpm.email2 is not null)
		and NOT EXISTS (select top 1 depomemberdataID from #tmpIndivTSA where depomemberdataID = fpm.depomemberdataID);

		-- delete the firm plan members since the master may have been added
		delete from #tmpIndivTSA
		where firmPlanMasterDepoID is not null;

		insert into platformQueue.dbo.queue_monthBillTSA (depomemberDataID, TransStartDate, TransEndDate, StatementDate, billingPeriodID, isOrg, statusID)
			OUTPUT INSERTED.billingPeriodID, INSERTED.depoMemberDataID, @EmailIndivStatusWaiting
			INTO platformQueue.dbo.queue_monthBillEmailIndiv (billingPeriodID, depoMemberDataID, statusID)
		select depomemberdataID, @BPStartSDT, @BPEndSDT, @BPEndSDT, @periodID, 0, @TSAINDstatusReady
		from #tmpIndivTSA;

		IF OBJECT_ID('tempdb..#tmpIndivTSA') IS NOT NULL 
			DROP TABLE #tmpIndivTSA;

		EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Statements Queue', @engine='TSAdminLinux';
		EXEC membercentral.dbo.sched_resumeTask @name='Process Monthly Billing Email Queue', @engine='MCLuceeLinux';
	COMMIT TRAN;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.up_autoPayOutstandingBalances
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    declare @now datetime = GETDATE(), @statusReady int, 
		@applicationTypeID int = membercentral.dbo.fn_getApplicationTypeIDFromName('BuyNow'),
		@siteID int = membercentral.dbo.fn_getSiteIDFromSiteCode('MC'), @settingsXML xml;
	SET @itemCount = 0;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='payTSBalance', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	SELECT @settingsXML = settingsXML
	FROM membercentral.dbo.cms_applicationTypeSettings
	WHERE siteID = @siteID
	AND applicationTypeID = @applicationTypeID;

	SELECT @settingsXML = ISNULL(@settingsXML,'<settings />');

	-- get the depomembers tied to orgs -- we wont run these
	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	CREATE TABLE #tmpOrgs (DepoMemberDataID int PRIMARY KEY);

	insert into #tmpOrgs (DepoMemberDataID)
	select distinct DepoMemberDataID 
	from dbo.membercentralBilling 
	where DepoMemberDataID is not null;


	-- get non-org depomembers with an outstanding balance, set to C, and has a non-declined card on file
	IF OBJECT_ID('tempdb..#tmpDepos') IS NOT NULL 
		DROP TABLE #tmpDepos;
	CREATE TABLE #tmpDepos (depoMemberDataID int PRIMARY KEY, payProfileID int, totalDue decimal(18,2));

	INSERT INTO #tmpDepos (depoMemberDataID, payProfileID, totalDue)
	select d.depomemberdataID, TSCC.payProfileID, SUM(t.AmountBilled + t.salesTaxAmount) AS TotalDue
	from dbo.depoMemberData as d
	inner join dbo.depoTransactions as t on t.depomemberdataID = d.depomemberdataID
	INNER JOIN dbo.ccMemberPaymentProfiles as TSCC on TSCC.depomemberdataID = d.depomemberdataID 
		and TSCC.orgcode = 'TS'
		and TSCC.declined = 0
	where d.paymentType = 'C'
	AND NOT EXISTS (select depoMemberDataID from #tmpOrgs where depomemberdataID = d.depomemberdataID)
	AND NOT EXISTS (select depoMemberDataID from platformQueue.dbo.queue_payTSBalance where depomemberdataID = d.depomemberdataID)
	group by d.depomemberdataID, TSCC.payProfileID
	HAVING (SUM(t.AmountBilled + t.salesTaxAmount) > 0);
	
	IF @@ROWCOUNT > 0 BEGIN
		INSERT INTO platformQueue.dbo.queue_payTSBalance (depoMemberDataID, payProfileID, paymentAmount, statusID, dateAdded, dateUpdated)
		SELECT depoMemberDataID, payProfileID, totalDue, @statusReady, @now, @now
		FROM #tmpDepos;
		SET @itemCount = @@ROWCOUNT;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Pay TS Balance Queue', @engine='MCLuceeLinux';
	END

	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	IF OBJECT_ID('tempdb..#tmpDepos') IS NOT NULL 
		DROP TABLE #tmpDepos;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_queuePurchaseCreditNotifications
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	CREATE TABLE #tmpCreditUsedDepoMembers (depomemberdataID int);
	CREATE TABLE #tmpDepoMembers (depomemberdataID int PRIMARY KEY);

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	SET @itemCount = 0;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='TSPurchaseCreditsNotify', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	-- get depoMembers whose credits were used in the previous week
	INSERT INTO #tmpCreditUsedDepoMembers
	SELECT DISTINCT depomemberdataID
	FROM dbo.PurchaseCredits
	WHERE CreditDate BETWEEN DATEADD(DAY,-7,@nowDate) AND @nowDate
	AND (PurchaseCreditAmount <= 0 OR AmazonBucksCreditAmount <= 0);

	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- DepoCredits
	INSERT INTO #tmpDepoMembers
	SELECT DISTINCT pc.depomemberdataID
	FROM dbo.PurchaseCredits AS pc
	INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
	GROUP BY pc.depomemberdataID
	HAVING SUM(pc.purchaseCreditAmount) <= 0
		INTERSECT
	SELECT DISTINCT pc.depomemberdataID
	FROM dbo.PurchaseCredits AS pc
	INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
	WHERE pc.purchaseCreditAmount > 0;

	-- AmazonCredits
	INSERT INTO #tmpDepoMembers
	SELECT depomemberdataID
	FROM (
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		GROUP BY pc.depomemberdataID
		HAVING SUM(pc.AmazonBucksCreditAmount) <= 0
			INTERSECT
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		WHERE pc.AmazonBucksCreditAmount > 0
	) tmp
		EXCEPT
	SELECT depomemberdataID FROM #tmpDepoMembers;

	-- populate queue
	INSERT INTO platformQueue.dbo.queue_TSPurchaseCreditsNotify (depomemberdataID, FirstName, LastName, Email, statusID, dateAdded, dateUpdated)
	SELECT d.depomemberdataID, d.FirstName, d.LastName, d.Email, @statusReady, GETDATE(), GETDATE()
	FROM #tmpDepoMembers AS tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID
	LEFT OUTER JOIN platformQueue.dbo.queue_TSPurchaseCreditsNotify AS qi ON qi.depomemberdataID = tmp.depomemberdataID
	WHERE d.Email <> ''
	AND d.optOutTSMarketing = 0
	AND qi.itemID IS NULL;
	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='TrialSmith Purchase Credits Notification', @engine='MCLuceeLinux';

	on_done:
	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_queueDepoDocumentReport
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	CREATE TABLE #tmpDepoMembers (depomemberdataID int PRIMARY KEY);

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	SET @itemCount = 0;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='TSDepoDocumentReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	-- get depoMembers whose depositions were approved in the previous week
	INSERT INTO #tmpDepoMembers
	SELECT DISTINCT doc.DepomemberdataID 
	FROM dbo.depoDocuments doc 
	INNER JOIN dbo.depoDocumentStatusHistory docSH ON docSH.documentID = doc.DocumentID
	INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = docSH.statusID
		AND ds.statusName = 'Approved'
	WHERE docSH.dateEntered BETWEEN DATEADD(DAY,-7,@nowDate) and @nowDate;
	
	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- populate queue
	INSERT INTO platformQueue.dbo.queue_TSDepoDocumentReport (depomemberdataID, FirstName, LastName, Email, statusID, dateAdded, dateUpdated)
	SELECT d.depomemberdataID, d.FirstName, d.LastName, d.Email, @statusReady, GETDATE(), GETDATE()
	FROM #tmpDepoMembers AS tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID
	LEFT OUTER JOIN platformQueue.dbo.queue_TSDepoDocumentReport AS qi ON qi.depomemberdataID = tmp.depomemberdataID
	WHERE d.Email <> ''
	AND d.optOutTSMarketing = 0
	AND qi.itemID IS NULL;
	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='TrialSmith Depo Document report', @engine='MCLuceeLinux';

	on_done:
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_queueDepoDocumentBuyReport 
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoCreditMembers') IS NOT NULL 
		DROP TABLE #tmpDepoCreditMembers;
	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	CREATE TABLE #tmpDepoMembers (depomemberdataID int PRIMARY KEY);
	CREATE TABLE #tmpDepoCreditMembers (depomemberdataID int PRIMARY KEY);
	CREATE TABLE #tmpCreditUsedDepoMembers (depomemberdataID int);

	DECLARE @statusReady int, @uploadedStatusID int, @nowDate datetime = GETDATE(), 
		@7Daysago datetime = DATEADD(DAY,-7,GETDATE()), @14Daysago datetime = DATEADD(DAY,-14,GETDATE()), @180Daysago datetime = DATEADD(DAY,-180,GETDATE());
	SET @itemCount = 0;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='TSDepoDocumentBuyReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	SELECT @uploadedStatusID = statusID FROM dbo.depoDocumentStatuses WHERE statusName = 'Uploaded';

	/*
	This needs to get all depoMembers who purchased depositions in the previous week, 
	where the sum of the amount billed was > $0, 
	and who also did not contribute any documents. 
	and the lastTSDepoDocumentBuyReportSent is older than 180 days (or it is null).
	*/
	INSERT INTO #tmpDepoMembers
	SELECT tmp.depoMemberDataID
	FROM (
		SELECT depoMemberDataID 
		FROM dbo.depoTransactions
		WHERE accountCode = '3000' 
		AND DatePurchased BETWEEN @7Daysago AND @nowDate 
		GROUP BY depoMemberDataID 
		HAVING SUM(AmountBilled) > 0 
			EXCEPT
		SELECT DISTINCT doc.DepomemberdataID 
		FROM dbo.depoDocuments doc 
		INNER JOIN dbo.depoDocumentStatusHistory docSH ON docSH.documentID = doc.DocumentID 
			AND docSH.statusID = @uploadedStatusID
			AND docSH.dateEntered BETWEEN @7Daysago AND @nowDate
	) as tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID 
	WHERE d.Email is not null
	AND d.Email <> ''
	AND d.optOutTSMarketing = 0
	AND (d.lastTSDepoDocumentBuyReportSent is null OR lastTSDepoDocumentBuyReportSent < @180Daysago);

	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- get depoMembers whose credits were used in the previous week
	INSERT INTO #tmpCreditUsedDepoMembers
	SELECT DISTINCT depomemberdataID
	FROM dbo.PurchaseCredits
	WHERE CreditDate BETWEEN @14Daysago AND @nowDate
	AND (PurchaseCreditAmount <= 0 OR AmazonBucksCreditAmount <= 0);

	IF @@ROWCOUNT > 0 BEGIN
		-- DepoCredits
		INSERT INTO #tmpDepoCreditMembers
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		GROUP BY pc.depomemberdataID
		HAVING SUM(pc.purchaseCreditAmount) <= 0
			INTERSECT
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		WHERE pc.purchaseCreditAmount > 0;

		-- AmazonCredits
		INSERT INTO #tmpDepoCreditMembers
		SELECT depomemberdataID
		FROM (
			SELECT DISTINCT pc.depomemberdataID
			FROM dbo.PurchaseCredits AS pc
			INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
			GROUP BY pc.depomemberdataID
			HAVING SUM(pc.AmazonBucksCreditAmount) <= 0
				INTERSECT
			SELECT DISTINCT pc.depomemberdataID
			FROM dbo.PurchaseCredits AS pc
			INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
			WHERE pc.AmazonBucksCreditAmount > 0
		) tmp
			EXCEPT
		SELECT depomemberdataID FROM #tmpDepoCreditMembers;
	END

    IF NOT EXISTS (
        SELECT TOP 1 tmp.*
        FROM #tmpDepoMembers AS tmp
        LEFT OUTER JOIN #tmpDepoCreditMembers AS tmpc ON tmpc.depomemberdataID = tmp.depomemberdataID
        WHERE tmpc.depomemberdataID IS NULL)
    GOTO on_done;

	-- populate queue
	INSERT INTO platformQueue.dbo.queue_TSDepoDocumentBuyReport (depomemberdataID, FirstName, LastName, Email, statusID, dateAdded, dateUpdated)
	SELECT d.depomemberdataID, d.FirstName, d.LastName, d.Email, @statusReady, @nowDate, @nowDate
	FROM #tmpDepoMembers AS tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID
    LEFT OUTER JOIN #tmpDepoCreditMembers AS tmpc ON tmpc.depomemberdataID = tmp.depomemberdataID
	LEFT OUTER JOIN platformQueue.dbo.queue_TSDepoDocumentBuyReport AS qi ON qi.depomemberdataID = tmp.depomemberdataID
	WHERE tmpc.depomemberdataID IS NULL AND qi.itemID IS NULL;
	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='TrialSmith Deposition Purchasers Report', @engine='MCLuceeLinux';

	on_done:
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoCreditMembers') IS NOT NULL 
		DROP TABLE #tmpDepoCreditMembers;
	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_importDepoDocumentFromFS2Queue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @doneStatusID int, 
		@documentVersionID int, @depoMemberDataID int, @docOrgCode varchar(10), @fileExt varchar(20), 
		@s3prefix varchar(30), @depoDocumentID int, @MCDocumentObjectkey varchar(200), @docAttachReadyStatusID int,
		@TSDocumentObjectkey varchar(200), @nowDate datetime = GETDATE(), @s3CopyReadyStatusID int,
		@contributeDate date; 

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='fileShare2toDepoDocs', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='depoDocumentsAttach', @queueStatus='readyToProcess', @queueStatusID=@docAttachReadyStatusID OUTPUT;
	
	-- get info from queue item
	select @documentVersionID=documentVersionID, @depoMemberDataID=depoMemberDataID, @docOrgCode=docOrgCode, @fileExt=fileExt,
		@s3prefix=s3prefix, @contributeDate=contributeDate
	from platformQueue.dbo.queue_fileShare2toDepoDocs
	where itemID = @itemID
	and statusID = @readyToProcessStatusID
	and fileExt not in ('zip','rar');

	IF @documentVersionID IS NULL
		goto on_done;

	update platformQueue.dbo.queue_fileShare2toDepoDocs
	set statusID = @processingStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	SET @MCDocumentObjectkey = lower(@s3prefix + right('0000' + cast(@documentVersionID % 1000 as varchar(4)),4) + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
	
	BEGIN TRAN;
		EXEC dbo.ts_addDepoDocument @depomemberdataID=@depoMemberDataID, @docOrgCode=@docOrgCode, @originalExt=@fileExt, 
			@contributeDate=@contributeDate, @DepoAmazonBucks=0, @DepoAmazonBucksFullName=null, @DepoAmazonBucksEmail=null, 
			@enteredByDepomemberdataID=@depoMemberDataID, @documentID=@depoDocumentID OUTPUT;

		SET @TSDocumentObjectkey = lower('depos/original/' + right('0000' + cast(@depoDocumentID % 1000 as varchar(4)),4) + '/' + cast(@depoDocumentID as varchar(10)) + '.' + @fileExt);

		-- we have to just do the s3copy here since queue_depoDocumentsAttach requires the file be downloaded to mcfile01 already.
		-- and we cant just trigger reprocessing attachments because the file isnt in depos/original yet.
		-- so we will copy to depos/original, and use the lambda notifier to trigger reprocessing of the pdf
		INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
		VALUES ('membercentralcdn', @MCDocumentObjectkey, 'trialsmith-depos', @TSDocumentObjectkey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
	COMMIT TRAN;
	
	update platformQueue.dbo.queue_fileShare2toDepoDocs
	set statusID = @doneStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	delete from platformQueue.dbo.queue_fileShare2toDepoDocs
	where itemID = @itemID;
	
	on_done:
	RETURN 0;
	
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_forwardIncomingDepoConnectMessage
@receiverParticipantID int,
@senderParticipantID int,
@emailContent varchar(max),
@messageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @siteID int, @messageTypeID int, @adminToolSiteResourceID int,@newContentID int, @newResourceID int,
		@resourceTypeID int, @nowDate datetime = getdate(), @contentTitle varchar(250), @contentVersionID int,
		@conversationSubject varchar(400), @languageID int, @fromEmail varchar(400), @sentOnBehalfOfName varchar(200), @fromFirm varchar(200),
		@fromNameAndFirm varchar(200), @messageStatusIDInserting int, @tier varchar(12),
		@defaultMarketingSubUserID int, @replyToEmail varchar(200), @emailTypeID int, @fieldID int,
		@conversationID int, @receiverRoleCode varchar(20), @approvedByDepoMemberDataID int, @senderRoleCode varchar(20),
		@sentOnBehalfOfParticipantID int, @onBehalfOfReceiverDepoMemberID int, @onBehalfOfReceiverMemberID int,
		@delegateName varchar(200), @requestorName varchar(200), @respondentName varchar(200), @orgSysMemberID int,
		@itemGroupUID uniqueidentifier, @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int,
		@inquiryOptOutListID int, @globalOptOutListID int, @messageLogEmail varchar(100) = '<EMAIL>', @consentListIDs VARCHAR(MAX);

	DECLARE @tblRecipientRoles TABLE (roleCode varchar(20));

	IF OBJECT_ID('tempdb..#tmpRecipientEmails') IS NOT NULL
		DROP TABLE #tmpRecipientEmails;
	IF OBJECT_ID('tempdb..#tmpRecipientsAdded') IS NOT NULL
		DROP TABLE #tmpRecipientsAdded;
	CREATE TABLE #tmpRecipientEmails (email varchar(200), recipientName varchar(200));
	CREATE TABLE #tmpRecipientsAdded (recipientID INT, itemUID UNIQUEIDENTIFIER DEFAULT(NEWID()));

	SET @resourceTypeID = membercentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SET @languageID = membercentral.dbo.fn_getLanguageID('en');
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = 'TS';
	SELECT @orgSysMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);
	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'EXPERTINQUIRY';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	SELECT @tier = tier FROM membercentral.dbo.fn_getServerSettings();
	SELECT @defaultMarketingSubUserID = defaultMarketingSubUserID FROM membercentral.dbo.platform_environments WHERE environmentName = @tier;
	SELECT @fromEmail = 'noreply@' + sendingHostName FROM platformMail.dbo.sendgrid_subuserDomains WHERE subuserID = @defaultMarketingSubUserID;
	SELECT @emailTypeID = emailTypeID FROM membercentral.dbo.ams_memberEmailTypes WHERE orgID = @orgID AND emailTypeOrder = 1;

	SELECT @adminToolSiteResourceID = ast.siteResourceID
	FROM membercentral.dbo.admin_siteTools ast
	INNER JOIN membercentral.dbo.admin_toolTypes att ON att.tooltypeID = ast.toolTypeID
 		AND att.toolType = 'TrialSmithTools'
	WHERE ast.siteID = @siteID;

	SELECT @inquiryOptOutListID = ISNULL(optOutListID,0)
	FROM dbo.expertConnectInquirySettings;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID
		AND modeName = 'GlobalOptOut';

	DECLARE @initialQueuePriority int, @expectedRecipientCount int;
	SELECT @expectedRecipientCount = COUNT(*) FROM #tmpRecipientEmails;
	SELECT @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	SELECT @conversationID =  p.conversationID, @receiverRoleCode = r.roleCode
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	WHERE p.participantID = @receiverParticipantID;

	IF @receiverRoleCode IN ('Requestor','RequestorDelegate')
		INSERT INTO @tblRecipientRoles (roleCode)
		VALUES('Requestor'), ('RequestorDelegate');
	ELSE
		INSERT INTO @tblRecipientRoles (roleCode)
		VALUES('Respondent'), ('RespondentDelegate');
	
	-- get recipient emails
	INSERT INTO #tmpRecipientEmails (email, recipientName)
	SELECT email, LTRIM(RTRIM(ISNULL(p.firstName,'') + ' ' + ISNULL(p.lastName,'')))
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	INNER JOIN @tblRecipientRoles AS tmp ON tmp.roleCode = r.roleCode
	WHERE p.conversationID = @conversationID;

	-- add cc recipient
	INSERT INTO #tmpRecipientEmails (email, recipientName)
	VALUES(@messageLogEmail, 'DepoConnect Message Logs');

	DELETE tmp
	FROM #tmpRecipientEmails AS tmp
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID IN (@inquiryOptOutListID, @globalOptOutListID)
		AND clm.email = tmp.email;

	IF NOT EXISTS (SELECT 1 FROM #tmpRecipientEmails)
		GOTO on_done;

	-- get memberID for recipients
	IF @receiverRoleCode IN ('Requestor','Respondent')
		SELECT @onBehalfOfReceiverDepoMemberID = depoMemberDataID
		FROM dbo.expertConnectInquiryConversationParticipants
		WHERE participantID = @receiverParticipantID;
	ELSE BEGIN
		SELECT @onBehalfOfReceiverDepoMemberID = p.depoMemberDataID
		FROM dbo.expertConnectInquiryConversationParticipants AS p
		INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			AND r.roleCode = CASE
				WHEN @receiverRoleCode = 'RequestorDelegate' THEN 'Requestor'
				ELSE 'Respondent'
			END
		WHERE p.conversationID = @conversationID;
	END

	EXEC membercentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode='TS',
		@depomemberdataid=@onBehalfOfReceiverDepoMemberID, @memberID=@onBehalfOfReceiverMemberID OUTPUT;

	IF @onBehalfOfReceiverMemberID = 0
		SET @onBehalfOfReceiverMemberID = @orgSysMemberID;
	
	-- prep contentTitle, conversationSubject, fromName & replyToEmail
	SELECT @senderRoleCode = r.roleCode
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	WHERE p.participantID = @senderParticipantID;

	IF @senderRoleCode IN ('Requestor','Respondent')
		SET @sentOnBehalfOfParticipantID = @senderParticipantID;
	ELSE BEGIN
		SELECT @sentOnBehalfOfParticipantID = p.participantID
		FROM dbo.expertConnectInquiryConversationParticipants AS p
		INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			AND r.roleCode = CASE
				WHEN @senderRoleCode = 'RequestorDelegate' THEN 'Requestor'
				ELSE 'Respondent'
			END
		WHERE p.conversationID = @conversationID;
	END

	SELECT @contentTitle = 'Re: ' + LTRIM(RTRIM(ISNULL(i.expertFirstName,'') + ' ' + ISNULL(i.expertLastName,''))) + ' - ' + i.topic,
		@delegateName = CASE
				WHEN @sentOnBehalfOfParticipantID <> @senderParticipantID
					THEN (
						SELECT LTRIM(RTRIM(ISNULL(p2.firstName,'') + ' ' + ISNULL(p2.lastName,'')))
						FROM dbo.expertConnectInquiryConversationParticipants AS p2
						WHERE p2.participantID = @senderParticipantID
					)
				ELSE ''
			END,
		@sentOnBehalfOfName = LTRIM(RTRIM(ISNULL(p.firstName,'') + ' ' + ISNULL(p.lastName,''))),
		@fromFirm = CASE WHEN r.roleCode = 'Requestor' THEN i.companyName ELSE d.BillingFirm END
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	INNER JOIN dbo.depomemberdata AS d ON d.depoMemberDataID = p.depoMemberDataID
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = p.inquiryID
	WHERE p.participantID = @sentOnBehalfOfParticipantID;

	SET @fromNameAndFirm = @sentOnBehalfOfName + ' - ' + @fromFirm;
	IF LEN(@delegateName) > 0
		SET @fromNameAndFirm = @delegateName + ' on behalf of ' + @fromNameAndFirm;

	SELECT @requestorName = req.firstName + ' ' + req.lastName,
		@respondentName = resp.firstName + ' ' + resp.lastName,
		@replyToEmail = i.emailAddressSlug + '_' + CAST(i.inquiryID as varchar(10)) + '_' +
			CASE 
				WHEN @senderRoleCode IN ('Requestor','RequestorDelegate') THEN CAST(req.participantID as varchar(10))
				ELSE CAST(resp.participantID as varchar(10))
			END + '@depoconnect.trialsmith.com'
	FROM dbo.expertConnectInquiryConversations c
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = c.inquiryID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS req
		INNER JOIN dbo.expertConnectInquiryRoles AS r1 ON r1.roleID = req.roleID
			AND r1.roleCode = 'Requestor'
		INNER JOIN dbo.depomemberdata AS d1 ON d1.depoMemberDataID = req.depoMemberDataID
		ON req.conversationID = c.conversationID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS resp
		INNER JOIN dbo.expertConnectInquiryRoles AS r2 ON r2.roleID = resp.roleID
			AND r2.roleCode = 'Respondent'
		INNER JOIN dbo.depomemberdata AS d2 ON d2.depoMemberDataID = resp.depoMemberDataID
		ON resp.conversationID = c.conversationID
	WHERE c.conversationID = @conversationID;

	SET @emailContent = REPLACE(@emailContent,'[[replyToEmailAddress]]',@replyToEmail);

	SET @conversationSubject = @contentTitle + ' (' + @requestorName + ' and ' + @respondentName + ')';

	BEGIN TRAN;

		EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID,
			@parentSiteResourceID=@adminToolSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
			@languageID=@languageID, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@emailContent,
			@memberID=@orgSysMemberID, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;

		SELECT TOP 1 @contentVersionID = cv.contentVersionID
		FROM membercentral.dbo.cms_content AS c 
		INNER JOIN membercentral.dbo.cms_contentLanguages AS cl ON c.contentID = cl.contentID
			AND cl.languageID = @languageID
		INNER JOIN membercentral.dbo.cms_contentVersions AS cv ON cv.contentLanguageID = cl.contentLanguageID
			AND cv.isActive = 1
		WHERE c.contentID = @newContentID;

		-- get consentListIDS
		SELECT @consentListIDs = cast(NULLIF(@inquiryOptOutListID,0) as varchar(10));

		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, @orgIdentityID=NULL,
			@sendingSiteResourceID=@adminToolSiteResourceID, @isTestMessage=0, @sendOnDate=@nowDate, @recordedByMemberID=@orgSysMemberID,
			@fromName=@fromNameAndFirm, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='',  @subject=@conversationSubject,
			@contentVersionID=@contentVersionID, @messageWrapper='', @referenceType='EXPERTINQUIRY_REPLY',
			@referenceID=@conversationID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

		-- add recipients
		INSERT INTO platformMail.dbo.email_messageRecipientHistory(messageID, memberID, dateLastUpdated,
			toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
			OUTPUT INSERTED.recipientID
			INTO #tmpRecipientsAdded (recipientID)
		SELECT @messageID, CASE WHEN email = @messageLogEmail THEN @orgSysMemberID ELSE @onBehalfOfReceiverMemberID END,
			@nowDate, recipientName, email,
			@messageStatusIDInserting, NULL, NULL, @emailTypeID, @siteID, @initialQueuePriority
		FROM #tmpRecipientEmails;

		EXEC platformMail.dbo.email_insertMetadataField @fieldName='emailOptOutURL', @isMergeField=1, @fieldID=@fieldID OUTPUT;

		SET @itemGroupUID = NEWID();
		EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

		-- queue recipient details with extended merge code [[emailOptOutURL]]
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		SELECT itemUID, @insertingQueueStatusID
		FROM #tmpRecipientsAdded;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, tmp.recipientID
		FROM #tmpRecipientsAdded AS tmp
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCRecipientID'
			UNION
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, @messageID
		FROM #tmpRecipientsAdded AS tmp
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCMessageID';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID
		FROM platformMail.dbo.email_metadataFields AS mdf
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCExtMergeCodeFieldID'
		CROSS JOIN #tmpRecipientsAdded AS tmp
		WHERE mdf.fieldName = 'emailOptOutURL';

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = GETDATE()
		FROM platformQueue.dbo.tblQueueItems AS qi
		INNER JOIN #tmpRecipientsAdded AS tmp ON tmp.itemUID = qi.itemUID;

	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpRecipientEmails') IS NOT NULL
		DROP TABLE #tmpRecipientEmails;
	IF OBJECT_ID('tempdb..#tmpRecipientsAdded') IS NOT NULL
		DROP TABLE #tmpRecipientsAdded;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_addDepoDocumentToUnzipQueue
@pathToZip varchar(400),
@depomemberdataID int,
@docState varchar(10),
@DepoAmazonBucks bit = 0,
@DepoAmazonBucksFullName varchar(550),
@DepoAmazonBucksEmail varchar(255),
@uploadSourceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='depoDocumentsUnzip', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_depoDocumentsUnzip WHERE pathToZip = @pathToZip) BEGIN
		INSERT INTO platformQueue.dbo.queue_depoDocumentsUnzip (pathToZip, depomemberdataID, State, DepoAmazonBucks, 
			DepoAmazonBucksFullName, DepoAmazonBucksEmail, dateAdded, dateUpdated, statusID, uploadSourceID)
		VALUES (@pathToZip, @depomemberdataID, @docState, @DepoAmazonBucks, @DepoAmazonBucksFullName, 
			@DepoAmazonBucksEmail, @nowDate, @nowDate, @statusReady, @uploadSourceID);

		EXEC membercentral.dbo.sched_resumeTask @name='Process Depo Documents Unzip Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_addDepoDocumentToPreApproveQueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='processPreApproveDepoDocs', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_processPreApproveDepoDocs WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO platformQueue.dbo.queue_processPreApproveDepoDocs (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process Pre Approve Depo Document Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_addDepoDocumentToAttachQueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='depoDocumentsAttach', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_depoDocumentsAttach WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO platformQueue.dbo.queue_depoDocumentsAttach (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process Depo Document Attachments', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use seminarWeb
GO

ALTER PROC dbo.swod_importSeminars_validate
@orgCode varchar(10), 
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID int, @siteID int, @mincol varchar(255), @good bit, @dynSQL nvarchar(max);
	DECLARE @tblCols TABLE (columnName varchar(255) PRIMARY KEY);

	SET @participantID = dbo.fn_getParticipantIDFromOrgCode(@orgCode);
	SET @siteID = memberCentral.dbo.fn_getSiteIDFromSiteCode(@orgcode);
	SET @importResult = null;

	-- ***********
	-- clean table 
	-- ***********
	BEGIN TRY
		-- delete empty rows
		DELETE FROM #mc_SWODImport WHERE SeminarTitle IS NULL AND [Description] IS NULL;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to clean import table.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	IF NOT EXISTS (SELECT 1 FROM #mc_SWODImport) BEGIN
		INSERT INTO #tblSWODErrors (msg) VALUES('No data rows found.')
		GOTO on_done;
	END

	-- ****************
	-- required columns 
	-- ****************
	BEGIN TRY
		-- no blank SeminarTitle
		UPDATE #mc_SWODImport SET SeminarTitle = '' WHERE SeminarTitle IS NULL;

		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing SeminarTitle.'
		FROM #mc_SWODImport
		WHERE SeminarTitle = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- SeminarTitles must be at or under 250 chars
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid SeminarTitle. SeminarTitle must be 250 characters or less.'
		FROM #mc_SWODImport
		WHERE len(SeminarTitle) > 250 
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no blank Description
		UPDATE #mc_SWODImport SET [Description] = '' WHERE [Description] IS NULL;

		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing Description.'
		FROM #mc_SWODImport
		WHERE [Description] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- ensure OrigPublished is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN OrigPublished datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('The column OrigPublished contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null OrigPublished
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN OrigPublished datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required OrigPublished.'
			FROM #mc_SWODImport
			WHERE OrigPublished IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- no blank PlayerMode
		UPDATE #mc_SWODImport SET PlayerMode = '' WHERE PlayerMode IS NULL;

		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing PlayerMode.'
		FROM #mc_SWODImport
		WHERE PlayerMode = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		UPDATE tmp 
		set tmp.MCLayoutID = l.layoutID
		from dbo.tblSeminarsSWODLayouts as l
		inner join #mc_SWODImport as tmp on isnull(tmp.PlayerMode,'') = l.layout;

		-- check for missing player mode
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN MCLayoutID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + isnull(PlayerMode,'') + ' does not match an existing player mode.'
			FROM #mc_SWODImport
			WHERE MCLayoutID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- no blank PlayerQATab
		UPDATE #mc_SWODImport set [PlayerQATab] = '' where [PlayerQATab] is null;
		UPDATE #mc_SWODImport set [PlayerQATab] = '1' where [PlayerQATab] in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set [PlayerQATab] = '0' where [PlayerQATab] in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the PlayerQATab column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE [PlayerQATab] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN [PlayerQATab] bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the PlayerQATab column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank BlankPlayer
		UPDATE #mc_SWODImport set [BlankPlayer] = '' where [BlankPlayer] is null;
		UPDATE #mc_SWODImport set [BlankPlayer] = '1' where [BlankPlayer] in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set [BlankPlayer] = '0' where [BlankPlayer] in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the BlankPlayer column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE [BlankPlayer] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN [BlankPlayer] bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the BlankPlayer column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank CompleteTime
		UPDATE #mc_SWODImport set CompleteTime = '' where CompleteTime is null;
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the CompleteTime column. This column supports only whole numbers.'
		FROM #mc_SWODImport
		WHERE CompleteTime = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN CompleteTime INT NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the CompleteTime. This column supports only whole numbers.');

			GOTO on_done;
		END CATCH

		-- no blank Certificate
		UPDATE #mc_SWODImport set [Certificate] = '' where [Certificate] is null;
		UPDATE #mc_SWODImport set [Certificate] = '1' where [Certificate] in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set [Certificate] = '0' where [Certificate] in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the Certificate column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE [Certificate] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN [Certificate] bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the Certificate column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank SellInCatalog
		UPDATE #mc_SWODImport set SellInCatalog = '' where SellInCatalog is null;
		UPDATE #mc_SWODImport set SellInCatalog = '1' where SellInCatalog in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set SellInCatalog = '0' where SellInCatalog in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the SellInCatalog column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN SellInCatalog bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the SellInCatalog column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		UPDATE #mc_SWODImport set SellInCatalog = '' where SellInCatalog is null;

		-- ensure StartSale is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN StartSale datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('The column StartSale contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null StartSale when SellInCatalog = 1
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required StartSale.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = 1
		AND StartSale IS NULL
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- ensure EndSale is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN EndSale datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('The column EndSale contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null EndSale when SellInCatalog = 1
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EndSale.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = 1
		AND EndSale IS NULL
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- check dates
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a StartSale after the EndSale.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = 1
		AND StartSale > EndSale
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no blank Credit
		UPDATE #mc_SWODImport set Credit = '' where Credit is null;
		UPDATE #mc_SWODImport set Credit = '1' where Credit in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set Credit = '0' where Credit in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the Credit column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE Credit = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN Credit bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the Credit column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank AutoCreateTitle
		UPDATE #mc_SWODImport set AutoCreateTitle = '' where AutoCreateTitle is null;
		UPDATE #mc_SWODImport set AutoCreateTitle = '1' where AutoCreateTitle in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set AutoCreateTitle = '0' where AutoCreateTitle in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the AutoCreateTitle column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE AutoCreateTitle = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN AutoCreateTitle bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the AutoCreateTitle column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to validate data in required columns.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************
	-- optional columns 
	-- ****************
	BEGIN TRY
		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ProgramCode') BEGIN
			UPDATE #mc_SWODImport SET ProgramCode = '' WHERE ProgramCode IS NULL;

			-- ProgramCode must be at or under 15 chars
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + ProgramCode + ' is an invalid ProgramCode. ProgramCodes must be 15 characters or less.'
			FROM #mc_SWODImport
			WHERE len(ProgramCode) > 15 
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- ProgramCode must be unique in file
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'ProgramCode ' + ProgramCode + ' appears in the file multiple times. ProgramCodes must be unique.'
			FROM #mc_SWODImport
			WHERE ProgramCode <> ''
			GROUP BY ProgramCode
			HAVING COUNT(*) > 1
			ORDER BY ProgramCode;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- match on ProgramCode
			UPDATE tmp 
			SET tmp.MCSeminarID = s.seminarID
			FROM #mc_SWODImport as tmp 
			INNER JOIN dbo.tblSeminars as s on s.programCode = tmp.ProgramCode
				and s.isDeleted = 0
				and s.participantID = @participantID
			WHERE tmp.ProgramCode <> '';
		END

		-- bit columns provided in the original upload need a value for each row
		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'Featured') BEGIN
			UPDATE #mc_SWODImport set Featured = '0' where Featured is null;
			UPDATE #mc_SWODImport set Featured = '1' where Featured in ('Yes','Y','TRUE');
			UPDATE #mc_SWODImport set Featured = '0' where Featured in ('No','N','FALSE');

			BEGIN TRY
				ALTER TABLE #mc_SWODImport ALTER COLUMN Featured bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblSWODErrors (msg)
				VALUES ('There are invalid values in the Featured column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'SeminarSubtitle') BEGIN
			-- SeminarSubtitle must be at or under 15 chars
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid SeminarSubtitle. SeminarSubtitle must be 250 characters or less.'
			FROM #mc_SWODImport
			WHERE len(isnull(SeminarSubtitle,'')) > 250
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END

		IF NOT EXISTS (SELECT COLUMN_NAME FROM #tblColsAdded where COLUMN_NAME = 'Subjects') BEGIN
			IF OBJECT_ID('tempdb..#mc_SWODImport_subjects') IS NOT NULL
				DROP TABLE #mc_SWODImport_subjects;

			CREATE TABLE #mc_SWODImport_subjects(autoID int identity(1,1), rowID int, [subject] varchar(max), MCCategoryID int);

			INSERT INTO #mc_SWODImport_subjects (rowID, [subject], MCCategoryID)
			SELECT tmp.rowID, tmpSubject.listitem, c.categoryID
			FROM #mc_SWODImport AS tmp
			OUTER APPLY membercentral.dbo.fn_varcharListToTable(tmp.Subjects,'|') AS tmpSubject
			LEFT OUTER JOIN dbo.tblCategories AS c
				INNER JOIN dbo.tblParticipants AS p ON c.participantID = p.participantID
					AND p.participantID = @participantID
				ON c.categoryName = tmpSubject.listitem
			WHERE ISNULL(tmp.Subjects,'') <> '';

			-- invalid subjects
			INSERT INTO #tblSWODErrors (msg)
			SELECT 'Row ' + cast(rowID as varchar(10)) + ' has invalid subjects: ' + STRING_AGG([subject],'|') WITHIN GROUP (ORDER BY [subject] ASC)
			FROM #mc_SWODImport_subjects
			WHERE MCCategoryID IS NULL
			GROUP BY rowID;

			IF @@ROWCOUNT > 0 GOTO on_done;

			IF EXISTS(SELECT 1 FROM #mc_SWODImport_subjects WHERE MCCategoryID IS NOT NULL) BEGIN
				UPDATE tmp
				SET tmp.MCCategoryIDList = tmp2.MCCategoryIDList
				FROM #mc_SWODImport AS tmp
				INNER JOIN (
					SELECT rowID, STRING_AGG(MCCategoryID,'|') AS MCCategoryIDList
					FROM #mc_SWODImport_subjects
					WHERE MCCategoryID IS NOT NULL
					GROUP BY rowID
				) tmp2 ON tmp2.rowID = tmp.rowID;
			END

			IF OBJECT_ID('tempdb..#mc_SWODImport_subjects') IS NOT NULL
				DROP TABLE #mc_SWODImport_subjects;
		END

		-- validate rate amounts
		INSERT INTO @tblCols (columnName)
		VALUES ('RateAmount1'),('RateAmount2'),('RateAmount3'),('RateAmount4'),('RateAmount5');

		SELECT @mincol = min(columnName) FROM @tblCols;
		WHILE @mincol IS NOT NULL BEGIN
			SET @good = 1;
			SET @dynSQL = '
				set @good = 1;
				BEGIN TRY
					ALTER TABLE #mc_SWODImport ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) NULL;
					UPDATE #mc_SWODImport SET ' + quotename(@mincol) + ' = NULL WHERE ' + quotename(@mincol) + ' = '''';
					ALTER TABLE #mc_SWODImport ALTER COLUMN ' + quotename(@mincol) + ' decimal(9,2) NULL;
				END TRY
				BEGIN CATCH
					set @good = 0;
				END CATCH';
				EXEC sp_executesql @dynSQL, N'@good bit output', @good output;
			IF @good = 0
				INSERT INTO #tblSWODErrors (msg)
				VALUES ('The column ' + replace(quotename(@mincol),'''','''''') + ' contains invalid decimal values.');

			SELECT @mincol = min(columnName) FROM @tblCols WHERE columnname > @mincol;
		END
		
		IF NOT EXISTS (SELECT COLUMN_NAME FROM #tblColsAdded where COLUMN_NAME = 'Evaluation') BEGIN
			IF OBJECT_ID('tempdb..#mc_SWODImport_evaluation') IS NOT NULL
				DROP TABLE #mc_SWODImport_evaluation;

			CREATE TABLE #mc_SWODImport_evaluation(autoID int identity(1,1), rowID int, [evaluation] varchar(max), MCFormID int);

			INSERT INTO #mc_SWODImport_evaluation (rowID, [evaluation], MCFormID)
			SELECT tmp.rowID, tmpEvaluation.listitem, f.formID
			FROM #mc_SWODImport AS tmp
			OUTER APPLY membercentral.dbo.fn_varcharListToTable(tmp.Evaluation,'|') AS tmpEvaluation
			LEFT OUTER JOIN formbuilder.dbo.tblForms as f
				INNER JOIN formbuilder.dbo.tblFormTypes as ft on ft.formTypeID = f.formTypeID AND ft.formTypeAbbr = 'S'
				ON f.formTitle = tmpEvaluation.listitem AND f.isDeleted = 0 AND f.siteID = @siteID
			WHERE ISNULL(tmp.Evaluation,'') <> '';
			
			-- invalid evaluation
			INSERT INTO #tblSWODErrors (msg)
			SELECT 'Row ' + cast(rowID as varchar(10)) + ' has invalid evaluation: ' + STRING_AGG([evaluation],'|') WITHIN GROUP (ORDER BY [evaluation] ASC)
			FROM #mc_SWODImport_evaluation
			WHERE MCFormID IS NULL
			GROUP BY rowID;

			IF @@ROWCOUNT > 0 GOTO on_done;

			IF EXISTS(SELECT 1 FROM #mc_SWODImport_evaluation WHERE MCFormID IS NOT NULL) BEGIN
				UPDATE tmp
				SET tmp.MCFormIDList = tmp2.MCFormIDList
				FROM #mc_SWODImport AS tmp
				INNER JOIN (
					SELECT rowID, STRING_AGG(MCFormID,'|') AS MCFormIDList
					FROM #mc_SWODImport_evaluation
					WHERE MCFormID IS NOT NULL
					GROUP BY rowID
				) tmp2 ON tmp2.rowID = tmp.rowID;
			END

			IF OBJECT_ID('tempdb..#mc_SWODImport_evaluation') IS NOT NULL
				DROP TABLE #mc_SWODImport_evaluation;
		END

		IF EXISTS (SELECT 1 FROM #tblSWODErrors)
			GOTO on_done;
		
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to validate data in optional columns.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT membercentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSWODErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swod_importSeminars_import
@siteID int,
@recordedByMemberID int,
@ovAction char(1),
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @nowDate datetime = GETDATE(), @queueTypeID int, @statusInserting int, @statusReady int, 
		@itemGroupUID uniqueidentifier = NEWID(), @colList varchar(max), @selColList varchar(max), 
		@dynSQL nvarchar(max), @xmlMessage xml;
	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='importSWODPrograms', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='InsertingItems', @queueStatusID=@statusInserting OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	BEGIN TRY
		BEGIN TRAN;
			INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			SELECT itemUID, @statusInserting
			FROM #mc_SWODImport;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueInt
				FROM #mc_SWODImport
				unpivot (columnValueInt for columnname in (MCSeminarID, MCLayoutID, CompleteTime)) u
			) as unPvtInt on unPvtInt.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID
				AND dc.columnname = unPvtInt.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtString.columnValueString
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueString
				FROM #mc_SWODImport
				unpivot (columnValueString for columnname in (ProgramCode, SeminarTitle, SeminarSubtitle)) u
			) as unPvtString on unPvtString.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID
				AND dc.columnname = unPvtString.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDate.columnValueDate
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueDate
				FROM #mc_SWODImport
				unpivot (columnValueDate for columnname in (OrigPublished, StartSale, EndSale)) u
			) as unPvtDate on unPvtDate.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDate.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueBit)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtBit.columnValueBit
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueBit
				FROM #mc_SWODImport
				unpivot (columnValueBit for columnname in (Featured,PlayerQATab,BlankPlayer,[Certificate],SellInCatalog,Credit,AutoCreateTitle)) u
			) as unPvtBit on unPvtBit.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtBit.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueText)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtText.columnValueText
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueText
				FROM #mc_SWODImport
				unpivot (columnValueText for columnname in ([Description], Objective1, Objective2, Objective3, Objective4, Objective5, IntroductoryText, CompletionText, RateName1, RateName2, RateName3, RateName4, RateName5, MCCategoryIDList, MCFormIDList)) u
			) as unPvtText on unPvtText.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtText.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDecimal.columnValueDecimal2
			FROM #mc_SWODImport as tmp
			INNER JOIN (
				SELECT rowID, columnname, columnValueDecimal2
				FROM #mc_SWODImport
				unpivot (columnValueDecimal2 for columnname in (RateAmount1, RateAmount2, RateAmount3, RateAmount4, RateAmount5)) u
			) as unPvtDecimal on unPvtDecimal.rowID = tmp.rowID
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDecimal.columnname;

			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			SELECT @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, @ovAction
			FROM #mc_SWODImport as tmp
			INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'ovAction';

			-- resume task
			EXEC membercentral.dbo.sched_resumeTask @name='SWOD Programs Import Queue', @engine='BERLinux';

			-- update queue item groups to show ready to process
			UPDATE qi WITH (UPDLOCK, HOLDLOCK)
			SET qi.queueStatusID = @statusReady,
				qi.dateUpdated = getdate()
			FROM platformQueue.dbo.tblQueueItems as qi
			INNER JOIN #mc_SWODImport as tmp on tmp.itemUID = qi.itemUID;

			-- send message to service broker to create all the individual messages
			SELECT @xmlMessage = isnull((
				SELECT 'importSWODProgramsLoad' as t, cast(@itemGroupUID as varchar(60)) as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');
			EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to queue programs for import.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT membercentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSWODErrors
			order by rowID
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swod_cleanupLogTables
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @awsS3prefix varchar(100), @s3bucket varchar(100), @s3UploadReadyStatusID int, @seminarwebfiles varchar(100), @pathToLogs varchar(400);
	set @awsS3prefix = 'swodaccesslogs/';
	set @s3bucket = 'seminarweb';
	SET @itemCount = 1;
	SELECT @seminarwebfiles = seminarwebfiles FROM membercentral.dbo.fn_getServerSettings();
	SET @pathToLogs = @seminarwebfiles + 'SWODAccessLogs\';
			

	/* *********************************************************************************** */
	/* Write out activityLog, debugLog older than 1 day as text files and clear from table */
	/* *********************************************************************************** */
	IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
		DROP TABLE #tmpLogAccess;
	CREATE TABLE #tmpLogAccess (logAccessID int PRIMARY KEY, folder char(4), debug bit DEFAULT(0), activity bit DEFAULT(0),
		debugLogPath varchar(400), activityLogPath varchar(400), debugLogS3Key varchar(400), activityLogS3Key varchar(400));

	-- limit to this range so the query runs faster
	declare @oneDayAgo datetime, @twoDaysAgo datetime;
	set @oneDayAgo = dateadd(d,-1,getdate());
	set @twoDaysAgo = dateadd(d,-2,getdate());

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;
	
	insert into #tmpLogAccess (logAccessID, folder)
	select logaccessid, right('0000' + cast(logAccessID%1000 as varchar(4)),4)
	from dbo.tbllogaccessswod
	where datelastmodified between @twoDaysAgo and @oneDayAgo;

	update tmp set 
	    tmp.debug = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt',tbl.debuglog,1),
	    tmp.debugLogPath = @pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt',
	    tmp.debugLogS3Key = @awsS3prefix + tmp.folder + '/' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt'
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tbl.debuglog is not null;

	update tmp set 
	    tmp.activity = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt',tbl.activityLog,1),
	    tmp.activityLogPath = @pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt',
	    tmp.activityLogS3Key = @awsS3prefix + tmp.folder + '/' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt'
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tbl.activityLog is not null;

	-- add to Amazon S3 upload queue
	insert into platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
	select @s3UploadReadyStatusID, @s3bucket, debugLogS3Key, debugLogPath, 1, getdate(), getdate()
	from #tmpLogAccess as tmp
	where tmp.debug = 1;

	insert into platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
	select @s3UploadReadyStatusID, @s3bucket, activityLogS3Key, activityLogPath, 1, getdate(), getdate()
	from #tmpLogAccess as tmp
	where tmp.activity = 1;

	update tbl
	set tbl.debuglog = null
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tmp.debug = 1;

	update tbl
	set tbl.activityLog = null
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tmp.activity = 1;

	IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
		DROP TABLE #tmpLogAccess;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swl_convertToSWOD
@seminarID INT,
@recordedByMemberID INT,
@seminarErrCode INT OUTPUT,
@newSeminarID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @dateOrigPublished datetime, @seminarLength int, @seminarName varchar(250), @titleName varchar(250), @seminarSubTitle varchar(250), 
		@programCode varchar(15), @seminarDesc varchar(max), @participantID int, @freeRateDisplay varchar(5),
		@orgID int, @siteID int, @crlf varchar(10), @msgjson varchar(max), @siteCode varchar(10), @fileName varchar(100), @fileTitle varchar(200),
		@hasReplayVideo bit, @videoFileTypeID int, @paperFileTypeID int, @s3CopyReadyStatusID int, @objectKey varchar(200), 
		@oldObjectKey varchar(200), @newObjectKey varchar(200), @fileID int, @speakerAuthorTypeID int, @moderatorAuthorTypeID int, 
		@nowDate datetime = GETDATE(), @s3keyMod varchar(4), @seminarFeatureImageID int, @seminarImageConfigID int, @featureImageUsageID int, 
		@newTitleID int, @isPriceBasedOnActual int, @creditStatusId int, @layoutID int, @resourceTypeID int, @siteResourceID int, 
		@semWebCatalogSiteResourceID int, @seminarDocumentID int, @docFileName varchar(255), @docTitle varchar(255), @fileExt varchar(20),
		@documentVersionID int;

	SET @newSeminarID = 0;
	SET @seminarErrCode = 0;
	SET @crlf = CHAR(13) + CHAR(10);

	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

	-- check for valid swl seminar
	IF NOT EXISTS (select s.seminarID from dbo.tblSeminars as s inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID where s.seminarID = @seminarID) BEGIN
		set @seminarErrCode = 1;
		GOTO on_done;
	END
	IF EXISTS (select 1 from dbo.tblSeminarsSWOD where convertedFromSeminarID = @seminarID) BEGIN
		set @seminarErrCode = 2;
		GOTO on_done;
	END
	
	SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWSeminar');
	
	EXEC memberCentral.dbo.getUniqueCode @uniqueCode=@programCode OUTPUT;

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @siteCode = mcs.siteCode, @participantID = p.participantID, 
		@seminarName = s.seminarName, @seminarSubTitle = s.seminarSubTitle, @seminarDesc = s.seminarDesc, 
		@isPriceBasedOnActual = s.isPriceBasedOnActual,
		@freeRateDisplay = s.freeRateDisplay, @dateOrigPublished = sswl.dateStart, 
		@seminarLength = dateDiff(minute, sswl.dateStart, sswl.dateEnd),
		@hasReplayVideo = CASE WHEN p.offerSWLReplays = 1 AND sswl.offerReplay = 1 AND sswl.isUploadedReplay = 1 THEN 1 ELSE 0 END
	FROM dbo.tblSeminars AS s
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON sswl.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE s.seminarID = @seminarID;
	
	SELECT @semWebCatalogSiteResourceID = ai.siteResourceID
	FROM memberCentral.dbo.cms_applicationInstances AS ai 
	INNER JOIN memberCentral.dbo.cms_applicationTypes AS apt ON apt.applicationTypeID = ai.applicationTypeID 
		and apt.applicationTypeName = 'SemWebCatalog' 
	INNER JOIN memberCentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	WHERE ai.siteID = @siteID;

	SELECT @layoutID = layoutID
	FROM dbo.tblSeminarsSWODLayouts
	WHERE layout = 'largeVideo';

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;
	
	SELECT @speakerAuthorTypeID = authorTypeID
	FROM dbo.tblAuthorTypes
	WHERE authorType = 'Speaker';

	SELECT @moderatorAuthorTypeID = authorTypeID
	FROM dbo.tblAuthorTypes
	WHERE authorType = 'Moderator';
	
	BEGIN TRAN;
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
			@isVisible=1, @parentSiteResourceID=@semWebCatalogSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;
			
		-- seminar
		INSERT INTO dbo.tblSeminars (seminarName, seminarSubTitle, programCode, seminarDesc, isPublished, participantID, isDeleted, 
			dateCatalogStart, dateCatalogEnd, freeRateDisplay, isPriceBasedOnActual, siteResourceID)
		VALUES (@seminarName, @seminarSubTitle, @programCode, @seminarDesc, 0, @participantID, 0, @nowDate, dateadd(year,1,@nowDate), 
			@freeRateDisplay, @isPriceBasedOnActual, @siteResourceID);
			SET @newSeminarID = SCOPE_IDENTITY();

		UPDATE dbo.tblSeminars
		SET programCode = 'SWOD-' + CAST(@newSeminarID AS varchar(10))
		WHERE seminarID = @newSeminarID;

		INSERT INTO dbo.tblSeminarsSWOD (seminarID, dateOrigPublished, priceSyndication, allowSyndication, layoutID, 
			seminarLength, offerQA, convertedFromSeminarID, dateCreated, submittedByMemberID)
		VALUES (@newSeminarID, @dateOrigPublished, null, 0, @layoutID, @seminarLength, 0, @seminarID, @nowDate, @recordedByMemberID);

		-- auto opt in the publisher
		INSERT INTO dbo.tblSeminarsOptIn (seminarID, participantID, isPriceBasedOnActual, freeRateDisplay, dateAdded, isActive)
		VALUES (@newSeminarID, @participantID, @isPriceBasedOnActual, @freeRateDisplay, @nowDate, 1);

		SELECT @msgjson = 'SWOD- ' + CAST(@newSeminarID AS VARCHAR(20)) + ' has been created by converting from SWL-' + CAST(@seminarID AS VARCHAR(20)) + '.';

		-- auto add enrollments
		EXEC dbo.swod_autoAddEnrollments @newSeminarID;

		EXEC dbo.swod_addBillingLogForSeminarCreation @orgcode=@siteCode, @seminarID=@newSeminarID, @recordedByMemberID=@recordedByMemberID;

		-- swod speakers
		INSERT INTO dbo.tblSeminarsAndAuthors (seminarID, authorID, authorOrder)
		SELECT @newSeminarID, saa.authorID, saa.authorOrder
		FROM dbo.tblSeminarsAndAuthors AS saa
		INNER JOIN dbo.tblAuthors AS a ON a.authorID = saa.authorID
		WHERE saa.seminarID = @seminarID
		AND a.authorTypeID = @speakerAuthorTypeID;

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Speaker [' + a.firstName + ' ' + a.lastName + '] was added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblSeminarsAndAuthors AS saa
		INNER JOIN dbo.tblAuthors AS a ON a.authorID = saa.authorID
		WHERE saa.seminarID = @seminarID
		AND a.authorTypeID = @speakerAuthorTypeID;

		-- seminar subjects
		INSERT INTO dbo.tblSeminarsAndCategories (seminarID, categoryID)
		SELECT @newSeminarID, categoryID
		FROM dbo.tblSeminarsAndCategories
		WHERE seminarID = @seminarID;

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Subjects ' + STRING_AGG('[' + c.categoryName + ']',', ') WITHIN GROUP (ORDER BY c.categoryName ASC) + ' were added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblSeminarsAndCategories AS sc
		INNER JOIN dbo.tblCategories AS c ON c.categoryID = sc.categoryID
		WHERE sc.seminarID = @seminarID;

		-- seminar objectives
		INSERT INTO dbo.tblLearningObjectives (seminarID, objective, objectiveOrder)
		SELECT @newSeminarID, objective, objectiveOrder
		FROM dbo.tblLearningObjectives
		WHERE seminarID = @seminarID;

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'New Learning Objective ' + STRING_AGG('[' + lo.objective + ']',', ') WITHIN GROUP (ORDER BY lo.objectiveOrder ASC) + ' has been added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblLearningObjectives as lo
		WHERE seminarID = @seminarID;

		-- create title
		SET @titleName = 'Replay of ' + @seminarName;
		EXEC dbo.sw_addTitle @orgCode=@siteCode, @seminarID=@newSeminarID, @titleName=@titleName, @recordedByMemberID=@recordedByMemberID, @titleID=@newTitleID OUTPUT;

		INSERT INTO #tmpLogMessages(msg)
		VALUES('SWTL-' + CAST(@newTitleID AS VARCHAR(20)) + ' [Replay of ' + @seminarName +'] has been created as part of the conversion from SWL-' + CAST(@seminarID AS VARCHAR(20)) + '.');

		-- credit sponsors/authorities
		SELECT @creditStatusId = statusID from dbo.tblCreditStatuses where status = 'Not Submitted';

		INSERT INTO dbo.tblSeminarsAndCredit (seminarID, CSALinkID, statusID, courseapproval, wddxcreditsAvailable, creditOfferedStartDate, creditOfferedEndDate, creditCompleteByDate, isCreditRequired, isIDRequired, isCreditDefaulted, hasForm1)
		SELECT @newSeminarID, CSALinkID, @creditStatusId , '', wddxcreditsAvailable, NULL, NULL, NULL, 0, isIDRequired, 0, 0 
		FROM dbo.tblSeminarsAndCredit 
		WHERE seminarID = @seminarID;

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Credit Authorities ' + STRING_AGG('[' + ca.authorityName + ']',', ') WITHIN GROUP (ORDER BY ca.authorityName ASC) + ' have been added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblSeminarsAndCredit sc
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities csa ON sc.csalinkid = csa.csalinkid
		INNER JOIN dbo.tblCreditAuthorities ca ON ca.authorityID = csa.authorityID
		WHERE sc.seminarID = @seminarID;
		
		-- Seminar Featured Images
		IF EXISTS (select 1 from memberCentral.dbo.cms_featuredImageUsages WHERE referenceID = @seminarID AND referenceType='swlProgram') BEGIN
			SELECT TOP 1 @seminarFeatureImageID = featureImageID, @seminarImageConfigID = featureImageConfigID 
			FROM memberCentral.dbo.cms_featuredImageUsages 
			WHERE referenceID = @seminarID 
			AND referenceType='swlProgram';
			
			EXEC memberCentral.dbo.cms_createFeaturedImageUsage @featureImageID=@seminarFeatureImageID, @featureImageConfigID=@seminarImageConfigID,
				@referenceType='swodProgram', @referenceID=@newSeminarID, @featureImageUsageID=@featureImageUsageID OUTPUT;
				
			INSERT INTO #tmpLogMessages(msg)
			SELECT 'Featured Image is added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.';
		END
		
		-- Copying Rates
		EXEC dbo.sw_copyRates @participantID=@participantID, @copyFromSeminarID=@seminarID, @copyToSeminarID=@newSeminarID, @recordedByMemberID=@recordedByMemberID;

		IF @hasReplayVideo = 1 BEGIN
			select @videoFileTypeID = filetypeID from dbo.tblFilesTypes where fileType = 'video';
			SET @objectkey = LOWER('swlreplay/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(@seminarID as varchar(10)) +'.mp4');

			SET @fileName = LEFT(memberCentral.dbo.fn_RegExReplace('Replay of ' + @seminarName,'[^A-Za-z0-9]',''),100);
			SET @fileTitle = LEFT('Replay of ' + @seminarName,200);

			EXEC dbo.sw_addFile @orgCode=@siteCode, @titleID=@newTitleID, @fileTypeID=@videoFileTypeID, @fileName=@fileName, 
				@fileTitle=@fileTitle, @fileDesc=NULL, @recordedByMemberID=@recordedByMemberID, @addPVR=0, @fileID=@fileID OUTPUT;
			
			UPDATE dbo.tblFiles
			SET formatsAvailable = '<formats><format ext="mp4" accesstype="S" /></formats>'
			WHERE fileID = @fileID;

			IF (SELECT tier FROM memberCentral.dbo.fn_getServerSettings()) = 'production'
			BEGIN
				INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
				VALUES ('seminarweb', @objectkey, 'seminarweb', LOWER('swod/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(@fileID as varchar(10)) +'.mp4'),
					@nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
			END
		END

		-- swl materials to title download files (ignore mp3/mp4)
		SELECT @paperFileTypeID = filetypeID from dbo.tblFilesTypes where fileType = 'paper';
		DECLARE @tblSWLFiles TABLE (seminarDocumentID int, documentVersionID int, docFileName varchar(255), docTitle varchar(255), fileExt varchar(20));
			
		-- tblFiles expects the fileName to not have the extension, so strip it here
		INSERT INTO @tblSWLFiles (seminarDocumentID, documentVersionID, docFileName, docTitle, fileExt)
		SELECT sd.seminarDocumentID, dv.documentVersionID, replace(dv.fileName,'.'+dv.fileExt,''), dl.docTitle, dv.fileExt
		FROM dbo.tblSeminarsAndDocuments as sd
		INNER JOIN membercentral.dbo.cms_documents as d ON sd.documentID = d.documentID
		INNER JOIN membercentral.dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
		INNER JOIN membercentral.dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID 
			AND dv.isActive = 1
			AND dv.fileExt not in ('mp3','mp4')
		INNER JOIN membercentral.dbo.cms_siteResources as sr on sr.siteID = @siteID 
			AND sr.siteResourceStatusID = 1
			AND sr.siteResourceID = d.siteResourceID
		WHERE sd.seminarID = @seminarID;

		SELECT @seminarDocumentID = min(seminarDocumentID) from @tblSWLFiles;
		WHILE @seminarDocumentID is not null BEGIN
			SELECT @fileID = null, @docFileName = null, @docTitle = null, @fileExt = null, @documentVersionID = null;

			SELECT @documentVersionID = documentVersionID, @docFileName = docFileName, @docTitle = docTitle, @fileExt = fileExt
			FROM @tblSWLFiles
			WHERE seminarDocumentID = @seminarDocumentID;

			EXEC dbo.sw_addFile @orgCode=@siteCode, @titleID=@newTitleID, @fileTypeID=@paperFileTypeID, @fileName=@docFileName, 
				@fileTitle=@docTitle, @fileDesc=NULL, @recordedByMemberID=@recordedByMemberID, @addPVR=0, @fileID=@fileID OUTPUT;

			UPDATE dbo.tblFiles
			SET formatsAvailable = '<formats><format ext="' + @fileExt +'" accesstype="D" /></formats>'
			WHERE fileID = @fileID;

			IF (SELECT tier FROM memberCentral.dbo.fn_getServerSettings()) = 'production'
			BEGIN
				SET @s3keyMod = FORMAT(@documentVersionID % 1000, '0000');
				SET @oldObjectKey = LOWER('sitedocuments/' + membercentral.dbo.fn_getOrgCodeFromSiteCode(@siteCode) + '/' + @siteCode + '/' + @s3keyMod + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
				SET @newObjectkey = LOWER('swoddownloads/' + @siteCode + '/' + cast(@participantID as varchar(10)) + '/' + CAST(@fileID AS VARCHAR(10)) + '.' + @fileExt);

				INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
				VALUES ('membercentralcdn', @oldObjectKey, 'seminarweb', @newObjectkey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
			END

			SELECT @seminarDocumentID = min(seminarDocumentID) from @tblSWLFiles where seminarDocumentID > @seminarDocumentID;
		END

		-- re-populate search text
		EXEC dbo.swod_populateSearchText @seminarID=@newSeminarID;
	COMMIT TRAN	

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SET @msgjson = @msgjson + @crlf + 'The following actions have been performed:';

		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpLogMessages
		WHERE msg IS NOT NULL;
	END

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_replaceMediaFileFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL
		DROP TABLE #tmpEnrollments;
	IF OBJECT_ID('tempdb..#tmpEnrollmentLogAccessFiles') IS NOT NULL
		DROP TABLE #tmpEnrollmentLogAccessFiles;
	CREATE TABLE #tmpEnrollments (enrollmentID int PRIMARY KEY, completed bit);
	CREATE TABLE #tmpEnrollmentLogAccessFiles (fileLogAccessID int PRIMARY KEY, logAccessID int, enrollmentID int, accessDetails varchar(max), newAccessDetails varchar(max));

	declare @statusGrabbed int, @statusProcessing int, @itemStatus int, @orgID int, @siteID int, @participantID int,
		@seminarID int, @titleID int, @oldFileID int, @newFileID int, @oldVideoDurationInSeconds int, @newVideoDurationInSeconds int,
		@recordedByMemberID int, @oldVideoAccessDetailsLength int, @newVideoAccessDetailsLength int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SWReplaceMediaFile', @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SWReplaceMediaFile', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;
	
	select @orgID = qid.orgID, @siteID = qid.siteID, @seminarID = qid.seminarID, @participantID = qid.participantID, @titleID = qid.titleID, 
		@oldFileID = qid.oldFileID, @newFileID = qid.newFileID, @recordedByMemberID = qid.recordedByMemberID,
		@itemStatus = qid.statusID, @oldVideoDurationInSeconds = fo.duration, @newVideoDurationInSeconds = fn.duration
	from platformQueue.dbo.queue_SWReplaceMediaFile AS qid
	inner join dbo.tblFiles as fo on fo.fileID = qid.oldFileID
	inner join dbo.tblFiles as fn on fn.fileID = qid.newFileID
	where qid.itemID = @itemID;

	-- if itemID is not grabbedForProcessing, kick out now
	IF @itemStatus <> @statusGrabbed
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_SWReplaceMediaFile
	SET statusID = @statusProcessing,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- accessDetails value will be like 11000, where each 1 is 15 seconds
	INSERT INTO #tmpEnrollmentLogAccessFiles (fileLogAccessID, logAccessID, enrollmentID, accessDetails)
	SELECT af.fileLogAccessID, af.logAccessID, e.enrollmentID, af.accessDetails
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblLogAccessSWOD AS a ON a.enrollmentID = e.enrollmentID
	INNER JOIN dbo.tblLogAccessSWODFiles AS af ON af.logAccessID = a.logAccessID
	INNER JOIN dbo.tblFiles AS f ON f.fileID = af.fileID
	WHERE e.seminarID = @seminarID
	AND e.participantID = @participantID
	AND e.isActive = 1
	AND f.fileID = @oldFileID;

	-- these enrollments have completed viewing the media file in full.
	INSERT INTO #tmpEnrollments (enrollmentID, completed)
	SELECT DISTINCT enrollmentID, 1
	FROM #tmpEnrollmentLogAccessFiles
	WHERE RIGHT(accessDetails,1) = 1;

	-- enrollments not completed
	INSERT INTO #tmpEnrollments (enrollmentID, completed)
	SELECT DISTINCT enrollmentID, 0
	FROM #tmpEnrollmentLogAccessFiles
		EXCEPT
	SELECT enrollmentID, 0
	FROM #tmpEnrollments
	WHERE completed = 1;


	SET @oldVideoAccessDetailsLength = LEN(REPLICATE(1,CEILING(CAST(@oldVideoDurationInSeconds AS decimal)/15)));
	SET @newVideoAccessDetailsLength = LEN(REPLICATE(1,CEILING(CAST(@newVideoDurationInSeconds AS decimal)/15)));


	-- update newAccessDetails
	IF @oldVideoAccessDetailsLength = @newVideoAccessDetailsLength
		UPDATE #tmpEnrollmentLogAccessFiles
		SET newAccessDetails = accessDetails;
	ELSE
		UPDATE laf
		SET laf.newAccessDetails = CASE WHEN tmp.completed = 1 THEN 
										CASE WHEN @newVideoAccessDetailsLength > @oldVideoAccessDetailsLength 
											THEN LEFT(laf.accessDetails,@oldVideoAccessDetailsLength) + RIGHT(laf.accessDetails,@newVideoAccessDetailsLength - @oldVideoAccessDetailsLength)
											ELSE LEFT(laf.accessDetails,@newVideoAccessDetailsLength)
										END
									ELSE
										CASE WHEN @newVideoAccessDetailsLength > @oldVideoAccessDetailsLength 
											THEN LEFT(laf.accessDetails,@oldVideoAccessDetailsLength) + REPLICATE(0,@newVideoAccessDetailsLength - @oldVideoAccessDetailsLength)
											ELSE LEFT(laf.accessDetails,@newVideoAccessDetailsLength)
										END
									END
		FROM #tmpEnrollmentLogAccessFiles AS laf
		INNER JOIN #tmpEnrollments AS tmp ON tmp.enrollmentID = laf.enrollmentID
		WHERE ISNULL(laf.accessDetails,'') <> '';


	BEGIN TRAN;
		IF EXISTS (SELECT 1 FROM #tmpEnrollmentLogAccessFiles) BEGIN
			UPDATE laf
			SET laf.fileID = @newFileID,
				laf.accessDetails = tmp.newAccessDetails
			FROM dbo.tblLogAccessSWODFiles AS laf
			INNER JOIN #tmpEnrollmentLogAccessFiles AS tmp ON tmp.fileLogAccessID = laf.fileLogAccessID;

			-- audit log
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			VALUES ('{ "c":"auditLog", "d": {
				"AUDITCODE":"SW",
				"ORGID":' + cast(@orgID as varchar(10)) + ',
				"SITEID":' + cast(@siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('File [' + cast(@newFileID as varchar(10)) + '] carried the registrant progress of the old file [' + CAST(@oldFileID AS varchar(10)) + '].'),'"','\"') + '" } }');
		END
	
		DELETE FROM platformQueue.dbo.queue_SWReplaceMediaFile
		WHERE itemID = @itemID;
	COMMIT TRAN;

	on_done:

	IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL
		DROP TABLE #tmpEnrollments;
	IF OBJECT_ID('tempdb..#tmpEnrollmentLogAccessFiles') IS NOT NULL
		DROP TABLE #tmpEnrollmentLogAccessFiles;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_removeSeminarCredit
@seminarCreditID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @orgID int, @siteID int, @seminarID int, @programType varchar(4), @hasForm1 bit, @objectkey varchar(50), @s3DeleteReadyStatusID int,
		@nowdate datetime = getdate(), @msgjson varchar(max);

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID , @seminarID = sac.seminarID, 
		@programType = CASE WHEN swl.seminarID IS NOT NULL THEN 'SWL' WHEN swod.seminarID IS NOT NULL THEN 'SWOD' END,
		@msgjson = 'Credit Authority [' + ca.code + ' - ' + ca.authorityName + ' (' + cs.sponsorName + ')] removed from '
	FROM dbo.tblSeminarsAndCredit AS sac
	INNER JOIN dbo.tblSeminars AS s ON s.seminarID = sac.seminarID
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID
	INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID
	INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
	LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = sac.seminarID
	LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = sac.seminarID
	WHERE sac.seminarCreditID = @seminarCreditID;

	SET @msgjson = @msgjson + @programType + '-' + cast(@seminarID as varchar(10));
	SET @objectkey = 'swodprograms/form1_'+ cast(@seminarCreditID as varchar(10)) +'.pdf';
	SELECT @hasForm1 = hasForm1 FROM dbo.tblSeminarsAndCredit WHERE seminarCreditID = @seminarCreditID;

	IF NOT EXISTS (SELECT 1 FROM dbo.tblEnrollmentsAndCredit WHERE seminarCreditID = @seminarCreditID) BEGIN
		BEGIN TRAN;
			DELETE FROM dbo.tblSeminarsAndCredit WHERE seminarCreditID = @seminarCreditID;
			
			IF @programType = 'SWL' AND NOT EXISTS (SELECT TOP 1 seminarCreditID FROM dbo.tblSeminarsAndCredit WHERE seminarID = @seminarID)
				UPDATE dbo.tblSeminarsSWLive
				SET offerCredit = 0
				WHERE seminarID = @seminarID;

			IF @hasForm1 = 1 BEGIN
				EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;
				
				INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
				VALUES (@s3DeleteReadyStatusID, 'seminarweb', @objectkey, @nowdate, @nowdate);
			END

			IF @programType = 'SWL'
				EXEC dbo.swl_populateSearchText @seminarID = @seminarID;
			IF @programType = 'SWOD'
				EXEC dbo.swod_populateSearchText @seminarID = @seminarID;
		COMMIT TRAN;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES('{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + CAST(@orgID AS varchar(10)) + ',
			"SITEID":' + CAST(@siteID AS varchar(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
			"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_populateCompleteSeminarReminder
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueStatusID int, @nowDate datetime = getdate(), @participantID int, @orgID int, 
		@emailTagTypeID int, @emailsubject varchar(255), @selectedTimeframes varchar(50);
	DECLARE @tblParticipants TABLE (participantID int, orgID int, emailTagTypeID int, selectedTimeframes varchar(50), emailsubject varchar(255));
	DECLARE @tblTimeFrames TABLE (timeframe int);
	DECLARE @tblTimeFrames2 TABLE (participantID int, timeframe int, dateValue date);
	SET @itemCount = 0;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='completeSeminarReminder', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

	-- emailOption1 is based on enrollment date
	INSERT INTO @tblParticipants (participantID, orgID, emailTagTypeID, selectedTimeframes, emailsubject)
	SELECT p.participantID, s.orgID, etag.emailTagTypeID, st.selectedTimeframes, st.emailSubject
	FROM dbo.tblSeminarsSWODTasks AS st
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = st.participantID and p.isActive = 1
	INNER JOIN membercentral.dbo.sites as s on s.siteCode = p.orgcode
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as etag ON etag.orgID = s.orgID and etag.emailTagType = 'Primary'
	WHERE st.isCompletionReminderEnabled = 1
	AND st.emailOption = 1;

	SELECT @participantID = min(participantID) FROM @tblParticipants;
	WHILE @participantID IS NOT NULL BEGIN
		SELECT @orgID = orgID, @emailTagTypeID = emailTagTypeID, @selectedTimeframes = selectedTimeframes, @emailsubject = emailsubject
		FROM @tblParticipants 
		WHERE participantID = @participantID;

		INSERT INTO @tblTimeFrames (timeframe)
		SELECT listitem
		FROM membercentral.dbo.fn_intListToTableInline(@selectedTimeframes,',');
		
		IF @@ROWCOUNT > 0 BEGIN
			INSERT INTO platformQueue.dbo.queue_completeSeminarReminder (orgcode, memberID, email, firstName, lastName, overrideEmail,
				EmailFrom, TimeLapse, CatalogURL, seminarID, seminarName, dateEnrolled, supportPhone, supportEmail,
				lastdatetoComplete, statusID, dateAdded, dateUpdated, emailOptionID, orgIdentityID, emailsubject)
			SELECT DISTINCT p.orgcode, m2.memberID, me.email, m2.firstName, m2.lastName, ISNULL(eo.email,''), 
				p.EmailFrom, DATEDIFF(dd,e.dateEnrolled,getdate()), p.CatalogURL, s.seminarID, s.seminarName, e.dateEnrolled, 
				p.supportPhone, p.supportEmail, null, @queueStatusID, @nowDate, @nowDate, 1, p.orgIdentityID, @emailsubject
			FROM dbo.tblEnrollments AS e
			INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
			INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID 
				AND s.isPublished = 1 
				AND s.isDeleted = 0
			INNER JOIN memberCentral.dbo.ams_members AS m on m.orgID = @orgID and m.memberID = e.MCMemberID
			INNER JOIN membercentral.dbo.ams_members AS m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			INNER JOIN memberCentral.dbo.ams_memberEmails AS me on me.orgID = @orgID and me.memberID = m2.memberID
			INNER JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
				and metag.emailTagTypeID = @emailTagTypeID 
				and metag.memberID = me.memberID 
				AND metag.emailTypeID = me.emailTypeID
			LEFT OUTER JOIN memberCentral.dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID 
				AND eo.itemType = 'semwebreg'
			WHERE e.participantID = @participantID
			AND e.dateCompleted IS NULL
			AND e.isActive = 1
			AND DATEDIFF(dd,e.dateEnrolled,getdate()) in (select timeframe from @tblTimeFrames);

			SET @itemCount = @itemCount + @@ROWCOUNT;

			DELETE FROM @tblTimeFrames;
		END

		SELECT @participantID = min(participantID) FROM @tblParticipants WHERE participantID > @participantID;
	END

	DELETE FROM @tblParticipants;


	-- emailOption2 is based on credit complete by date
	INSERT INTO @tblParticipants (participantID, orgID, emailTagTypeID, selectedTimeframes, emailsubject)
	SELECT p.participantID, s.orgID, etag.emailTagTypeID, st.selectedTimeframes, st.emailSubject
	FROM dbo.tblSeminarsSWODTasks AS st
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = st.participantID and p.isActive = 1
	INNER JOIN membercentral.dbo.sites as s on s.siteCode = p.orgcode
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as etag ON etag.orgID = s.orgID and etag.emailTagType = 'Primary'
	WHERE st.isCompletionReminderEnabled = 1
	AND st.emailOption = 2;

	INSERT INTO @tblTimeFrames2 (participantID, timeframe, dateValue)
	SELECT distinct p.participantID, li.listitem, DATEADD(DAY,li.listItem,@nowDate)
	FROM @tblParticipants as p
	CROSS APPLY membercentral.dbo.fn_intListToTableInline(p.selectedTimeframes,',') as li;

	SELECT @participantID = min(participantID) FROM @tblParticipants;
	WHILE @participantID IS NOT NULL BEGIN
		SELECT @orgID = orgID, @emailTagTypeID = emailTagTypeID, @selectedTimeframes = selectedTimeframes, @emailsubject = emailsubject
		FROM @tblParticipants 
		WHERE participantID = @participantID;

		IF (SELECT COUNT(*) FROM @tblTimeFrames2 where participantID = @participantID) > 0 BEGIN
			INSERT INTO platformQueue.dbo.queue_completeSeminarReminder (orgcode, memberID, email, firstName, lastName, overrideEmail,
				EmailFrom, TimeLapse, CatalogURL, seminarID, seminarName, dateEnrolled, supportPhone, supportEmail,
				lastdatetoComplete, statusID, dateAdded, dateUpdated, emailOptionID, orgIdentityID, emailsubject)
			SELECT DISTINCT p.orgcode, m2.memberID, me.email, m2.firstName, m2.lastName, ISNULL(eo.email,''), 
				p.EmailFrom, null, p.CatalogURL, s.seminarID, s.seminarName, e.dateEnrolled, p.supportPhone, p.supportEmail, 
				eac.lastdatetoComplete, @queueStatusID, @nowDate, @nowDate, 2, p.orgIdentityID, @emailsubject
			FROM dbo.tblEnrollments AS e
			INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
			INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID 
				AND s.isPublished = 1 
				AND s.isDeleted = 0
			INNER JOIN dbo.tblEnrollmentsAndCredit as eac ON e.enrollmentID = eac.enrollmentID
				AND cast(eac.lastdatetoComplete as date) in (select dateValue from @tblTimeFrames2 where participantID = @participantID)
			INNER JOIN dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
				and sac.creditCompleteByDate > @nowDate
			INNER JOIN memberCentral.dbo.ams_members AS m on m.orgID = @orgID and m.memberID = e.MCMemberID
			INNER JOIN membercentral.dbo.ams_members AS m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			INNER JOIN memberCentral.dbo.ams_memberEmails AS me on me.orgID = @orgID and me.memberID = m2.memberID
			INNER JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
				and metag.emailTagTypeID = @emailTagTypeID 
				and metag.memberID = me.memberID 
				AND metag.emailTypeID = me.emailTypeID
			LEFT OUTER JOIN memberCentral.dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID 
				AND eo.itemType = 'semwebreg'
			WHERE e.participantID = @participantID
			AND e.dateCompleted IS NULL
			AND e.isActive = 1;
		
			SET @itemCount = @itemCount + @@ROWCOUNT;
		END

		SELECT @participantID = min(participantID) FROM @tblParticipants WHERE participantID > @participantID;
	END

	DELETE FROM @tblParticipants;

	-- resume task 
	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='Daily Complete Seminar Reminder', @engine='MCLuceeLinux';

	RETURN 0;
	
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_deleteVideoPreview
@previewID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @s3DeleteReadyStatusID int, @siteResourceID int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

	-- video previews
	INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
	select @s3DeleteReadyStatusID, 'seminarweb', LOWER('swod/' + sP.orgcode + '/' + cast(sP.participantID as varchar(10)) + '/' 
								+ cast(p.baseFileID as varchar(10)) + '_s_'
								+ cast(p.seminarID as varchar(10)) + '_preview.mp4'),
			GETDATE(), GETDATE()
	from dbo.tblVideoPreviews as p
	inner join dbo.tblSeminars as s on s.seminarID = p.seminarID
	inner join dbo.tblParticipants as sP on sP.participantID = s.participantID
	where p.previewID = @previewID
	and p.isOnline = 1;

	BEGIN TRAN;
		DELETE FROM dbo.tblVideoPreviews
		WHERE previewID = @previewID;

		DELETE FROM platformQueue.dbo.queue_SWVideoPreview
		WHERE previewID = @previewID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_deleteParticipant
@orgcode varchar(10),
@pathToFileS varchar(400),
@pathToFileD varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @participantID int, @siteID int, @siteResourceID int, @s3DeleteReadyStatusID int;

	select @participantID = p.participantID, @siteID = mcs.siteID
	from dbo.tblParticipants as p
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	where p.orgcode = @orgcode 
	and isActive = 1;

	IF @participantID is null
		GOTO on_done;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tblCategories') IS NOT NULL
		DROP TABLE #tblCategories;
	IF OBJECT_ID('tempdb..#tblSeminars') IS NOT NULL
		DROP TABLE #tblSeminars;
	IF OBJECT_ID('tempdb..#tblBundles') IS NOT NULL
		DROP TABLE #tblBundles;
	IF OBJECT_ID('tempdb..#tblTitles') IS NOT NULL
		DROP TABLE #tblTitles;
	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL
		DROP TABLE #tmpFiles;
	IF OBJECT_ID('tempdb..#tmpFilesS3') IS NOT NULL
		DROP TABLE #tmpFilesS3;
	CREATE TABLE #tblCategories (categoryID int PRIMARY KEY);
	CREATE TABLE #tblSeminars (seminarID int PRIMARY KEY);
	CREATE TABLE #tblBundles (bundleID int PRIMARY KEY);
	CREATE TABLE #tblTitles (titleID int PRIMARY KEY);
	CREATE TABLE #tmpFiles (fileID int PRIMARY KEY);
	CREATE TABLE #tmpFilesS3 (objectKey varchar(800), fileSize bigint);
	
	insert into #tblCategories (categoryID)
	select categoryID from dbo.tblCategories where participantID = @participantID;

	insert into #tblSeminars (seminarID)
	select seminarID from dbo.tblSeminars where participantID = @participantID;

	insert into #tblBundles (bundleID)
	select bundleID from dbo.tblBundles where participantID = @participantID;

	insert into #tblTitles (titleID)
	select titleID from dbo.tblTitles where participantID = @participantID;

	insert into #tmpFiles (fileID)
	select fileID from dbo.tblFiles where participantID = @participantID;

	-- import from file
	declare @fullSql varchar(max);
	set @fullSql = 'BULK INSERT #tmpFilesS3 FROM ''' + @pathToFileS + ''' WITH (FIELDTERMINATOR = ''|'')';
	EXEC(@fullSql);
	set @fullSql = 'BULK INSERT #tmpFilesS3 FROM ''' + @pathToFileD + ''' WITH (FIELDTERMINATOR = ''|'')';
	EXEC(@fullSql);

	-- also consider video previews
	INSERT INTO #tmpFilesS3 (objectKey)
	select LOWER('swod/' + sP.orgcode + '/' + cast(sP.participantID as varchar(10)) + '/' 
				+ cast(p.baseFileID as varchar(10)) + '_s_'
				+ cast(p.seminarID as varchar(10)) + '_preview.mp4')
	from dbo.tblVideoPreviews as p
	inner join #tmpFiles as tmp on tmp.fileID = p.baseFileID
	inner join dbo.tblSeminars as s on s.seminarID = p.seminarID
	inner join dbo.tblParticipants as sP on sP.participantID = s.participantID
	where p.isOnline = 1;

	BEGIN TRAN;
		delete from dbo.tblParticipantEvents where participantID = @participantID;
		UPDATE dbo.tblSeminarsOptIn SET IsActive = 0 where participantID = @participantID;
		delete from dbo.tblNationalProgramParticipants where participantID = @participantID;
		UPDATE dbo.tblBundlesOptIn SET isActive = 0 WHERE participantID = @participantID;
		UPDATE dbo.tblBundles set [status] = 'D' where participantID = @participantID;
		UPDATE dbo.tblParticipants set isActive = 0, isConf = 0, isSWOD = 0, isSWL = 0 where participantID = @participantID;
		
		-- from delete submissions
		DELETE FROM dbo.tblSWODSubmissionsAndCategories WHERE categoryID in (select categoryID from #tblCategories);
		DELETE FROM dbo.tblSWODSubmissionsAndCategories WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndAuthors WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndObjectives WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndForms WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndCredits WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndRateGroups WHERE rateID in (SELECT rateID FROM dbo.tblSWODSubmissionsAndRates WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID));
		DELETE FROM dbo.tblSWODSubmissionsAndRates WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissionsAndLinks WHERE submissionID IN (SELECT submissionID FROM dbo.tblSWODSubmissions WHERE participantID = @participantID);
		DELETE FROM dbo.tblSWODSubmissions WHERE participantID = @participantID;

		-- from delete category
		DELETE FROM dbo.tblSeminarsAndCategories WHERE categoryID in (select categoryID from #tblCategories);
		DELETE FROM dbo.tblCategories WHERE categoryID in (select categoryID from #tblCategories);		
		
		-- from delete seminar
		UPDATE dbo.tblSeminars SET isDeleted = 1, isPublished = 0 where seminarID in (select seminarID from #tblSeminars);
		DELETE FROM dbo.tblSeminarAndFilesSyncPoints WHERE seminarID in (select seminarID from #tblSeminars);

		-- from delete title
		UPDATE dbo.tblTitles SET isDeleted = 1 where titleID in (select titleID from #tblTitles);
		DELETE FROM dbo.tblSeminarsAndTitles WHERE titleID in (select titleID from #tblTitles);

		-- from delete file
		UPDATE dbo.tblFiles SET isDeleted = 1, formatsAvailable = '<formats/>' where fileID in (select fileID from #tmpFiles);
		DELETE FROM dbo.tblVideoPreviews WHERE baseFileID in (select fileID from #tmpFiles);
		delete from dbo.tblTitlesAndFiles where fileID in (select fileID from #tmpFiles);
		DELETE FROM dbo.tblSeminarAndFilesSyncPoints where fileID in (select fileID from #tmpFiles);
		DELETE FROM dbo.tblSeminarAndFilesSyncPoints where linkedfileID in (select fileID from #tmpFiles);

		-- delete from search tables
		DELETE FROM searchMC.dbo.sw_programs WHERE programType IN ('SWL','SWOD') AND programID IN (select seminarID from #tblSeminars)
		DELETE FROM searchMC.dbo.sw_programs WHERE programType = 'SWTL' AND programID IN (select titleID from #tblTitles)
		DELETE FROM searchMC.dbo.sw_programs WHERE programType = 'SWB' AND programID IN (select bundleID from #tblBundles)
	COMMIT TRAN;

	
	SET @siteResourceID = null;
	SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblSeminars WHERE participantID = @participantID AND isDeleted = 1 AND siteResourceID is not null;
	WHILE @siteResourceID is not null BEGIN
		EXEC membercentral.dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
		SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblSeminars WHERE participantID = @participantID AND isDeleted = 1 AND siteResourceID is not null AND siteResourceID > @siteResourceID;
	END
	
	SET @siteResourceID = null;
	SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblBundles WHERE participantID = @participantID AND status = 'D' AND siteResourceID is not null;
	WHILE @siteResourceID is not null BEGIN
		EXEC membercentral.dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
		SELECT @siteResourceID = MIN(siteResourceID) FROM dbo.tblBundles WHERE participantID = @participantID AND status = 'D' AND siteResourceID is not null AND siteResourceID > @siteResourceID;
	END

	INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
	SELECT @s3DeleteReadyStatusID, 'seminarweb', objectKey, GETDATE(), GETDATE()
	FROM #tmpFilesS3;

	delete from lyris.trialslyris1.dbo.sw_marketing where orgcode = @orgcode;

	on_done:

	IF OBJECT_ID('tempdb..#tblCategories') IS NOT NULL
		DROP TABLE #tblCategories;
	IF OBJECT_ID('tempdb..#tblSeminars') IS NOT NULL
		DROP TABLE #tblSeminars;
	IF OBJECT_ID('tempdb..#tblBundles') IS NOT NULL
		DROP TABLE #tblBundles;
	IF OBJECT_ID('tempdb..#tblTitles') IS NOT NULL
		DROP TABLE #tblTitles;
	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL
		DROP TABLE #tmpFiles;
	IF OBJECT_ID('tempdb..#tmpFilesS3') IS NOT NULL
		DROP TABLE #tmpFilesS3;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_deleteFileFormat
@fileID int,
@fileEXT varchar(100),
@fileMode varchar(1),
@objectKey varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @s3DeleteReadyStatusID int, @siteResourceID int, @formatsAvailable xml;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

	SET @fileEXT = LOWER(@fileEXT);
	SET @fileMode = UPPER(@fileMode);

	-- delete the node format from formatsAvailable field 
	SELECT @formatsAvailable = formatsAvailable FROM dbo.tblFiles WHERE fileID = @fileID;
	SET @formatsAvailable.modify('delete /formats/format[@ext=sql:variable("@fileEXT")][@accesstype=sql:variable("@fileMode")]');

	BEGIN TRAN;
		IF (SELECT tier FROM memberCentral.dbo.fn_getServerSettings()) = 'production'
		BEGIN
			INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
			VALUES (@s3DeleteReadyStatusID, 'seminarweb', @objectKey, GETDATE(), GETDATE());
		END

		UPDATE dbo.tblFiles
		SET formatsAvailable = @formatsAvailable
		WHERE fileID = @fileID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_deleteFile
@fileID int,
@pathToFileS varchar(400),
@pathToFileD varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL 
		DROP TABLE #tmpFiles;
	CREATE TABLE #tmpFiles (objectKey varchar(800), fileSize bigint);

	DECLARE @s3DeleteReadyStatusID int, @siteResourceID int, @siteID int;
	
	IF(membercentral.dbo.fn_FileExists(@pathToFileS) = 1 OR membercentral.dbo.fn_FileExists(@pathToFileD) = 1)
	BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;
		
		-- import from file
		declare @fullSql varchar(max);
		IF(membercentral.dbo.fn_FileExists(@pathToFileS) = 1)
		BEGIN
			set @fullSql = 'BULK INSERT #tmpFiles FROM ''' + @pathToFileS + ''' WITH (FIELDTERMINATOR = ''|'')';
			EXEC(@fullSql);
		END
		IF(membercentral.dbo.fn_FileExists(@pathToFileD) = 1)
		BEGIN
			set @fullSql = 'BULK INSERT #tmpFiles FROM ''' + @pathToFileD + ''' WITH (FIELDTERMINATOR = ''|'')';
			EXEC(@fullSql);
		END

		INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
		SELECT @s3DeleteReadyStatusID, 'seminarweb', objectKey, GETDATE(), GETDATE()
		FROM #tmpFiles;

		-- also delete any video previews
		IF(membercentral.dbo.fn_FileExists(@pathToFileS) = 1)
		BEGIN
			INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
			select @s3DeleteReadyStatusID, 'seminarweb', LOWER('swod/' + sP.orgcode + '/' + cast(sP.participantID as varchar(10)) + '/' 
										+ cast(p.baseFileID as varchar(10)) + '_s_'
										+ cast(p.seminarID as varchar(10)) + '_preview.mp4'), 
					GETDATE(), GETDATE()
			from dbo.tblVideoPreviews as p
			inner join seminarWeb.dbo.tblSeminars as s on s.seminarID = p.seminarID
			inner join seminarWeb.dbo.tblParticipants as sP on sP.participantID = s.participantID
			where p.baseFileID = @fileID
			and p.isOnline = 1;
		END
	END
	
	SELECT @siteResourceID = f.siteResourceID, @siteID = mcs.siteID
	FROM dbo.tblFiles as f
	INNER JOIN dbo.tblParticipants as p on p.participantID = f.participantID
	INNER JOIN membercentral.dbo.sites as mcs on mcs.sitecode = p.orgcode
	WHERE f.fileID = @fileID;

	BEGIN TRAN;
		UPDATE dbo.tblFiles
		SET isDeleted = 1, 
			formatsAvailable = '<formats/>'
		WHERE fileID = @fileID;
		
		IF @siteResourceID IS NOT NULL
			EXEC membercentral.dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;

		DELETE FROM platformQueue.dbo.queue_SWVideoPreview
		WHERE baseFileID = @fileID;

		DELETE FROM dbo.tblVideoPreviews
		WHERE baseFileID = @fileID;

		DELETE FROM dbo.tblTitlesAndFiles
		WHERE fileID = @fileID;

		DELETE FROM dbo.tblSeminarAndFilesSyncPoints
		WHERE fileID = @fileID;

		DELETE FROM dbo.tblSeminarAndFilesSyncPoints
		WHERE linkedfileID = @fileID;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL 
		DROP TABLE #tmpFiles

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_createVideoPreview
@seminarID int,
@baseFileID int,
@timeCodeStart varchar(10),
@timeCodeEnd varchar(10),
@isOnline bit,
@previewID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @previewID = NULL;

	DECLARE @readyToProcessStatusID int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SWVideoPreview', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;

	SELECT @previewID = previewID
	FROM dbo.tblVideoPreviews
	WHERE baseFileID = @baseFileID
	AND ISNULL(seminarID,0) = ISNULL(@seminarID,0);

	IF @previewID IS NULL BEGIN
		BEGIN TRAN;
			INSERT INTO dbo.tblVideoPreviews (seminarID, baseFileID, timeCodeStart, timeCodeEnd, isOnline)
			VALUES (@seminarID, @baseFileID, @timeCodeStart, @timeCodeEnd, @isOnline);
				SELECT @previewID = SCOPE_IDENTITY();

			INSERT INTO platformQueue.dbo.queue_SWVideoPreview (statusID, previewID, seminarID, baseFileID, timeCodeStart, timeCodeEnd, dateAdded, dateUpdated)
			VALUES (@readyToProcessStatusID, @previewID, @seminarID, @baseFileID, @timeCodeStart, @timeCodeEnd, GETDATE(), GETDATE());

			-- resume task
			EXEC memberCentral.dbo.sched_resumeTask @name='SeminarWeb Video Preview', @engine='BERLinux';
		COMMIT TRAN;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_copyTitle
@titleID int,
@newSeminarID int,
@oldSeminarID int,
@recordedByMemberID int,
@newTitleID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpTitlesAndFiles;
	IF OBJECT_ID('tempdb..#tmpNewTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpNewTitlesAndFiles;
	CREATE TABLE #tmpNewTitlesAndFiles (newFileID int PRIMARY KEY, newTitleID int, fileID int, titleID int, fileOrder int, 
		formatsAvailable xml, isDownloadable int, isDefaultStream int, isDefaultPaper int, isSupportingDoc int, previewPct int);

	DECLARE @orgID int, @orgCode varchar(10), @participantID int, @siteID int, @minNewFileID int, 
		@nowDate datetime = GETDATE(), @siteCode varchar(10), @s3CopyReadyStatusID int, @resourceTypeID int, @siteResourceID int, @seminarSiteResourceID int;;

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @siteCode = mcs.siteCode, @participantID = p.participantID, @orgCode=p.orgCode
	FROM dbo.tblTitles AS t
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = t.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE t.titleID = @titleID;

	BEGIN TRAN;
		-- insert new title record
		SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWTitle');
			
		SELECT @seminarSiteResourceID = s.siteResourceID
		FROM dbo.tblSeminars as s
		WHERE s.seminarID = @newSeminarID;
			
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
			@isVisible=1, @parentSiteResourceID=@seminarSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;

		INSERT INTO dbo.tblTitles (titleName, participantID, dateCreated, siteResourceID)
		SELECT titleName, participantID, @nowDate, @siteResourceID
		FROM dbo.tblTitles
		WHERE titleID = @titleID;

		SELECT @newTitleID = SCOPE_IDENTITY();
		
		INSERT INTO dbo.tblSeminarsAndTitles (titleID, seminarID, titleOrder)
		SELECT @newTitleID, CASE WHEN @newSeminarID > 0 THEN @newSeminarID ELSE seminarID END AS seminarID, titleOrder
		FROM dbo.tblSeminarsAndTitles
		WHERE titleID = @titleID
		AND seminarID = CASE WHEN @oldSeminarID > 0 THEN @oldSeminarID ELSE seminarID END;

		SELECT @newTitleID as newTitleID, f.fileID, tf.titleID, f.fileTypeID, f.participantID, f.[fileName], 
			f.fileTitle, f.fileDesc, f.formatsAvailable, tf.fileOrder, tf.isDownloadable, tf.isDefaultStream, 
			tf.isDefaultPaper, tf.isSupportingDoc, tf.previewPct
		INTO #tmpTitlesAndFiles
		FROM dbo.tblFiles f
		INNER JOIN dbo.tblTitlesAndFiles tf ON tf.fileID = f.fileID
		WHERE tf.titleID = @titleID;
			
		MERGE INTO dbo.tblFiles as f USING #tmpTitlesAndFiles AS tmp on 1 = 0
		WHEN NOT MATCHED THEN
			INSERT (fileTypeID, participantID, [fileName], fileTitle, fileDesc, formatsAvailable)
			VALUES (fileTypeID, participantID, [fileName], fileTitle, fileDesc, formatsAvailable)
				OUTPUT INSERTED.fileID, tmp.newTitleID, tmp.fileID, tmp.titleID, tmp.fileOrder, tmp.formatsAvailable, 
					tmp.isDownloadable, tmp.isDefaultStream, tmp.isDefaultPaper, tmp.isSupportingDoc, tmp.previewPct
				INTO #tmpNewTitlesAndFiles (newFileID, newTitleID, fileID, titleID, fileOrder, formatsAvailable, 
					isDownloadable, isDefaultStream, isDefaultPaper, isSupportingDoc, previewPct);

		INSERT INTO dbo.tblTitlesAndFiles (titleID, fileID, fileOrder, isDownloadable, isDefaultStream, isDefaultPaper, isSupportingDoc, previewPct)
		SELECT newTitleID, newFileID, fileOrder, isDownloadable, isDefaultStream, isDefaultPaper, isSupportingDoc, previewPct
		FROM #tmpNewTitlesAndFiles;

		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;

		WITH fileInfo AS (
			SELECT f.fileID, f.newFileID, 
				FFR.formatType.value('(@accesstype)[1]', 'varchar(2)') AS accesstype,
				FFR.formatType.value('(@ext)[1]', 'varchar(10)') AS ext, cast(null as varchar(100)) AS pg
			FROM #tmpNewTitlesAndFiles f
			CROSS APPLY f.formatsAvailable.nodes('/formats/format[@ext!=''pvr'']') AS FFR(formatType)
				union all
			SELECT f.fileID, f.newFileID,
				FFR.formatType.value('(../@accesstype)[1]', 'varchar(2)') AS accesstype,
				FFR.formatType.value('(../@ext)[1]', 'varchar(10)') AS ext,
				FFR.formatType.value('(@pg)[1]', 'varchar(100)') AS pg
			FROM #tmpNewTitlesAndFiles f
			CROSS APPLY f.formatsAvailable.nodes('/formats/format[@ext=''pvr'']/page') AS FFR(formatType)
		)
		INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID) 
		SELECT 'seminarweb',
			CASE 
			WHEN accesstype = 'D' AND ext = 'pvr' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(fileID as varchar(10)) +'.pvr/'+ pg)
			WHEN accesstype = 'D' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(fileID as varchar(10)) +'.'+ ext) 
			WHEN accesstype = 'S' THEN 
				LOWER('swod/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(fileID as varchar(10)) +'.'+ ext)					
			END,
			'seminarweb',
			CASE 
			WHEN accesstype = 'D' AND ext = 'pvr' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(newFileID as varchar(10)) +'.pvr/'+ pg)
			WHEN accesstype = 'D' THEN
				LOWER('swoddownloads/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(newFileID as varchar(10)) +'.'+ ext) 
			WHEN accesstype = 'S' THEN 
				LOWER('swod/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(newFileID as varchar(10)) +'.'+ ext)					
			END,
			@nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID 
		FROM fileInfo;

	COMMIT TRAN;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	SELECT '{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWTL-' + cast(titleID as varchar(20)) + ' [' + titleName + '] has been created by copying SWTL-' + cast(@titleID as varchar(20)) + '.'),'"','\"') + '" } }'
	FROM dbo.tblTitles
	WHERE titleID = @newTitleID;

	IF OBJECT_ID('tempdb..#tmpTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpTitlesAndFiles;
	IF OBJECT_ID('tempdb..#tmpNewTitlesAndFiles') IS NOT NULL 
		DROP TABLE #tmpNewTitlesAndFiles;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

