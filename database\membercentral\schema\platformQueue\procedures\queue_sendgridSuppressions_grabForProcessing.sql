ALTER PROC dbo.queue_sendgridSuppressions_grabForProcessing
@batchCount_singleEmail int = 30,
@batchCount_subUser int = 5

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	DECLARE @queueTypeID int, @readyToProcessStatusID int, @grabbedForProcessingStatusID int, @itemGroupUID uniqueIdentifier;
	EXEC dbo.queue_getQueueTypeID @queueType='SendgridSuppressions', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@grabbedForProcessingStatusID OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @grabbedForProcessingStatusID,
		qi.dateUpdated = GETDATE()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_sendgridSuppressions AS qi
	INNER JOIN (
		SELECT top(@batchCount_subUser) qi2.itemID
		FROM dbo.queue_sendgridSuppressions AS qi2
		WHERE qi2.statusID = @readyToProcessStatusID
		AND qi2.runAfter < GETDATE()
		AND qi2.emailAddress is null
		ORDER BY qi2.runAfter, qi2.dateAdded, qi2.itemID

		union

		SELECT top(@batchCount_singleEmail) qi2.itemID
		FROM dbo.queue_sendgridSuppressions AS qi2
		WHERE qi2.statusID = @readyToProcessStatusID
		AND qi2.runAfter < GETDATE()
		AND qi2.emailAddress is not null
		ORDER BY qi2.runAfter, qi2.dateAdded, qi2.itemID
	) AS batch ON batch.itemID = qi.itemID
	WHERE qi.statusID = @readyToProcessStatusID;

	SELECT qi.itemID, qi.itemGroupUID, qi.siteID, qi.subuserID, ss.username AS subuserName,
		qi.emailAddress, qi.referencedRecipientID, qi.dateAdded, qi.dateUpdated, qi.runAfter
	FROM dbo.queue_sendgridSuppressions AS qi
	INNER JOIN #tmpQueueItems AS tmp ON tmp.itemID = qi.itemID
	INNER JOIN platformmail.dbo.sendgrid_subusers AS ss ON ss.subuserID = qi.subuserID
	ORDER BY qi.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
