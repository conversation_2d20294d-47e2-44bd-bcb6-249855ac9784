ALTER PROC dbo.sf_disableMemberDocs
@siteID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @s3DeleteReadyStatusID int;
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tmpMemberDocs') IS NOT NULL 
		DROP TABLE #tmpMemberDocs;
	CREATE TABLE #tmpMemberDocs (documentID int PRIMARY KEY);

	INSERT INTO #tmpMemberDocs (documentID)
	SELECT DISTINCT d.documentID
	FROM dbo.ams_memberDocuments AS md
	INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = md.memberID
	INNER JOIN dbo.cms_documents AS d ON d.siteID = @siteID and d.documentID = md.documentID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = d.siteResourceID 
		AND sr.siteResourceStatusID = 1;

	BEGIN TRAN;
		IF @@ROWCOUNT > 0 BEGIN
			-- add to s3deletequeue
			INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
			select @s3DeleteReadyStatusID, s3bucketName, objectKey, GETDATE(), GETDATE()
			from (
				select 'membercentralcdn' as s3bucketName, lower('sitedocuments/' + o.orgcode + '/' + s.sitecode + '/' + right('0000' + cast(dv.documentVersionID % 1000 as varchar(4)),4) + '/' + cast(dv.documentVersionID as varchar(10)) + '.' + dv.fileExt) as objectKey
				from dbo.cms_documents as d
				inner join #tmpMemberDocs as tmp on tmp.documentID = d.documentID
				inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID
				inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID
					and dv.isActive = 1
				inner join dbo.sites as s on s.siteID = d.siteid
				inner join dbo.organizations as o on o.orgID = s.orgID
					EXCEPT
				select s3bucketName, objectKey
				from platformQueue.dbo.queue_S3Delete
			) tmp;

			-- mark as deleted
			UPDATE sr
			SET	sr.siteResourceStatusID = 3
			FROM dbo.cms_documents AS d
			INNER JOIN #tmpMemberDocs AS tmp ON tmp.documentID = d.documentID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = d.siteResourceID
			WHERE sr.siteResourceStatusID = 1;

			-- inactivate
			UPDATE dv
			SET	dv.isActive = 0
			FROM dbo.cms_documentVersions AS dv
			INNER JOIN dbo.cms_documentLanguages AS dl ON dl.documentLanguageID = dv.documentLanguageID
			INNER JOIN dbo.cms_documents AS d ON d.documentID = dl.documentID
			INNER JOIN #tmpMemberDocs AS tmp ON tmp.documentID = d.documentID
			WHERE dv.isActive = 1;
		END

		-- disable feature
		UPDATE dbo.siteFeatures
		SET memberDocuments = 0
		WHERE siteID = @siteID
		AND memberDocuments = 1;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpMemberDocs') IS NOT NULL 
		DROP TABLE #tmpMemberDocs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
