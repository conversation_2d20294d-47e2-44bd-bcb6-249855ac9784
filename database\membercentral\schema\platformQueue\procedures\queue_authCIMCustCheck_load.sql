ALTER PROC dbo.queue_authCIMCustCheck_load
@profileID int,
@gatewayUsername varchar(50),
@gatewayPassword varchar(75),
@customerProfileID varchar(50)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int;
	EXEC dbo.queue_getStatusIDbyType @queueType='authCIMCustCheck', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM dbo.queue_authCIMCustCheck WHERE profileID = @profileID AND customerProfileID = @customerProfileID) BEGIN
		INSERT INTO dbo.queue_authCIMCustCheck (profileID, gatewayUsername, gatewayPassword, customerProfileID, statusID, dateAdded, dateUpdated)
		VALUES (@profileID, @gatewayUsername, @gatewayPassword, @customerProfileID, @statusReady, GETDATE(), GETDATE());

		EXEC membercentral.dbo.sched_resumeTask @name='Process Authorize CIM Customers Check Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
