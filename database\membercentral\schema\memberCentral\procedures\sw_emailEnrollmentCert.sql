ALTER PROC dbo.sw_emailEnrollmentCert
@siteID int,
@enrollmentID int,
@emailSubject varchar(200),
@messageHTML varchar(max),
@toEmail varchar(200),
@outgoingType varchar(30),
@recordedByMemberID int,
@recordedByDepoMemberDataID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @orgName varchar(100), @resourceTypeID int, @messageTypeID int, 
		@messageStatusIDInserting int, @sendingSiteResourceID int, @supportProviderEmail varchar(100), 
		@messageID int, @emailTypeID int, @recipientID int, @memberID int, @fullname varchar(200), 
		@queueType varchar(25), @readyQueueStatusID int, @itemGroupUID uniqueIdentifier, 
		@swFormat varchar(4), @contentID int, @siteResourceID int, @contentVersionID int, 
		@overrideEmail varchar(200), @nowDate datetime = GETDATE();
	DECLARE @tblRecipients TABLE (recipientID int PRIMARY KEY);

	SELECT @memberID = e.MCMemberID,
		@swFormat = CASE 
			WHEN eswod.swodID is not null THEN 'SWOD'
			WHEN eswl.swlID is not null THEN 'SWL'
			ELSE ''
		END,
		@overrideEmail = ISNULL(eo.email,'')
	FROM seminarWeb.dbo.tblEnrollments AS e
	LEFT OUTER JOIN seminarWeb.dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	LEFT OUTER JOIN seminarWeb.dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID
	LEFT OUTER JOIN dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID 
		AND eo.itemType = 'semwebreg'
	WHERE e.enrollmentID = @enrollmentID
	AND e.isActive = 1;

	SELECT @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SELECT @sendingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('SemWebCatalog',@siteID);
	
	select @orgID = o.orgID, @orgName = oi.organizationName, @supportProviderEmail = n.supportProviderEmail
	from dbo.sites as s
	inner join dbo.organizations as o on o.orgid = s.orgid
	inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	inner join dbo.networkSites as ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1
	inner join dbo.networks as n on n.networkID = ns.networkID
	where s.siteID = @siteID;

	SELECT @emailTypeID = emailTypeID FROM dbo.ams_memberEmailTypes WHERE orgID = @orgID AND emailTypeOrder = 1;

	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'SEMWEBCERT';
	SELECT @messageStatusIDInserting = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'I';

	SET @itemGroupUID = newid();

	SELECT @queueType = CASE @swFormat WHEN 'SWL' THEN 'swlCertificate' WHEN 'SWOD' THEN 'swodCertificate' END;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType=@queueType, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;
	
	SELECT @fullname = mActive.firstName + ' ' + mActive.lastName 
	FROM dbo.ams_members AS m
	INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
	WHERE m.memberID = @memberID;

	-- create content object
	EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, 
		@parentSiteResourceID=@sendingSiteResourceID, @siteResourceStatusID=1, @isHTML=1, 
		@languageID=1, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', @rawContent=@messageHTML, 
		@memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

	SELECT TOP 1 @contentVersionID = cv.contentVersionID
	FROM dbo.cms_content as c 
	INNER JOIN dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
	INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
	WHERE c.contentID = @contentID;

	-- add email_message
	EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@nowDate, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@orgName, @fromEmail=@supportProviderEmail, @replyToEmail=@supportProviderEmail, 
		@senderEmail='', @subject=@emailSubject, @contentVersionID=@contentVersionID, @messageWrapper='', 
		@referenceType=null, @referenceID=null, @consentListIDs=null, @messageID=@messageID OUTPUT;

	-- add recipients as I (not ready to be queued yet)
	EXEC platformMail.dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@memberID, 
		@toName=@fullname, @toEmail=@toEmail, @emailTypeID=@emailTypeID, @statusCode='I', @siteID=@siteID, 
		@recipientID=@recipientID OUTPUT;
	
	INSERT INTO @tblRecipients (recipientID)
	VALUES (@recipientID);

	IF @overrideEmail <> '' AND @overrideEmail <> @toEmail BEGIN
		EXEC platformMail.dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@memberID, 
			@toName=@fullname, @toEmail=@overrideEmail, @emailTypeID=@emailTypeID, @statusCode='I', @siteID=@siteID, 
			@recipientID=@recipientID OUTPUT;

		INSERT INTO @tblRecipients (recipientID)
		VALUES (@recipientID);
	END
	
	-- add recepient references
	INSERT INTO platformMail.dbo.email_messageRecipientReferences (recipientID, referenceType, referenceID)
	SELECT recipientID, 'SWEnrollmentCert', @enrollmentID
	FROM @tblRecipients;

	-- platformQueue to handle certificate attachments
	IF @swFormat = 'SWL' BEGIN
		INSERT INTO platformQueue.dbo.queue_swlCertificate (itemGroupUID, enrollmentID, recipientID, outgoingType, performedByDepoMemberDataID, isProcessed, statusID, dateAdded, dateUpdated)
		SELECT @itemGroupUID, @enrollmentID, recipientID, @outgoingType, @recordedByDepoMemberDataID, 0, @readyQueueStatusID, GETDATE(), GETDATE()
		FROM @tblRecipients;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Process Email SWL Certificates Queue', @engine='MCLuceeLinux';
	END

	IF @swFormat = 'SWOD' BEGIN
		INSERT INTO platformQueue.dbo.queue_swodCertificate (itemGroupUID, enrollmentID, recipientID, outgoingType, performedByDepoMemberDataID, isProcessed, statusID, dateAdded, dateUpdated)
		SELECT @itemGroupUID, @enrollmentID, recipientID, @outgoingType, @recordedByDepoMemberDataID, 0, @readyQueueStatusID, GETDATE(), GETDATE()
		FROM @tblRecipients;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Process Email SWOD Certificates Queue', @engine='MCLuceeLinux';
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
