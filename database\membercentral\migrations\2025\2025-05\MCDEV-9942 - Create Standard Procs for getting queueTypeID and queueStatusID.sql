use platformQueue
GO

CREATE PROC dbo.queue_getQueueTypeID
@queueType varchar(25),
@queueTypeID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT @queueTypeID = queueTypeID
	FROM dbo.tblQueueTypes
	WHERE queueType = @queueType;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.queue_getStatusIDbyTypeID
@queueTypeID int,
@queueStatus varchar(30),
@queueStatusID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT @queueStatusID = queueStatusID
	FROM dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = @queueStatus;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 R<PERSON>L<PERSON><PERSON><PERSON> TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.queue_getStatusIDbyType
@queueType varchar(25),
@queueStatus varchar(30),
@queueStatusID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT;

	EXEC dbo.queue_getQueueTypeID @queueType=@queueType, @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus=@queueStatus, @queueStatusID=@queueStatusID OUTPUT;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use customApps
GO

ALTER PROC dbo.DBA_scannedRenewalsImport
@profileID int,
@batchID int,
@runByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	CREATE TABLE #tblImportErrors (rowid int IDENTITY(1,1), msg varchar(300));

	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#DBA_scannedRenewalsImport') IS NULL BEGIN
		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to locate the imported data for processing.');

		GOTO on_done;
	END

	DECLARE @orgID int, @siteID int, @gatewayType varchar(30), @subscriberIDList varchar(max), @itemGroupUID uniqueidentifier, 
		@markSubsAsAcceptedImportResult xml, @overridePendingPayment bit, @statusReady int;
	
	SET @importResult = null;

	SELECT @orgID = orgID, @siteID = siteID 
	FROM memberCentral.dbo.sites 
	WHERE siteCode = 'DBA';

	SELECT @gatewayType = g.gatewayType
	FROM memberCentral.dbo.mp_profiles as mp
	INNER JOIN memberCentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
	WHERE mp.profileID = @profileID;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='subscriptionAccept', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;


	-- ************************
	-- validate columns 
	-- ************************
	BEGIN TRY
		
		-- no blank MemberNumber
		update #DBA_scannedRenewalsImport set MemberNumber = '' where MemberNumber is null;

		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing MemberNumber.'
		FROM #DBA_scannedRenewalsImport
		WHERE MemberNumber = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- match on member
		update tmp 
		set tmp.MCMemberID = m.memberID
		from #DBA_scannedRenewalsImport as tmp 
		inner join memberCentral.dbo.ams_members as m on m.memberNumber = tmp.MemberNumber
			and m.orgID = @orgID
			and m.memberID = m.activeMemberID
			and m.status <> 'D';

		-- check for missing members
		BEGIN TRY
			ALTER TABLE #DBA_scannedRenewalsImport ALTER COLUMN MCMemberID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + MemberNumber + ' does not match an existing member.'
			FROM #DBA_scannedRenewalsImport
			WHERE MCMemberID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH


		-- check for missing subscriberID
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing SubscriberID.'
		FROM #DBA_scannedRenewalsImport
		WHERE SubscriberID IS NULL
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no dupe subscriberIDs
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'SubscriberID ' + cast(SubscriberID as varchar(10)) + ' appears multiple times; they must be unique.'
		FROM #DBA_scannedRenewalsImport
		GROUP BY SubscriberID
		HAVING COUNT(*) > 1
		ORDER BY 1;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- verify subscriberID
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing subscriber.'
		FROM #DBA_scannedRenewalsImport AS tmp
		LEFT OUTER JOIN memberCentral.dbo.sub_subscribers AS s 
				INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID = @orgID and m.memberID = s.memberID
				INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
				INNER JOIN memberCentral.dbo.sub_subscriptions AS sub ON sub.subscriptionID = s.subscriptionID
					AND s.subscriberID = s.rootSubscriberID
			ON s.subscriberID = tmp.subscriberID
		WHERE tmp.MCMemberID <> ISNULL(mActive.memberID,0)
		ORDER BY tmp.rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;


		-- clean AmountToCharge
		update #DBA_scannedRenewalsImport set AmountToCharge = memberCentral.dbo.fn_regexReplace(AmountToCharge,'[^0-9\.\-\(\)]','') where AmountToCharge is not null;

		-- check for null or negative AmountToCharge
		BEGIN TRY
			ALTER TABLE #DBA_scannedRenewalsImport ALTER COLUMN AmountToCharge decimal(18,2) not null;
			ALTER TABLE #DBA_scannedRenewalsImport ADD CONSTRAINT AmountToChargeCheck CHECK (AmountToCharge >= 0);
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not have a positive AmountToCharge amount.'
			FROM #DBA_scannedRenewalsImport
			WHERE AmountToCharge IS NULL OR AmountToCharge < 0
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		IF @gatewayType = 'OfflineCash' BEGIN
			-- check for missing checknumber
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing CheckNumber.'
			FROM #DBA_scannedRenewalsImport
			WHERE CheckNumber = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			IF ISNULL(@batchID,0) = 0 BEGIN
				INSERT INTO #tblImportErrors (msg)
				VALUES ('Select a valid open batch.');
				GOTO on_done;
			END

			-- check for missing paymentDate
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing Payment Date.'
			FROM #DBA_scannedRenewalsImport
			WHERE PaymentDate IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- check for invalid paymentDate
			BEGIN TRY
				ALTER TABLE #DBA_scannedRenewalsImport ALTER COLUMN PaymentDate date not null;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblImportErrors (msg)
				VALUES ('Import file contains invalid payment dates.');
				GOTO on_done;
			END CATCH

			SET @overridePendingPayment = 1;
			
		END
		ELSE BEGIN
			-- check for missing payprofileid
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing PayProfileID.'
			FROM #DBA_scannedRenewalsImport
			WHERE PayProfileID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			SET @batchID = NULL;
			SET @overridePendingPayment = 0;
		END


	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to validate data in required columns.');

		INSERT INTO #tblImportErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH


	-- ************************
	-- queue import data
	-- ************************
	BEGIN TRY
		SELECT @subscriberIDList = COALESCE(@subscriberIDList + ',', '') + cast(subscriberID as varchar(10)) 
		FROM #DBA_scannedRenewalsImport;

		IF @subscriberIDList IS NOT NULL BEGIN
			BEGIN TRAN;
			
				EXEC memberCentral.dbo.sub_queueMarkAccepted @recordedByMemberID=@runByMemberID, @subscriberIDList=@subscriberIDList, 
					@suppressEmails=0, @markQueueAsReady=0, @importResult=@markSubsAsAcceptedImportResult OUTPUT;

				SELECT @itemGroupUID = @markSubsAsAcceptedImportResult.value('(/import/@itemGroupUID)[1]','uniqueidentifier');

				-- update payment information & mark queue as ready
				UPDATE qi
				SET qi.merchantProfileID = @profileID,
					qi.paymentDate = tmp.PaymentDate,
					qi.batchID = @batchID,
					qi.memberPayProfileID = tmp.PayProfileID,
					qi.checkNumber = tmp.checkNumber,
					qi.amountToCharge = tmp.amountToCharge,
					qi.overridePendingPayment = @overridePendingPayment,
					qi.statusID = @statusReady,
					qi.dateUpdated = getdate()
				FROM platformQueue.dbo.queue_subscriptionAccept as qi
				INNER JOIN #DBA_scannedRenewalsImport as tmp on tmp.subscriberID = qi.subscriberID
				WHERE qi.itemGroupUID = @itemGroupUID;
			COMMIT TRAN;
		END

	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to add data to the queue.');

		INSERT INTO #tblImportErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT memberCentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.job_ALLmemberJoinDates
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	DECLARE @readyToProcessStatusID int, @minItemID int, @xmlData xml;
	SET @itemCount = 0;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberJoinDates', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	
	INSERT INTO platformQueue.dbo.queue_memberJoinDates (udid, dateAdded, dateUpdated, statusID)
		OUTPUT INSERTED.itemID INTO #tmpQueueItems (itemID)
	SELECT udid, GETDATE(), GETDATE(), @readyToProcessStatusID
	FROM dbo.schedTask_memberJoinDates 
	WHERE isActive = 1;

	SET @itemCount = @@ROWCOUNT;

	SELECT @minItemID = MIN(itemID) FROM #tmpQueueItems;
	WHILE @minItemID IS NOT NULL BEGIN
		SET @xmlData = NULL;

		SELECT @xmlData = isnull((
			SELECT @minItemID as i
			FOR XML RAW('mc'), TYPE
		),'<mc/>');

		EXEC platformQueue.dbo.queue_memberJoinDates_sendMessage @xmlMessage=@xmlData;

		SELECT @minItemID = MIN(itemID) FROM #tmpQueueItems WHERE itemID > @minItemID;
	END

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.job_memberJoinDates 
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	IF OBJECT_ID('tempdb..#tmpUDIDSubs') IS NOT NULL 
		DROP TABLE #tmpUDIDSubs;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp2') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp2;
	IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
		DROP TABLE #subJoinDates;
	IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
		DROP TABLE #mc_PartialMemImport;

	CREATE TABLE #tmpUDIDSubs (subscriptionID int PRIMARY KEY);
	CREATE TABLE #subJoinDatesTmp (activeMemberID int PRIMARY KEY, theDate dateTime);
	CREATE TABLE #subJoinDatesTmp2 (activeMemberID int PRIMARY KEY);
	CREATE TABLE #subJoinDates (row int identity(1,1) PRIMARY KEY, activeMemberID int, joinDate dateTime, 
		dropDate dateTime, rejoinDate dateTime, paidThruDate dateTime, renewalDate dateTime, [action] varchar(1));
	
	declare @readyToProcessStatusID int, @processingStatusID int, @udid int, @siteID int, @orgID int, 
		@siteCode varchar(10), @orgShortName varchar(20), @joinColumnID int, @compareDate datetime, @joinFieldName varchar(128), 
		@dropFieldName varchar(128), @rejoinFieldName varchar(128), @paidthrudatefieldname varchar(128), 
		@renewalDateFieldName varchar(128), @droppedColumnID int, @rejoinColumnID int, @paidThruColumnID int, 
		@renewalColumnID int, @emailSubject varchar(50), @AStatusID int, @EStatusID int, @XStatusID int, 
		@DStatusID int, @PStatusID int, @OStatusID int, @IStatusID int, @PPayStatusID int, @importResult xml, 
		@errCount int = 0, @runByMemberID int, @errorSubject VARCHAR(100), @errmsg NVARCHAR(2048);
	declare @tblMemberJoinDates table (memberID int, joinDate dateTime, dropDate dateTime, rejoinDate dateTime, 
		paidThruDate dateTime, renewalDate dateTime, oldJoinDate datetime, olddropDate dateTime, oldrejoinDate dateTime, 
		oldpaidThruDate dateTime, oldrenewalDate dateTime);

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberJoinDates', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberJoinDates', @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;

	SELECT @udid = udid
	FROM platformQueue.dbo.queue_memberJoinDates
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @udid IS NULL
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_memberJoinDates
	SET statusID = @processingStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	select @siteCode = sitecode, @compareDate = isnull(lastSuccessDate,'1/1/1900'),
		@joinFieldName = joinDateFieldName, @dropFieldName = droppedDateFieldName,
		@rejoinFieldName = rejoinDateFieldName, @paidthrudatefieldname = paidThruDateFieldName,
		@renewalDateFieldName = renewalDateFieldName
	from dbo.schedTask_memberJoinDates
	where udid = @udid;

	select @siteID = s.siteID, @orgID = o.orgID, @orgShortName = oi.organizationShortName
	from membercentral.dbo.sites as s
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	where s.siteCode = @siteCode;

	SELECT @runByMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);

	set @emailSubject = @orgShortName + ' Member Join Dates Update Report';

	select @joinColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @joinFieldName;
	select @droppedColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @dropFieldName;
	select @rejoinColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @rejoinFieldName;
	select @paidThruColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @paidthrudatefieldname;
	select @renewalColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @renewalDateFieldName;
	select @AStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'A';
	select @EStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'E';
	select @IStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'I';
	select @XStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'X';
	select @DStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'D';
	select @PStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'P';
	select @OStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'O';
	select @PPayStatusID = statusID from membercentral.dbo.sub_paymentStatuses where statusCode = 'P';

	-- get all the subscriptions we are considering from the udid types/subs
	insert into #tmpUDIDSubs (subscriptionID)
	select distinct s.subscriptionID 
	from dbo.schedTask_memberJoinDateSubTypes as mjdst
	inner join membercentral.dbo.sub_types as t on t.siteID = @siteID 
		and t.[uid] = mjdst.subscriptionTypeUID
	inner join membercentral.dbo.sub_subscriptions as s on s.orgID = @orgID 
		and s.typeID = t.typeID
		and isnull(mjdst.subscriptionUID,s.[uid]) = s.[uid]
	where mjdst.memberJoinDateUDID = @udid;

	-- get rows that do not have a join date but do have an active subscription
	IF @joinColumnID IS NOT NULL BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(sh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID and sh.subscriberID = s.subscriberID 
			and sh.statusID = @AStatusID
		left outer join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and (sh.updateDate > @compareDate or psh.updateDate > @compareDate)
		group by m.activeMemberID;

		insert into #subJoinDates (activeMemberID, joinDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'J'
		from #subJoinDatesTmp as tmp
		left outer join membercentral.dbo.ams_memberData as md 
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @joinColumnID and mdcv.valueID = md.valueID
			on md.memberID = tmp.activeMemberID
		where mdcv.columnvalueDate is null;

		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- get rows that have been expired, deleted, or had an offer expired and do not have an active, pending, or offered Status
	IF @droppedColumnID IS NOT NULL BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(sh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID and sh.subscriberID = s.subscriberID 
			and sh.statusID in (@EStatusID,@XStatusID,@DStatusID)
		where m.orgID = @orgID
		and sh.updateDate > @compareDate
		group by m.activeMemberID;

		delete from #subJoinDatesTmp
		where activeMemberID in (
			select distinct m.activeMemberID
			from membercentral.dbo.ams_members as m 
			inner join #subJoinDatesTmp as sd on sd.activeMemberID = m.activeMemberID
			inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
				and s.memberID = m.memberID
				and s.statusID in (@AStatusID,@PStatusID,@OStatusID)
			inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
			where m.orgID = @orgID
		);

		insert into #subJoinDates (activeMemberID, dropDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'D'
		from #subJoinDatesTmp as tmp;

		TRUNCATE TABLE #subJoinDatesTmp;	
	END

	-- get people who have been moved to active, have a drop date, and rejoin date is null or before drop date
	IF @rejoinColumnID is not null BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(sh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
			and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID 
			and sh.subscriberID = s.subscriberID 
			and sh.statusID = @AStatusID
		left outer join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and (sh.updateDate > @compareDate or psh.updateDate > @compareDate)
		group by m.activeMemberID;

		insert into #subJoinDates (activeMemberID, rejoinDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'R'
		from #subJoinDatesTmp as tmp
		inner join membercentral.dbo.ams_memberData as mdD on mdD.memberID = tmp.activeMemberID
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcvD on mdcvD.columnID = @droppedColumnID and mdcvD.valueID = mdD.valueID
		left outer join membercentral.dbo.ams_memberData as mdR 
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcvR on mdcvR.columnID = @rejoinColumnID and mdcvR.valueID = mdR.valueID
			on mdR.memberID = tmp.activeMemberID
		where mdcvD.columnvalueDate is not null
		and (mdcvR.columnvalueDate is null or mdcvR.columnvalueDate < mdcvD.columnvalueDate);
		
		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- find members with subscription activity and add those needing paidThrudate changes
	-- only consider if the current status is one that implies that the subscription was or will be active at some point 
	IF @paidThruColumnID IS NOT NULL BEGIN
		insert into #subJoinDatesTmp2 (activeMemberID)
		select distinct m.activeMemberID
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
			and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID 
			and sh.subscriberID = s.subscriberID 
		left outer join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and (sh.updateDate > @compareDate or psh.updateDate > @compareDate);

		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select tmp.activeMemberID, max(latestActivatedS.subenddate)
		from #subJoinDatesTmp2 as tmp
		inner join membercentral.dbo.ams_members as allm on allm.orgID = @orgID
			and allm.activeMemberID = tmp.activeMemberID
		inner join membercentral.dbo.sub_subscribers as latestActivatedS on latestActivatedS.orgID = @orgID
			and latestActivatedS.memberID = allm.memberID
			and latestActivatedS.statusID in (@AStatusID,@IStatusID,@PStatusID,@EStatusID)
			and latestActivatedS.paymentStatusID = @PPayStatusID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = latestActivatedS.subscriptionID
		group by tmp.activeMemberID;

		insert into #subJoinDates (activeMemberID, paidThruDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'P'
		from #subJoinDatesTmp as tmp
		left outer join membercentral.dbo.ams_memberData as md 
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @paidThruColumnID and mdcv.valueID = md.valueID
			on md.memberID = tmp.activeMemberID
		where (mdcv.columnvalueDate is null or mdcv.columnvalueDate <> tmp.theDate);

		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- get people who have been moved to active or accepted, have a join date, updatedate is after join date, and updatedate is after renewal date or renewaldate is null
	IF @renewalColumnID is not null BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(psh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
			and s.memberID = m.memberID
			and s.statusID in (@AStatusID,@PStatusID)
			and s.paymentStatusID = @PPayStatusID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = s.rfid
		inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID
			and r.isRenewalRate = 1
			and r.status = 'A'
		inner join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and psh.updateDate > @compareDate
		group by m.activeMemberID;	

		insert into #subJoinDates (activeMemberID, renewalDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'W'
		from #subJoinDatesTmp as tmp
		inner join membercentral.dbo.ams_memberData as mdJ on mdJ.memberID = tmp.activeMemberID
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcvJ on mdcvJ.columnID = @joinColumnID and mdcvJ.valueID = mdJ.valueID
		left outer join membercentral.dbo.ams_memberData as mdR
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcvR on mdcvR.columnID = @renewalColumnID and mdcvR.valueID = mdR.valueID
			on mdR.memberID = tmp.activeMemberID
		where mdcvJ.columnvalueDate is not null
		and (mdcvR.columnvalueDate is null or tmp.theDate > mdcvR.columnvalueDate)
		and tmp.theDate > mdcvJ.columnvalueDate;

		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- if no members to update, get out now
	IF not exists (select top 1 activeMemberID from #subJoinDates)
		GOTO on_done;

	-- get unique active members that are not deleted
	insert into @tblMemberJoinDates (memberID)
	select distinct jd.activeMemberID
	from #subJoinDates as jd
	inner join membercentral.dbo.ams_members as m on m.memberID = jd.activeMemberID
	where m.[status] in ('A','I');

	-- flatten table
	update tmp
	set tmp.joinDate = jd.joinDate,
		tmp.dropDate = dd.dropDate,
		tmp.rejoinDate = rd.rejoinDate,
		tmp.paidThruDate = pd.paidThruDate,
		tmp.renewalDate = wd.renewalDate
	from @tblMemberJoinDates as tmp
	left outer join #subJoinDates as jd on jd.activeMemberID = tmp.memberID and jd.[action] = 'J'
	left outer join #subJoinDates as dd on dd.activeMemberID = tmp.memberID and dd.[action] = 'D'
	left outer join #subJoinDates as rd on rd.activeMemberID = tmp.memberID and rd.[action] = 'R'
	left outer join #subJoinDates as pd on pd.activeMemberID = tmp.memberID and pd.[action] = 'P'
	left outer join #subJoinDates as wd on wd.activeMemberID = tmp.memberID and wd.[action] = 'W';

	-- get existing values
	update tmp 
	set tmp.oldjoinDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @joinColumnID;

	update tmp 
	set tmp.olddropDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @droppedColumnID;

	update tmp 
	set tmp.oldrejoinDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @rejoinColumnID;
	
	update tmp 
	set tmp.oldpaidThruDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @paidThruColumnID;

	update tmp 
	set tmp.oldrenewalDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @renewalColumnID;

	-- force times to midnight
	update @tblMemberJoinDates
	set joinDate = DATEADD(dd, DATEDIFF(dd,0,joinDate), 0),
		dropDate = DATEADD(dd, DATEDIFF(dd,0,dropDate), 0),
		rejoinDate = DATEADD(dd, DATEDIFF(dd,0,rejoinDate), 0),
		paidThruDate = DATEADD(dd, DATEDIFF(dd,0,paidThruDate), 0),
		renewalDate = DATEADD(dd, DATEDIFF(dd,0,renewalDate), 0),
		oldjoinDate = DATEADD(dd, DATEDIFF(dd,0,oldjoinDate), 0),
		olddropDate = DATEADD(dd, DATEDIFF(dd,0,olddropDate), 0),
		oldrejoinDate = DATEADD(dd, DATEDIFF(dd,0,oldrejoinDate), 0),
		oldpaidThruDate = DATEADD(dd, DATEDIFF(dd,0,oldpaidThruDate), 0),
		oldrenewalDate = DATEADD(dd, DATEDIFF(dd,0,oldrenewalDate), 0);

	-- set the date fields if they are null to use existing values
	update @tblMemberJoinDates set joinDate = oldJoinDate where joinDate is null;
	update @tblMemberJoinDates set rejoinDate = oldRejoinDate where rejoinDate is null;
	update @tblMemberJoinDates set paidThruDate = oldpaidThruDate where paidThruDate is null;
	update @tblMemberJoinDates set renewalDate = oldrenewalDate where renewalDate is null;
	update @tblMemberJoinDates set dropDate = olddropDate where dropDate is null;

	-- delete records with no changes
	delete from @tblMemberJoinDates
	where isnull(joinDate,'1/1/1900') = isnull(oldJoinDate,'1/1/1900')
	AND isnull(rejoinDate,'1/1/1900') = isnull(oldRejoinDate,'1/1/1900')
	AND isnull(paidThruDate,'1/1/1900') = isnull(oldpaidThruDate,'1/1/1900')
	AND isnull(renewalDate,'1/1/1900') = isnull(oldrenewalDate,'1/1/1900')
	AND isnull(dropDate,'1/1/1900') = isnull(olddropDate,'1/1/1900');

	IF not exists (select top 1 memberID from @tblMemberJoinDates)
		GOTO on_done;

	select m.memberNumber, tmp.joinDate, tmp.dropDate, tmp.rejoinDate, tmp.paidThruDate, tmp.renewalDate,
		ROW_NUMBER() over (order by m.memberNumber asc) as rowID
	into #mc_PartialMemImport
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_Members as m on m.memberID = tmp.memberID;

	IF @joinFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.joinDate', @joinFieldName, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@joinFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN joinDate;
	
	IF @dropFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.dropDate', @dropFieldName, 'COLUMN';;
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@dropFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN dropDate;

	IF @rejoinFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.rejoinDate', @rejoinFieldName, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@rejoinFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN rejoinDate;

	IF @paidthrudatefieldname is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.paidThruDate', @paidthrudatefieldname, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@paidthrudatefieldname+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN paidThruDate;

	IF @renewalDateFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.renewalDate', @renewalDateFieldName, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@renewalDateFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN renewalDate;

	EXEC membercentral.dbo.ams_importPartialMemberData_autoconfirm @orgID=@orgID, @importTitle='Member Join Dates Nightly Job', 
		@runByMemberID=null, @activateIncMembers=0, @inactivateNonIncMembers=0, @thresholdLimit=0, @bypassRO=1, 
		@finalMSGHeader='MemberCentral automatically', @emailSubject=@emailSubject,
		@environmentName=null, @importResult=@importResult OUTPUT, @errCount=@errCount OUTPUT;
	
	on_done:
	IF @errCount = 0
		update dbo.schedTask_memberJoinDates
		set lastSuccessDate = getDate()	
		where udid = @udid;

	-- delete from queue
	IF @itemID IS NOT NULL
		DELETE FROM platformQueue.dbo.queue_memberJoinDates
		WHERE itemID = @itemID;

	IF OBJECT_ID('tempdb..#tmpUDIDSubs') IS NOT NULL 
		DROP TABLE #tmpUDIDSubs;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp2') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp2;
	IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
		DROP TABLE #subJoinDates;
	IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
		DROP TABLE #mc_PartialMemImport;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_processSubscriberDepositionsFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpFilteredDocIDs') IS NOT NULL 
		DROP TABLE #tmpFilteredDocIDs;
	IF OBJECT_ID('tempdb..#tmpDepoMemberDataIDs') IS NOT NULL 
		DROP TABLE #tmpDepoMemberDataIDs;
	IF OBJECT_ID('tempdb..#tmpSiteAdmins') IS NOT NULL
		DROP TABLE #tmpSiteAdmins;
	CREATE TABLE #tmpFilteredDocIDs (documentID int);
	CREATE TABLE #tmpDepoMemberDataIDs (depomemberDataID int);
	CREATE TABLE #tmpSiteAdmins (depomemberdataID int);

	DECLARE @readyToProcessStatusID int, @processingItemStatusID int, @firstName varchar(100), @lastName varchar(100), @searchText varchar(400);

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='tsSubscriberDepos', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='tsSubscriberDepos', @queueStatus='processingItem', @queueStatusID=@processingItemStatusID OUTPUT;

	SELECT @firstName = firstName, @lastName = lastName
	FROM platformQueue.dbo.queue_tsSubscriberDepos
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @firstName IS NULL
		GOTO on_done;

	-- update queue item status
	UPDATE platformQueue.dbo.queue_tsSubscriberDepos
	SET statusID = @processingItemStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- site admins
	INSERT INTO #tmpSiteAdmins (depomemberdataID)
	SELECT DISTINCT np.depomemberdataID
	FROM membercentral.dbo.ams_networkProfiles AS np
	INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp ON mnp.profileID = np.profileID
		AND mnp.status = 'A'
		AND np.status = 'A'
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = mnp.memberID
	INNER JOIN membercentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.organizations AS org ON org.orgID = mActive.orgID
	INNER JOIN membercentral.dbo.ams_groups AS g ON g.orgID = org.orgID
		AND g.groupCode = 'SiteAdmins'
	INNER JOIN membercentral.dbo.cache_members_groups AS mg ON mg.memberID = mActive.memberID
		AND mg.groupID = g.groupID;

	-- get matching depomembers
	INSERT INTO #tmpDepoMemberDataIDs (depomemberDataID)
	SELECT DISTINCT d.depomemberdataID
	FROM trialsmith.dbo.depomemberdataUniqueNames AS du
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.uniqueNameID = du.uniqueID
	INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.depoMemberDataID = d.depomemberdataID
	INNER JOIN trialsmith.dbo.membertype as mt on mt.AcctCode = dt.AccountCode
		AND mt.AcctCode NOT IN ('1000','1201','1007')
	LEFT OUTER JOIN #tmpSiteAdmins AS tmp ON tmp.depoMemberDataID = d.depomemberdataID
	WHERE du.firstName = @firstName
	AND du.lastName = @lastName
	AND d.TLAMemberState <> 'CP'
	AND d.adminFlag2 <> 'Y'
	AND d.includeInSubscriberDepos = 1
	AND tmp.depomemberdataID IS NULL;

	IF @@ROWCOUNT > 0 BEGIN
		-- clear existing documents tied to these depomemberdataids
		IF EXISTS (
			select 1 
			FROM searchMC.dbo.ts_subscriberDepositions as tsd
			INNER JOIN #tmpDepoMemberDataIDs AS dm on dm.depomemberDataID = tsd.depomemberdataID)
		DELETE tsd
		FROM searchMC.dbo.ts_subscriberDepositions as tsd
		INNER JOIN #tmpDepoMemberDataIDs AS dm on dm.depomemberDataID = tsd.depomemberdataID;

		-- escape single quotes and double quotes
		SET @firstName = REPLACE(REPLACE(@firstName,'''',''''''),'"','""');
		SET @lastName = REPLACE(REPLACE(@lastName,'''',''''''),'"','""');

		DECLARE @FirstGroup TABLE (AGroup VARCHAR(50));
		INSERT INTO @FirstGroup VALUES ('Taken by');
		INSERT INTO @FirstGroup VALUES ('For the plaintiff');
		INSERT INTO @FirstGroup VALUES ('For the plaintiffs');
		INSERT INTO @FirstGroup VALUES ('For plaintiffs');
		INSERT INTO @FirstGroup VALUES ('For plaintiff');
		INSERT INTO @FirstGroup VALUES ('Represented by');
		INSERT INTO @FirstGroup VALUES ('On Behalf of the Plaintiff');
		INSERT INTO @FirstGroup VALUES ('On Behalf of the Plaintiffs');

		DECLARE @SearchString VARCHAR(3000);
		SET @SearchString = 'tsdoctypeid1xxx and ( NEAR(("Appearances","'+@firstName+'","'+@lastName+'") , 50, TRUE) OR ';

		SELECT @SearchString = @SearchString + 'NEAR(("'+AGroup+'","'+@firstName+'", "'+@lastName+'"), 20, TRUE) OR '
		FROM @FirstGroup;

		SELECT @SearchString = LEFT(@SearchString, LEN(@SearchString) - 3);
		SELECT @SearchString = @SearchString + ')';
		
		INSERT INTO #tmpFilteredDocIDs (documentID)
		SELECT d.documentID
		FROM containstable(search.dbo.depoDocuments,limitedsearchtext, @Searchstring) as ft inner join search.dbo.depoDocuments d on d.id = ft.[KEY];
		
		IF @@ROWCOUNT > 0
			INSERT INTO searchMC.dbo.ts_subscriberDepositions (depomemberdataID, depoDocumentID)
			SELECT dm.depomemberdataID, d.documentID
			FROM #tmpFilteredDocIDs AS tmp
			INNER JOIN trialsmith.dbo.depodocuments AS d ON d.documentID = tmp.documentID
			OUTER APPLY #tmpDepoMemberDataIDs AS dm
			WHERE d.disabled = 'N';
	END

	-- delete queue item
	DELETE FROM platformQueue.dbo.queue_tsSubscriberDepos
	WHERE itemID = @itemID;

	on_done:

	IF OBJECT_ID('tempdb..#tmpFilteredDocIDs') IS NOT NULL 
		DROP TABLE #tmpFilteredDocIDs;
	IF OBJECT_ID('tempdb..#tmpDepoMemberDataIDs') IS NOT NULL 
		DROP TABLE #tmpDepoMemberDataIDs;
	IF OBJECT_ID('tempdb..#tmpSiteAdmins') IS NOT NULL
		DROP TABLE #tmpSiteAdmins;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use platformMail
GO

ALTER PROC dbo.email_checkEmailSendingQueue

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @fifteenMinutesInTheFuture datetime = dateadd(minute,15,getdate()), @insertingStatusID int, @cancelledStatusID int, 
		@queuedStatusID int, @requeuedStatusID int, @pendingStatusID int, @releasedStatusID int, @processingStatusID int, 
		@scheduledStatusID int, @erroredStatusID int, @scheduledJobsFound bit=0, @futureDatedQueuedMessagesFound bit = 0, 
		@issueCount int, @tier varchar(12), @defaultMarketingSubUserID int, @defaultTransactionalSubUserID int, @environmentID int,
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @errorContent varchar(max), @checkDate datetime, 
		@actionNeeded bit = 0, @numFound int = 0, @anyCancelled bit = 0, @statusID int, @now datetime,  @errmsg varchar(max);
	declare @recipientsToChange TABLE (recipientID int PRIMARY KEY, messageID int);
	declare @recipientsToConsider TABLE (recipientID int PRIMARY KEY, messageID int);

	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	select @insertingStatusID = statusID from dbo.email_statuses where statusCode = 'I';
	select @cancelledStatusID = statusID from dbo.email_statuses where statusCode = 'C';
	select @processingStatusID = statusID from dbo.email_statuses where statusCode = 'G';
	select @pendingStatusID = statusID from dbo.email_statuses where statusCode = 'P';
	select @queuedStatusID = statusID from dbo.email_statuses where statusCode = 'Q';
	SELECT @requeuedStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'R';
	SELECT @erroredStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'E';
	select @scheduledStatusID = statusID from dbo.email_statuses where statusCode = 'scheduled';
	SELECT @releasedStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'released';

	-- check for items marked as Queued where message send date is more than 10 minutes in the future
	-- hardcoded @queuedStatusID as 2 to use filtered index
	select top 1 @futureDatedQueuedMessagesFound = m.messageID
	from dbo.email_messageRecipientHistory as mrh
	inner join dbo.email_messages as m 
		on mrh.emailStatusID = 2
		and mrh.siteID = m.siteID
		and m.status = 'A'
		and m.sendOnDate > @fifteenMinutesInTheFuture
		and m.messageID = mrh.messageID;

	-- check for scheduled jobs that are now due within the next 10 minutes, mark then as Queued if found
	-- hardcoded @scheduledStatusID as 18 to use filtered index
	select top 1 @scheduledJobsFound = mrh.recipientID
	from dbo.email_messageRecipientHistory as mrh
	inner join dbo.email_messages as m 
		on mrh.emailStatusID = 18
		and mrh.siteID = m.siteID
		and mrh.batchID is null
		and m.status = 'A'
		and m.messageID = mrh.messageID
		and m.sendOnDate < @fifteenMinutesInTheFuture;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	IF @futureDatedQueuedMessagesFound = 1
		-- hardcoded @queueStatusID as 2 to use filtered index
		update mrh 
		set mrh.emailStatusID = @scheduledStatusID
		from dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m 
			on mrh.emailStatusID = 2
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate > @fifteenMinutesInTheFuture
			and m.messageID = mrh.messageID;

	if @scheduledJobsFound = 1
		-- hardcoded @scheduledStatusID as 18 to use filtered index
		update mrh 
		set mrh.emailStatusID = @queuedStatusID
		from dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m 
			on mrh.emailStatusID = 18
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate < @fifteenMinutesInTheFuture
			and m.messageID = mrh.messageID;
	

	-- get all recipients not in the emailExtMergeCode queue or the eventCertificate queue
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
		declare @recipientsToCancel TABLE (recipientID int PRIMARY KEY);
		DECLARE @EMCqueueTypeID int, @EMCstatusReady int, @EMCstatusGrabbed int, @EMCrecipientIDDataColumnID int;
		EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@EMCqueueTypeID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@EMCqueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@EMCstatusReady OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@EMCqueueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@EMCstatusGrabbed OUTPUT;
		select @EMCrecipientIDDataColumnID = columnID from platformQueue.dbo.tblQueueTypeDataColumns where queueTypeID = @EMCqueueTypeID and columnName = 'MCRecipientID';
		SET @checkDate = dateadd(minute,-30,getdate());
		SET @numFound = 0;
		SET @anyCancelled = 0;

		-- hardcoded @insertingStatusID as 1 for inserting to use filtered index 
		INSERT INTO @recipientsToConsider (recipientID, messageID)  
		SELECT recipientID, messageID  
		FROM dbo.email_messageRecipientHistory
		WHERE emailStatusID = 1  
		AND dateLastUpdated < @checkDate;

		insert into @recipientsToCancel (recipientID)
		SELECT recipientID
		FROM @recipientsToConsider
			except
		SELECT recipientID
		FROM (
			SELECT qid.columnValueInteger as recipientID
			FROM platformQueue.dbo.tblQueueItems as qi2
			inner join platformQueue.dbo.tblQueueItemData as qid on qi2.itemUID = qid.itemUID 
				AND qid.columnID = @EMCrecipientIDDataColumnID
			WHERE qi2.queueStatusID in (@EMCstatusReady, @EMCstatusGrabbed)
				UNION
			SELECT recipientID
			FROM platformQueue.dbo.queue_eventCertificate
				UNION
			SELECT recipientID
			FROM platformQueue.dbo.queue_swodCertificate
				UNION
			SELECT recipientID
			FROM platformQueue.dbo.queue_swlCertificate
		) as tmp;

		SET @numFound = @@ROWCOUNT;
		IF @numFound > 0
			SET @anyCancelled = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @anyCancelled = 1 BEGIN
			update mrh WITH (UPDLOCK, HOLDLOCK) 
			SET mrh.emailStatusID = @cancelledStatusID
			FROM @recipientsToCancel rc 
			inner join dbo.email_messageRecipientHistory mrh on mrh.recipientID = rc.recipientID;

			IF @tier = 'Production' BEGIN
				SET @errorSubject = 'Investigation Needed: Email Sending queue had potential problem Inserting Recipients. The recipients have been cancelled.';
				SET @errmsg = 'There were recipients marked as Inserting that hadn''t been updated for 30 minutes. They have been cancelled.';
				EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
			END
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	-- items marked as Queued WHERE datelastupdated older than 2 hours 
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		SET @checkDate = dateadd(hour,-2,getdate());

		-- hardcoded @queuedStatusID as 2 to use filtered index
		SELECT @numFound=count(*)
		FROM dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m 
			on mrh.emailStatusID = 2
			and mrh.siteID = m.siteID
			and m.status = 'A'
			AND m.sendOnDate < @checkDate
			and m.messageID = mrh.messageID
			and mrh.dateLastUpdated < @checkDate
		group by mrh.siteID, mrh.messageID;
		
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 and @tier = 'Production' BEGIN
			SET @errorSubject = 'Investigation Needed: Email Sending queue has old recipients marked as queued.';
			SET @errmsg = 'There are recipients marked as Queued that haven''t been updated in at least 2 hours. Developer needs to make sure scheduled task is running AND not failing.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	-- items marked as Requeue with datelastupdated more than 5 mins ago
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-5,@now);

		DECLARE @recipientsToUpdate TABLE (recipientID int PRIMARY KEY)

		-- hardcoded @requeuedStatusID as 7 to use filtered index
		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m 
			on mrh.emailStatusID = 7
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.messageID = mrh.messageID
			and mrh.dateLastUpdated < @checkDate;

		IF @numFound > 0
			insert into @recipientsToUpdate (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 7
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				and mrh.dateLastUpdated < @checkDate;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @queuedStatusID
			FROM @recipientsToUpdate temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		IF @numFound > 0 and @tier = 'Production' BEGIN
			SET @errorSubject = 'Email Sending queue had recipients in Requeue status.';
			SET @errmsg = 'There were '+CAST(@numFound AS varchar)+ 'recipient(s) in Requeue status with datelastupdated more than 5 mins ago. They have been reset to Queued.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END      

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	--  items marked as Errored WHERE datelastupdated older than 15 minutes
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SET @checkDate = dateadd(minute,-15,getdate());

			-- hardcoded @erroredStatusID as 5 to use filtered index
			SELECT @numFound=count(*)
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 5 
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				and mrh.dateLastUpdated < @checkDate;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			IF @numFound > 0 BEGIN
				SET @errorSubject = 'Investigation Needed: Email Sending queue has errors sending to recipients.';
				SET @errmsg = 'There are recipients marked as Errored that haven''t been updated in at least 15 minutes. These must be resolved by a developer.';
				EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END


	--  items marked as Processing WHERE datelastupdated older than 15 minutes. Auto cancel.
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @recipientsToUpdate1 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-15,getdate());
		SET @anyCancelled = 0;
		
		-- hardcoded @pendingStatusID as 3 to use filtered index
		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m 
			on mrh.emailStatusID = 3 
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.messageID = mrh.messageID
			and mrh.dateLastUpdated < @checkDate;

		IF @numFound > 0
			insert into @recipientsToUpdate1 (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 3
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				and mrh.dateLastUpdated < @checkDate;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 BEGIN
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @cancelledStatusID
			FROM @recipientsToUpdate1 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

			IF @@ROWCOUNT > 0
				SET @anyCancelled = 1;
		END

		IF @anyCancelled > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject = 'Investigation Needed: Email Sending queue had old recipients marked as processing. The recipients have been cancelled.';
			SET @errmsg = 'There were recipients marked as Processing that hadn''t been updated for 15 minutes. They have been cancelled.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH



	-- cancel recipients marked as Queued WHERE message has been deleted
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @deletedMessageRecipientsToCancel TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @anyCancelled = 0;

		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m on mrh.siteID = m.siteID
			and m.status = 'D'
			and m.messageID = mrh.messageID
		WHERE mrh.emailStatusID in (@insertingStatusID,	@queuedStatusID,@requeuedStatusID,@erroredStatusID,@scheduledStatusID);

		IF @numFound > 0
			insert into @deletedMessageRecipientsToCancel (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m on mrh.siteID = m.siteID
				and m.status = 'D'
				and m.messageID = mrh.messageID
			WHERE mrh.emailStatusID in (@insertingStatusID,	@queuedStatusID,@requeuedStatusID,@erroredStatusID,@scheduledStatusID);

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 BEGIN
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @cancelledStatusID
			FROM @deletedMessageRecipientsToCancel temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

			IF @@ROWCOUNT > 0
				SET @anyCancelled = 1;
		END

		IF @anyCancelled > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject = 'Notification: Email Sending queue had remaining recipients for deleted messages. The recipients have been cancelled.';
			SET @errmsg = 'There were recipients marked as Loading, Queued, Requeued, or Scheduled for messages that have been deleted. They have been cancelled.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	-- items grabbedForProcessing with datelastupdated more than 10 mins ago
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
		DECLARE @recipientsToUpdate2 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-10,@now);
		-- hardcoded @processingStatusID as 6 to use filtered index
		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m 
			on mrh.emailStatusID = 6
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.messageID = mrh.messageID
			AND mrh.dateLastUpdated < @checkDate;

		IF @numFound > 0
			insert into @recipientsToUpdate2 (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 6
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				AND mrh.dateLastUpdated < @checkDate;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @queuedStatusID
			FROM @recipientsToUpdate2 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		IF @numFound > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject = 'Email Sending queue had recipients grabbedForProcessing with datelastupdated older than 10 minutes.';
			SET @errmsg = 'There were '+CAST(@numFound AS varchar)+ ' recipient(s) in grabbedForProcessing with datelastupdated older than 10 minutes. We assume that the sending process died. They have been marked as queued.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	--  items sent in the last 5 minutes before a container was killed 
	BEGIN TRY
		SET XACT_ABORT OFF;
		
		DECLARE @lastHeartbeatDateStart datetime, @lastHeartbeatDateEnd datetime;
		DECLARE @recentBatchesofKilledTaskWorkers TABLE (batchIdentifier varchar(50) PRIMARY KEY, dockerContainerID varchar(50), dateLastCheckin datetime, recipientCutoffDate datetime);
		DECLARE @recipientsToUpdate3 TABLE (recipientID int PRIMARY KEY);

		SET @now = getdate();
		SET @lastHeartbeatDateStart = dateadd(minute,-45,@now);
		SET @lastHeartbeatDateEnd = dateadd(minute,-10,@now);

		update sth 
		SET sth.batchIdentifier = NULL
		FROM platformStatsMC.dbo.scheduledTaskRunnerHeartbeats strh
		inner join platformStatsMC.dbo.scheduledTaskHistory sth on sth.dockerContainerID = strh.dockerContainerID
			AND sth.batchIdentifier is not null 
			AND sth.batchIdentifier = ''
		WHERE strh.dateLastCheckin between @lastHeartbeatDateStart AND @lastHeartbeatDateEnd;			

		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		insert into @recentBatchesofKilledTaskWorkers (batchIdentifier,dockerContainerID, dateLastCheckin,recipientCutoffDate)
		SELECT sth.batchIdentifier, strh.dockerContainerID, strh.dateLastCheckin, dateadd(minute,-5,strh.dateLastCheckin)
		FROM platformStatsMC.dbo.scheduledTaskRunnerHeartbeats strh
		inner join platformStatsMC.dbo.scheduledTaskHistory sth on sth.dockerContainerID = strh.dockerContainerID
			AND sth.batchIdentifier is not null
		where strh.dateLastCheckin between @lastHeartbeatDateStart AND @lastHeartbeatDateEnd;
			
		if exists(SELECT * FROM @recentBatchesofKilledTaskWorkers) BEGIN
			SELECT @numFound = count(mrh.recipientID)
			FROM @recentBatchesofKilledTaskWorkers kw
			inner join dbo.email_messageRecipientHistory mrh on kw.batchIdentifier = mrh.batchID
				AND mrh.emailStatusID = @releasedStatusID
				AND mrh.dateLastUpdated > kw.recipientCutoffDate;

			IF @numFound > 0
				insert into @recipientsToUpdate3 (recipientID)
				SELECT mrh.recipientID
				FROM @recentBatchesofKilledTaskWorkers kw
				inner join dbo.email_messageRecipientHistory mrh on kw.batchIdentifier = mrh.batchID
					AND mrh.emailStatusID = @releasedStatusID
					AND mrh.dateLastUpdated > kw.recipientCutoffDate;
		END

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @queuedStatusID
			FROM @recipientsToUpdate3 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		IF @numFound > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject='Email Sending queue had recipients awaiting verification near heartbeat checkin.';
			SET @errorTitle = 'Email Sending queue had recipients awaiting verification sent within 5 minutes of a suspected killed task worker last heartbeat checkin (Last Heartbeat Checkin between 10 AND 60 minutes ago). We believe these were lost in the mail queue of the container.';
			SET @errmsg = 'There were '+CAST(@numFound AS varchar)+' recipient(s) awaiting verification. We assume that the container died while the messages were still in the queue. They have been remarked as queued.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHANDler @raise=0, @email=1;
	END CATCH


	-- Detect Emails in Unconfirmed Status in the last 2 hours and Requeue them
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @oldestbatchstartdateWithAwaitingValidation datetime;
		DECLARE @2hoursago datetime = dateadd(hour,-2,getdate());
		DECLARE @recipientsToUpdate4 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();

		select @oldestbatchstartdateWithAwaitingValidation=min(batchstartdate)
		from dbo.email_messageRecipientHistory
		where emailStatusID = 16 
		and batchstartdate < @2hoursago;

		-- push it 15 mins earlier
		set @oldestbatchstartdateWithAwaitingValidation = dateadd(minute,-15,@oldestbatchstartdateWithAwaitingValidation);

		IF OBJECT_ID('tempdb..#tmpallrecipients') is not null
			drop table #tmpallrecipients;

		select recipientID, messageID, siteID, batchStartDate, sendingWindow=DATEDIFF(minute,'1/1/2023',batchStartDate)/15, 
			confirmed = case when emailStatusID = 16 then 0 when emailStatusID in (9,10,11,12,13,14,15,17,19) then 1 end
		into #tmpallrecipients
		from dbo.email_messageRecipientHistory
		where batchStartDate between @oldestbatchstartdateWithAwaitingValidation and @2hoursago
		and emailstatusID in (16, 9,10,11,12,13,14,15,17,19);

		-- items to requeue (set emailStatus = requeued, batchStartDate = null, batchID = null, dateLastUpdated = getdate())
		SELECT @numFound = count(r.recipientID)
		from #tmpallrecipients r
		inner join (
			select sendingWindow
			from #tmpallrecipients
			group by sendingWindow
			having cast(sum(confirmed) as decimal(7,2)) /count(*) > .95
		) reQ on reQ.sendingWindow = r.sendingWindow and r.confirmed=0;

		IF @numFound > 0
			insert into @recipientsToUpdate4 (recipientID)
			SELECT r.recipientID
			from #tmpallrecipients r
			inner join (
				select sendingWindow
				from #tmpallrecipients
				group by sendingWindow
				having cast(sum(confirmed) as decimal(7,2)) /count(*) > .95
			) reQ on reQ.sendingWindow = r.sendingWindow and r.confirmed=0;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @requeuedStatusID
			FROM @recipientsToUpdate4 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		IF @numFound > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject = 'Email Sending queue had recipients unacknowledged by Sendgrid that have been requeued with more than 95% of other email from timeperiod confirmed sent.';
			SET @errmsg = 'There were '+CAST(@numFound AS varchar)+ ' recipient(s) still in status "Processing (awaiting validation)" after more than 2 hours with at least 95% of other mail in that quarter-hour timeblock confirmed. We assume that the sending of these emails were lost. They have been marked as requeued.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHANDler @raise=0, @email=1;
	END CATCH


	-- Trigger prioritizeQueue if Unprioritied Emails  are found or if queue is more than 30 mins behind
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @problemMessageTypes varchar(100);
			set @actionNeeded = 0;
			set @checkDate = dateadd(hour,-30,getdate());

			IF EXISTS (select 1 from dbo.email_messageRecipientHistory where emailStatusID in (1,2,18) and queuePriority is null) BEGIN
				set @actionNeeded = 1;

				select @problemMessageTypes = STRING_AGG(messageTypeCode,', ') WITHIN GROUP (ORDER BY messageTypeCode ASC)
				from (
					select distinct mt.messageTypeCode
					from dbo.email_messageRecipientHistory mrh 
					inner join dbo.email_messages m on mrh.siteID = m.siteID
						and m.messageID = mrh.messageID
					inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
					where mrh.emailStatusID in (1,2,18) 
					and mrh.queuePriority is null
				) as tmp;

				SET @errorTitle = ' Email Sending Queue - Unprioritied Queued or Scheduled Emails (Codes: ' + @problemMessageTypes + ')';
				SET @errorSubject = 'Investigation Needed: Email Sending queue had Unprioritied Queued or Scheduled Emails (Codes: ' + @problemMessageTypes + ')';
				SET @errorContent = 'Email Sending queue had Unprioritied Queued or Scheduled Emails . The recipients have now been prioritized. Please track down the code problem -- all inserts to this table need to set the priority using fn_getInitialRecipientQueuePriority(). The corrected messages had type code(s): ' + @problemMessageTypes 
				EXEC platformQueue.dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
			END

			-- hardcoded queueStatusID to use filtered index
			-- Loading Queue StatusID       : 1
			-- Processing (queued) StatusID : 2
			IF EXISTS (select 1 from dbo.email_messageRecipientHistory where emailStatusID in (1,2) and dateLastUpdated < @checkDate)
				set @actionNeeded = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			IF @actionNeeded=1
				exec dbo.email_prioritizeQueue;

			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			EXEC membercentral.dbo.up_MCErrorHANDler @raise=0, @email=1;
		END CATCH
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_queueSuppressionListForRecipient
@recipientID int,
@subUserName varchar(100),
@runAfter datetime

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @itemID int, @siteID int, @subuserID int, @emailAddress varchar(200), @emailStatusCode varchar(10),
		@queueTypeID int, @readyToProcessStatusID int, @entryID int;
			
	declare @tier varchar(20);
	select @tier=tier from membercentral.dbo.fn_getServerSettings();
	IF @tier <> 'production'
		GOTO on_done;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SendgridSuppressions', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	
	SELECT @siteID = mrh.siteID, @emailAddress = mrh.toEmail, @emailStatusCode = s.statusCode
	FROM dbo.email_messageRecipientHistory AS mrh
	INNER JOIN dbo.email_statuses AS s ON s.statusID = mrh.emailStatusID
	WHERE mrh.recipientID = @recipientID;

	SELECT @subuserID = u.subuserID
	FROM dbo.sendgrid_subusers AS u
	INNER JOIN dbo.sendgrid_subuserStatuses AS s ON s.subuserStatusID = u.statusID
	WHERE u.siteID = @siteID
	AND u.username = @subUserName
	AND s.status = 'Active';

	IF @subuserID IS NULL
		GOTO on_done;

	IF @emailStatusCode = 'sg_drop' BEGIN
		SELECT TOP 1 @entryID = s.entryID
		FROM dbo.sendgrid_suppressions AS s
		INNER JOIN dbo.sendgrid_suppressionListTypes AS slt ON slt.typeID = s.typeID
		WHERE s.subuserID = @subuserID
		AND slt.typeCode IN ('bounces', 'invalid_emails', 'spam_reports')
		AND s.emailAddress = @emailAddress;

		IF @entryID IS NOT NULL
			GOTO on_done;
	END

	SELECT @itemID = itemID
	FROM platformQueue.dbo.queue_sendgridSuppressions
	WHERE siteID = @siteID
	AND subuserID = @subuserID
	AND emailAddress = @emailAddress
	AND referencedRecipientID = @recipientID
	AND statusID = @readyToProcessStatusID;

	IF @itemID IS NULL 
		INSERT INTO platformQueue.dbo.queue_sendgridSuppressions (itemGroupUID, siteID, subuserID, emailAddress, referencedRecipientID, statusID, 
			dateAdded, dateUpdated, runAfter)
		VALUES (NEWID(), @siteID, @subuserID, @emailAddress, @recipientID, @readyToProcessStatusID, GETDATE(), GETDATE(), @runAfter);

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_SendMessage
@fromName varchar(200),
@fromEmail varchar(200),
@toEmailList varchar(max),
@replyToEmail varchar(200),
@subject varchar(400),
@title varchar(400),
@messageContent nvarchar(max),
@attachmentsList varchar(max),
@siteID int,
@memberID int,
@messageTypeID int,
@sendingSiteResourceID int,
@referenceType varchar(20),
@referenceID int,
@doWrapEmail bit,
@environmentName varchar(50),
@messageID int OUTPUT,
@recipientIDList varchar(max) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @resourceTypeID int, @contentID int, @siteResourceID int, @contentVersionID int, 
		@sendOnDate datetime = getdate(), @orgID int, @emailTagTypeID int, @emailTypeID int, @recipientID int,
		@messageStatusIDQueued int, @attachmentID int, @s3keyMod varchar(4), @objectKey varchar(400), 
		@attachmentFileName varchar(400), @filePathForS3Upload varchar(400), @s3bucketName varchar(100) = 'platformmail-membercentral-com',
		@contentTitle varchar(100) = left(@title,100), @s3UploadReadyStatusID int, @nowDate datetime = getdate(),
		@thisToEmail varchar(255), @autoID int, @tier varchar(12), @backendTempPath varchar(400), @emailRegex varchar(max),
		@changedEmailsList varchar(max), @injectedNote varchar(max), @sendToAddress varchar(255), @fileName varchar(400),
		@folderPath varchar(400), @sharedTempNoWebPath varchar(40), @sharedTempNoWebS3UploaderPath varchar(60);

	DECLARE @tblRecipients table (recipientID int PRIMARY KEY);
	DECLARE @tblToEmails table (autoID int, email varchar(255));
	DECLARE @tblAttachments table (autoID int, [fileName] varchar(400), folderPath varchar(400));

	SET @messageID = NULL;
	SET @recipientIDList = NULL;
	SET @emailRegex = '^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$';
	SET @resourceTypeID = membercentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SELECT @orgID = orgID FROM membercentral.dbo.sites WHERE siteID = @siteID;
	SELECT @emailTagTypeID = emailTagTypeID FROM membercentral.dbo.ams_memberEmailTagTypes WHERE orgID = @orgID AND emailTagType = 'Primary';
	SELECT @emailTypeID = emailTypeID FROM membercentral.dbo.ams_memberEmailTags WHERE orgID = @orgID and memberID = @memberID AND emailTagTypeID = @emailTagTypeID;
	SELECT @messageStatusIDQueued = statusID FROM dbo.email_statuses WHERE statusCode = 'Q';

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;
	
	-- validate the to emails and kick out if no valid ones left & trim the emails.
	SET @toEmailList = REPLACE(REPLACE(@toEmailList,',',';'),' ','');

	INSERT INTO @tblToEmails (autoID, email)
	SELECT autoID, listItem
	FROM membercentral.dbo.fn_varCharListToTable(@toEmailList,';');

	DELETE FROM @tblToEmails
	WHERE email = ''
	OR LEN(email) > 200
	OR membercentral.dbo.fn_RegExReplace(email,@emailRegex,'') <> ''

	IF NOT EXISTS (SELECT 1 FROM @tblToEmails)
		RAISERROR('No Valid To Email Provided.', 16, 1);

	-- sendgrid API requires only one reply-to address. so pick first one in case there is a list.
	SELECT TOP 1 @replyToEmail = LTRIM(RTRIM(listItem))
	FROM membercentral.dbo.fn_varCharListToTable(REPLACE(@replyToEmail,',',';'),';')
	ORDER BY autoID;

	-- replace line feeds with html br
	SET @messageContent = replace(@messageContent,char(13) + char(10),'<br/>');

	IF @doWrapEmail = 1 BEGIN
		DECLARE @emailContentHTML varchar(max), @customHeadContent varchar(max), @titleHTML varchar(500) = '';

		SELECT @customHeadContent = customHead.rawContent
		FROM membercentral.dbo.sites AS s
		CROSS APPLY membercentral.dbo.fn_getContent(s.customHeadContentID,1) AS customHead
		WHERE siteID = @siteID;

		SET @emailContentHTML = '
			<table cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="background-color:#eee;padding:10px 20px 20px 20px;">
					<table style="background-color:#eee;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr><td style="font:bold 18px Verdana,Helvetica,Arial,sans-serif;color:#069;padding-left:20px;padding-bottom:8px;">'+@title+'</td></tr>
					<tr><td style="background-color:#fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:#333;padding:20px;">'+@messageContent+'</td></tr>
					</table>
				</td>
			</tr>
			</table>';

		IF LEN(@title) > 0
			SET @titleHTML = '<title>'+ @title +'</title>';

		SET @messageContent = '<!DOCTYPE html>
			<html>
			<head>
				'+@titleHTML+'
				<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
				'+ @customHeadContent +'
			</head>
			<body>'+ @emailContentHTML +'</body>
			</html>';
	END

	SELECT @tier=tier FROM membercentral.dbo.fn_getServerSettings();

	-- email sending queue will always override emails when not in production, but this allows us to inject the note at the top of the message
	IF @environmentName <> 'production' BEGIN
		SET @sendToAddress = '<EMAIL>';

		SELECT @changedEmailsList = COALESCE(@changedEmailsList + ';', '') + email
		FROM @tblToEmails
		WHERE email <> @sendToAddress;

		IF LEN(@changedEmailsList) > 0 OR LEN(@replyToEmail) > 0 BEGIN
			SET @injectedNote = '';

			IF LEN(@changedEmailsList) > 0
				SET @injectedNote += '<b><span style="color:blue;">TO</span></b> changed from <span style="color:blue;">'+ @changedEmailsList +'</span> to <span style="color:blue;">'+ @sendToAddress +'</span><br/>';

			IF LEN(@replyToEmail) > 0
				SET @injectedNote += '<b><span style="color:blue;">REPLYTO</span></b> changed from <span style="color:blue;">'+ @replyToEmail +'</span> to <span style="color:blue;">-blank-</span><br/>';

			SET @injectedNote = '<table width="100%" border="0" cellspacing="0" cellpadding="4" style="border:1px solid #999;border-collapse:collapse;">
				<tr bgcolor="#DEDEDE">
					<td style="background-color:#fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:#333;padding:10px;">
						<b><span style="color:red;">NOTE:</span></b> The recipients of this email have been modified.<br/>
						'+ @injectedNote +'
					</td>
				</tr>
				</table><br/>';

			SET @messageContent = replace(@messageContent,'<body>','<body>' + @injectedNote);
		END

		SET @toEmailList = @sendToAddress;
		SET @replyToEmail = '';
	END

	-- attachments: folderpath1|filename1.jpg***folderpath2|filename2.jpg***folderpath3|filename3.jpg
	IF LEN(@attachmentsList) > 0 BEGIN
		INSERT INTO @tblAttachments(autoID, [fileName], folderPath)
		select eachfileset.autoid, 
			CASE WHEN CHARINDEX('|',listItem) > 0 THEN LTRIM(RTRIM(LEFT(listItem, CHARINDEX('|',listItem) - 1))) ELSE '' END,
			CASE WHEN CHARINDEX('|',listItem) > 0 THEN LTRIM(RTRIM(RIGHT(listItem, LEN(listItem) - CHARINDEX('|',listItem) - 2))) ELSE '' END
		FROM membercentral.dbo.fn_varCharListToTable(@attachmentsList,'***') as eachfileset;

		DELETE FROM @tblAttachments WHERE [fileName] = '' OR folderPath = '';
	END

	BEGIN TRAN;
		EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, 
			@parentSiteResourceID=@sendingSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@messageContent,
			@memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

		SELECT @contentVersionID = contentVersionID
		FROM membercentral.dbo.cms_contentVersions
		WHERE siteID = @siteID
		AND contentID = @contentID;

		EXEC dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@memberID,
			@fromName=@fromName, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='',
			@subject=@subject, @contentVersionID=@contentVersionID, @messageWrapper='',
			@referenceType=@referenceType, @referenceID=@referenceID, @consentListIDs=null, @messageID=@messageID OUTPUT;

		-- add recipients as I (not ready to be queued yet)
		SELECT @autoID = MIN(autoID) FROM @tblToEmails;
		WHILE @autoID IS NOT NULL BEGIN
			SELECT @thisToEmail = email FROM @tblToEmails WHERE autoID = @autoID;

			EXEC dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@memberID, 
				@toName='', @toEmail=@thisToEmail, @emailTypeID=@emailTypeID, @statusCode='I', @siteID=@siteID,
				@recipientID=@recipientID OUTPUT;

			INSERT INTO @tblRecipients (recipientID)
			VALUES (@recipientID);

			SELECT @autoID = MIN(autoID) FROM @tblToEmails WHERE autoID > @autoID;
		END

		-- adding attachment entries
		SELECT @sharedTempNoWebPath=sharedTempNoWebPath, @sharedTempNoWebS3UploaderPath=sharedTempNoWebS3UploaderPath 
		FROM membercentral.dbo.fn_getServerSettings();
		
		SELECT @autoID = MIN(autoID) FROM @tblAttachments;
		WHILE @autoID IS NOT NULL BEGIN
			SELECT @fileName=[fileName], @folderPath=folderPath 
			FROM @tblAttachments 
			WHERE autoID = @autoID;
			
			SET @filePathForS3Upload = REPLACE(REPLACE(@folderPath + '/' + @fileName, @sharedTempNoWebPath, @sharedTempNoWebS3UploaderPath),'/','\');

			IF membercentral.dbo.fn_FileExists(@filePathForS3Upload) = 1 BEGIN
				EXEC dbo.email_insertAttachment @fileName=@fileName, @localDirectory=@folderPath, @attachmentID=@attachmentID OUTPUT;

				INSERT INTO dbo.email_messageRecipientAttachments (recipientID, attachmentID)
				SELECT recipientID, @attachmentID
				FROM @tblRecipients;

				-- insert to s3 upload queue
				SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
				SET @objectKey = LOWER(@environmentName + '/outgoing/' + @s3keyMod + '/' + CAST(@attachmentID as varchar(10)) + '/' + @fileName);

				-- set deleteOnSuccess=0 because we may need to send the same actual attachment in multiple messages. It can be cleaned up by the directory cleanup scripts.
				IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
					INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
					VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);
			END

			SELECT @autoID = MIN(autoID) FROM @tblAttachments WHERE autoID > @autoID;
		END

		-- mark recipients as queued
		UPDATE mrh
		SET mrh.emailStatusID = @messageStatusIDQueued
		FROM @tblRecipients as tmp
		INNER JOIN dbo.email_messageRecipientHistory as mrh on mrh.recipientID = tmp.recipientID;
	COMMIT TRAN;

	SELECT @recipientIDList = COALESCE(@recipientIDList + ',', '') + CAST(recipientID as varchar(10))
	FROM @tblRecipients;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sendgrid_createSubusers
@siteID int,
@siteCode varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @tier varchar(12);
	select @tier = tier from membercentral.dbo.fn_getServerSettings();

	if @tier <> 'Production'
		GOTO on_done;
	if @siteCode is null 
		GOTO on_done;

	DECLARE @subuserTransactionalID int, @subuserMarketingID int, @sysMemberID int, @environmentID int,
			@statusReadyToCreate int, @statusWaitingForSubuserCreation int, @xmlMessage xml, @statusID int,
			@ippool_medium INT,  @ippool_renewal INT, @ippool_transactional INT,
			@mailstreamID_marketing int, @mailstreamID_transactional int, @mailstreamID_renewal int

	select @mailstreamID_marketing = mailStreamID from email_mailstreams where mailStreamCode = 'MKT'
	select @mailstreamID_transactional = mailStreamID from email_mailstreams where mailStreamCode = 'TXN'
	select @mailstreamID_renewal = mailStreamID from email_mailstreams where mailStreamCode = 'RENEWAL'

	select @ippool_medium = ipPoolID from sendgrid_ipPools where poolName = 'Medium'
	select @ippool_transactional = ipPoolID from sendgrid_ipPools where poolName = 'Transactional'
	select @ippool_renewal = ipPoolID from sendgrid_ipPools where poolName = 'Renewal'

	select @environmentID = environmentID from membercentral.dbo.platform_environments where environmentName='production'
	select @sysMemberID = membercentral.dbo.fn_ams_getMCSystemMemberID();
	set @siteCode = UPPER(@siteCode);

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SendgridSubuserCreate', @queueStatus='ReadyToCreate', @queueStatusID=@statusReadyToCreate OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SendgridSubuserDomainAuth', @queueStatus='waitingForSubuserCreation', @queueStatusID=@statusWaitingForSubuserCreation OUTPUT;
	
	select @statusID = subuserStatusID from dbo.sendgrid_subuserStatuses where [status] = 'Not Created';
	if @statusID is null 
		GOTO on_done;

	-- Create Transactional subuser
	insert into dbo.sendgrid_subusers (siteID, firstname, lastname, email, username, [password], environmentID, statusID)
	values (@siteID, @sitecode, 'Transactional', '<EMAIL>', 'membercentral_' + LOWER(@sitecode) + '_transactional', 
		@sitecode + '-' + CAST(NEWID() as varchar(36)), @environmentID, @statusID);
	select @subuserTransactionalID = SCOPE_IDENTITY();

	-- Create Marketing subuser
	insert into dbo.sendgrid_subusers (siteID, firstname, lastname, email, username, [password], environmentID, statusID)
	values (@siteID, @sitecode, 'Marketing', '<EMAIL>', 'membercentral_' + LOWER(@sitecode) + '_marketing', 
		@sitecode + '-' + CAST(NEWID() as varchar(36)), @environmentID, @statusID);
	select @subuserMarketingID = SCOPE_IDENTITY();

	-- Create Listservers subuser (This will be down in a job in OPS-374)

	-- Create the subuser mailstream relationships default <NAME_EMAIL>(firstname).membercentral.org for transactional
	insert into dbo.sendgrid_subuserMailstreams (subuserID, mailstreamID, ipPoolID, defaultFromUsername)
	select subuserid, @mailstreamID_transactional as mailstreamId, @ippool_renewal as ipPoolID, 'noreply' as defaultFromUsername
	from dbo.sendgrid_subusers 
	where subuserID = @subuserTransactionalID;

	insert into dbo.sendgrid_subuserMailstreams (subuserID, mailstreamID, ipPoolID, defaultFromUsername)
	select subuserid, @mailstreamID_renewal as mailstreamId, @ippool_transactional as ipPoolID, 'noreply' as defaultFromUsername
	from dbo.sendgrid_subusers 
	where subuserID = @subuserTransactionalID;


	-- Create the subuser mailstream relationships default <NAME_EMAIL>(firstname).membercentral.org for marketing
	insert into dbo.sendgrid_subuserMailstreams (subuserID, mailstreamID, ipPoolID, defaultFromUsername)
	select subuserid, @mailstreamID_marketing as mailstreamId, @ippool_medium as ipPoolID, 'noreply' as defaultFromUsername
	from dbo.sendgrid_subusers 
	where subuserID = @subuserMarketingID;

	-- Create the subuser mailstream relationships default <NAME_EMAIL>(firstname).membercentral.org for listservers (OPS-374)

	-- populate sendgrid_subuserDomains and activeSubuserDomainID for subuser
	insert into dbo.sendgrid_subuserDomains (subuserID, dateCreated, sendingHostname, linkBrandHostname, statusID)
	select subuserID, getdate(), lower(left(lastname,1) + '.' + firstname + '.membercentral.org'),
		'link.' + lower(left(lastname,1) + '.' + firstname + '.membercentral.org'), @statusID
	from dbo.sendgrid_subusers
	where subuserID in (@subuserTransactionalID, @subuserMarketingID);
    
	update u
	set activeSubuserDomainID = sud.subuserDomainID
	from dbo.sendgrid_subuserDomains sud
	inner join dbo.sendgrid_subusers u
	on sud.subuserID = u.subuserID
	and u.subuserID in (@subuserTransactionalID, @subuserMarketingID);

	BEGIN TRAN;
		-- transactional subuser
		INSERT INTO platformQueue.dbo.queue_sendgridSubuserCreate (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserTransactionalID, 'subuserTransactionalID', @sysMemberID, @statusReadyToCreate, getdate(), getdate());

		INSERT INTO platformQueue.dbo.queue_SendgridSubuserDomainAuth (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserTransactionalID, 'subuserTransactionalID', @sysMemberID, @statusWaitingForSubuserCreation, getdate(), getdate());

		-- marketing subuser
		INSERT INTO platformQueue.dbo.queue_sendgridSubuserCreate (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserMarketingID, 'subuserMarketingID', @sysMemberID, @statusReadyToCreate, getdate(), getdate());

		INSERT INTO platformQueue.dbo.queue_SendgridSubuserDomainAuth (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserMarketingID, 'subuserMarketingID', @sysMemberID, @statusWaitingForSubuserCreation, getdate(), getdate());

		-- listservers subuser (OPS-374)

		-- resume Subuser Create task
		EXEC memberCentral.dbo.sched_resumeTask @name='Sendgrid Subuser Create Queue', @engine='BERLinux';

		-- resume Domain Auth task
		EXEC memberCentral.dbo.sched_resumeTask @name='Sendgrid Domain Auth Queue', @engine='BERLinux';

	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use search
GO

ALTER PROC dbo.searchEngineIndex_populateQueue

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpNamesHold') IS NOT NULL
		DROP TABLE #tmpNamesHold;
	IF OBJECT_ID('tempdb..#tmpNames') IS NOT NULL
		DROP TABLE #tmpNames;
	IF OBJECT_ID('tempdb..#tmpNamesToDelete') IS NOT NULL
		DROP TABLE #tmpNamesToDelete;
	CREATE TABLE #tmpNamesHold (firstname VARCHAR(150), lastname VARCHAR(150));
	CREATE TABLE #tmpNames (nameID INT IDENTITY(1,1), firstname VARCHAR(150), lastname VARCHAR(150), baseSlug VARCHAR(200) INDEX IX_baseSlug);
	CREATE TABLE #tmpNamesToDelete (nameID INT PRIMARY KEY);

	INSERT INTO #tmpNamesHold (firstname, lastname)
	SELECT fname, lname
	FROM trialsmith.dbo.depoDocuments d
	WHERE NULLIF(fname, '') IS NOT NULL 
	AND NULLIF(lname, '') IS NOT NULL 
	AND DocumentTypeID = 1
	AND disabled = 'N'
	GROUP BY lname, fname;

	-- strip non A-Z 0-9 dash and space
	UPDATE #tmpNamesHold 
	SET lastname = replace(LTRIM(RTRIM(membercentral.dbo.fn_RegExReplace(lastname,'[^A-Za-z0-9\- ]+',' '))),'  ',' '),
		firstname = replace(LTRIM(RTRIM(membercentral.dbo.fn_RegExReplace(firstname,'[^A-Za-z0-9\- ]+',' '))),'  ',' ');

	DELETE FROM #tmpNamesHold
	WHERE lastname = '' OR firstname = '';

	-- final pool of distinct names
	INSERT INTO #tmpNames (firstname, lastname)
	SELECT firstname, lastname
	FROM #tmpNamesHold
	GROUP BY firstname, lastname;

	-- Create baseSlug
	UPDATE #tmpNames
	SET baseSlug = LOWER(replace(firstname + ' ' + lastname,' ','-'));

	-- if there are still dupe baseSlugs, just keep the first one
	DELETE tmp
	FROM #tmpNames as tmp
	INNER JOIN (
		select nameID, baseSlug, ROW_NUMBER() OVER (PARTITION BY baseSlug ORDER BY nameID) as baseNum
		from #tmpNames
	) as tmp2 on tmp2.nameID = tmp.nameID and tmp2.baseNum > 1;

	-- Insert new names into tblSearchEngineIndex
	DECLARE @dateNextRun datetime = DATEADD(HOUR, 1, GETDATE());

	INSERT INTO dbo.tblSearchEngineIndex (firstname, lastname, baseSlug, slug, dateNextRun)
	SELECT tmp.firstname, tmp.lastname, tmp.baseSlug, tmp.baseSlug, @dateNextRun
	FROM #tmpNames as tmp
	LEFT OUTER JOIN dbo.tblSearchEngineIndex as sei on sei.baseSlug = tmp.baseSlug
	WHERE sei.nameID IS NULL;

	-- names to delete that no longer exist in depoDocuments
	INSERT INTO #tmpNamesToDelete (nameID)
	SELECT sei.nameID
	FROM dbo.tblSearchEngineIndex as sei
	LEFT OUTER JOIN #tmpNames as tmp on tmp.baseSlug = sei.baseSlug
	WHERE tmp.nameID IS NULL;

	DELETE seibc
	FROM dbo.tblSearchEngineIndexBucketCounts as seibc
	INNER JOIN #tmpNamesToDelete as tmp on tmp.nameID = seibc.nameID;

	DELETE sei
	FROM dbo.tblSearchEngineIndex as sei
	INNER JOIN #tmpNamesToDelete as tmp on tmp.nameID = sei.nameID;


	-- populate the queue with all names
	DECLARE @queueStatusID int, @itemCount int = 0;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='depoCrawlerIndex', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

	INSERT INTO platformQueue.dbo.queue_depoCrawlerIndex (nameID, statusID)
	SELECT nameID, @queueStatusID
	FROM dbo.tblSearchEngineIndex
		EXCEPT
	SELECT nameID, @queueStatusID
	FROM platformQueue.dbo.queue_depoCrawlerIndex;
									
	SET @itemCount = @@ROWCOUNT;
									
	IF @itemCount > 0 
		EXEC membercentral.dbo.sched_resumeTask @name='Expert Name Crawler Index', @engine='MCLuceeLinux';

	SELECT @itemCount as itemCount;

	IF OBJECT_ID('tempdb..#tmpNamesHold') IS NOT NULL
		DROP TABLE #tmpNamesHold;
	IF OBJECT_ID('tempdb..#tmpNames') IS NOT NULL
		DROP TABLE #tmpNames;
	IF OBJECT_ID('tempdb..#tmpNamesToDelete') IS NOT NULL
		DROP TABLE #tmpNamesToDelete;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

use platformStatsMC
GO

ALTER PROC dbo.cache_populateGroupMembershipHistory
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @statusProcessing int, @startDate datetime = getdate();;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='trackGrpMembershipHistory', @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='trackGrpMembershipHistory', @queueStatus='Processing', @queueStatusID=@statusProcessing OUTPUT;

	-- update status
	UPDATE platformQueue.dbo.queue_trackGrpMembershipHistory
	SET statusID = @statusProcessing
	WHERE orgID = @orgID
	AND statusID = @statusReady;

	IF @@ROWCOUNT = 0
		RETURN 0;

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpActiveGroups') IS NOT NULL 
		DROP TABLE #tmpActiveGroups;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipStatus') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipStatus;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipChanges') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipChanges;
	CREATE TABLE #tmpActiveGroups (groupID int PRIMARY KEY);
	CREATE TABLE #tmpCacheGrpMembershipStatus (memberID int, groupID int, lastChangeDate date, lastStatusID tinyint,
		INDEX IX_tmpCacheGrpMembershipStatus__memberID__Includes2 (memberID) INCLUDE (groupID, lastStatusID));
	CREATE TABLE #tmpCacheGrpMembershipChanges (memberID int, groupID int, statusID tinyint);

	INSERT INTO #tmpActiveGroups (groupID)
	SELECT groupID
	FROM membercentral.dbo.ams_groups
	WHERE orgID = @orgID
	AND [status] = 'A';

	INSERT INTO #tmpCacheGrpMembershipStatus (memberID, groupID, lastChangeDate)
	SELECT mgh.memberID, mgh.groupID, MAX(mgh.changeDate)
	FROM dbo.cache_members_groups_history AS mgh
	INNER JOIN #tmpActiveGroups AS g ON g.groupID = mgh.groupID
	WHERE mgh.orgID = @orgID
	GROUP BY mgh.memberID, mgh.groupID;

	-- update status
	UPDATE tmp
	SET tmp.lastStatusID = mgh.statusID
	FROM #tmpCacheGrpMembershipStatus AS tmp
	INNER JOIN dbo.cache_members_groups_history AS mgh ON mgh.orgID = @orgID
		AND mgh.memberID = tmp.memberID
		AND mgh.groupID = tmp.groupID
		AND mgh.changeDate = tmp.lastChangeDate;

	INSERT INTO #tmpCacheGrpMembershipChanges (memberID, groupID, statusID)
	SELECT mg.memberID, mg.groupID, 1
	FROM #tmpActiveGroups AS g
	INNER JOIN membercentral.dbo.cache_members_groups AS mg ON mg.orgID = @orgID
		AND mg.groupID = g.groupID
		AND mg.memberID > 0
	LEFT OUTER JOIN #tmpCacheGrpMembershipStatus AS tmp ON tmp.groupID = mg.groupID
		AND tmp.memberID = mg.memberID
	WHERE tmp.memberID IS NULL
	OR tmp.lastStatusID = 0;

	INSERT INTO #tmpCacheGrpMembershipChanges (memberID, groupID, statusID)
	SELECT tmp.memberID, tmp.groupID, 0
	FROM #tmpCacheGrpMembershipStatus AS tmp
	LEFT OUTER JOIN membercentral.dbo.cache_members_groups AS mg ON mg.orgID = @orgID
		AND mg.groupID = tmp.groupID
		AND mg.memberID = tmp.memberID
	WHERE tmp.lastStatusID = 1
	AND mg.autoID IS NULL

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	BEGIN TRAN;
		-- new history entries
		INSERT INTO dbo.cache_members_groups_history (orgID, memberID, groupID, changeDate, statusID)
		SELECT @orgID, memberID, groupID, GETDATE(), statusID
		FROM #tmpCacheGrpMembershipChanges;

		DELETE FROM platformQueue.dbo.queue_trackGrpMembershipHistory
		WHERE orgID = @orgID;

		INSERT INTO platformStatsMC.dbo.cache_members_groups_historyLog (orgID, dateStarted, timeMS)
		VALUES (@orgID, @startDate, datediff(ms,@startDate,getdate()));

	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpActiveGroups') IS NOT NULL 
		DROP TABLE #tmpActiveGroups;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipStatus') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipStatus;
	IF OBJECT_ID('tempdb..#tmpCacheGrpMembershipChanges') IS NOT NULL 
		DROP TABLE #tmpCacheGrpMembershipChanges;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use platformQueue
GO

ALTER PROC dbo.queue_NJFirmSubStatements_grabForProcessing
@batchSize int,
@filePath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @readyToProcessStatusID int, @grabbedForProcessingStatusID int, @itemGroupUID uniqueIdentifier;
	EXEC dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus='grabbedForProcessing', @queueStatusID=@grabbedForProcessingStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_queueItems') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_queueItems;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firms') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firms;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firmChild') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firmChild;
	CREATE TABLE #tmpNJFirmSubStatement_queueItems (itemID int);
	CREATE TABLE #tmpNJFirmSubStatement_firms (itemID int, itemGroupUID uniqueidentifier, firmMemberID int, firmMemberNumber varchar(50), 
		firmFirstName varchar(75), firmLastName varchar(75), firmCompany varchar(200), firmAddress1 varchar(100), firmAddress2 varchar(100), 
		firmCity varchar(75), firmStateCode varchar(4), firmPostalCode varchar(25), firmCountry varchar(100), firmPhone varchar(40), 
		firmFax varchar(40), firmPastDues decimal(18,2), firmDues decimal(18,2), firmGrandTotalDues decimal(18,2),
		xmlConfigParam varchar(max), xmlFirm varchar(max));
	CREATE TABLE #tmpNJFirmSubStatement_firmChild (detailID int, itemID int, firmChildMemberID int, firmChildMemberNumber varchar(50), 
		firmChildFirstName varchar(75), firmChildLastName varchar(75), firmChildDues decimal(18,2));

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @grabbedForProcessingStatusID,
		qi.dateUpdated = GETDATE()
		OUTPUT inserted.itemID INTO #tmpNJFirmSubStatement_queueItems
	FROM dbo.queue_NJFirmSubStatements AS qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID
		FROM dbo.queue_NJFirmSubStatements AS qi2
		WHERE qi2.statusID = @readyToProcessStatusID
		ORDER BY qi2.dateAdded, qi2.itemID
	) AS batch ON batch.itemID = qi.itemID
	WHERE qi.statusID = @readyToProcessStatusID;

	INSERT INTO #tmpNJFirmSubStatement_firms (itemID, itemGroupUID, firmMemberID, firmMemberNumber, 
		firmFirstName, firmLastName, firmCompany, firmAddress1, firmAddress2, firmCity, firmStateCode, 
		firmPostalCode, firmCountry, firmPhone, firmFax, firmPastDues, firmDues, firmGrandTotalDues,
		xmlConfigParam)
	SELECT qi.itemID, qi.itemGroupUID, mFirmActive.memberID, mFirmActive.memberNumber, mFirmActive.firstName, 
		mFirmActive.lastName, mFirmActive.company, qi.firmAddress1, qi.firmAddress2, qi.firmCity, qi.firmStateCode, 
		qi.firmPostalCode, qi.firmCountry, qi.firmPhone, qi.firmFax, qi.firmPastDues, qi.firmDues, qi.firmGrandTotalDues, 
		qi.xmlConfigParam
	FROM #tmpNJFirmSubStatement_queueItems AS tmp
	INNER JOIN dbo.queue_NJFirmSubStatements AS qi ON qi.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.ams_members AS mFirm ON mFirm.memberID = qi.firmMemberID
	INNER JOIN membercentral.dbo.ams_members AS mFirmActive ON mFirmActive.memberID = mFirm.activeMemberID;

	INSERT INTO #tmpNJFirmSubStatement_firmChild (detailID, itemID, firmChildMemberID, firmChildMemberNumber, firmChildFirstName, 
		firmChildLastName, firmChildDues)
	SELECT qid.detailID, qid.itemID, mFirmChildActive.memberID, mFirmChildActive.memberNumber, mFirmChildActive.firstName, 
		mFirmChildActive.lastName, qid.firmChildDues
	FROM #tmpNJFirmSubStatement_firms AS tmp
	INNER JOIN dbo.queue_NJFirmSubStatementsDetail AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.ams_members AS mFirmChild ON mFirmChild.memberID = qid.firmChildMemberID
	INNER JOIN membercentral.dbo.ams_members AS mFirmChildActive ON mFirmChildActive.memberID = mFirmChild.activeMemberID;

	-- firms xml. casting to varchar(max) because it speed up final data query return
	UPDATE tmp
	SET tmp.xmlFirm = firms.firmXML
	FROM #tmpNJFirmSubStatement_firms AS tmp
	cross apply (
		select cast(isnull((		
			select firm.firmMemberID as '@firmmemberid',
				firm.firmMemberNumber as '@firmmembernumber' ,
				firm.firmFirstname as '@firmfirstname' ,
				firm.firmLastname as '@firmlastname' ,
				firm.firmCompany as '@firmcompany' ,
				firm.firmAddress1 as '@firmaddress1' ,
				firm.firmAddress2 as '@firmaddress2' ,
				'' as '@firmaddress3' ,
				firm.firmCity as '@firmcity' ,
				firm.firmStateCode as '@firmstate' ,
				firm.firmPostalCode as '@firmpostalcode',
				firm.firmCountry as '@firmcountry',
				firm.firmPhone as '@firmphone',
				firm.firmfax as '@firmfax',
				firm.firmPastDues as '@pastdues',  
				firm.firmDues as '@firmdues',
				firm.firmGrandTotalDues as '@grandtotal',
				( select
					member.firmChildMemberID as '@childmemberID',
					member.firmChildMemberNumber as '@childmembernumber',
					firm.firmMemberID as '@firmmemberid',
					member.firmChildFirstName as '@firstname',
					member.firmChildLastName as '@lastname',
					'' as '@prefix',
					'' as '@suffix',
					member.firmChildLastName + ', ' + member.firmChildFirstName + ' (' + member.firmChildMemberNumber + ')' as '@namestring',
					'' as '@company',
					'' as '@address1' ,
					'' as '@address2' ,
					'' as '@address3' ,
					'' as '@city' ,
					'' as '@state' ,
					'' as '@postalcode',
					'' as '@country',
					member.firmChildDues as '@dues'
					from #tmpNJFirmSubStatement_firmChild as member
					where member.itemID = firm.itemID
					order by member.firmChildLastName, member.firmChildFirstName, member.firmChildMemberID
					for xml path('member'), type
				)
			from #tmpNJFirmSubStatement_firms as firm
			where firm.itemID = tmp.itemID
			for xml path('company'), root('companies'), type
		),'<companies/>') as varchar(max)) as firmXML
	) as firms;

	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs;
	select distinct itemGroupUID, membercentral.dbo.fn_createDirectory(@filePath + cast(itemGroupUID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpNJFirmSubStatement_firms
	where membercentral.dbo.fn_DirectoryExists(@filePath + cast(itemGroupUID as varchar(50))) = 0;

	-- final data
	select itemID, itemGroupUID, firmMemberID, firmMemberNumber, firmFirstName, firmLastName, firmCompany, xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePath + cast(itemGroupUID as varchar(50)) + '\' + cast(itemID as varchar(10)) + '.xml',xmlFirm,1) as writeFileResult
	from #tmpNJFirmSubStatement_firms
	order by itemID;

	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_queueItems') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_queueItems;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firms') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firms;
	IF OBJECT_ID('tempdb..#tmpNJFirmSubStatement_firmChild') IS NOT NULL 
		DROP TABLE #tmpNJFirmSubStatement_firmChild;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SubscriptionExpire_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getStatusIDbyType @queueType='expireSubs', @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyType @queueType='expireSubs', @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionExpire
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionExpire
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionExpire as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemUID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, sites.siteName, sites.sitecode
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionExpire as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as st on st.typeID = sub.typeID
	INNER JOIN membercentral.dbo.sites on sites.siteID = st.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when errorMessage is null then 0 else 1 end desc, subscriberName, subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

