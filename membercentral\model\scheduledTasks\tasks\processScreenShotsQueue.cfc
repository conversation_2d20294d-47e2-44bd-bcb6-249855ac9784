<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cfset variables.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
		<cfset variables.objEmailBlastAdmin = createObject('component', 'model.admin.emailBlast.emailBlastAdmin')>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_screenshots_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryScreenshots" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryScreenshots.recordCount>

			<cfquery name="local.arrScreenShotRequests" dbtype="query" returntype="array">
				select * 
				FROM local.qryScreenshots
			</cfquery>

			<cfset local.arrProcessingResults = local.arrScreenShotRequests.map(callback=processitem,parallel=true,maxThreads=3)>

			<!--- Will reduce to false if any elements in array are false --->
			<cfset local.returnStruct.success = local.arrProcessingResults.reduce(function(prev, element){
				return (prev and not element.exception);
			}, "true")>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>			
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processitem" access="private" output="false" returntype="struct">
		<cfargument name="item" type="struct" required="true">
		<cfargument name="index" type="numeric" required="true">
		<cfargument name="array" type="array" required="true">

		<cfset var local = {}>
		<cfset local.returnStruct= {success: true, exception: false}>
		<cfset local.featureImageID = 0>
		<cftry>
			<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
				SET NOCOUNT ON;

				DECLARE @queueTypeID int, @statusProcessing int;
				EXEC dbo.queue_getStatusIDbyType @queueType='screenshots', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

				UPDATE dbo.queue_screenshots
				SET statusID = @statusProcessing,
					dateUpdated = GETDATE()
				WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.item.itemID#">;

				-- if entry was deleted after batch was grabbed, rowcount will be zero
				select @@rowcount as doesQueueItemStillExist;
			</cfquery>

			<cfset local.screenshotDebugData = {
				queueItemID: arguments.item.itemID,
				referenceType: arguments.item.referenceType
			}>

			<cfif local.updateToProcessing.doesQueueItemStillExist>
				<!--- process merge codes --->
				<cfif arguments.item.referenceType EQ 'emailBlastScreenshot'>
					<cfset local.previeweMessage = variables.objEmailBlastAdmin.getPreviewedMessage(blastID=arguments.item.referenceID, mid=arguments.item.enteredByMemberID, 
						etid=0, overrideRawContent=arguments.item.htmlContent)>
					<cfset local.screenshotHTML = (local.previeweMessage.msg.body ?: arguments.item.htmlContent)>
					<cfset local.screenshotDebugData.blastID = arguments.item.referenceID>

				<cfelse>
					<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.item.siteCode)>

					<cfif application.MCEnvironment eq "production">
						<cfset local.thisHostname = local.mc_siteInfo.mainhostname>
					<cfelse>
						<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
					</cfif>

					<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.item.enteredByMemberID)>
					<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=encodeForHTML(local.memberInfo.FirstName), middleName=encodeForHTML(local.memberInfo.MiddleName),
							lastName=encodeForHTML(local.memberInfo.LastName), company=encodeForHTML(local.memberInfo.Company), suffix=encodeForHTML(local.memberInfo.Suffix),
							prefix=encodeForHTML(local.memberInfo.Prefix), membernumber=local.memberInfo.membernumber, professionalSuffix=encodeForHTML(local.memberInfo.professionalSuffix), 
							orgcode=local.memberInfo.orgcode, siteID=local.mc_siteInfo.siteID, hostname=local.thisHostname, useRemoteLogin=local.mc_siteInfo.useRemoteLogin }>
					<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteInfo.orgID, memberID=local.memberInfo.memberID, content=arguments.item.htmlContent)>

					<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
						<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
							<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
							<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
						</cfif>
					</cfloop>

					<cfset local.screenshotHTML = application.objMergeCodes.processMergeCodes(content=arguments.item.htmlContent, memberdata=local.tempMemberData,
						orgCode=arguments.item.orgCode, siteCode=arguments.item.siteCode, outputShortCodeDataToBrowser=false).content>
					<cfif not application.objEmailWrapper.isCompleteHTMLDocument(htmlcontent=local.screenshotHTML)>
						<cfset local.screenshotHTML = application.objEmailWrapper.cleanupHTML(htmlcontent=local.screenshotHTML)>
					</cfif>
				</cfif>

				<cfset local.screenShotResults = application.objCommon.generateScreenshot(sitecode=arguments.item.siteCode, htmlContent=local.screenshotHTML,
					viewportWidth=arguments.item.viewportWidth, viewportHeight=arguments.item.viewportHeight,
					viewportDeviceScaleFactor=arguments.item.deviceScaleFactor, fullpage=arguments.item.fullpage, screenshotDebugData=local.screenshotDebugData)>

				<cfif local.screenShotResults.success AND val(arguments.item.featureImageConfigID)>
					<cfset local.screenShotImgFileName = listLast(local.screenShotResults.screenshotImgFilePath,'/')>
					<cfset local.screenShotImgFileExt = listLast(local.screenShotImgFileName,'.')>

					<!--- delete existing image usage if any --->
					<cfset variables.objFeaturedImages.deleteFeaturedImageUsage(referenceID=arguments.item.referenceID, referenceType=arguments.item.referenceType)>

					<cfset local.featureImageID = variables.objFeaturedImages.createFeaturedImageEntryAndThumbnails(orgCode=arguments.item.orgcode, siteCode=arguments.item.sitecode, 
							referenceID=arguments.item.referenceID, referenceType=arguments.item.referenceType, featureImageConfigIDList=arguments.item.featureImageConfigID, 
							uploadedFileName=local.screenShotImgFileName, uploadedFile=local.screenShotResults.screenshotImgFilePath, uploadedFileExt=local.screenShotImgFileExt,
							enteredByMemberID=arguments.item.enteredByMemberID)>
				<cfelse>
					<cfset local.returnStruct.success = false>
				</cfif>
				
				<cfif local.featureImageID and local.returnStruct.success>
					<cfquery name="local.delFromQueue" datasource="#application.dsn.platformQueue.dsn#">
						DELETE FROM dbo.queue_screenshots
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.item.itemID#">
					</cfquery>
				<cfelse>
					<cfquery name="local.requeue" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @newStatus int;
						EXEC dbo.queue_getStatusIDbyType @queueType='screenshots', @queueStatus='readyToProcess', @queueStatusID=@newStatus OUTPUT;

						UPDATE dbo.queue_screenshots
						SET statusID = @newStatus,
							dateUpdated = GETDATE(),
							nextattemptdate = dateadd(minute,30,getdate())
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.item.itemID#">;
					</cfquery>
				</cfif>
			</cfif>


			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.exception = true>
			</cfcatch>
		</cftry>			
			
		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>