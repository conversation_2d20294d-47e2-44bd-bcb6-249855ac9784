﻿<cfoutput>
	<cfset local.selectedTab = event.getTrimValue("tab","#local.itemTypeName#")>
	<cfif event.getTrimValue("lockTab","false")>
		<cfset local.lockTab = local.selectedTab>
	<cfelse>
		<cfset local.lockTab = "">
	</cfif>
	<cfif arguments.event.getValue('formatid') EQ 0>
		<cfset local.strTitleText = 'Add New Product #local.itemTypeName#'>
	<cfelse>
		<cfset local.strTitleText = 'Edit Product #local.itemTypeName#'>
	</cfif>
	<cfsavecontent variable="local.ratesJS">
		<cfif structKeyExists(local, "strProductRevenueGLAcctWidget") and structKeyExists(local, "strShippingRevenueGLAcctWidget")>
			#local.strProductRevenueGLAcctWidget.js#
			#local.strShippingRevenueGLAcctWidget.js#
		</cfif>
		<script language="javascript">
		var currentTab = 'fr';
		function validateFormatForm() {
			mca_hideAlert('err_format');
			if ($('##formatName').val() == '') {
				mca_showAlert('err_format', 'Enter the Name of this Product Format');
				return false;
			}
			checkOA();
			top.$('##btnMCModalSave').prop('disabled',true);
			return true;
		}
		function submitFormatForm() {
			$('##btnSaveFormat').trigger('click');
		}
		function checkOA() {
			if ($('##offerAffirmations').val() == '0') { $('##quantity').val(0); $('##tbodyQty').hide(); }
			else $('##tbodyQty').show();
		}			
		function checkOAStream() {
			if ($('##offerAffirmations').val() == '0') 
				$('##quantity').val(0);
			else 
				$('##quantity').val(1);
		}	
		$(function() {
			<cfif this.storeInfo.OfferStreams is 1 and not this.storeInfo.OfferAffirmations is 1>
				checkOAStream();
			<cfelse>
				checkOA();
			</cfif>
			mca_initNavPills('editFormatPills', '#local.selectedTab#', '#local.lockTab#');
			top.$('##MCModalHeader ##MCModalLabel').text('#local.strTitleText#');
			top.$('##btnMCModalSave').text('Save #local.itemTypeName#');

		});
		function onTabChange(tabName){
			if(tabName == 'fr'){
				currentTab = 'fr';
				top.$('##btnMCModalSave').text('Save #local.itemTypeName#');
				top.$('##btnMCModalSave').show();
			}else{
				currentTab = 'cr';
				top.$('##btnMCModalSave').hide();
			}
		}
		</script>
	</cfsavecontent>
	<cfhtmlhead text="#local.ratesJS#">

	<div id="divRateForm">
		
		
		<ul class="nav nav-pills nav-pills-dotted editFormatPills" role="tablist" id="editFormatPills">
			<cfset local.thisTabID = "#local.itemTypeName#">
			<cfset local.thisTabName = "#local.itemTypeName#">

			<li class="nav-item"><a 
				id="#local.thisTabID#" onClick="onTabChange('fr');" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
				class="nav-link editFormatPills" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">#local.itemTypeName#</a>
			</li>

			<cfif arguments.event.getValue('formatid') gt 0 and (this.storeInfo.OfferAffirmations is 1 OR this.storeInfo.OfferStreams is 1)>
				<cfset local.thisTabID = "credit">
				<cfset local.thisTabName = "credit">

				<li class="nav-item"><a 
					id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
					class="nav-link editFormatPills" onClick="onTabChange('cr');" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Credit</a>
				</li>
			</cfif>
		</ul>

		<div class="tab-content mc_tabcontent p-3 pb-0" id="pills-tabContent">
			<div class="tab-pane fade" id="pills-#local.itemTypeName#" role="tabpanel" aria-labelledby="#local.itemTypeName#">
				<form name="frmFormat" action="#local.formlink#" method="post" onSubmit="return validateFormatForm();">
					<input type="hidden" name="formatid" value="#arguments.event.getValue('formatid')#">
					<input type="hidden" name="isAffirmation" value="#arguments.event.getValue('isAffirmation')#">
					
					<div id="err_format" class="alert alert-danger mb-2 d-none"></div>
					<div class="form-row">
						<div class="col">
							<div class="form-label-group">
								<input class="form-control" name="formatName" id="formatName" value="#arguments.event.getValue('formatName')#" type="text" maxlength="250">
								<label for="formatName">Name of #local.itemTypeName# *</label>
							</div>
						</div>
					</div>
					<div class="form-row">
						<div class="col">
							<div class="form-label-group">
								<select name="status" id="status" class="form-control">
									<option value="A" <cfif arguments.event.getValue('status') IS "A">selected</cfif>>Active</option>
									<option value="I" <cfif arguments.event.getValue('status') IS "I">selected</cfif>>Inactive</option>
									<option value="D" <cfif arguments.event.getValue('status') IS "D">selected</cfif>>Deleted</option>
								</select>
								<label for="status">Status of #local.itemTypeName# *</label>
							</div>
						</div>
					</div>

					<cfif (this.storeInfo.OfferAffirmations is 1 and this.storeInfo.OfferStreams is 1)>
						<cfif arguments.event.getValue('isAffirmation')>
							<input type="hidden" name="offerAffirmations" value="1">
							<input type="hidden" name="quantity" value="1">
						<cfelse>
							<div class="form-row">
								<div class="col">
									<div class="form-label-group">
										<select name="offerAffirmations" id="offerAffirmations" onChange="checkOA();" class="form-control">
											<option value="0" <cfif arguments.event.getValue('offerAffirmations') eq "0">selected</cfif>>No</option>
											<option value="1" <cfif arguments.event.getValue('offerAffirmations') eq "1">selected</cfif>>Yes</option>
										</select>
										<label for="offerAffirmations">Allow Affirmations/Streaming Credit *</label>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-label-group">
										<input class="form-control" name="quantity" id="quantity" value="#int(val(arguments.event.getValue('quantity')))#" type="text" maxlength="3">
										<label for="quantity">How many people can get credit per Purchase</label>
									</div>
								</div>
							</div>
						</cfif>
					<cfelseif this.storeInfo.OfferAffirmations>
						<cfif arguments.event.getValue('isAffirmation')>
							<input type="hidden" name="offerAffirmations" value="1">
							<input type="hidden" name="quantity" value="1">
						<cfelse>
							<div class="form-row">
								<div class="col">
									<div class="form-label-group">
										<select name="offerAffirmations" id="offerAffirmations" onChange="checkOA();" class="form-control">
											<option value="0" <cfif arguments.event.getValue('offerAffirmations') eq "0">selected</cfif>>No</option>
											<option value="1" <cfif arguments.event.getValue('offerAffirmations') eq "1">selected</cfif>>Yes</option>
										</select>
										<label for="offerAffirmations">Accepts Affirmations *</label>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-label-group">
										<input class="form-control" name="quantity" id="quantity" value="#int(val(arguments.event.getValue('quantity')))#" type="text" maxlength="3">
										<label for="quantity">Number of Affirmations Included per Purchase</label>
									</div>
								</div>
							</div>
						</cfif>
					<cfelseif this.storeInfo.OfferStreams is 1>
						<input type="hidden" name="quantity" id="quantity" value="0">
						<div class="form-row">
							<div class="col">
								<div class="form-label-group">
									<select name="offerAffirmations" id="offerAffirmations" onChange="checkOAStream();" class="form-control">
										<option value="0" <cfif arguments.event.getValue('offerAffirmations') eq "0">selected</cfif>>No</option>
										<option value="1" <cfif arguments.event.getValue('offerAffirmations') eq "1">selected</cfif>>Yes</option>
									</select>
									<label for="offerAffirmations">Allow Streaming Credit *</label>
								</div>
							</div>
						</div>
					<cfelse>
						<input type="hidden" name="offerAffirmations" value="0">
						<input type="hidden" name="quantity" value="0">
					</cfif>

					<div class="form-group">
						#local.strProductRevenueGLAcctWidget.html#
					</div>

					<div class="form-group">
						#local.strShippingRevenueGLAcctWidget.html#
					</div>

					<div class="form-group row mt-2">
						<div class="offset-sm-4 col-sm-8">
							<button name="btnSaveFormat" id="btnSaveFormat" type="submit" class="btn btn-sm btn- d-none">Save #local.itemTypeName#</button>
						</div>
					</div>			
				</form>
			</div>
			
			<cfif arguments.event.getValue('formatid') gt 0 and (this.storeInfo.OfferAffirmations is 1 OR this.storeInfo.OfferStreams is 1)>
				<div class="tab-pane fade" id="pills-credit" role="tabpanel" aria-labelledby="credit">
					<cfset local.objCredit = CreateObject("component","model.admin.credit.credit")>

					<cfsavecontent variable="local.EventCreditJS">
						<cfoutput>
						<script language="JavaScript">
							function reloadCreditOfferedGrid() {
								self.location.href = '#this.link.editFormat#&itemID=#arguments.event.getValue('itemID')#&formatID=#arguments.event.getValue('formatid')#&tab=credit';
							}
						</script>
						</cfoutput>
					</cfsavecontent>
					<cfhtmlhead text="#Application.objCommon.minText(local.EventCreditJS)#">

					<cfif NOT local.objCredit.isCreditOffered(applicationType='Store', itemID=arguments.event.getValue('formatid'))>
						<b>Credit is not currently offered for this product format.</b>
					<cfelse>
						<b>Credit is offered for this product format as defined below:</b>
					</cfif>

					<div class="mb-2">
						#local.objCredit.showCreditOfferedGrid(applicationType='Store', itemID=arguments.event.getValue('formatid'), siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
					</div>
				</div>
			</cfif>
		</div>
	</div>
	<div id="divFMTGL"></div>
</cfoutput>