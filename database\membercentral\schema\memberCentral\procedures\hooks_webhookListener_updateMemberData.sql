ALTER PROC dbo.hooks_webhookListener_updateMemberData
@xmlData xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @siteID int, @orgID int, @webhookID int, @webhookURL varchar(400), 
		@eventID int, @payloadXML xml, @payloadMessage varchar(max), @SQSQueueName varchar(80), @memberID int,
		@hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @environmentName varchar(50),
		@environmentID int, @hostName varchar(80), @updatedFieldsJSON varchar(max), @nowDate datetime = GETDATE();
	DECLARE @updatedFields TABLE (columnName sysname);

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='webhook', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	SELECT @eventID = eventID FROM dbo.hooks_events where [event] = 'updateMemberData';

	SELECT @siteID = @xmlData.value('(/wh/@s)[1]','int');
	SELECT @webhookID = @xmlData.value('(/wh/@w)[1]','int');	
	SELECT @payloadXML = @xmlData.query('wh/data');
	SELECT @memberID = @payloadXML.value('(/data/memberid)[1]', 'int');

	INSERT INTO @updatedFields (columnName)
	SELECT x.value('.','sysname')
	FROM @payloadXML.nodes('data/updated/c') AS M(x);

	SELECT @webhookURL = hookURL, @SQSQueueName = SQSQueueName FROM dbo.hooks_webhooks WHERE webhookID = @webhookID;

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SELECT @hasPrefix = hasPrefix, @hasMiddleName = hasMiddleName, @hasSuffix = hasSuffix, @hasProfessionalSuffix = hasProfessionalSuffix
	FROM dbo.organizations 
	WHERE orgID = @orgID;

	SELECT @environmentName = tier FROM dbo.fn_getServerSettings();
	
	SELECT @environmentID = environmentID FROM dbo.platform_environments WHERE environmentName = @environmentName;
	SELECT TOP 1 @hostName = sh.hostName 
	FROM dbo.siteHostnames sh
	INNER JOIN dbo.siteEnvironments AS se ON se.siteID = sh.siteID 
		AND se.environmentID = @environmentID 
		AND se.environmentID = sh.environmentID
		AND se.mainHostnameID = sh.hostNameID
		AND sh.siteID = @siteID;

	SELECT @updatedFieldsJSON = '[' + STRING_AGG('"' + columnName + '"',',') WITHIN GROUP (ORDER BY columnName ASC) + ']'
	FROM @updatedFields;

	SELECT @payloadMessage = '{ "mcwh_event":"memberupdate", "mcwh_eventid":"' + CAST(NEWID() as varchar(36)) + '", "mcwh_env":"' + @environmentName + '", '+
		'"membernumber": "' + m.membernumber + '", "firstname": "' + m.firstname + '", "lastname": "' + m.lastname + '", ' +
		'"company": "' + ISNULL(m.company,'') + '", "datecreated": "' +  FORMAT(m.earliestDateCreated,'MMMM, dd yyyy hh:mm:ss tt') + '", "mcaccounttype": "' + mt.membertype + '", ' +
		'"mcaccountstatus": "' + CASE WHEN m.status = 'A' THEN 'Active' ELSE 'Inactive' END + '", "mcrecordtype":"' + rt.recordTypeName + '", ' +
		CASE WHEN @hasPrefix = 1 THEN '"prefix": "' + ISNULL(m.prefix,'') + '", ' ELSE '' END +
		CASE WHEN @hasMiddleName = 1 THEN '"middlename": "' + ISNULL(m.middlename,'') + '", ' ELSE '' END +
		CASE WHEN @hasSuffix = 1 THEN '"suffix": "' + ISNULL(m.suffix,'') + '", ' ELSE '' END +
		CASE WHEN @hasProfessionalSuffix = 1 THEN '"professionalsuffix": "' + ISNULL(m.professionalSuffix,'') + '", ' ELSE '' END +
		'"datelastupdated": "' +  FORMAT(m.dateLastUpdated,'MMMM, dd yyyy hh:mm:ss tt') + '", ' +
		'"x-api-uri": "/v1/member/' + m.membernumber + '",' + 
		'"x-photo-uri": "' + CASE WHEN m.hasMemberPhoto = 1 THEN 'https://' + @hostName + '/memberphotos/' + LOWER(m.membernumber) + '.jpg' ELSE '' END + '", ' +
		'"updated": ' + @updatedFieldsJSON + ' }'
	FROM dbo.ams_members AS m
	INNER JOIN dbo.ams_memberTypes as mt on mt.memberTypeID = m.memberTypeID
	INNER JOIN dbo.ams_recordTypes AS rt ON rt.orgID = @orgID 
		AND rt.recordTypeID = m.recordTypeID
	WHERE m.memberID = @memberID
	AND m.orgID = @orgID;
  
	INSERT INTO platformQueue.dbo.queue_webhook (siteID, webhookID, webhookURL, eventID, payloadMessage, SQSQueueName, statusID, dateAdded, dateUpdated, nextAttemptDate)
	VALUES (@siteID, @webhookID, @webhookURL, @eventID, @payloadMessage, @SQSQueueName, @statusReady, @nowDate, @nowDate, @nowDate);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
