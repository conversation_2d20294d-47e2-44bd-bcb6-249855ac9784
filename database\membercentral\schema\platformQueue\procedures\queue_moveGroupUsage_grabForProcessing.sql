ALTER PROC dbo.queue_moveGroupUsage_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='moveGroupUsage', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpmoveGroupUsage') IS NOT NULL
		DROP TABLE #tmpmoveGroupUsage;
	CREATE TABLE #tmpmoveGroupUsage (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpmoveGroupUsage
	FROM dbo.queue_moveGroupUsage AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_moveGroupUsage
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.orgID, qid.siteID, qid.groupID, qid.moveToGroupID, qid.recordedByMemberID, qid.statusID
	FROM #tmpmoveGroupUsage AS tmp
	INNER JOIN dbo.queue_moveGroupUsage AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpmoveGroupUsage') IS NOT NULL
		DROP TABLE #tmpmoveGroupUsage;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
