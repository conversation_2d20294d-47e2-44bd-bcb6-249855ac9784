<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.strResult = processQueue()>
		<cfif NOT local.strResult.success>
			<cfthrow message="Error running processQueue()">
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.strResult.itemCount)>
	</cffunction>

	<cffunction name="updateToProcessingItem" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">

		<cfquery name="local.qryUpdateToProcessingItem" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @statusProcessing int;
			EXEC dbo.queue_getStatusIDbyType @queueType='monthBillSW', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

			UPDATE dbo.queue_monthBillSW
			SET statusID = @statusProcessing,
				dateUpdated = getdate()
			WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="updateToDone" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="folderPath" type="string" required="true">
		<cfargument name="filename" type="string" required="true">
		<cfargument name="isPayable" type="boolean" required="true">
		<cfargument name="payableAmount" type="numeric" required="true">

		<cfquery name="local.qryUpdateToDone" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @statusDone int;
			EXEC dbo.queue_getStatusIDbyType @queueType='monthBillSW', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

			UPDATE dbo.queue_monthBillSW
			SET statusID = @statusDone,
				folderPath = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.folderPath#">,
				fileName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.fileName#">,
				isPayable = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isPayable#">,
				payableAmount = <cfqueryparam cfsqltype="cf_sql_decimal" scale="2" value="#arguments.payableAmount#">,
				dateUpdated = getdate()
			WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.strReturn = { success:true, itemCount:0 }>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_monthBillSW_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryStatements" resultset="1">
			</cfstoredproc>
			<cfset local.strReturn.itemCount = local.qryStatements.recordCount>

			<cfif local.strReturn.itemCount gt 0>
				<cfset var objSWStatement = CreateObject("component","model.admin.seminarWeb.seminarWebDesktopAdmin")>

				<!--- this doesnt use parallel because of the constant pdf generation errors --->
				<cfset local.qryStatements.each(function(row) {
					updateToProcessingItem(itemID=arguments.row.itemID);

					local.strPDF = objSWStatement.runMonthlyBilling(runID=arguments.row.runID, participantID=arguments.row.participantID, format="pdf");

					updateToDone(itemID=arguments.row.itemID, folderPath=local.strPDF.strFolder.folderPath, fileName=local.strPDF.fileName, 
						isPayable=local.strPDF.isPayable, payableAmount=local.strPDF.payableAmount);
					}, false)>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>