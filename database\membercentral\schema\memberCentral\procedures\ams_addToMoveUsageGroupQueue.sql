ALTER PROC dbo.ams_addToMoveUsageGroupQueue
@orgID int,
@siteID int,
@groupID int,
@moveToGroupID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @nowDate datetime = getdate();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='moveGroupUsage', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	insert into platformQueue.dbo.queue_moveGroupUsage (orgID, siteID, groupID, moveToGroupID, recordedByMemberID, statusID, dateAdded, dateUpdated)
	select @orgID, @siteID, @groupID, @moveToGroupID, @recordedByMemberID, @statusReady, @nowDate, @nowDate
		except
	select @orgID, @siteID, @groupID, moveToGroupID, recordedByMemberID, statusID, dateAdded, dateUpdated
	from platformQueue.dbo.queue_moveGroupUsage;

	IF @@ROWCOUNT > 0
		EXEC dbo.sched_resumeTask @name='Process Move Group Usage Queue', @engine='BERLinux';

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
