ALTER PROC dbo.sub_queueMassDelete
@siteID int,
@recordedByMemberID int,
@subscriberIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @statusReady int, @queueTypeID int, @itemGroupUID uniqueidentifier = NEWID(), @xmlMessage xml;

	select @orgID = orgID from dbo.sites where siteID = @siteID;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='subscriptionDelete', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	-- get subscribers to Mass Delete
	IF OBJECT_ID('tempdb..#tmpSubscribersToDelete') IS NOT NULL 
		DROP TABLE #tmpSubscribersToDelete;
	CREATE TABLE #tmpSubscribersToDelete (subscriberID int, rootSubscriberID int, newStatusCode char(1), memberID int);

	insert into #tmpSubscribersToDelete (subscriberID, rootSubscriberID, memberID, newStatusCode)
	select distinct s.subscriberID, s.rootSubscriberID, s.memberID, 
		case when st.statusCode = 'O' and s.rootSubscriberID = s.subscriberID then 'X' else 'D' end
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem
	inner join dbo.sub_statuses st on st.statusID = s.statusID;

	BEGIN TRAN;
		-- queue items
		insert into platformQueue.dbo.queue_subscriptionDelete (itemGroupUID, recordedByMemberID, orgID, siteID,
			memberID, subscriberID, rootSubscriberID, newStatusCode, statusID, dateAdded, dateUpdated)
		select @itemGroupUID, @recordedByMemberID, @orgID, @siteID, memberID, subscriberID, rootSubscriberID, 
			newStatusCode, @statusReady, getdate(), getdate()
		from #tmpSubscribersToDelete;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Subscriber Delete Queue', @engine='BERLinux';

		-- send message to service broker to create all the individual messages
		select @xmlMessage = isnull((
			select 'subscriptionDeleteLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpSubscribersToDelete') IS NOT NULL 
		DROP TABLE #tmpSubscribersToDelete;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
