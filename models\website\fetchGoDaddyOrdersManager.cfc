<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">

		<cfset local.lastRunDate = arguments.strTask.scheduledTasksUtils.getLastRunDate(taskCFC=arguments.strTask.taskCFC, defaultDate='12/1/2021')>
		<cfset local.lastRunDate = dateFormat(local.lastRunDate, "mm-dd-yyyy")>

		<cfset local.getOrdersResult = getOrders(lastRunDate=local.lastRunDate)>
		<cfif NOT local.getOrdersResult.success>
			<cfthrow message="Error running getOrders()">
		<cfelse>
			<cfif local.getOrdersResult.itemCount gt 0>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sched_resumeTask">
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="Process GoDaddy Orders Queue">
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="BERLinux">
				</cfstoredproc>
			</cfif>

			<cfset checkForUnlinkedDomains(strTask=arguments.strTask)>

			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.getOrdersResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="getOrders" access="private" output="false" returntype="struct">
		<cfargument name="lastRunDate" type="string" required="true" hint="string to not mess up dateformat">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfset local.totalItemsFound = 0>
			<cfset local.offset = 0>
			<cfset local.loadDocuments = 1>
			<cfset local.dateStart = dateFormat(arguments.lastRunDate, "yyyy-mm-dd")>
			<cfset local.dateEnd = dateFormat(now(), "yyyy-mm-dd")>
			
			<cfset local.getOrdersURL = "https://api.godaddy.com/v1/orders?periodStart=#urlencodedformat(local.dateStart)#&periodEnd=#urlencodedformat(local.dateEnd)#&limit=250&sort=createdAt">
			<cfset local.apiAuthorization = "sso-key 2uPXGHs2Qg_GSEewngShxwQxT13SCSrc2:MXUXgHQQg2Nb3ZvLqeiNaG">

			<cfif local.dateStart neq local.dateEnd>
				<cfloop condition="local.loadDocuments is 1">
					<cfset local.getOrdersURLToRun = "#local.getOrdersURL#&offset=#local.offset#">
					
					<cfhttp method="get" url="#local.getOrdersURLToRun#" throwonerror="Yes" charset="utf-8" result="local.APIResult" useragent="MemberCentral.com">
						<cfhttpparam type="header" name="Authorization" value="#local.apiAuthorization#">
					</cfhttp>

					<cfset local.apiJSONResponse = toString(trim(local.APIResult.fileContent))>
					<cfset local.strAPIResponse = deserializeJSON(local.apiJSONResponse)>
					<cfset local.totalItemsFound = local.totalItemsFound + arrayLen(local.strAPIResponse.orders)>
					<cfif isArray(local.strAPIResponse.orders) AND arrayLen(local.strAPIResponse.orders)>
						<cfquery name="local.qryAddToQueue" datasource="#application.dsn.platformQueue.dsn#">
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								DECLARE @queueStatusID int, @goDaddyOrderID varchar(20);
								EXEC dbo.queue_getStatusIDbyType @queueType='goDaddyOrders', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

								<cfloop array="#local.strAPIResponse.orders#" index="local.thisOrder">
									SET @goDaddyOrderID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.thisOrder.orderId#">;

									IF NOT EXISTS (select itemID from dbo.queue_goDaddyOrders where orderID = @goDaddyOrderID)
										INSERT INTO dbo.queue_goDaddyOrders (orderID, statusID, dateAdded, dateUpdated)
										VALUES (@goDaddyOrderID, @queueStatusID, getdate(), getdate());
								</cfloop>

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
							END CATCH
						</cfquery>
					</cfif>

					<cfif arrayLen(local.strAPIResponse.orders) is 0 OR NOT isDefined("local.strAPIResponse.pagination.total") or val(local.strAPIResponse.pagination.total) is 0 or local.strAPIResponse.pagination.total eq local.totalItemsFound>
						<cfset local.loadDocuments = 0>
					<cfelse>
						<cfset local.offset = local.offset + arrayLen(local.strAPIResponse.orders)>
					</cfif>
				</cfloop>

				<cfset local.returnStruct.itemCount = local.totalItemsFound>
			</cfif>
			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="checkForUnlinkedDomains" access="private" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.messageToSupport = "">

		<cfquery name="local.qryUnlinkedDomains" datasource="#application.dsn.platformQueue.dsn#">
			SELECT orderDate, domain, orderDetail
			FROM platformStatsMC.dbo.site_GoDaddyOrders
			WHERE siteID IS NULL
			ORDER BY orderDate
		</cfquery>

		<cfif local.qryUnlinkedDomains.recordCount>
			<cfsavecontent variable="local.messageToSupport">
				<cfoutput>
				<h4>Unlinked Domains Found</h4>
				<div>There are unlinked GoDaddy orders that need to be linked to a site. Review the list below and notify Development how these should be linked. Ensure the domain, as it is listed below, is setup in the site's Domains tab.</div>
				<br/><br/>
				
				<ul>
				<cfloop query="local.qryUnlinkedDomains">
					<li>#dateFormat(local.qryUnlinkedDomains.orderDate,"m/d/yy")#: #local.qryUnlinkedDomains.domain# - #local.qryUnlinkedDomains.orderDetail#</li>
				</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>

			<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="SUPPORTISSUE")>
			<cfset local.subject = "GoDaddy Domains Need to Be Linked to Sites">

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo['MC'].networkEmailFrom},
				emailto=[{ name="", email="<EMAIL>" }],
				emailreplyto="",
				emailsubject=local.subject,
				emailtitle=local.subject,
				emailhtmlcontent=local.messageToSupport,
				siteID=application.objSiteInfo.mc_siteInfo['MC'].siteID,
				memberID=application.objSiteInfo.mc_siteInfo['MC'].sysMemberID,
				messageTypeID=local.messageTypeID,
				sendingSiteResourceID=application.objSiteInfo.mc_siteInfo['MC'].siteSiteResourceID
				)>
		</cfif>
	</cffunction>

</cfcomponent>