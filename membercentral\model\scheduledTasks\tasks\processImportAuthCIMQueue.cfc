<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cfset variables.objAuthCIM = CreateObject("component","model.system.platform.gateways.authorizeCCCIM")>
	<cfset variables.iterationsRemaining = 60>

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<!--- Process queue --->
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>

			<!--- Process notifications for itemGroupUIDs that are readyToNotify --->
			<cfset local.success = processNotifications()>
			<cfif NOT local.success>
				<cfthrow>
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_importAuthCIM_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryCIMProfile" resultset="1">
			</cfstoredproc>
	
			<cfif local.qryCIMProfile.recordCount is 1>
				<cfset variables.iterationsRemaining = variables.iterationsRemaining - 1>

				<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
					SELECT s.siteID, s.orgID, pr.profileID, pr.profileCode, pr.gatewayUsername, pr.gatewayPassword, pr.enableMCPay
					FROM dbo.mp_profiles as pr
					INNER JOIN dbo.sites as s on s.siteID = pr.siteID
					WHERE pr.profileID = #local.qryCIMProfile.profileID#
					AND pr.[status] = 'A';
				</cfquery>

				<cfset local.tmpStr = { qryGateWayID=local.qryGateWayID, pmid=local.qryCIMProfile.memberID, customerID=local.qryCIMProfile.memberID, 
										customerName='#local.qryCIMProfile.membernumber# - #local.qryCIMProfile.lastname#, #local.qryCIMProfile.firstName#', 
										customerProfileId='', fld_1_=local.qryCIMProfile.FirstNameOnCard, fld_2_=local.qryCIMProfile.LastNameOnCard, 
										fld_4_=local.qryCIMProfile.CardNumber, fld_6_=local.qryCIMProfile.Expiration, fld_7_='' }>
				<cfif len(local.qryCIMProfile.BillingAddress)><cfset local.tmpStr.fld_12_ = local.qryCIMProfile.BillingAddress></cfif>
				<cfif len(local.qryCIMProfile.BillingCity)><cfset local.tmpStr.fld_13_ = local.qryCIMProfile.BillingCity></cfif>
				<cfif len(local.qryCIMProfile.BillingState)><cfset local.tmpStr.fld_14_ = local.qryCIMProfile.BillingState></cfif>
				<cfif len(local.qryCIMProfile.BillingZIP)><cfset local.tmpStr.fld_11_ = local.qryCIMProfile.BillingZIP></cfif>
				<cfif len(local.qryCIMProfile.BillingCountry)><cfset local.tmpStr.fld_15_ = local.qryCIMProfile.BillingCountry></cfif>
				<cfset local.strAdd = variables.objAuthCIM.insertPaymentProfile(argumentcollection=local.tmpStr)>

				<cfif len(local.qryCIMProfile.nickname) and NOT FindNoCase("objErr.err", local.strAdd.head)>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateNickname">
						set nocount on;

						declare @payProfileID int;

						select top 1 @payProfileID = payProfileID
						from dbo.ams_memberPaymentProfiles
						where memberID = #local.qryCIMProfile.memberID#
						and profileID = #local.qryCIMProfile.profileID#
						and detail = 'XXXX#right(local.qryCIMProfile.CardNumber,4)#'
						and status = 'A';

						IF @payProfileID is not null
							update dbo.ams_memberPaymentProfiles
							set nickname = '#left(local.qryCIMProfile.nickname,30)#'
							where payProfileID = @payProfileID;
					</cfquery>
				<cfelseif FindNoCase("objErr.err", local.strAdd.head)>
					<cfquery datasource="#application.dsn.platformQueue.dsn#" name="local.qryUpdateError">
						update dbo.queue_importAuthCIM
						set errResult = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strAdd.errMessage#">
						where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCIMProfile.itemID#">
					</cfquery>
				</cfif>

				<!--- UPDATE STATUS --->
				<cfquery name="local.updateToReadyToNotify" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @statusReady int;
					EXEC dbo.queue_getStatusIDbyType @queueType='importAuthCIM', @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;

					UPDATE dbo.queue_importAuthCIM
					SET statusID = @statusReady,
						dateUpdated = getdate()
					WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCIMProfile.itemID#">;
				</cfquery>

				<!--- run loop again if have not reached iteration limit --->
				<cfif variables.iterationsRemaining gt 0>
					<cfset local.success = processQueue()>
				</cfif>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
					
		<cfreturn local.success>
	</cffunction>

	<cffunction name="processNotifications" access="private" returntype="boolean" output="false">
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cftry>

			<cfstoredproc procedure="queue_importAuthCIM_grabForNotification" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryNotifications" resultset="1">
			</cfstoredproc>
			<cfif local.qryNotifications.recordcount>
				<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;padding:0;">
				<cfset local.tdStyle2 = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##999;padding:0 0 4px 20px;">
				<cfset local.thStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;border-bottom:1px solid ##333;padding:0;">
				<cfset local.pageStyle = "width:600px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;">
				<cfset local.errStyle = "color:##f00;font-weight:bold;">
				<cfset local.sucStyle = "color:##393;font-weight:bold;">

				<cfoutput query="local.qryNotifications" group="itemGroupUID">
					<cftry>
						<cfset local.thisEmail = local.qryNotifications.reportEmail>
						<cfset local.thisSiteCode = local.qryNotifications.siteCode>
						<cfset local.thisSiteName = local.qryNotifications.siteName>
						
						<cfif len(local.thisEmail)>
							<cfsavecontent variable="local.thisEmailContent">
								<div style="#local.pageStyle#">
									<div>
										We have completed processing the following Authorize CIM profiles.
										You should review this list for any issues.
									</div>
									<br/>
									<table style="width:600px;">
									<tr>
										<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
										<td style="#local.thStyle#padding-left:4px;text-align:right;"><b>Profile&nbsp;Code</b></td>
										<td style="#local.thStyle#padding-left:4px;text-align:right;"><b>Card&nbsp;Number</b></td>
										<td style="#local.thStyle#padding-left:4px;text-align:right;"><b>Result</b></td>
									</tr>
									
									<cfset local.rowNumber = 0>
									<cfoutput>
										<cfset local.rowNumber = local.rowNumber + 1>
										<tr valign="top">
											<td style="#local.tdStyle#width:18px;" nowrap>#local.rowNumber#.</td>
											<td style="#local.tdStyle#">
												#local.qryNotifications.memberName#<br/>
												#local.qryNotifications.company#
											</td>
											<td style="#local.tdStyle#text-align:right;padding-left:4px;">#local.qryNotifications.profileCode#</td>
											<td style="#local.tdStyle#text-align:right;padding-left:4px;">#local.qryNotifications.cardNumber#</td>
											<td style="#local.tdStyle#text-align:right;padding-left:4px;">
												<cfif len(local.qryNotifications.errResult)>
													<div style="#local.errStyle#">Failure</div>
												<cfelse>
													<div style="#local.sucStyle#">Success</div>
												</cfif>
											</td>
										</tr>
										<cfif len(local.qryNotifications.errResult)>
											<tr valign="top">
												<td></td>
												<td colspan="3" style="#local.tdStyle2#">
													<div style="#local.errStyle#">#local.qryNotifications.errResult#</div>
												</td>
											</tr>
										</cfif>
									</cfoutput>
									
									</table>
									<br/>
									<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have any questions about this report.</div>
								</div>
							</cfsavecontent>
							
							<cfscript>
								local.arrEmailTo = [];
								
								local.toEmailArr = listToArray(local.thisEmail ,';');
								for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
									local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
								}
							</cfscript>

							<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.thisSiteCode)>
							<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name='MemberCentral', email='<EMAIL>' },
								emailto=local.arrEmailTo,
								emailreplyto="<EMAIL>",
								emailsubject="#local.thisSiteName# Authorize CIM Profile Import Report" ,
								emailtitle="Authorize CIM Profile Import Report",
								emailhtmlcontent=local.thisEmailContent,
								emailAttachments=[],
								siteID=local.mc_siteinfo.siteID,
								memberID=local.qryNotifications.recordedByMemberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
								sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
							)>
						</cfif>
						
						<!--- ------------- --->
						<!--- UPDATE STATUS --->
						<!--- ------------- --->
						<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;
	
							declare @newstatus int;
							select @newstatus = qs.queueStatusID 
								from dbo.tblQueueStatuses as qs
								inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
								where qt.queueType = 'importAuthCIM'
								and qs.queueStatus = 'done';
							
							IF @newstatus is not null
								update dbo.queue_importAuthCIM
								set statusID = @newstatus,
									dateUpdated = getdate()
								where itemGroupUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryNotifications.itemGroupUID#">;
						</cfquery>

					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
				</cfoutput>

			</cfif>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_importAuthCIM_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_importAuthCIM;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>