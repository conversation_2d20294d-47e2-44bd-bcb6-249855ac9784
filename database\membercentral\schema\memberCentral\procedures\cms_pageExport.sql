ALTER PROC dbo.cms_pageExport
@siteID int,
@submittedMemberID int,
@keyword varchar(250), 
@createStartDate varchar(10),
@createEndDate varchar(10),
@modifiedStartDate varchar(10),
@modifiedEndDate varchar(10),
@pageTypes varchar(200),
@pageStatus varchar(20),
@showOnlyPgWithPermission bit,
@fileName varchar(400),
@mode int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblPageLanguages') IS NOT NULL
		DROP TABLE #tblPageLanguages;
	IF OBJECT_ID('tempdb..##tblPagePivot') IS NOT NULL
		DROP TABLE ##tblPagePivot;
	IF OBJECT_ID('tempdb..##tmpPagesExport') IS NOT NULL 
		DROP TABLE ##tmpPagesExport;
		
	declare @itemGroupUID uniqueidentifier, @statusReady INT, @nowDate DATETIME;
	declare @siteLanguages TABLE (languageID int PRIMARY KEY, languageName varchar(100));
	declare @thisLanguage varchar(100), @metadataQuery varchar(4000);
	CREATE TABLE #tblPageLanguages (pageLanguageID int PRIMARY KEY, languagename varchar(100), pageID int, 
		pageTitle varchar(200), pageDesc varchar(400), keywords varchar(400), dateModified datetime);

	insert into @siteLanguages (languageID, languageName)
	select distinct l.languageID, l.languageName
	from dbo.cms_pages as p
	inner join dbo.cms_pageLanguages as pl on pl.pageID = p.pageID
		and p.siteID = @siteID
	inner join dbo.cms_languages as l on l.languageID = pl.languageID;

	insert into #tblPageLanguages (pageLanguageID, languagename, pageID, pageTitle, pageDesc, keywords, dateModified)
	select pl.pageLanguageID, l.languagename, p.pageID, pl.pageTitle, pl.pageDesc, pl.keywords, pl.dateModified
	from dbo.cms_pages as p
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID
		and p.siteID = @siteID
	inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
	inner join dbo.cms_pageLanguages as pl on pl.pageID = p.pageID
	inner join dbo.cms_languages as l on l.languageID = pl.languageID;

	set @metadataQuery = 'select p.pageID, max(dateModified) as dateLastModified';
	select @thisLanguage = min(languageName) from @siteLanguages;
	while @thisLanguage is not null BEGIN
		set @metadataQuery = @metadataQuery + ',
			' + @thisLanguage + 'DateLastModified = max(case when languageName = ''' + @thisLanguage + ''' then dateModified end),
			' + @thisLanguage + 'PageTitle = max(case when languageName = ''' + @thisLanguage + ''' then pageTitle end),
			' + @thisLanguage + 'PageDesc = max(case when languageName = ''' + @thisLanguage + ''' then pageDesc end),
			' + @thisLanguage + 'Keywords = max(case when languageName = ''' + @thisLanguage + ''' then keywords end)';
		select @thisLanguage = min(languageName) from @siteLanguages where languageName > @thisLanguage;
	END

	set @metadataQuery = @metadataQuery + '
		into ##tblPagePivot	
		from dbo.cms_pages as p
		inner join dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID
			and p.siteID = ' + cast(@siteID as varchar(10)) + '
		inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
		inner join dbo.cms_pageLanguages as pl on pl.pageID = p.pageID
		inner join dbo.cms_languages as l on l.languageID = pl.languageID
		group by p.pageID
	';
	exec(@metadataQuery);
	
	declare @dynSQL varchar(max);
	IF @mode = 1
		set @dynSQL = 'select p.pageID, p.pagename, pageResourceType.resourceType as pageResourceType';
		
	IF @mode <> 1
		set @dynSQL = 'select ps.sectionName, p.pagename,
		case srs.siteResourceStatusDesc 
			when ''active'' then ''Published'' 
			when ''deleted'' then ''Deleted''
			else ''Non-Published'' end as pageStatus,
		case when p.allowReturnAfterLogin = 1 then ''Yes'' else ''No'' end as allowReturnAfterLogin, 
		pageTemplate.templatename as pageOverrideTemplate, pageMode.modename as pageOverrideMode,
		p.dateCreated as pageDateCreated, case when p.inheritPlacements = 1 then ''Yes'' else ''No'' end as pageInheritPlacements, 
		pageTemplateMobile.templatename as pageOverrideTemplateMobile, 
		isnull(AppResourceTypes.resourceType,''Content Page'') as applicationType, 
		derivedpageMode.modename as derivedpageOverrideMode, 
		derivedpageTemplate.templatename as derivedpageOverrideTemplate,
		derivedpageTemplateMobile.templatename as derivedpageOverrideTemplateMobile,
		pvt.*';
		
	set @dynSQL = @dynSQL + ' into ##tmpPagesExport
	from dbo.cms_pages as p
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID and p.siteID = ' + cast(@siteID as varchar(10)) + '
	inner join dbo.cms_siteResourceTypes pageResourceType on sr.resourceTypeID = pageResourceType.resourceTypeID
	inner join ##tblPagePivot as pvt on pvt.pageID = p.pageID
	inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
	inner join dbo.cms_pageSections as ps on ps.sectionID = p.sectionID
	inner join dbo.cache_cms_derivedPageSectionSettings as dpss on dpss.sectionID = ps.sectionID
	left outer join dbo.cms_pageLanguages as pageMeta ON pageMeta.pageID = p.pageID and pageMeta.languageID = 1
	left outer join dbo.cms_pageModes as derivedPageMode on derivedPageMode.modeID = dpss.ovModeID
	left outer join dbo.cms_pageTemplates as derivedPageTemplate on derivedPageTemplate.templateID = dpss.ovTemplateID
	left outer join dbo.cms_pageTemplates as derivedPageTemplateMobile on derivedPageTemplateMobile.templateID = dpss.ovTemplateIDMobile
	left outer join dbo.cms_pageTemplates as pageTemplate on pageTemplate.templateID = p.ovTemplateID
	left outer join dbo.cms_pageModes as pageMode on pageMode.modeID = p.ovModeID
	left outer join dbo.cms_pageTemplates as pageTemplateMobile on pageTemplateMobile.templateID = p.ovTemplateIDMobile
	left outer join dbo.cms_siteResources as AppResource
		inner join dbo.cms_siteResourceTypes as AppResourceTypes on AppResourceTypes.resourceTypeID = AppResource.resourceTypeID
		inner join dbo.cms_siteResourceTypeClasses as AppResourceTypeClasses on AppResourceTypeClasses.resourceTypeClassID = AppResourceTypes.resourceTypeClassID
			and AppResourceTypeClasses.resourceTypeClassName = ''application''
		inner join dbo.cms_applicationInstances as ai on ai.siteResourceID = AppResource.siteResourceID
		inner join dbo.cms_applicationTypes as at on ai.applicationTypeID = at.applicationTypeID
		on AppResource.parentSiteResourceID = sr.siteResourceID
	WHERE 1=1 ';

	IF @createStartDate is not null
		set @dynSQL = @dynSQL + ' AND p.dateCreated >=''' + cast(@createStartDate as varchar(50)) + '''';
	
	IF @createEndDate is not null
		set @dynSQL = @dynSQL + ' AND p.dateCreated <=''' + cast(@createEndDate as varchar(50)) + ' 23:59:59:997''';

	IF @keyword <> ''
		set @dynSQL = @dynSQL + ' AND lower(isNull(p.pageName,'''') + '' '' + isNull(pageMeta.pageTitle,'''') + '' '' + isNull(pageMeta.pageDesc,'''') + '' '' + isNull(pageMeta.keywords,'''')) LIKE ''%' + @keyword + '%''';

	IF @modifiedStartDate is not null
		set @dynSQL = @dynSQL + ' AND cast(pvt.dateLastModified as datetime) >=''' + cast(@modifiedStartDate as varchar(50)) + '''';

	IF @modifiedEndDate is not null
		set @dynSQL = @dynSQL + ' AND cast(pvt.dateLastModified as datetime) <=''' + cast(@modifiedEndDate as varchar(50)) + ' 23:59:59:997''';
	
	IF @pageTypes <> ''
		set @dynSQL = @dynSQL + ' AND EXISTS (SELECT listItem from dbo.fn_varcharListToTable(''' + @pageTypes + ''','','') WHERE listItem = ISNULL(at.applicationTypeID,0))';

	IF @pageStatus <> ''
		set @dynSQL = @dynSQL + ' AND srs.siteResourceStatusID IN (' + @pageStatus + ')';

	IF @showOnlyPgWithPermission = 1
		set @dynSQL = @dynSQL + ' AND EXISTS (
							select distinct Innerp.pageID
							from dbo.cms_pages as Innerp
							inner join dbo.cms_siteResources as Innersr on Innerp.siteresourceID = Innersr.siteResourceID
							inner join dbo.cms_siteResourceStatuses Innersrs on Innersrs.siteResourceStatusID = Innersr.siteResourceStatusID and Innersrs.siteResourceStatusDesc = ''Active''
							inner join dbo.cms_pageZonesResources as Innerpzr on Innerpzr.pageID = Innerp.pageID
							inner join dbo.cms_siteResources as Innersr2 on Innerpzr.siteresourceID = Innersr2.siteResourceID
							inner join dbo.cms_siteResourceStatuses Innersrs2 on Innersrs2.siteResourceStatusID = Innersr2.siteResourceStatusID and Innersrs2.siteResourceStatusDesc = ''Active''
							inner join dbo.cms_content Innerc on Innerc.siteresourceID = Innersr2.siteResourceID
							left outer join dbo.cms_siteResourceRights Innersrr on Innersrr.resourceID = Innerc.siteResourceID and Innersrr.functionID = 4 and Innersrr.siteID = ' + cast(@siteID as varchar(10)) + '
							where Innerp.siteID = p.siteID
							and Innerp.pageID = p.pageID
							and Innersrr.resourcerightsid is null
						)';

	exec(@dynSQL);
	
	IF @mode = 1 BEGIN
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='exportPagesJSON', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
		SET @nowDate = getdate();
		SET @itemGroupUID = newid();

		INSERT INTO platformQueue.dbo.queue_exportPagesJSON (itemGroupUID, siteID, submittedMemberID, pageID ,statusID, dateAdded, dateUpdated)
		SELECT @itemGroupUID, @siteID, @submittedMemberID, pageID, @statusReady, @nowDate, @nowDate 
		FROM ##tmpPagesExport
		where pageResourceType = 'UserCreatedPage' or pagename in ('main','404');

		EXEC dbo.sched_resumeTask @name='Process Export Pages JSON Queue', @engine='BERLinux';
	END
	
	IF @mode = 0 BEGIN
		DECLARE @selectsql varchar(max) = '
			SELECT *, ROW_NUMBER() OVER(order by case when sectionName = ''Root'' then 0 else 1 end, sectionName, pagename) as mcCSVorder 
			*FROM* ##tmpPagesExport';
		EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@fileName, @returnColumns=0;
	END

	IF @mode = 2 BEGIN
		IF OBJECT_ID('tempdb..##tmpPagesExportPermission') IS NOT NULL 
		DROP TABLE ##tmpPagesExportPermission;
		declare @dynSQLPermission varchar(max);
		declare @orgID INT;

		select  @orgID = orgID from dbo.sites where siteid = @siteID;

		select DISTINCT tmpPages.pageName,tmpPages.sectionName,g.groupName,x.zoneName,x.contentTitle into ##tmpPagesExportPermission
		   from ##tmpPagesExport as tmpPages inner join dbo.cms_pages as Innerp on Innerp.pageID = tmpPages.pageID
			inner join dbo.cms_siteResources as Innersr on Innersr.siteID = @siteID
						and Innerp.siteresourceID = Innersr.siteResourceID 
						and Innersr.siteResourceStatusID = 1
			inner join dbo.cms_pageZonesResources as Innerpzr on Innerpzr.pageID = Innerp.pageID
					inner join dbo.cms_siteResources as Innersr2 on Innersr2.siteID = @siteID
						and Innerpzr.siteresourceID = Innersr2.siteResourceID and Innersr2.siteResourceStatusID = 1
					inner join dbo.cms_content Innerc on Innerc.siteID = @siteID and Innerc.siteresourceID = Innersr2.siteResourceID
					inner join dbo.cms_siteResourceRights Innersrr on Innersrr.siteID = @siteID and Innersrr.resourceID = Innerc.siteResourceID 
						and Innersrr.functionID = 4
						inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = Innersrr.groupID
					inner join dbo.cms_siteResourceFunctions as srf on srf.functionID = Innersrr.functionID
					inner join
					(select sr2.siteid, aiU.siteResourceID, ISNULL(aiU.applicationInstanceName,'') as contentTitle,pzr.pageID,cpz.zoneName,cpz.zoneID
					from dbo.cms_applicationInstances as aiU
					inner join dbo.cms_pageZonesResources pzr on pzr.siteResourceID = aiU.siteResourceID
					inner join dbo.cms_pageZones cpz on cpz.zoneID = pzr.zoneID
					inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = aiU.siteResourceID
						and sr2.siteid = @siteID
						and sr2.siteResourceStatusID = 1
					inner join dbo.cms_siteResourceTypes srt on sr2.resourceTypeID = srt.resourceTypeID
					inner join dbo.cms_siteResourceTypeClasses srtc on srt.resourceTypeClassID = srtc.resourceTypeClassID
					inner join dbo.cms_applicationTypes as atU on aiU.applicationTypeID = atU.applicationTypeID
					where aiU.siteID = @siteID and aiU.siteResourceID = aiU.siteResourceID 

					UNION 

					select sr2.siteid, awi.siteResourceID, 
						 ISNULL(awi.applicationWidgetInstanceName,'') as contentTitle,pzr.pageID,cpz.zoneName,cpz.zoneID
					from dbo.cms_applicationWidgetInstances as awi
					inner join dbo.cms_pageZonesResources pzr on pzr.siteResourceID = awi.siteResourceID
					inner join dbo.cms_pageZones cpz on cpz.zoneID = pzr.zoneID
					inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = awi.siteResourceID 
						and sr2.siteid= @siteID
						and sr2.siteResourceStatusID = 1
					inner join dbo.cms_siteResourceTypes srt on sr2.resourceTypeID = srt.resourceTypeID
					inner join dbo.cms_siteResourceTypeClasses srtc on srt.resourceTypeClassID = srtc.resourceTypeClassID
					inner join dbo.cms_applicationWidgetTypes as awt on awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
					inner join dbo.cms_applicationInstances as aiU on aiU.siteID = @siteID and awi.applicationInstanceID = aiU.applicationInstanceID
					inner join dbo.cms_applicationTypes as atU on aiU.applicationTypeID = atU.applicationTypeID
					WHERE  awi.siteResourceID = awi.siteResourceID 

					UNION 

					select c.siteid, c.siteResourceID, ISNULL(cl.contentTitle,'') as contentTitle,pzr.pageID,cpz.zoneName,cpz.zoneID
					FROM dbo.cms_content as c
					inner join dbo.cms_pageZonesResources pzr on pzr.siteResourceID = c.siteResourceID
					inner join dbo.cms_pageZones cpz on cpz.zoneID = pzr.zoneID
					inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = c.siteResourceID
						and sr2.siteID = @siteID
						and sr2.siteResourceStatusID = 1
					inner join dbo.cms_siteResourceTypes srt on sr2.resourceTypeID = srt.resourceTypeID
					inner join dbo.cms_siteResourceTypeClasses srtc on srt.resourceTypeClassID = srtc.resourceTypeClassID
					left outer join dbo.cms_contentLanguages as cl
						inner join dbo.sites s on cl.languageID = s.defaultLanguageID and s.siteID = @siteID
						on cl.contentID = c.contentID
					where c.siteID = @siteID and c.siteResourceID = c.siteResourceID ) x on x.pageID = Innerp.pageID
					where Innerp.siteID = @siteID;

		DECLARE @selectsql1 varchar(max) = '
			SELECT *, ROW_NUMBER() OVER(order by case when sectionName = ''Root'' then 0 else 1 end, sectionName, pagename) as mcCSVorder 
			*FROM* ##tmpPagesExportPermission as tmpPages';

		EXEC dbo.up_queryToCSV @selectsql=@selectsql1, @csvfilename=@fileName, @returnColumns=0;

		IF OBJECT_ID('tempdb..##tmpPagesExportPermission') IS NOT NULL 
			DROP TABLE ##tmpPagesExportPermission;
	END

	IF OBJECT_ID('tempdb..#tblPageLanguages') IS NOT NULL
		DROP TABLE #tblPageLanguages;
	IF OBJECT_ID('tempdb..##tblPagePivot') IS NOT NULL
		DROP TABLE ##tblPagePivot;
	IF OBJECT_ID('tempdb..##tmpPagesExport') IS NOT NULL 
		DROP TABLE ##tmpPagesExport;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
