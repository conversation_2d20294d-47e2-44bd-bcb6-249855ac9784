ALTER PROC dbo.queue_exportPagesJSON_grabForNotifying

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='exportPagesJSON', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;
	CREATE TABLE #tmpExportPagesJSON (itemID int);

	WITH processedJobs AS (
		SELECT qi.itemID 
		FROM dbo.queue_exportPagesJSON AS qi
		WHERE qi.statusID = @statusReady
		AND NOT EXISTS (
			SELECT 1
			FROM dbo.queue_exportPagesJSON AS tmp
			WHERE tmp.itemGroupUID = qi.itemGroupUID
			AND tmp.statusID <> @statusReady
		)
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpExportPagesJSON
	FROM dbo.queue_exportPagesJSON AS qid
	INNER JOIN processedJobs AS batch ON batch.itemID = qid.itemID;

	SELECT DISTINCT qid.itemGroupUID, qid.itemID, qid.siteID, qid.submittedMemberID, qid.pageJSON, mActive.firstName + ' ' + mActive.lastName as memberName, me.email AS memberEmail
	FROM #tmpExportPagesJSON AS tmp
	INNER JOIN dbo.queue_exportPagesJSON AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.orgID in (s.orgID,1) and m.memberID = qid.submittedMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID in (s.orgID,1) and me.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID in (s.orgID,1) and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID in (s.orgID,1) and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	ORDER BY qid.itemGroupUID, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
