<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="30" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_schedReportChangeNotify_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryReportUpdate" resultset="1">
			</cfstoredproc>

			<cfif local.qryReportUpdate.recordCount>
				<cfset local.thisReportLink = CreateObject('component', 'model.admin.admin').buildLinkToTool(toolType=local.qryReportUpdate.toolType, mca_ta='showReport') & "&rptID=#local.qryReportUpdate.reportID#">
				<cfset local.thisSiteInfo = application.objSiteInfo.getSiteInfo(local.qryReportUpdate.siteCode)>

				<cfsavecontent variable="local.emailContentTemplate">
					<cfoutput>
					<p>Hello [[SchedulerName]],</p>
					<p>
						This message is to notify you that a report that you scheduled has been modified by <strong>#local.qryReportUpdate.actorFirstName# #local.qryReportUpdate.actorLastName#</strong>.
					</p>
					<p>Please <a href="#local.thisSiteInfo.scheme#://#local.thisSiteInfo.mainhostname##local.thisReportLink#">click here</a> to review the report to ensure it still meets your requirements.</p>
					<p>#local.qryReportUpdate.actorFirstName# #local.qryReportUpdate.actorLastName# will receive replies to this email if you want to discuss the changes.</p>
					<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="5" width="100%" border="1">
						<tr><td><strong>Report:</strong></td><td>#local.qryReportUpdate.reportName# (#local.qryReportUpdate.toolDesc#)</td></tr>
						<cfif len(local.qryReportUpdate.changeSection)>
							<tr><td><strong>Area Changed:</strong></td><td>#local.qryReportUpdate.changeSection#</td></tr>
						</cfif>
						[[ScheduleInfoRows]]
					</table>
					
					<p>Thanks,<br/>MemberCentral Support</p>
					</cfoutput>
				</cfsavecontent>

				<cfloop query="local.qryReportUpdate">
					<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
					<cfquery name="local.qryCheckItemID" datasource="#application.dsn.platformQueue.dsn#">
						SELECT COUNT(qi.itemID) AS itemCount
						FROM dbo.queue_schedReportChangeNotify as qi
						INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
						WHERE qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryReportUpdate.itemID#">
						AND qs.queueStatus = 'grabbedForProcessing'
					</cfquery>

					<cfif local.qryCheckItemID.itemCount>
						<cfquery name="local.qryUpdateToProcessingItem" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @statusProcessing int;
							EXEC dbo.queue_getStatusIDbyType @queueType='schedReportChangeNotify', @queueStatus='processing', @queueStatusID=@statusProcessing OUTPUT;

							UPDATE dbo.queue_schedReportChangeNotify
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryReportUpdate.itemID#">;
						</cfquery>

						<cfquery name="local.qryReportSchedules" datasource="#application.dsn.membercentral.dsn#">
							DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryReportUpdate.orgID#">;

							SELECT sr.itemID, sr.enteredByMemberID AS schedulerMemberID, sr.nextRunDate, me.email AS schedulerEmail, mActive.firstName AS schedulerFirstName,
								mActive.lastName AS schedulerLastName, sr.toEmail AS recipientEmailList
							FROM dbo.rpt_SavedReports AS r
							INNER JOIN dbo.rpt_scheduledReports AS sr ON sr.reportID = r.reportID
								AND sr.enteredByMemberID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryReportUpdate.actorMemberID#">
							INNER JOIN dbo.ams_memberEmails AS me on me.orgID IN (1,@orgID)
								AND me.memberID = sr.enteredByMemberID
							INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID IN (1,@orgID)
								AND metag.memberID = me.memberID
								AND metag.emailTypeID = me.emailTypeID
							INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID IN (1,@orgID)
								AND metagt.emailTagTypeID = metag.emailTagTypeID
								AND metagt.emailTagType = 'Primary'
							INNER JOIN dbo.ams_members AS m ON m.memberID = sr.enteredByMemberID
							INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
							WHERE r.reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryReportUpdate.reportID#">
							AND ISNULL(me.email,'') <> '';
						</cfquery>

						<cfif local.qryReportSchedules.recordCount>
							<cfquery name="local.qryUniqueSchedulers" dbtype="query">
								SELECT schedulerMemberID, schedulerEmail, schedulerFirstName, schedulerLastName
								FROM [local].qryReportSchedules
								GROUP BY schedulerMemberID, schedulerEmail, schedulerFirstName, schedulerLastName
							</cfquery>

							<cfloop query="local.qryUniqueSchedulers">
								<cfset local.thisEmailContent = local.emailContentTemplate>
								<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[SchedulerName]]',"#local.qryUniqueSchedulers.schedulerFirstName# #local.qryUniqueSchedulers.schedulerLastName#")>

								<cfquery name="local.qrySchedules" dbtype="query">
									SELECT nextRunDate, recipientEmailList
									FROM [local].qryReportSchedules
									WHERE schedulerMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryUniqueSchedulers.schedulerMemberID#">
								</cfquery>

								<cfif local.qrySchedules.recordCount gt 1>
									<cfsavecontent variable="local.scheduleInfoRowsHTML">
										<cfoutput>
										<tr>
											<td colspan="2">
												<p>You have #local.qrySchedules.recordCount# schedules set for this report.</p>
												<ol>
													<cfloop query="local.qrySchedules">
														<li>
															<table>
																<tr><td><strong>Recipient List:</strong></td><td>#replace(local.qrySchedules.recipientEmailList, ";", ", ","all")#</td></tr>
																<tr><td><strong>Next Scheduled Run:</strong></td><td>#dateFormat(local.qrySchedules.nextRunDate,"m/d/yyyy")# #timeformat(local.qrySchedules.nextRunDate,'h:mm:ss tt')#</td></tr>
															</table>
														</li>
													</cfloop>
												</ol>
											</td>
										</tr>
										</cfoutput>
									</cfsavecontent>
								<cfelse>
									<cfsavecontent variable="local.scheduleInfoRowsHTML">
										<cfoutput>
										<tr><td><strong>Recipient List:</strong></td><td>#replace(local.qrySchedules.recipientEmailList, ";", ", ","all")#</td></tr>
										<tr><td><strong>Next Scheduled Run:</strong></td><td>#dateFormat(local.qrySchedules.nextRunDate,"m/d/yyyy")# #timeformat(local.qrySchedules.nextRunDate,'h:mm:ss tt')#</td></tr>
										</cfoutput>
									</cfsavecontent>
								</cfif>

								<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[ScheduleInfoRows]]',local.scheduleInfoRowsHTML)>

								<cfset local.subject = "Scheduled Report Updated - #local.qryReportUpdate.reportName#">

								<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name=local.thisSiteInfo.orgname, email=local.thisSiteInfo.networkEmailFrom },
									emailto=[{ name:'#local.qryUniqueSchedulers.schedulerFirstName# #local.qryUniqueSchedulers.schedulerLastName#', email:local.qryUniqueSchedulers.schedulerEmail }],
									emailreplyto=local.qryReportUpdate.actorEmail,
									emailsubject=local.subject,
									emailtitle=local.subject,
									emailhtmlcontent=local.thisEmailContent,
									siteID=local.thisSiteInfo.siteID,
									memberID=local.thisSiteInfo.sysMemberID,
									messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
									sendingSiteResourceID=local.thisSiteInfo.siteSiteResourceID,
									referenceType="ScheduledReport",
									referenceID=local.qryReportUpdate.reportID
								)>
							</cfloop>

							<cfset deleteQueueEntry(itemID=local.qryReportUpdate.itemID)>
						<cfelse>
							<cfset deleteQueueEntry(itemID=local.qryReportUpdate.itemID)>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) AS itemCount
			FROM dbo.queue_schedReportChangeNotify;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

	<cffunction name="deleteQueueEntry" access="private" output="false" returntype="void">
		<cfargument  name="itemID" type="numeric" required="true">

		<cfset var qryDeleteQueueItem = "">

		<cfquery name="qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
			DELETE FROM dbo.queue_schedReportChangeNotify
			WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">
		</cfquery>
	</cffunction>

</cfcomponent>