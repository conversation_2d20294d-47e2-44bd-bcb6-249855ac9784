<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="2" } ];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
		
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>

		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cftry>
			<cfstoredproc procedure="queue_refreshMemberPhoto_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryOrders" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryOrders.recordCount>

			<cfloop query="local.qryOrders">
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='refreshMemberPhoto', @queueStatus='Processing', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_refreshMemberPhoto
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrders.itemID#">;

					EXEC membercentral.dbo.ams_refreshHasMemberPhoto @orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrders.orgID#">;
				</cfquery>

				<cfquery name="local.qryRemoveFromQueue" datasource="#application.dsn.platformQueue.dsn#">
					DELETE FROM dbo.queue_refreshMemberPhoto
					WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrders.itemID#">
				</cfquery>
			</cfloop>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>