ALTER PROC dbo.sub_setSubscriberAsBilledFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusProcessing int, @statusNotify int, @itemStatus int, 
		@siteID int, @subscriberID int, @rowID int, @indivSubscriberID int, @recordedByMemberID int,
		@trashID int, @memberID int, @orgID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='subscriptionBilled', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusNotify OUTPUT;

	-- if itemID is not readyToProcess, kick out now
	select @itemStatus = statusID, @siteID = siteID, @subscriberID = subscriberID, 
		@recordedByMemberID = recordedByMemberID, @memberID = memberID, @orgID = orgID
	from platformQueue.dbo.queue_subscriptionBilled 
	where itemID = @itemID;
	
	IF @itemStatus <> @statusReady OR @subscriberID is null
		GOTO on_done;

	update platformQueue.dbo.queue_subscriptionBilled
	set statusID = @statusProcessing,
		dateUpdated = getdate()
	where itemID = @itemID;
	
	EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode='O',
		@siteID=@siteID, @enteredByMemberID=@recordedByMemberID, @updateDate=null, 
		@byPassQueue=1, @result=@trashID OUTPUT;

	update platformQueue.dbo.queue_subscriptionBilled
	set statusID = @statusNotify,
		dateUpdated = getdate()
	where itemID = @itemID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpBillSubscribers') IS NOT NULL 
		DROP TABLE #tmpBillSubscribers;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
