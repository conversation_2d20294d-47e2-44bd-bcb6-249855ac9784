<cfsavecontent variable="local.ratesJS">
	<cfoutput>
	<cfif structKeyExists(local, "strProductRevenueGLAcctWidget") and structKeyExists(local, "strShippingRevenueGLAcctWidget")>
		#local.strProductRevenueGLAcctWidget.js#
		#local.strShippingRevenueGLAcctWidget.js#
	</cfif>
	<script language="javascript">
	function validateRateForm(thisForm) {
		var arrReq = new Array();

		mca_hideAlert('err_rate');
		if ($('##rateName').val() == '') arrReq[arrReq.length] = "Enter the name of this rate";
		if ($('##startDate').val() == '') arrReq[arrReq.length] = "Enter the Rate Available From Date";

		if(arrReq.length){
			mca_showAlert('err_rate', arrReq.join('<br/>'));
			return false;
		}

		<cfloop query="local.shippingMethods">	
		thisForm.shipPerShipment_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#.value = formatCurrency(thisForm.shipPerShipment_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#.value);
		thisForm.shipPerItem_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#.value = formatCurrency(thisForm.shipPerItem_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#.value);
		</cfloop>
		thisForm.rate.value = formatCurrency(thisForm.rate.value);

		top.$('##btnMCModalSave').prop('disabled',true);
		return true;
	}
	function submitRateForm() {
		$('##btnSaveRate').trigger('click');
	}	
	$(function() {
		mca_setupDateTimePickerRangeFields('startDate','endDate');
		mca_setupCalendarIcons('frmRate');
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.ratesJS)#">

<cfoutput>
<div id="divRateForm">

	<form name="frmRate" id="frmRate" class="p-2" action="#local.formlink#" method="post" onSubmit="return validateRateForm(this);">
		<input type="hidden" name="rateid" id="rateid" value="#arguments.event.getValue('rateid')#">
		
		<div id="err_rate" class="alert alert-danger mb-2 d-none"></div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="rateName" id="rateName" value="#arguments.event.getValue('rateName')#" class="form-control" maxlength="100">
					<label for="rateName">Name of Rate *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="reportCode" id="reportCode" class="form-control" value="#arguments.event.getValue('reportCode')#" maxlength="15">
					<label for="reportCode">Report Code</label>
					<small class="form-text text-black-50">(used to label this rate in reports)</small>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<cfif arguments.event.getValue('mc_admintoolInfo.myrights.EditProducts')>
						<input class="form-control" type="text" name="rate" id="rate" value="#DollarFormat(arguments.event.getValue('rate'))#" onBlur="this.value=formatCurrency(this.value);">
					<cfelse>
						<input type="hidden" name="rate" id="rate" value="#NumberFormat(arguments.event.getValue('rate'),"0.00")#">
						#DollarFormat(arguments.event.getValue('rate'))#
					</cfif>								
					<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif>
					<label for="rate">Amount *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<div class="input-group dateFieldHolder">
						<input type="text" name="startDate" id="startDate" value="#dateFormat(arguments.event.getValue('startDate'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('startDate'),'h:mm tt')#" class="form-control dateControl">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="startDate"><i class="fa-solid fa-calendar"></i></span>
							<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('startDate');"><i class="fa-solid fa-circle-xmark"></i></a></span>
						</div>
						<label for="startDate">Rate Available From * (#local.regTimeZoneName# Time)</label>
					</div>
				</div>
			</div>
		</div>
		
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<div class="input-group dateFieldHolder">
						<input type="text" name="endDate" id="endDate" value="<cfif isDate(arguments.event.getValue('endDate'))>#dateFormat(arguments.event.getValue('endDate'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('endDate'),'h:mm tt')#</cfif>" class="form-control dateControl">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="endDate"><i class="fa-solid fa-calendar"></i></span>
							<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('endDate');"><i class="fa-solid fa-circle-xmark"></i></a></span>
						</div>
						<label for="endDate">Rate Available To (#local.regTimeZoneName# Time)</label>
					</div>
				</div>
			</div>
		</div>
		
		<cfif local.shippingMethods.recordcount>
			<div class="form-group row no-gutters mb-0 font-weight-bold">
				<label class="col-sm-4 col-form-label-sm font-size-md mb-0">Shipping Options</label>
				<label class="col-sm-4 col-form-label-sm font-size-md mb-0">Per Shipment</label>
				<label class="col-sm-4 col-form-label-sm font-size-md mb-0">Per Item</label>
			</div>
			<cfloop query="local.shippingMethods">
				<cfquery name="local.qryGetSM" dbtype="query">
					select PerShipment, PerItem
					from [local].qryShipping
					where shippingID = #local.shippingMethods.shippingID#
				</cfquery>
				<div class="form-group row no-gutters mb-0">
					<label for="shipPerShipment_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#" class="col-sm-4 col-form-label-sm font-size-md">#local.shippingMethods.shippingName#
						<cfif local.shippingMethods.Visible is 0><br><small>(This shipping option is not visible.)</small></cfif>
					</label>
					<div class="col-sm-4" >
						<input class="form-control form-control-sm mt-1" type="text" name="shipPerShipment_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#" id="shipPerShipment_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#" value="#DollarFormat(val(local.qryGetSM.PerShipment))#" onBlur="this.value=formatCurrency(this.value);">
					</div>
					<div class="col-sm-4">
						<input class="form-control form-control-sm mt-1" type="text" name="shipPerItem_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#" id="shipPerItem_#local.shippingMethods.shippingID#_#arguments.event.getValue('rateid')#" value="#DollarFormat(val(local.qryGetSM.PerItem))#" onBlur="this.value=formatCurrency(this.value);">
					</div>
				</div>
			</cfloop>
		</cfif>

		<div class="form-group">
			#local.strProductRevenueGLAcctWidget.html#
		</div>

		<div class="form-group">
			#local.strShippingRevenueGLAcctWidget.html#
		</div>

		<div class="form-group row no-gutters mt-2">
			<div class="offset-sm-4 col-sm-8">
				<button type="submit" name="btnSaveRate" id="btnSaveRate" class="btn btn-sm btn-primary d-none">Save Rate</button>
			</div>
		</div>
	</form>
</div>
<div id="divRATEGL"></div>
</cfoutput>