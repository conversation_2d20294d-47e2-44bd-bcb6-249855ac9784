<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>
		
		<cfsetting requesttimeout="3600">
		<cfset local.scheduledReportTypeID = getScheduledReportTypeID()>
		<cfset local.insertItemsResult = insertItemsToQueue()>
		<cfif NOT local.insertItemsResult.success>
			<cfthrow message="Error running insertItemsToQueue()">
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset local.itemCount = getQueueItemCount(local.scheduledReportTypeID)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>

		<cfset local.success = processQueue(local.scheduledReportTypeID)>
		<cfif NOT local.success>
			<cfthrow message="Error running processQueue()">
		</cfif>
	</cffunction>

	<cffunction name="insertItemsToQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.objAdminReferrals = CreateObject("component","model.admin.referrals.referrals")>
		<cfset local.qryScheduledJobs =  getScheduledJobs()>

		<cfset local.returnStruct = { "success":true, "jobsCount":local.qryScheduledJobs.recordCount }>

		<cftry>		

			<cfloop query="local.qryScheduledJobs">
				<cfset local.siteID = local.qryScheduledJobs.siteID>
				<cfset local.referralID = local.qryScheduledJobs.referralID>
				<cfset local.templateID = local.qryScheduledJobs.TemplateID>
				<cfset local.template = 'reportMemberReferral'>
				<cfset local.scheduleJobID = local.qryScheduledJobs.scheduleReportID>				
				<cfset local.strReferralSettings = local.objAdminReferrals.getReferralSettings(siteID=local.siteID)>
				<cfset local.emailRecipient = local.strReferralSettings.emailRecipient>
				<cfset local.staffEmail = len(local.emailRecipient) ? replace(local.emailRecipient,";",",","all") : ''>
				<cfset local.fromEmail = trim(local.qryScheduledJobs.fromEmail)>
				<cfset local.clientReferralStatusIDs = trim(local.qryScheduledJobs.clientReferralStatusIDs)>
				<cfset local.lastUpdatedDate = local.qryScheduledJobs.lastUpdatedDate>
				
				<cfstoredproc procedure="ref_addReferralScheduledReports" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.referralID#">
					<cfprocparam type="in" cfsqltype="CF_SQL_DATE" null="true">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.templateID#">
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.template#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.scheduleJobID#">
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.staffEmail#">
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.fromEmail#">
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.clientReferralStatusIDs#">
					<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#local.lastUpdatedDate#">
				</cfstoredproc>
			</cfloop>
		
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStruct.success = false>
			</cfcatch>
		</cftry>			
					
		<cfreturn local.returnStruct>			
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="scheduledReportTypeID" type="numeric" required="yes">
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.objAdminReferrals = CreateObject("component","model.admin.referrals.referrals")>

		<cftry>		

			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_referralScheduledReports_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduledReportTypeID#">
				<cfprocresult name="local.qryMembers">
			</cfstoredproc>

			<cfloop query="local.qryMembers">
				<cfset local.thisItemID = local.qryMembers.itemID>

				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. 												--->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and 	--->
				<!--- those items may be grabbed by another job, causing it to possible be processed twice.	--->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_referralScheduledReports as qi
					inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">
					and qs.queueStatus = 'grabbedForProcessing'
				</cfquery>

				<cfif local.checkItemID.itemCount>	
					
					<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @statusProcessing int;
						EXEC dbo.queue_getStatusIDbyType @queueType='referralScheduledReports', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

						UPDATE dbo.queue_referralScheduledReports
						SET statusID = @statusProcessing,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItemID#">;
					</cfquery>

					<cfset local.memberID = local.qryMembers.memberID>
					<cfset local.refMemberName = local.qryMembers.memberName>
					<cfset local.siteID = local.qryMembers.siteID>
					<cfset local.referralID = local.qryMembers.referralID>
					<cfset local.siteCode = local.qryMembers.siteCode>
					<cfset local.sitename = local.qryMembers.siteName>
					<cfset local.orgID = local.qryMembers.orgID>
					<cfset local.orgCode = local.qryMembers.orgCode>
					<cfset local.templateId = local.qryMembers.TemplateID>					
					<cfset local.template = local.qryMembers.template>
					<cfset local.orgName = local.qryMembers.orgName>
					<cfset local.mainhostname = application.objSiteInfo.getSiteInfo(local.siteCode).mainHostName>
					<cfset local.defaultTimeZoneID = application.objSiteInfo.getSiteInfo(local.siteCode).defaultTimeZoneID>
					<cfset local.showCurrencyType = application.objSiteInfo.getSiteInfo(local.siteCode).showCurrencyType>
					<cfset local.defaultCurrencyType = application.objSiteInfo.getSiteInfo(local.siteCode).defaultCurrencyType>
					<cfset local.scheduleJobID = local.qryMembers.scheduleJobID>
					<cfset local.staffEmail = trim(local.qryMembers.staffEmail)>
					<cfset local.fromEmail = trim(local.qryMembers.fromEmail)>
					
					<cfset local.templateObj = local.objAdminReferrals.getEmailTemplate(
						mcproxy_orgID = local.orgID,
						mcproxy_siteID = local.siteID, 
						mcproxy_orgcode = local.orgcode, 
						mcproxy_siteCode = local.siteCode,
						templateId = local.templateId,
						template = local.template,
						mainhostname = local.mainhostname,
						defaultTimeZoneID = local.defaultTimeZoneID,
						sitename = local.sitename,
						showCurrencyType = local.showCurrencyType,
						defaultCurrencyType = local.defaultCurrencyType,
						memberID = local.memberID,
						scheduleJobID = local.scheduleJobID,
						wrapMessage = 0
					)>				
					
					<cfset local.replyToEmail = local.templateObj.EMAILFROM>
					<cfset local.subjectLine = URLDecode(local.templateObj.SUBJECTLINE)>
					<cfset local.emailContent = local.templateObj.TEMPLATEDISP>						

					<cfset local.sendEmailStr = local.objAdminReferrals.sendReferralEmail(
						referralID=local.referralID,
						siteid=local.siteID,
						recordedByMemberID=0,
						messageContent=local.emailContent,
						contentTitle=local.orgName,
						fromName='',
						fromEmail=local.fromEmail,
						replyToEmail=local.replyToEmail,
						subject=local.subjectLine,								
						refMemberID=local.memberID,
						refMemberName=local.refMemberName,
						refMemberEmail=local.staffEmail,
						messageTypeCode="REFREMINDERTASK")>

					<!--- UPDATE STATUS --->
					<cfquery name="local.updateStatus" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @statusDone int;
						EXEC dbo.queue_getStatusIDbyType @queueType='referralScheduledReports', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

						UPDATE dbo.queue_referralScheduledReports
						SET statusID = @statusDone,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;
					</cfquery>
				</cfif>	
			</cfloop>
			
			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_referralScheduledReports_clearDone" datasource="#application.dsn.platformQueue.dsn#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduledReportTypeID#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.success = false>
			</cfcatch>
		</cftry>			
					
		<cfreturn local.success>			
	</cffunction>	

	<cffunction name="getScheduledJobs" access="private" returntype="query" output="false">		
		<cfset var local = structnew()>
		
		<cfquery name="local.qryScheduledJobs" datasource="#application.dsn.membercentral.dsn#">		
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT rs.scheduleReportID, rs.name, rs.fromEmail, rs.TemplateID, rs.isActive, rs.referralID, 
				ai.siteID, s.siteCode, s.siteName, s.orgID, o.orgCode, rs.clientReferralStatusIDs, rs.lastUpdateddate
			FROM dbo.ref_scheduledReports rs
			INNER JOIN dbo.ref_scheduledReportTypes st on st.scheduledReportTypeID = rs.scheduledReportTypeID 
				AND st.scheduledReportTypeName = 'Referral Reminders'
			INNER JOIN dbo.ref_referrals rf ON rf.referralID = rs.referralID
			INNER JOIN dbo.cms_applicationInstances ai ON ai.applicationInstanceID = rf.applicationInstanceID
			INNER JOIN dbo.sites s ON s.siteID = ai.siteID
			INNER JOIN dbo.organizations o ON o.orgID = s.orgID
			WHERE rs.nextRunDate < GETDATE()
			AND rs.isActive = 1
			AND NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_referralScheduledReports WHERE scheduleJobID = rs.scheduleReportID);
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryScheduledJobs>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfargument name="scheduledReportTypeID" required="yes" type="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_referralScheduledReports 
			WHERE scheduledReportTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.scheduledReportTypeID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

	<cffunction name="getScheduledReportTypeID" access="private" output="false" returntype="numeric">
		<cfset var local = structNew()>

		<cfquery name="local.qryGetScheduledReportTypeID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT scheduledReportTypeID 
			FROM dbo.ref_scheduledReportTypes 
			WHERE scheduledReportTypeName = 'Referral Reminders';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryGetScheduledReportTypeID.scheduledReportTypeID>
	</cffunction>

</cfcomponent>
