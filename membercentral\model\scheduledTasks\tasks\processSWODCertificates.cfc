<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>
		
		<cfsetting requesttimeout="1800">

		<cfset local.itemCount = getQueueItemCount()>
	
		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>
		
		<cfset local.objSWODSeminars = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_swodCertificate_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qrySWODEnrollments" resultset="1">
			</cfstoredproc>
			
			<cfoutput query="local.qrySWODEnrollments" group="itemID">
				<cfset local.thisItemID = local.qrySWODEnrollments.itemID>
				
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_swodCertificate as qi
					inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">
					and qs.queueStatus = 'grabbedForProcessing'
				</cfquery>
				
				<cfif local.checkItemID.itemCount>
					<cftry>
						<!--- UPDATE STATUS --->
						<cfquery name="local.qryUpdateStatus" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;
	
							declare @newstatus int;
							select @newstatus = qs.queueStatusID 
								from dbo.tblQueueStatuses as qs
								inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
								where qt.queueType = 'swodCertificate'
								and qs.queueStatus = 'processingItem';
							
							IF @newstatus is not null
								update dbo.queue_swodCertificate
								set statusID = @newstatus,
									dateUpdated = getdate()
								where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;
						</cfquery>
						
						<cfquery name="local.qryThisSWODEnrollment" dbtype="query">
							select recipientID, emailSiteID, emailMessageID, enrollmentID, signUpOrgCode, seminarID, outgoingType, performedByDepoMemberDataID, email
							from [local].qrySWODEnrollments
							where isProcessed = 0
							and itemID = <cfqueryparam value="#local.thisItemID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>

						<cfif local.qryThisSWODEnrollment.recordCount>
							<cfset local.insertCertSuccess = doInsertCertAttachments( 
								itemID=local.thisItemID, recipientID=local.qryThisSWODEnrollment.recipientID, 
								emailSiteID=local.qryThisSWODEnrollment.emailSiteID,emailMessageID= local.qryThisSWODEnrollment.emailMessageID,
								enrollmentID=local.qryThisSWODEnrollment.enrollmentID, siteCode=local.qryThisSWODEnrollment.signUpOrgCode)>

							<cfif local.insertCertSuccess>
								<cfset local.objSWODSeminars.logAction(seminarID=local.qryThisSWODEnrollment.seminarID, typeName=local.qryThisSWODEnrollment.outgoingType, 
																performedBy=local.qryThisSWODEnrollment.performedByDepoMemberDataID, contact=local.qryThisSWODEnrollment.email, 
																enrollmentID=local.qryThisSWODEnrollment.enrollmentID)>
							</cfif>
						</cfif>
						
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
							<cfset local.success = false>
						</cfcatch>
					</cftry>
				
				</cfif>
			
			</cfoutput>
			
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.success = false>
			</cfcatch>
		</cftry>

		<cftry>
			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_swodCertificate_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
			
		<cfreturn local.success>
	</cffunction>
	
	<cffunction name="doInsertCertAttachments" access="private" output="false" returntype="boolean">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="recipientID" type="numeric" required="yes">
		<cfargument name="emailMessageID" type="numeric" required="yes">
		<cfargument name="emailSiteID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="siteCode" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.objCertificate = CreateObject('component', 'model.seminarweb.SWCertificates')>
		<cfset local.success = false>

		<cfset local.strCertificate = local.objCertificate.generateCertificate(enrollmentID=arguments.enrollmentID, siteCode=arguments.siteCode)>
		<cfif StructKeyExists(local.strCertificate,"certificatePath") AND len(local.strCertificate.certificatePath)>
			<!--- insert email attachments --->
			<cfif FileExists(local.strCertificate.certificatePath)>
				<cftry>

					<cfset local.pathFromS3Uploader = replacenocase(local.strCertificate.certificatePath,application.paths.SharedTempNoWeb.path,application.paths.SharedTempNoWeb.pathS3Uploader)>
					<cfset local.pathFromS3Uploader = replace(local.pathFromS3Uploader,'/','\','all')>

					<cfquery name="local.qryInsertEmailAttachments" datasource="#application.dsn.platformMail.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @itemID int, @fileName varchar(400), @localDirectory varchar(400), @s3keyMod varchar(4), @recipientID int, @emailSiteID int, @emailMessageID int, 
								@objectKey varchar(400), @filePathForS3Upload varchar(400), @attachmentID int, @s3bucketName varchar(100),
								@s3UploadReadyStatusID int, @evCertDoneStatusID int, @nowDate datetime = getdate();
							SET @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
							SET @recipientID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recipientID#">;
							SET @emailSiteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailSiteID#">;
							SET @emailMessageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailMessageID#">;
							SET @fileName = 'certificate.pdf';
							SET @localDirectory = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(local.strCertificate.certificatePath,'/certificate.pdf','','one')#">;
							SET @filePathForS3Upload = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.pathFromS3Uploader#">;
							SET @s3bucketName = 'platformmail-membercentral-com';

							EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;
							EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='swodCertificate', @queueStatus='done', @queueStatusID=@evCertDoneStatusID OUTPUT;

							BEGIN TRAN;
								EXEC dbo.email_insertAttachment @fileName=@fileName, @localDirectory=@localDirectory, @attachmentID=@attachmentID OUTPUT;

								INSERT INTO dbo.email_messageRecipientAttachments (recipientID, attachmentID)
								VALUES(@recipientID, @attachmentID);

								<!--- insert to s3 upload queue --->
								SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
								SET @objectKey = LOWER('#application.MCEnvironment#/outgoing/' + @s3keyMod + '/' + cast(@attachmentID as varchar(10)) + '/' + @fileName);

								IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
									INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
									VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);

								-- set recipient as ready
								EXEC platformMail.dbo.email_setMessageRecipientHistoryStatus @siteID=@emailSiteID, @messageID=@emailMessageID,@recipientID=@recipientID, @statusCode='Q', @updateDate=0;

								-- update queue item status as done
								UPDATE platformQueue.dbo.queue_swodCertificate
								SET statusID = @evCertDoneStatusID,
									dateUpdated = @nowDate,
									isProcessed = 1, 
									errMessage = ''
								WHERE itemID = @itemID;
							COMMIT TRAN;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
					<cfset local.success = true>
				<cfcatch type="Any">
					<cfquery name="local.updateQueueItem" datasource="#application.dsn.platformQueue.dsn#">
						UPDATE dbo.queue_swodCertificate
						SET isProcessed = 0, 
							errMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#cfcatch.message#">,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
					</cfquery>

					<cfset local.success = false>
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>
		<cfelseif StructKeyExists(local.strCertificate,"contentID") AND local.strCertificate.contentID EQ 0>
			<cfquery name="local.qryDeleteQueueItem" datasource="#application.dsn.platformMail.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @itemID int, @recipientID int, @nowDate datetime = getdate(), @emailSiteID int, @emailMessageID int;
					SET @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
					SET @recipientID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recipientID#">;
					SET @emailSiteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailSiteID#">;
					SET @emailMessageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailMessageID#">;

					BEGIN TRAN;
						-- mark recipient as cancelled
						EXEC dbo.email_setMessageRecipientHistoryStatus @siteID=@emailSiteID, @messageID=@emailMessageID,@recipientID=@recipientID, @statusCode='C', @updateDate=0;

						-- delete queue item
						DELETE FROM platformQueue.dbo.queue_swodCertificate
						WHERE itemID = @itemID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_swodCertificate;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>