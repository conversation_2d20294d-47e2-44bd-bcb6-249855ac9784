<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.processResult = populateDeleteCardsOnFileQueue()>
		
		<cfif NOT local.processResult.success>
			<cfthrow message="Error running populateDeleteCardsOnFileQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="populateDeleteCardsOnFileQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		
		<cftry>			
			<!--- get cards tied to referral client fees as well as failed cards that have never had a successful charge --->
			<cfquery name="local.qryCards" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @queueStatusID int, @recordedByMemberID int, @itemGroupUID uniqueidentifier = NEWID(), @itemCount int = 0;
					DECLARE @4hoursAgo datetime = dateadd(hh,-4,getdate());

					EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='deleteCardsOnFile', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

					SET @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();			
					
					INSERT INTO platformQueue.dbo.queue_deleteCardsOnFile (itemGroupUID, payProfileID, profileID, memberID, customerProfileID, paymentProfileID, 
						recordedByMemberID, statusID, dateAdded, dateUpdated)
					select @itemGroupUID, mpp.payProfileID, mpp.profileID, mpp.memberID, mpp.customerProfileID, mpp.paymentProfileID, @recordedByMemberID, 
						@queueStatusID, getdate(), getdate()
					from dbo.ams_memberPaymentProfiles as mpp
					inner join dbo.ref_referrals as ref on ref.clientFeeMemberID = mpp.memberID
					inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
					inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
					where mpp.status = 'A'
					and mp.status = 'A'
					and mg.isActive = 1
					and mpp.dateAdded < @4hoursAgo;

					SET @itemCount = @itemCount + @@ROWCOUNT;

					INSERT INTO platformQueue.dbo.queue_deleteCardsOnFile (itemGroupUID, payProfileID, profileID, memberID, customerProfileID, paymentProfileID, 
						recordedByMemberID, statusID, dateAdded, dateUpdated)
					select @itemGroupUID, mpp.payProfileID, mpp.profileID, mpp.memberID, mpp.customerProfileID, mpp.paymentProfileID, @recordedByMemberID, 
						@queueStatusID, getdate(), getdate()
					from dbo.ams_memberPaymentProfiles as mpp
					inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
					inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
					where mpp.status = 'A'
					and mp.status = 'A'
					and mg.isActive = 1
					and mpp.failedLastDate < dateadd(dd,-1*ISNULL(mp.minDaysFailedCleanup,14),getdate())
					and mpp.failedCount is not null 
					and mpp.failedCount > 0
					and NOT EXISTS (select historyID from dbo.tr_paymentHistory where memberPaymentProfileID = mpp.payProfileID and isSuccess = 1);

					SET @itemCount = @itemCount + @@ROWCOUNT;

					select @itemCount as itemCount;

					IF @itemCount > 0
						EXEC membercentral.dbo.sched_resumeTask @name='Delete Cards on File', @engine='MCLuceeLinux';

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.itemCount = local.qryCards.itemCount>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>