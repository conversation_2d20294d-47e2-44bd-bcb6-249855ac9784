ALTER PROC dbo.queue_sendgridSubuserDomainAuth_clearDone
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusDone int, @subuserID int;
	EXEC dbo.queue_getStatusIDbyType @queueType='SendgridSubuserDomainAuth', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	select @subuserID = subUserID 
	from dbo.queue_SendgridSubuserDomainAuth
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	DELETE from dbo.queue_SendgridSubuserDomainAuth
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	-- Remove the sub user from queue_sendgridSubuserCreate if this was a subuser ALTER PROCess.
	DELETE from dbo.queue_sendgridSubuserCreate
	WHERE subUserID = @subuserID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_<PERSON><PERSON><PERSON>rHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
