ALTER PROC dbo.queue_depoDocumentsAttach_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize tinyint = 100;
	EXEC dbo.queue_getQueueTypeID @queueType='depoDocumentsAttach', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_depoDocumentsAttach as qi
	INNER JOIN (
		SELECT top (@batchSize) qi2.itemID 
		FROM dbo.queue_depoDocumentsAttach as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoDocumentID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_depoDocumentsAttach as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
