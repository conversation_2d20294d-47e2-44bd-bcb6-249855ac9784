﻿<cfcomponent output="false" cache="true">

	<cfset variables.APIAppKey = "3D8E11D1-ECAE-E9E4-B3E7D2E27D071468">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.itemCount = 0>

		<cfsetting requesttimeout="600">

		<!--- get the last time this task ran --->
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sched_getLastRunDate">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.strTask.taskCFC#">
			<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#month(now())#/1/#year(now())#">
			<cfprocparam type="out" cfsqltype="CF_SQL_TIMESTAMP" variable="local.lastRunDate">
		</cfstoredproc>
		<cfset local.lastRunDate = dateFormat(local.lastRunDate, "mm-dd-yyyy")>

		<cfset local.getDocResult = getDocuments(lastRunDate=local.lastRunDate)>
		<cfif NOT local.getDocResult.success>
			<cfthrow message="Error running getDocuments()">
		<cfelse>
			<cfset local.itemCount = local.itemCount + local.getDocResult.itemCount>
		</cfif>

		<cfset local.getProfResult = getProfessionals(lastRunDate=local.lastRunDate)>
		<cfif NOT local.getProfResult.success>
			<cfthrow message="Error running getProfessionals()">
		<cfelse>
			<cfset local.itemCount = local.itemCount + local.getProfResult.itemCount>
		</cfif>

		<cfif local.itemCount gt 0>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sched_resumeTask">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="Process Disp Actions Queues">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="BERLinux">
			</cfstoredproc>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.itemCount)>
	</cffunction>

	<cffunction name="getDocuments" access="private" output="false" returntype="struct">
		<cfargument name="lastRunDate" type="string" required="true" hint="string to not mess up dateformat">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfset local.totalItemsFound = 0>
			<cfset local.offset = 0>
			<cfset local.loadDocuments = 1>
			<cfset local.lastRunDate = dateFormat(arguments.lastRunDate, "dd/mm/yyyy")>
			<cfset local.dateEnd = dateFormat(now(), "dd/mm/yyyy")>
			<cfset local.loadDocumentsURL = "http://www.checkyourpro.com/api/document?modifiedDateStart=#local.lastRunDate#&modifiedDateEnd=#local.dateEnd#&limit=100">
			<cfloop condition="local.loadDocuments is 1">
				<cfset local.loadDocumentsURLToRun = "#local.loadDocumentsURL#&offset=#local.offset#">
			
				<cfhttp method="get" url="#local.loadDocumentsURLToRun#" useragent="MemberCentral.com" result="local.apiResult">
					<cfhttpparam type="header" name="X-Application-Key" value="#variables.APIAppKey#">
					<cfhttpparam type="header" name="Content-Type" value="application/json; charset=utf-8">
				</cfhttp>

				<cfset local.strAPIResponse = DeSerializeJSON(local.apiResult.fileContent)>
				<cfset local.totalItemsFound = local.totalItemsFound + arrayLen(local.strAPIResponse.data)>

				<cfif arrayLen(local.strAPIResponse.data)>
					<cfquery name="local.qryAddToQueue" datasource="#application.dsn.platformQueue.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @queueStatusID int;
							EXEC dbo.queue_getStatusIDbyType @queueType='dispActionsDoc', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

							<cfloop array="#local.strAPIResponse.data#" index="local.thisDoc">
								INSERT INTO dbo.queue_dispActionsDoc (jsonDoc, statusID, dateAdded, dateUpdated)
								VALUES (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#SerializeJSON(local.thisDoc)#">, @queueStatusID, getdate(), getdate());
							</cfloop>

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				</cfif>

				<cfif arrayLen(local.strAPIResponse.data) is 0 OR NOT isDefined("local.strAPIResponse.numberRecords") or len(local.strAPIResponse.numberRecords) is 0 or local.strAPIResponse.numberRecords eq local.totalItemsFound>
					<cfset local.loadDocuments = 0>
				<cfelse>
					<cfset local.offset = local.offset + arrayLen(local.strAPIResponse.data)>
				</cfif>
			</cfloop>

			<cfset local.returnStruct.itemCount = local.totalItemsFound>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>	
	</cffunction>

	<cffunction name="getProfessionals" access="private" output="false" returntype="struct">
		<cfargument name="lastRunDate" type="string" required="true" hint="string to not mess up dateformat">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfset local.totalItemsFound = 0>
			<cfset local.arrProfessionals = []>
			<cfset local.offset = 0>
			<cfset local.loadProfessionals = 1>
			<cfset local.lastRunDate = dateFormat(arguments.lastRunDate, "dd/mm/yyyy")>
			<cfset local.dateEnd = dateFormat(now(), "dd/mm/yyyy")>
			<cfset local.loadProfessionalsURL = "http://www.checkyourpro.com/api/professional?modifiedDateStart=#local.lastRunDate#&modifiedDateEnd=#local.dateEnd#&limit=100">
			<cfloop condition="local.loadProfessionals is 1">
				<cfset local.loadProfessionalsURLToRun = "#local.loadProfessionalsURL#&offset=#local.offset#">
			
				<cfhttp method="get" url="#local.loadProfessionalsURLToRun#" useragent="MemberCentral.com" result="local.apiResult">
					<cfhttpparam type="header" name="X-Application-Key" value="#variables.APIAppKey#">
					<cfhttpparam type="header" name="Content-Type" value="application/json; charset=utf-8">
				</cfhttp>

				<cfset local.strAPIResponse = DeSerializeJSON(local.apiResult.fileContent)>
				<cfset local.totalItemsFound = local.totalItemsFound + arrayLen(local.strAPIResponse.data)>

				<cfif arrayLen(local.strAPIResponse.data)>
					<cfquery name="local.qryAddToQueue" datasource="#application.dsn.platformQueue.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @queueStatusID int;
							EXEC dbo.queue_getStatusIDbyType @queueType='dispActionsProf', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

							<cfloop array="#local.strAPIResponse.data#" index="local.thisProf">
								INSERT INTO dbo.queue_dispActionsProf (jsonDoc, statusID, dateAdded, dateUpdated)
								VALUES (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#SerializeJSON(local.thisProf)#">, @queueStatusID, getdate(), getdate());
							</cfloop>

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				</cfif>

				<cfif arrayLen(local.strAPIResponse.data) is 0 OR NOT isDefined("local.strAPIResponse.numberRecords") or len(local.strAPIResponse.numberRecords) is 0 or local.strAPIResponse.numberRecords eq local.totalItemsFound>
					<cfset local.loadProfessionals = 0>
				<cfelse>
					<cfset local.offset = local.offset + arrayLen(local.strAPIResponse.data)>
				</cfif>
			</cfloop>

			<cfset local.returnStruct.itemCount = local.totalItemsFound>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>	
	</cffunction>
		
</cfcomponent>