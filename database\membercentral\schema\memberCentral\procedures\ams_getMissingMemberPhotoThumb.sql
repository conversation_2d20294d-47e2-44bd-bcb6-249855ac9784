ALTER PROC dbo.ams_getMissingMemberPhotoThumb 
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueStatusIDReady int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberPhotoThumb', @queueStatus='readyToProcess', @queueStatusID=@queueStatusIDReady OUTPUT;

	INSERT INTO platformQueue.dbo.queue_memberPhotoThumb (orgcode, memberid, membernumber, width, height, dateAdded, dateUpdated, statusID)
	select o.orgcode, m.memberID, m.memberNumber, o.memberPhotoWidth, o.memberPhotoHeight, getdate(), getdate(), @queueStatusIDReady
	from dbo.ams_members as m 
	inner join dbo.organizations as o on o.orgID = m.orgID 
	where m.hasMemberPhoto = 1 
	and m.hasMemberPhotoThumb = 0
	and m.status <> 'D';

	SET @itemCount = @@ROWCOUNT;

	-- resume task 
	IF @itemCount > 0
		EXEC dbo.sched_resumeTask @name='Member Photo Thumbnails Queue', @engine='BERLinux';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
