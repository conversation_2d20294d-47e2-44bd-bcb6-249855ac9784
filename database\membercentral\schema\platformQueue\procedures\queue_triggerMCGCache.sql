ALTER PROC dbo.queue_triggerMCGCache
@runImmediately bit,
@type varchar(26)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- temp table needs to be there
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NULL
		RAISERROR('holding table #tblMCQRun does not exist.',16,1);

	-- if no rows, no need to continue
	IF (SELECT count(*) from #tblMCQRun) = 0 GOTO on_done;

	DECLARE @orgID int;
	select top 1 @orgID = orgID from #tblMCQRun;

	-- remove deleted members from the table. 
	-- This can happen in member merges where this proc is triggered when updating the merged accounts. This code is a safeguard to try to remove them from processing.
	DELETE tmp
	FROM #tblMCQRun as tmp
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID = @orgID 
		AND m.memberID = tmp.memberID 
		AND m.status = 'D';

	-- if no rows, no need to continue
	IF (SELECT count(*) from #tblMCQRun) = 0 GOTO on_done;

	-- valid type
	IF @type NOT IN ('ConditionsOnly','ConditionsOnlyNonImm','GroupsOnly','ConditionsAndGroups','ConditionsAndGroupsChanged')
		RAISERROR('invalid trigger type.',16,1);

	-- construct XML message
	DECLARE @xmlMessage xml;
	select @xmlMessage = isnull((
		select 'memberConditions' as t, @type as pt, tmp.orgID as o, 
			(
			select distinct memberID as m
			from #tblMCQRun
			where memberID is not null
			FOR XML PATH(''), TYPE
			),
			(
			select distinct conditionID as c
			from #tblMCQRun
			where conditionID is not null
			FOR XML PATH(''), TYPE
			)
		from #tblMCQRun as tmp
		group by tmp.orgID
		FOR XML RAW('mc'), TYPE
	),'<mc/>');


	-- log the message and add logID to the xml
	declare @triggerLogID bigint, @dateTriggered datetime = getdate();

	INSERT INTO platformStatsMC.dbo.cache_conditionsTriggerLog (dateTriggered, runImmediately, processType, xmlData)
	VALUES (@dateTriggered, @runImmediately, @type, @xmlMessage);

	select @triggerLogID = SCOPE_IDENTITY();

	SET @xmlMessage.modify('insert attribute x {sql:variable("@triggerLogID")} into (/*)[1]');
	SET @xmlMessage.modify('insert attribute dt {sql:variable("@dateTriggered")} into (/*)[1]');


	-- if runImmediately = 0 we use service broker
	IF @runImmediately = 0
		EXEC dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

	-- Else, we process cache immediately
	ELSE BEGIN
		
		DECLARE @itemGroupUID uniqueidentifier = NEWID(), @dateAdded datetime = getdate(), @queueTypeID int, @readyQueueStatusID int, 
			@notReadyQueueStatusID int, @memberConditionsQueueTypeID int, @memberConditionsReadyQueueStatusID int;
		EXEC dbo.queue_getQueueTypeID @queueType='memberGroups', @queueTypeID=@queueTypeID OUTPUT;
		EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;
		EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='notReady', @queueStatusID=@notReadyQueueStatusID OUTPUT;
		EXEC dbo.queue_getQueueTypeID @queueType='memberConditions', @queueTypeID=@memberConditionsQueueTypeID OUTPUT;
		EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@memberConditionsQueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@memberConditionsReadyQueueStatusID OUTPUT;

		IF OBJECT_ID('tempdb..#tmpMCQCondCacheChanges') IS NOT NULL 
			DROP TABLE #tmpMCQCondCacheChanges;
		CREATE TABLE #tmpMCQCondCacheChanges (memberID int, conditionID int, isAdded bit);

		IF @type = 'ConditionsOnly' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=0;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'ConditionsOnlyNonImm' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=1;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'GroupsOnly' BEGIN
			IF EXISTS (select 1 from #tblMCQRun where memberID is null)
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select @itemGroupUID, @triggerLogID, @orgID, m.memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from membercentral.dbo.ams_members as m
				where m.orgID = @orgID
				and m.status <> 'D';
			ELSE 
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberGroupsFromQueue @orgID=@orgID, @itemGroupUID=@itemGroupUID, @runImmediately=1,
				@optimizeQueue=0, @dateTriggered=@dateTriggered;
			EXEC dbo.queue_MemberGroups_clearDone @orgID=@orgID, @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'ConditionsAndGroups' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=0;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			set @dateAdded = getdate();

			IF EXISTS (select 1 from #tblMCQRun where memberID is null)
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select @itemGroupUID, @triggerLogID, @orgID, m.memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from membercentral.dbo.ams_members as m
				where m.orgID = @orgID
				and m.status <> 'D';
			ELSE 
				insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
				select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
				from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberGroupsFromQueue @orgID=@orgID, @itemGroupUID=@itemGroupUID, @runImmediately=1, 
				@optimizeQueue=0, @dateTriggered=@dateTriggered;
			EXEC dbo.queue_MemberGroups_clearDone @orgID=@orgID, @itemGroupUID=@itemGroupUID;

			GOTO on_done;
		END

		IF @type = 'ConditionsAndGroupsChanged' BEGIN
			insert into dbo.queue_memberConditions (itemGroupUID, processType, triggerLogID, orgID, memberID, conditionID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @type, @triggerLogID, orgID, memberID, conditionID, @dateAdded, @dateAdded, @memberConditionsReadyQueueStatusID
			from #tblMCQRun;

			EXEC membercentral.dbo.ams_processMemberConditionsFromQueue @itemGroupUID=@itemGroupUID, @optimizeQueue=0, @includeNonImmediate=0;
			EXEC dbo.queue_MemberConditions_clearDone @itemGroupUID=@itemGroupUID;

			set @dateAdded = getdate();

			insert into dbo.queue_memberGroups (itemGroupUID, triggerLogID, orgID, memberID, dateAdded, dateUpdated, statusID)
			select distinct @itemGroupUID, @triggerLogID, @orgID, memberID, @dateAdded, @dateAdded, @readyQueueStatusID
			from #tmpMCQCondCacheChanges;

			EXEC membercentral.dbo.ams_processMemberGroupsFromQueue @orgID=@orgID, @itemGroupUID=@itemGroupUID, @runImmediately=1, 
				@optimizeQueue=0, @dateTriggered=@dateTriggered;
			EXEC dbo.queue_MemberGroups_clearDone @orgID=@orgID, @itemGroupUID=@itemGroupUID;
		END

		IF OBJECT_ID('tempdb..#tmpMCQCondCacheChanges') IS NOT NULL 
			DROP TABLE #tmpMCQCondCacheChanges;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
