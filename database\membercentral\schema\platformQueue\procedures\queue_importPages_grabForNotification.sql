ALTER PROC dbo.queue_importPages_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='importPages', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	INSERT INTO #tmpNotify (itemGroupUID)
	select distinct itemGroupUID
	from dbo.queue_importPages
	where statusID = @statusReady
		except
	select distinct itemGroupUID
	from dbo.queue_importPages
	where statusID <> @statusReady;

	-- mark as grabbed
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
	FROM dbo.queue_importPages as qi
	INNER JOIN #tmpNotify as tmp on tmp.itemGroupUID = qi.itemGroupUID
	WHERE qi.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	SELECT DISTINCT tmpN.itemGroupUID, qid.pageName, ps.sectionName, s.siteName, s.siteCode,
		m.activeMemberID as runByMemberID, m.firstName, m.lastName, m.memberNumber, me.email as reportEmail
	FROM #tmpNotify as tmpN
	INNER JOIN dbo.queue_importPages as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.cms_pageSections as ps on ps.sectionID = qid.sectionID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = qid.runByMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1) and metag.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1) 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	ORDER BY tmpN.itemGroupUID, qid.pageName, ps.sectionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
