ALTER PROC dbo.sub_queueMarkAccepted
@recordedByMemberID int,
@subscriberIDList varchar(max),
@suppressEmails bit,
@markQueueAsReady bit,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	CREATE TABLE #tmpSubscribers (subscriberID int, itemUID uniqueidentifier DEFAULT NEWID());

	declare @statusInserting int, @statusReady int, @queueTypeID int;
	declare @emailStatusID int, @rightnow datetime;

	set @importResult = null;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='subscriptionAccept', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@statusInserting OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	-- there should only be one itemGroupUID for these emails
	declare @itemGroupUID uniqueidentifier = NEWID();

	-- get subscribers to Mark Accepted
	insert into #tmpSubscribers (subscriberID)
	select s.subscriberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem
		except
	select qi.subscriberID
	from platformQueue.dbo.queue_subscriptionAccept as qi
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID
	where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done');

	BEGIN TRAN;
		--suppress emails
		if @suppressEmails = 1 BEGIN
			set @rightnow = getdate();

			select @emailStatusID = statusID 
			from dbo.ams_emailLogStatuses
			where statusCode = 'D';

			insert into dbo.ams_emailLog (memberID, subscriberID, emailTemplateID, dateSent, actorMemberID, emailStatusID)
			select m.activeMemberID, s.subscriberID, subs.renewEmailTemplateID, @rightnow, @recordedByMemberID, @emailStatusID
			from #tmpSubscribers temp
			inner join dbo.sub_subscribers s on s.rootsubscriberID = temp.subscriberID
			inner join dbo.ams_members m on m.memberID = s.memberID
			inner join dbo.sub_rateFrequencies rf on rf.rfid = s.rfid
			inner join dbo.sub_rates r on r.rateID = rf.rateID and r.isRenewalRate=1
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
			inner join dbo.et_emailTemplates et on et.templateID = subs.renewEmailTemplateID
				union
			select m.activeMemberID, s.subscriberID, subs.emailTemplateID, @rightnow, @recordedByMemberID, @emailStatusID
			from #tmpSubscribers temp
			inner join dbo.sub_subscribers s on s.rootsubscriberID = temp.subscriberID
			inner join dbo.ams_members m on m.memberID = s.memberID
			inner join dbo.sub_rateFrequencies rf on rf.rfid = s.rfid
			inner join dbo.sub_rates r on r.rateID = rf.rateID and r.isRenewalRate=0
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
			inner join dbo.et_emailTemplates et on et.templateID = subs.emailTemplateID;
		END

		-- queue items
		INSERT INTO platformQueue.dbo.queue_subscriptionAccept (itemGroupUID, subscriberID, recordedByMemberID, statusID, dateAdded, dateUpdated)
		select @itemGroupUID, subscriberID, @recordedByMemberID, @statusInserting, getdate(), getdate()
		from #tmpSubscribers;

		-- update queue item groups to show ready to process
		IF @markQueueAsReady = 1
			update platformQueue.dbo.queue_subscriptionAccept 
			set statusID = @statusReady,
				dateUpdated = getdate()
			where itemGroupUID = @itemGroupUID;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Process Subscription MarkAccepted Queue', @engine='MCLuceeLinux';
	COMMIT TRAN;

	-- return import results
	select @importResult = (
		select getdate() as "@date",
			(select count(subscriberID) from #tmpSubscribers) as "@totalcount",
			@itemGroupUID as "@itemGroupUID"
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
