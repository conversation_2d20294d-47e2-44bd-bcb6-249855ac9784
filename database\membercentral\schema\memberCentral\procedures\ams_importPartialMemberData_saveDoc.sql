ALTER PROC dbo.ams_importPartialMemberData_saveDoc
@jobID int,
@docType varchar(20),
@sourceFile varchar(400),
@sourceFileExt varchar(3)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @defaultSiteID int, @siteCode varchar(10), @orgCode varchar(10), @documentContributorMemberID int, 
		@documentSectionID int, @documentSRTID int, @siteDocumentsPath varchar(40), 
		@docTitle varchar(100), @docDesc varchar(100), @fileName varchar(100), @fileExt varchar(3), @documentID int, 
		@documentVersionID int, @documentSiteResourceID int, @destinationFile varchar(400), @s3keyMod varchar(4), 
		@objectKey varchar(400), @trashID bit, @logProcStart datetime = getdate(), @logID bigint, @s3UploadReadyStatusID int;
	select @logID = logID from #tmpPMILogID;

	select @orgID = orgID 
	from platformQueue.dbo.memimport_jobs 
	where jobID = @jobID;

	select @defaultSiteID = s.siteID, @orgCode = o.orgcode, @siteCode = s.siteCode
	from dbo.sites as s
	inner join dbo.organizations as o on o.defaultSiteID = s.siteID
	where o.orgID = @orgID;

	select @documentContributorMemberID = runByMemberID
	from platformStatsMC.dbo.ams_memberImportHistory
	where jobID = @jobID;

	set @documentSectionID = dbo.fn_getRootSectionID(@defaultSiteID);
	set @documentSRTID = dbo.fn_getResourceTypeId('ApplicationCreatedDocument');
	select @siteDocumentsPath = siteDocumentsPath from dbo.fn_getServerSettings();

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;
	
	-- ensure directories exist
	IF (select dbo.fn_DirectoryExists(@siteDocumentsPath)) = 0
		select @trashID = dbo.fn_createDirectory(@siteDocumentsPath);
	IF (select dbo.fn_DirectoryExists(@siteDocumentsPath + @orgCode)) = 0
		select @trashID = dbo.fn_createDirectory(@siteDocumentsPath + @orgCode);
	IF (select dbo.fn_DirectoryExists(@siteDocumentsPath + @orgCode + '\' + @siteCode)) = 0
		select @trashID = dbo.fn_createDirectory(@siteDocumentsPath + @orgCode + '\' + @siteCode);

	IF @docType = 'data'
		select @docTitle = 'Data for update', 
			@docDesc = 'Data sent to update.',
			@fileName = 'original.' + @sourceFileExt,
			@fileExt = @sourceFileExt;
	IF @docType = 'backup'
		select @docTitle = 'Backup of existing data', 
			@docDesc = 'Backup of existing data prior to update.',
			@fileName = 'backup.' + @sourceFileExt,
			@fileExt = @sourceFileExt;
	IF @docType = 'report'
		select @docTitle = 'Report of update', 
			@docDesc = 'Detailed report of the update.',
			@fileName = 'report.' + @sourceFileExt,
			@fileExt = @sourceFileExt;
	IF @docType = 'newmember'
		select @docTitle = 'New Members List in update', 
			@docDesc = 'List of New Members found in the update.',
			@fileName = 'newmember.' + @sourceFileExt,
			@fileExt = @sourceFileExt;
	IF @docType = 'guestmember'
		select @docTitle = 'Guest Members List in update', 
			@docDesc = 'List of Guest Members found in the update.',
			@fileName = 'guestmember.' + @sourceFileExt,
			@fileExt = @sourceFileExt;
	IF @docType = 'rejected'
		select @docTitle = 'Rejected changes in update', 
			@docDesc = 'List of Rejected changes in the update.',
			@fileName = 'rejected.' + @sourceFileExt,
			@fileExt = @sourceFileExt;
	IF @docType = 'changes'
		select @docTitle = 'Existing member changes in update', 
			@docDesc = 'List of Existing member changes in the update.',
			@fileName = 'changes.' + @sourceFileExt,
			@fileExt = @sourceFileExt;

	EXEC dbo.cms_createDocument @siteID=@defaultSiteID, @resourceTypeID=@documentSRTID, @siteResourceStatusID=1,
		@languageID=1, @sectionID=@documentSectionID, @contributorMemberID=@documentContributorMemberID, 
		@recordedByMemberID=@documentContributorMemberID, @isActive=1, @isVisible=1, @docTitle=@docTitle, 
		@docDesc=@docDesc, @author=null, @publicationDate=null, @fileName=@fileName, @fileExt=@fileExt, 
		@documentID=@documentID OUTPUT, @documentVersionID=@documentVersionID OUTPUT, 
		@documentSiteResourceID=@documentSiteResourceID OUTPUT;

	-- need to rename/move the file
	set @destinationFile = lower(@siteDocumentsPath + @orgCode + '\' + @siteCode + '\' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
	select @trashID = dbo.fn_copyFile(@sourceFile,@destinationFile,1);

	set @s3keyMod = FORMAT(@documentVersionID % 1000, '0000');
	set @objectKey = LOWER('sitedocuments/' + @orgCode + '/' + @siteCode + '/' + @s3keyMod + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);

	-- set to deleteonsuccess=1 because we dont need searchtext to be run on these documents and that process would have handled it.
	INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
	VALUES (@s3UploadReadyStatusID, 'membercentralcdn', @objectKey, @destinationFile, 1, getdate(), getdate());

	IF @docType = 'data'
		update platformStatsMC.dbo.ams_memberImportHistory set dataDocumentID = @documentID where jobID = @jobID;
	IF @docType = 'backup'
		update platformStatsMC.dbo.ams_memberImportHistory set backupDocumentID = @documentID where jobID = @jobID;
	IF @docType = 'report'
		update platformStatsMC.dbo.ams_memberImportHistory set reportDocumentID = @documentID where jobID = @jobID;
	IF @docType = 'newmember'
		update platformStatsMC.dbo.ams_memberImportHistory set newMemDocumentID = @documentID where jobID = @jobID;
	IF @docType = 'guestmember'
		update platformStatsMC.dbo.ams_memberImportHistory set guestMemDocumentID = @documentID where jobID = @jobID;
	IF @docType = 'rejected'
		update platformStatsMC.dbo.ams_memberImportHistory set rejectedDocumentID = @documentID where jobID = @jobID;
	IF @docType = 'changes'
		update platformStatsMC.dbo.ams_memberImportHistory set changesDocumentID = @documentID where jobID = @jobID;
	
	INSERT INTO platformStatsMC.dbo.ams_memberImportLogStats (logID, procname, timeMS) 
	select @logID, 'ams_importPartialMemberData_saveDoc', datediff(ms,@logProcStart,getdate());

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
