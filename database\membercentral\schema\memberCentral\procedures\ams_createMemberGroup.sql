ALTER PROC dbo.ams_createMemberGroup
@orgID int,
@memberID int,
@groupID int,
@addedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @memberGroupID int, @dateAdded datetime = GETDATE(), @queueStatusID int, @itemGroupUID uniqueidentifier = NEWID();

	-- Ensure we have the activememberid
	SELECT @memberID = activeMemberID 
	from dbo.ams_members
	where orgID = @orgid
	and memberID = @memberID;

	-- if not there and group is in same org as member, add it
	SELECT @memberGroupID = memberGroupID
	from dbo.ams_memberGroups
	where orgID = @orgID
	and memberID = @memberID
	and groupID = @groupID;
	
	IF @memberGroupID IS NULL BEGIN
		INSERT INTO dbo.ams_memberGroups (orgID, memberID, groupID, addedByMemberID, dateAdded)
		VALUES (@orgID, @memberID, @groupID, @addedByMemberID, @dateAdded);

		-- add to cache table for immediate viewing on the grid
		IF NOT EXISTS (select memberID from dbo.cache_members_groups where orgID = @orgID and memberID = @memberID and groupID = @groupID) BEGIN
			INSERT INTO dbo.cache_members_groups (orgid, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
			VALUES (@orgID, @memberID, @groupID, 1, 0, 0, 0);

			-- Update the group prints for the member
			EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='MemberGroupPrints', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

			INSERT INTO platformQueue.dbo.queue_memberGroupPrints (itemGroupUID, orgID, memberID, statusID, dateAdded, dateUpdated)
			VALUES (@itemGroupUID, @orgID, @memberID, @queueStatusID, @dateAdded, @dateAdded);
			
			exec membercentral.dbo.cache_perms_updateGroupPrintsForMembersBulk @itemGroupUID=@itemGroupUID;
		END

		-- reprocess groups for member. dont need to reprocess conditions since conditions cannot be based on existing groups.
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		VALUES (@orgID, @memberID, null);

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='GroupsOnly';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
