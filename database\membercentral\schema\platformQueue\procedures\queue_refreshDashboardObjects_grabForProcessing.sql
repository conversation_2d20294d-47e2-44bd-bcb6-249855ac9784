ALTER PROC dbo.queue_refreshDashboardObjects_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	EXEC dbo.queue_getQueueTypeID @queueType='refreshDashboardObjects', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpDashboardObjects') IS NOT NULL
		DROP TABLE #tmpDashboardObjects;
	CREATE TABLE #tmpDashboardObjects (itemID int);

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = GETDATE()
		OUTPUT inserted.itemID
		INTO #tmpDashboardObjects
	FROM dbo.queue_refreshDashboardObjects as qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_refreshDashboardObjects
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT DISTINCT qid.itemID, qid.siteID, qid.siteCode, qid.objectID, qid.objectTypeCode
	FROM #tmpDashboardObjects AS tmp
	INNER JOIN dbo.queue_refreshDashboardObjects AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpDashboardObjects') IS NOT NULL
		DROP TABLE #tmpDashboardObjects;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
