ALTER PROC dbo.sub_importSubscribers_import
@siteID int,
@recordedByMemberID int, 
@recordRevenueTransaction bit,
@useAccrualAccounting bit,
@skipEmailTemplateNotifications bit,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @itemGroupUID uniqueidentifier, @queueTypeID int, @statusInserting int, @statusReady int, @xmlMessage xml;

	select @orgID = orgID from dbo.sites where siteID = @siteID;
	set @itemGroupUID = NEWID();

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='addSubscribers', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@statusInserting OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees;
	CREATE TABLE #tmpSubscriberTrees (memberID int, treeCode varchar(50), itemUID uniqueidentifier DEFAULT NEWID());

	INSERT INTO #tmpSubscriberTrees (memberID, treeCode)
	select distinct MCMemberID, treeCode
	from #mc_SubImport;

	BEGIN TRY
		BEGIN TRAN;
			insert into platformQueue.dbo.queue_subscriptionAdd (itemUID, itemGroupUID, recordedByMemberID, orgID, siteID, memberID, 
				treeCode, skipEmailTemplateNotifications, recordRevenueTransaction, statusID, dateAdded)
			select distinct itemUID, @itemGroupUID, @recordedByMemberID, @orgID, @siteID, memberID, treeCode, 
				@skipEmailTemplateNotifications, @recordRevenueTransaction, @statusInserting, getdate()
			from #tmpSubscriberTrees;

			insert into platformQueue.dbo.queue_subscriptionAddDetail (itemUID, rowID, subscriptionID, parentSubscriptionID, RFID, 
				rateUID, subStartDate, subEndDate, graceEndDate, recogStartDate, recogEndDate, [status], lastPrice, storeModifiedRate)
			select tmpT.itemUID, tmp.rowID, tmp.subID, tmp.parentSubID, tmp.RFID, tmp.rateUID, tmp.StartDate, tmp.EndDate, tmp.graceEndDate, 
				tmp.recogStartDate, tmp.recogEndDate, tmp.status, tmp.lastPrice, tmp.storeModifiedRate
			from #mc_SubImport as tmp
			inner join #tmpSubscriberTrees as tmpT on tmpT.memberID = tmp.MCMemberID and tmpT.treeCode = tmp.TreeCode;

			-- update queue item groups to show ready to process
			update qi
			set qi.statusID = @statusReady
			from platformQueue.dbo.queue_subscriptionAdd as qi
			inner join #tmpSubscriberTrees as tmpT on tmpT.itemUID = qi.itemUID;

			-- resume task
			EXEC dbo.sched_resumeTask @name='Subscriber Add Queue', @engine='BERLinux';

			-- send message to service broker to create all the individual messages
			select @xmlMessage = isnull((
				select 'addSubscribersLoad' as t, cast(@itemGroupUID as varchar(60)) as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');
			EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblSubErrors (msg)
		VALUES ('Unable to queue subscribers for import.');

		INSERT INTO #tblSubErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSubErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
