ALTER PROC dbo.sub_queueMarkBilled
@siteID int,
@recordedByMemberID int,
@subscriberIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @statusReady int, @itemGroupUID uniqueidentifier = NEWID(), @xmlMessage xml;

	select @orgID = orgID from dbo.sites where siteID = @siteID;
	
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='subscriptionBilled', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	-- get subscribers to Mark Billed
	IF OBJECT_ID('tempdb..#tmpMarkAsBilledSubscribers') IS NOT NULL 
		DROP TABLE #tmpMarkAsBilledSubscribers;
	CREATE TABLE #tmpMarkAsBilledSubscribers (subscriberID int, memberID int);

	insert into #tmpMarkAsBilledSubscribers (subscriberID, memberID)
	select distinct s.subscriberID, s.memberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem;

	BEGIN TRAN;
		-- queue items
		insert into platformQueue.dbo.queue_subscriptionBilled (itemGroupUID, recordedByMemberID, orgID, siteID,
			memberID, subscriberID, statusID, dateAdded, dateUpdated)
		select @itemGroupUID, @recordedByMemberID, @orgID, @siteID, memberID, subscriberID, @statusReady, getdate(), getdate()
		from #tmpMarkAsBilledSubscribers;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Subscriber Billed Queue', @engine='BERLinux';

		-- send message to service broker to create all the individual messages
		select @xmlMessage = isnull((
			select 'subscriptionBilledLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpMarkAsBilledSubscribers') IS NOT NULL 
		DROP TABLE #tmpMarkAsBilledSubscribers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
