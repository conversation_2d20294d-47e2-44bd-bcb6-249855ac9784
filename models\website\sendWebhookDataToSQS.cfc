<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSize", type="INTEGER", desc="Batch Size", value="200" },
				{ name="MaxEntriesPerSQSQueueName", type="INTEGER", desc="Maximum Entries per SQS Queue Name", value="100" },
				{ name="Threads", type="INTEGER", desc="Number of Threads", value="4" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.processQueueResult = processQueue(strTask=arguments.strTask, batchSize=local.strTaskFields.batchSize, maxEntriesPerSQSQueueName=local.strTaskFields.maxEntriesPerSQSQueueName, threads=local.strTaskFields.threads)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.totalItemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="maxEntriesPerSQSQueueName" type="numeric" required="true">
		<cfargument name="threads" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "totalItemCount":0 }>

		<!--- reprioritize the queue --->
		<cftry>
			<cfstoredproc procedure="queue_webhooks_prioritize" datasource="#application.dsn.platformQueue.dsn#">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cftry>
			<cfstoredproc procedure="queue_webhooks_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxEntriesPerSQSQueueName#">
				<cfprocresult name="local.qryWebhookEntries" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.totalItemCount = val(local.qryWebhookEntries.totalItemCount)>
						
			<cfset var objAWS = new modules.awscfml.aws(awsKey='********************', awsSecretKey='y9JjMOFs3Wuv9Laee9hy+Gr5jCpphRacz7XebZg1', defaultRegion="us-east-1")>

			<cfset local.queuesArray = arrayNew(1)>			
			<cfset local.listQueues = objAWS.sqs.listQueues()>
			<cfif val(local.listQueues.statusCode) EQ 200>
				<cfif structKeyExists(local.listQueues, "data")>					
					<cfif isArray(local.listQueues.data.ListQueuesResult)>
						<cfloop array="#local.listQueues.data.ListQueuesResult#" item="local.queueName">
							<cfset arrayAppend(local.queuesArray, ListLast(local.queueName,'/'))>
						</cfloop>
					<cfelseif local.listQueues.data.ListQueuesResult EQ "">
					<cfelse>
						<cfset arrayAppend(local.queuesArray, ListLast(local.listQueues.data.ListQueuesResult,'/'))>
					</cfif>
				</cfif>
			</cfif>

			<cfquery name="local.qryDistinctWebhookEntries" dbtype="query">
				SELECT DISTINCT siteID, sitecode, webhookID 
				FROM [local].qryWebhookEntries
			</cfquery>

			<cfset local.createdSQSQueueNameItemID = "">
			<cfloop query="local.qryDistinctWebhookEntries">
				<cfset local.SQSQueueName = "webhook_#local.qryDistinctWebhookEntries.sitecode#_#local.qryDistinctWebhookEntries.webhookID#">
				<cfif NOT ArrayContains(local.queuesArray,local.SQSQueueName)>
					<cfset local.createQueue = objAWS.sqs.createQueue(queueName=local.SQSQueueName)>
					<cfif val(local.createQueue.statusCode) EQ 200>
						 <cfquery name="local.updateWebhooks" datasource="#application.dsn.memberCentral.dsn#">
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY
								IF OBJECT_ID('tempdb..##tmpQueueWebhook') IS NOT NULL 
									DROP TABLE ##tmpQueueWebhook;
								CREATE TABLE ##tmpQueueWebhook (itemID int);

								DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
								EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='webhook', @queueTypeID=@queueTypeID OUTPUT;
								EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
								EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

								UPDATE dbo.hooks_webhooks
								SET SQSQueueName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.SQSQueueName#"> 
								WHERE webhookID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryDistinctWebhookEntries.webhookID#">;

								UPDATE platformQueue.dbo.queue_webhook 
								SET SQSQueueName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.SQSQueueName#">,
									statusID = @statusReady,
									dateUpdated = getdate()
								WHERE webhookID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryDistinctWebhookEntries.webhookID#"> 
								and statusID in (@statusReady,@statusGrabbed);

								IF OBJECT_ID('tempdb..##tmpQueueWebhook') IS NOT NULL 
									DROP TABLE ##tmpQueueWebhook;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
							END CATCH
						</cfquery>
						<cfset local.createEventSourceMapping = objAWS.lambda.createEventSourceMapping(EventSourceArn='arn:aws:sqs:us-east-1:839440652392:' & local.SQSQueueName, FunctionName='arn:aws:lambda:us-east-1:839440652392:function:webhookProcessor')>
					</cfif>
				</cfif>
			</cfloop>
		
			<cfset QueryEach(local.qryWebhookEntries, function(struct thisRow, numeric currentrownumber, query fullQuery) { 
				try {				
					if (len(thisRow.SQSQueueName)){
						updateQueueStatus(itemIDList=thisRow.itemID, queueStatus="Processing");
						var sqsMessage = { "destinationurl":"#thisRow.webhookURL#", "payload":"#thisRow.payloadMessage#" }
						var sqsResult = objAWS.sqs.sendMessage(queueName="#thisRow.SQSQueueName#", message=serializeJSON(sqsMessage));
						if (val(sqsResult.statusCode) EQ 200) {
							updateQueueStatus(itemIDList=thisRow.itemID, queueStatus="Done");
						}
						else {
							throw (message="SQS message was not sent cleanly to AWS");
						}
					}
				} catch (e) {
					local.sqsDebug = {};
					local.sqsDebug.sqsMessage = sqsMessage;
					local.sqsDebug.sqsResult = sqsResult;
					application.objError.sendError(cfcatch=e, objectToDump=local.sqsDebug);
					rethrow;
				}
			}, true, arguments.threads)>

			<cfset deleteCompletedQueueEntries()>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateQueueStatus" access="private" output="false" returntype="void">
		<cfargument name="itemIDList" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @queueStatusID INT;
			EXEC dbo.queue_getStatusIDbyType @queueType='webhook', @queueStatus=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueStatus#">, @queueStatusID=@queueStatusID OUTPUT;

			UPDATE dbo.queue_webhook
			SET statusID = @queueStatusID,
				dateUpdated = getdate()
			WHERE itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">);
		</cfquery>
	</cffunction>

	<cffunction name="deleteCompletedQueueEntries" access="private" output="false" returntype="void">
		<cfset var updateQueueStatuses = "">

		<cfquery name="updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @statusDone INT;
			EXEC dbo.queue_getStatusIDbyType @queueType='webhook', @queueStatus='Done', @queueStatusID=@statusDone OUTPUT;

			DELETE qi
			FROM dbo.queue_webhook AS qi 
			WHERE qi.statusID = @statusDone;
		</cfquery>
	</cffunction>

</cfcomponent>