ALTER PROC dbo.queue_EventsImport_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpItemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpItemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpEVQueueItems') IS NOT NULL 
		DROP TABLE #tmpEVQueueItems;
	CREATE TABLE #tmpItemGroupUIDs (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpEVQueueItems (siteID int, eventID int);

	declare @importEVStatusDone int, @evSearchTxtStatusReady int, @siteID int, @itemGroupUID uniqueidentifier, @xmlMessage xml;
	
	EXEC dbo.queue_getStatusIDbyType @queueType='importEvents', @queueStatus='done', @queueStatusID=@importEVStatusDone OUTPUT;
	EXEC dbo.queue_getStatusIDbyType @queueType='eventSearchText', @queueStatus='readyToProcess', @queueStatusID=@evSearchTxtStatusReady OUTPUT;

	INSERT INTO #tmpItemGroupUIDs (itemGroupUID)
	select distinct qid.itemGroupUID
	from dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	where qi.queueStatusID = @importEVStatusDone
		except
	select distinct qid.itemGroupUID
	from dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	where qi.queueStatusID <> @importEVStatusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- existing events
	INSERT INTO #tmpEVQueueItems (siteID, eventID)
	SELECT DISTINCT ev.siteID, ev.eventID
	FROM dbo.tblQueueItems AS qi
	INNER JOIN dbo.tblQueueItemData AS qid ON qid.itemUID = qi.itemUID
	INNER JOIN #tmpItemGroupUIDs AS tmp ON tmp.itemGroupUID = qid.itemGroupUID
	INNER JOIN dbo.tblQueueTypeDataColumns AS dc ON dc.columnID = qid.columnID
		AND dc.columnName = 'MCEventID'
	INNER JOIN membercentral.dbo.ev_events AS ev ON ev.siteID = qid.siteID
		AND ev.eventID = qid.columnValueInteger;

	-- new events
	INSERT INTO #tmpEVQueueItems (siteID, eventID)
	SELECT DISTINCT ev.siteID, ev.eventID
	FROM dbo.tblQueueItems AS qi
	INNER JOIN dbo.tblQueueItemData AS qid ON qid.itemUID = qi.itemUID
	INNER JOIN #tmpItemGroupUIDs AS tmp ON tmp.itemGroupUID = qid.itemGroupUID
	INNER JOIN dbo.tblQueueTypeDataColumns AS dc ON dc.columnID = qid.columnID
		AND dc.columnName = 'EventCode'
	INNER JOIN membercentral.dbo.ev_events AS ev ON ev.siteID = qid.siteID
		AND ev.reportCode = qid.columnValueString
		EXCEPT
	SELECT siteID, eventID
	FROM #tmpEVQueueItems;

	SELECT @siteID = MIN(siteID) FROM #tmpEVQueueItems;
	WHILE @siteID IS NOT NULL BEGIN
		SELECT @itemGroupUID = NULL, @xmlMessage = NULL;

		SET @itemGroupUID = NEWID();

		-- refreshCalendarEventsCache
		EXEC membercentral.dbo.ev_refreshCalendarEventsCache @siteID=@siteID;

		-- refreshCalendarEventsCategoryIDList
		EXEC membercentral.dbo.ev_refreshCalendarEventsCategoryIDList @siteID=@siteID;

		-- queue update search text
		INSERT INTO dbo.queue_eventSearchText (itemGroupUID, siteID, eventID, dateAdded, dateUpdated, statusID)
		SELECT @itemGroupUID, tmp.siteID, tmp.eventID, GETDATE(), GETDATE(), @evSearchTxtStatusReady
		FROM #tmpEVQueueItems AS tmp
		LEFT OUTER JOIN dbo.queue_eventSearchText AS qi ON qi.eventID = tmp.eventID
			AND qi.siteID = tmp.siteID
		WHERE tmp.siteID = @siteID
		AND qi.itemID IS NULL;

		SELECT @xmlMessage = ISNULL((
			SELECT 'eventSearchTextLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

		SELECT @siteID = MIN(siteID) FROM #tmpEVQueueItems WHERE siteID > @siteID;
	END

	BEGIN TRAN;
		DELETE from dbo.tblQueueItems
		where itemUID in (
			select qi.itemUID
			FROM dbo.tblQueueItems as qi
			inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
			INNER JOIN #tmpItemGroupUIDs as tmp on tmp.itemGroupUID = qid.itemGroupUID
			WHERE qi.queueStatusID = @importEVStatusDone
		);

		DELETE from dbo.tblQueueItemData
		where itemUID not in (select itemUID from dbo.tblQueueItems);
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpItemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpItemGroupUIDs;
	IF OBJECT_ID('tempdb..#tmpEVQueueItems') IS NOT NULL 
		DROP TABLE #tmpEVQueueItems;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
