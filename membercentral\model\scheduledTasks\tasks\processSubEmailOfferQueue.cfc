<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="100" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.processQueueResult.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true }>
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>

		<cftry>
			<!--- reprioritize the queue --->
			<cfstoredproc procedure="queue_subscriptionOffers_prioritize" datasource="#application.dsn.platformQueue.dsn#">
			</cfstoredproc>

			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_subscriptionOffers_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qrySubscribers" resultset="1">
				<cfprocresult name="local.qryMessageFields" resultset="2">
			</cfstoredproc>
	
			<!--- loop per subscriber --->
			<cfloop query="local.qrySubscribers">
			
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_subscriptionOffers as qi
					INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">
					AND qs.queueStatus = 'grabbedForProcessing'
				</cfquery>
				<cfif local.checkItemID.itemCount>

					<cftry>
						<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @statusProcessing int;
							EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionOffers', @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;

							UPDATE dbo.queue_subscriptionOffers
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">;
						</cfquery>
	
						<!--- GET OFFER MERGE FIELDS --->
						<cfset local.offerMemberData = local.objSubs.getOfferMemberData(subscriberID=local.qrySubscribers.subscriberID, emailTemplateID=local.qrySubscribers.emailTemplateID, contentVersionID=local.qrySubscribers.templateContentVersionID, overrideEmail=local.qrySubscribers.overrideEmail)>
	
						<!--- check email --->
						<cfif isValid("regex",local.offerMemberData.toEmail,application.regEx.email)>
						
							<!--- ------------- --->
							<!--- Add recipient --->
							<!--- ------------- --->
							<cftry>
								<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="email_insertMessageRecipientHistory">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="In" value="#local.qrySubscribers.emailMessageid#">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="In" value="#local.offerMemberData.mergeFields.memberID#">
									<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="In" value="#local.offerMemberData.mergeFields.firstname# #local.offerMemberData.mergeFields.lastname#">
									<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="In" value="#local.offerMemberData.toEmail#">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="In" value="#local.offerMemberData.emailTypeID#">
									<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="In" value="I">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="In" value="#local.offerMemberData.siteID#">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="Out" variable="local.recipientID">
								</cfstoredproc>
								<cfquery name="local.updateItemDetails" datasource="#application.dsn.platformQueue.dsn#">
									update dbo.queue_subscriptionOffers
									set emailRecipientID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.recipientID#">
									where itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">
								</cfquery>
							<cfcatch type="Any">
								<cfthrow message="Unable to add recipient to email queue.">
							</cfcatch>
							</cftry>

							<!--- Add recipient reference --->
							<cftry>
								<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="email_insertMessageRecipientReference">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="In" value="#local.recipientID#">
									<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="In" value="rootSubscriber">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="In" value="#local.qrySubscribers.subscriberID#">	
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="Out" variable="local.recipientReferenceID">							
								</cfstoredproc>
							<cfcatch type="Any">
								<cfthrow message="Unable to add recipient reference to email queue.">
							</cfcatch>
							</cftry>
							
							<!--- Add recipient fields --->
							<cfquery name="local.qryMsgMessageFields" dbtype="query">
								select fieldID, fieldName
								from [local].qryMessageFields
								where emailMessageID = #local.qrySubscribers.emailMessageid#
							</cfquery>
							<cfloop query="local.qryMsgMessageFields">
								<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="email_insertMessageMetadataField">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.qrySubscribers.emailMessageid#">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.qryMsgMessageFields.fieldID#">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.offerMemberData.mergeFields.memberID#">
									<cfif structKeyExists(local.offerMemberData.mergeFields,'#local.qryMsgMessageFields.fieldName#')>
										<cfprocparam cfsqltype="cf_sql_longvarchar" type="In" value="#local.offerMemberData.mergeFields[local.qryMsgMessageFields.fieldName]#">
									<cfelse>
										<cfprocparam cfsqltype="cf_sql_longvarchar" type="In" value="">
									</cfif>
									<cfprocparam cfsqltype="CF_SQL_LONGVARCHAR" type="In" null="true">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.recipientID#">
									<cfprocparam cfsqltype="CF_SQL_INTEGER" type="out" variable="local.messageFieldID">
								</cfstoredproc>
							</cfloop>
							
							<!--- Mark recipient as ready --->
							<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryChangeStatus">
								exec dbo.email_setMessageRecipientHistoryStatus
									@siteID= <cfqueryparam value="#local.offerMemberData.siteID#" cfsqltype="CF_SQL_INTEGER">,
									@messageID= <cfqueryparam value="#local.qrySubscribers.emailMessageid#" cfsqltype="CF_SQL_INTEGER">,
									@recipientID= <cfqueryparam value="#local.recipientID#" cfsqltype="CF_SQL_INTEGER">,
									@statusCode= <cfqueryparam value="Q" cfsqltype="CF_SQL_VARCHAR">,
									@updateDate= <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
							</cfquery>
						<cfelse>
							<cfthrow message="Email address #local.offerMemberData.toEmail# is not valid.">
						</cfif>
						
					<cfcatch type="Any">
						<cfset local.errMsg = cfcatch.message>
						
						<cfquery name="local.updateItemDetails" datasource="#application.dsn.platformQueue.dsn#">
							update dbo.queue_subscriptionOffers
							set errorMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.errMsg#">
							where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.itemID#">
						</cfquery>
					</cfcatch>
					</cftry>
				
					<cfquery name="local.updateToReadyToNotify" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @statusReady int;
						EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionOffers', @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;

						UPDATE dbo.queue_subscriptionOffers
						SET statusID = @statusReady,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscribers.itemID#">;
					</cfquery>
				</cfif>
				
			</cfloop>

			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_subscriptionOffers_grabForNotification" datasource="#application.dsn.platformQueue.dsn#">
					<cfprocresult name="local.qryNotifications" resultset="1">
				</cfstoredproc>
				<cfif local.qryNotifications.recordcount>
	
					<cfoutput query="local.qryNotifications" group="itemGroupUID">
						
						<cftry>
							<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
							<cfset local.thisSiteName = local.qryNotifications.siteName>
							<cfif len(local.thisReportEmail)>
								<cfsavecontent variable="local.thisEmailContent">
									<p>We processed the following subscription offer emails. You should review this list for any issues.</p>
	
									<div><b>Member / Subscription</b></div>
									<ol>
									<cfoutput>
										<li>#local.qryNotifications.subscriberName#<br/>
											#local.qryNotifications.subscriptionName#<br/>
											<cfif len(local.qryNotifications.errorMessage)>
												<div style="color:##f00;font-weight:bold;">#local.qryNotifications.errorMessage#</div>
											</cfif></li>
									</cfoutput>
									</ol>
								</cfsavecontent>

								<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryNotifications.sitecode)>

								<cfset local.emailTitle = "#local.thisSiteName# Subscription Email Offer Report">
								<cfscript>
									local.arrEmailTo = [];
									
									local.toEmailArr = listToArray(local.thisReportEmail,';');
									for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
										local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
									}
								</cfscript>

								<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name='MemberCentral', email='<EMAIL>' },
									emailto=local.arrEmailTo,
									emailreplyto="<EMAIL>",
									emailsubject="Subscription Email Offer Report for #local.thisSiteName#" ,
									emailtitle=local.emailTitle,
									emailhtmlcontent=local.thisEmailContent,
									emailAttachments=[],
									siteID=local.mc_siteinfo.siteID,
									memberID=local.qryNotifications.reportMemberID,
									messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
									sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
								)>
							</cfif>
							
							<cftry>
								<cfset local.arrOffers = arrayNew(1)>
								<cfset local.arrMessages = arrayNew(1)>
								<cfoutput>
									<cfif len(local.qryNotifications.errorMessage)>
										<cfset local.errStruct = {
											memberID = local.qryNotifications.memberID,
											memberName = local.qryNotifications.subscriberName,
											errType = "Error",
											errMessage = local.qryNotifications.errorMessage
										}>
										<cfset ArrayAppend(local.arrMessages, local.errStruct)>
									<cfelse>
										<cfset local.errStruct = {
											memberID = local.qryNotifications.memberID,
											memberName = local.qryNotifications.subscriberName,
											errType = "Offer",
											errMessage = local.qryNotifications.subscriptionName
										}>
										<cfset ArrayAppend(local.arrOffers, local.errStruct)>
									</cfif>
								</cfoutput>
								<cfset local.resultMsg = "Billed Results:<br>#arrayLen(local.arrOffers)# successful billed<br>#arrayLen(local.arrMessages)# errors">
								<cfset CreateObject('component','model.system.platform.history').addSubOfferUpdateHistory(orgID=local.qryNotifications.reportOrgID,
									actorMemberID=local.qryNotifications.reportMemberID, mainMessage=local.resultMsg, offers=local.arrOffers)>
							<cfcatch type="any">
								<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage="Unable to record history in Mongo.")>
							</cfcatch>
							</cftry>
							
							<!--- ------------- --->
							<!--- UPDATE STATUS --->
							<!--- ------------- --->
							<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
								set nocount on;
		
								declare @newstatus int;
								select @newstatus = qs.queueStatusID 
									from dbo.tblQueueStatuses as qs
									inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
									where qt.queueType = 'subscriptionOffers'
									and qs.queueStatus = 'done';
								
								IF @newstatus is not null
									update dbo.queue_subscriptionOffers
									set statusID = @newstatus,
										dateUpdated = getdate()
									where itemGroupUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryNotifications.itemGroupUID#">;
							</cfquery>
	
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
						</cftry>
						
					</cfoutput>
	
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>			

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_subscriptionOffers_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>			

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_subscriptionOffers;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>