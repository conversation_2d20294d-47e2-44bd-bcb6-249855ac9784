ALTER PROC dbo.lists_sendEmailDigest
@siteID INT,
@listName VARCHAR(200),
@digestType VARCHAR(25),
@digestDate DATE,
@listID INT,
@emailSubject VARCHAR(200),
@rawContent VARCHAR(MAX),
@contentVersionID INT,
@recordedByMemberID INT,
@sendOnDate DATETIME,
@isTestMode BIT=0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @messageTypeID INT, @messageStatusIDInserting INT, @messageStatusIDQueued INT, @orgID INT, @defaultOrgIdentityID int,
		@lyrisConfiguredSendingDomain varchar(100), @siteMailstreamConfiguredSendingDomain varchar(100), @selectedSendingDomain varchar(100),
		@mailstreamID int, @activeStatusID int, @fromaddress varchar(100), @replytoaddress varchar(200),
		@sendingSiteResourceID INT, @fieldID INT, @fieldName VARCHAR(300), @messageID INT, @vwSQL VARCHAR(MAX), 
		@emailTypeID INT, @ParamDefinition NVARCHAR(100), @fieldValueString VARCHAR(200), @mcSQL NVARCHAR(MAX), @colDataType VARCHAR(40), 
		@colList VARCHAR(MAX), @supportProviderEmail VARCHAR(100), @supportProviderName VARCHAR(100), @fID INT, @numRecipients INT,
		@publicationID INT, @orgName varchar(100), @testModeGroupCode varchar(25) = 'DigestTestModeGroup', @testModeGroupID int,
		@itemGroupUID uniqueidentifier, @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int;

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpMemberData') IS NOT NULL 
		DROP TABLE #tmpMemberData;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;

	CREATE TABLE #tmpRecipientsMID (memberID int, mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int);
	CREATE TABLE #tmpRecipientDetails (recipientID INT, recipientMemberID INT, memberNumber VARCHAR(50), listID INT);
	CREATE TABLE #tmpMemberData (memberNumber VARCHAR(50), email VARCHAR(255));
	CREATE TABLE #tmpEmailMetaDataFields (siteID int, referenceID int, referenceType varchar(20), fieldName varchar(300), 
		fieldID int, isExtMergeCode bit, fieldTextToReplace varchar(max));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	SELECT @sendingSiteResourceID = st.siteResourceID, @orgID = o.orgID, @emailTypeID = met.emailTypeID, @orgName = oi.organizationName,
		@defaultOrgIdentityID = s.defaultOrgIdentityID
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	INNER JOIN dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	INNER JOIN dbo.admin_siteTools AS st ON st.siteID = s.siteID AND st.siteID = @siteID
	INNER JOIN dbo.admin_toolTypes AS tt ON tt.toolTypeID = st.toolTypeID AND tt.toolType = 'ListAdmin'
	INNER JOIN dbo.ams_memberEmailTypes AS met ON met.orgID = s.orgID AND met.emailTypeOrder = 1;

	select @lyrisConfiguredSendingDomain = s.domainName_
	FROM lyrisarchive.trialslyris1.dbo.lists_ l
	inner join lyrisarchive.trialslyris1.dbo.topics_ t
		on l.Topic_ = t.Title_
		and l.name_ = @listName
	inner join lyrisarchive.trialslyris1.dbo.sites_ s
		on t.SiteName_ = s.Name_;

	select @activeStatusID = subuserStatusID
	from platformMail.[dbo].[sendgrid_subuserStatuses]
	where status = 'active'

	select @mailstreamID = ms.mailStreamID
	from platformMail.dbo.email_mailstreams ms
	inner join platformMail.dbo.email_messageTypes mt
		on mt.mailStreamID=ms.mailStreamID
		and mt.messageTypeCode = 'EMAILLSTDGST'

	select @siteMailstreamConfiguredSendingDomain=sud.sendingHostname
	from platformMail.dbo.sendgrid_subuserMailstreams sums
	inner join platformMail.dbo.sendgrid_subusers su
		on su.subuserID = sums.subuserID
		and su.siteID = @siteID
		and sums.mailstreamID = @mailstreamID
		and su.statusID = @activeStatusID
	inner join platformMail.dbo.sendgrid_subuserDomains sud
		on sud.subuserID=su.subuserID
		and su.activeSubuserDomainID = sud.subuserDomainID
		and sud.statusID = @activeStatusID

    -- if site doesn't have a valid config  mailstream subuser for DIGESTS use lyris configured domain
    IF @siteMailstreamConfiguredSendingDomain IS NULL
        SET @selectedSendingDomain = @lyrisConfiguredSendingDomain
    -- if site DIGEST subuser's primary domain doesn't belong to a lyris site, then we're assuming we're supposed to rebrand
    ELSE IF NOT EXISTS (select domainName_ from lyrisarchive.trialslyris1.dbo.sites_ where domainName_ = @siteMailstreamConfiguredSendingDomain)
		SET @selectedSendingDomain = @siteMailstreamConfiguredSendingDomain
    -- otherwise use lyris configured domain
	ELSE 
		SET @selectedSendingDomain = @lyrisConfiguredSendingDomain

	select @fromaddress='listsender@' + @selectedSendingDomain, @replytoaddress= @listName + '@' + @selectedSendingDomain

	set @itemGroupUID = NEWID();

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

	IF @sendOnDate IS NULL OR @sendOnDate < GETDATE()
		SET @sendOnDate = GETDATE();

	IF @isTestMode = 0 BEGIN

		IF @digestType='ListThreadIndex' BEGIN
			INSERT INTO #tmpMemberData (memberNumber, email)
			SELECT externalMemberID, emailaddr_
			FROM lyrisarchive.trialslyris1.dbo.members_ m 
			WHERE list_ = @listName collate Latin1_General_CI_AI
			AND receiveMCThreadIndex = 1
			AND memberType_ in ('normal','held');
		END ELSE BEGIN
			INSERT INTO #tmpMemberData (memberNumber, email)
			SELECT externalMemberID, emailaddr_
			FROM lyrisarchive.trialslyris1.dbo.members_ m 
			WHERE list_ = @listName collate Latin1_General_CI_AI
			AND receiveMCThreadDigest = 1
			AND memberType_ in ('normal','held');
		END

		INSERT INTO #tmpRecipientsMID (memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
		SELECT m.activeMemberID, tmp.email, @emailTypeID
		FROM #tmpMemberData AS tmp
		INNER JOIN dbo.ams_members AS m on m.memberNumber = tmp.memberNumber and m.status='A'
		INNER JOIN dbo.organizations AS o on o.orgID = m.orgID
		INNER JOIN dbo.sites s on s.orgID = o.orgID and s.siteID = @siteID;
	END ELSE BEGIN

		select @testModeGroupID = groupID
		from dbo.ams_groups g
		where orgID = 1
		and g.groupCode = @testModeGroupCode 
		and g.status='A';

		INSERT INTO #tmpMemberData (memberNumber, email)
		select m.memberNumber, me.email
		from dbo.cache_members_groups mg
		inner join dbo.ams_members m on m.orgID = 1
			and m.memberID = mg.memberID
			and m.status='A'
		inner join dbo.ams_memberEmails me on me.orgID = 1
			and me.memberID = mg.memberID
			and mg.groupID = @testModeGroupID
		inner join dbo.ams_memberEmailTags as metag on metag.orgID = 1
			and me.memberID = metag.memberID
			and me.emailTypeID = metag.emailTypeID
		inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = 1
			and metagt.emailTagTypeID = metag.emailTagTypeID
			and metagt.emailTagType = 'Primary';

		INSERT INTO #tmpRecipientsMID (memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
		select m.memberID, me.email, me.emailTypeID
		from dbo.cache_members_groups mg
		inner join dbo.ams_members m on m.orgID = 1
			and m.memberID = mg.memberID
			and m.status='A'
		inner join dbo.ams_memberEmails me on me.orgID = 1
			and me.memberID = mg.memberID
			and mg.groupID = @testModeGroupID
		inner join dbo.ams_memberEmailTags as metag on metag.orgID = 1
			and me.memberID = metag.memberID
			and me.emailTypeID = metag.emailTypeID
		inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = 1
			and metagt.emailTagTypeID = metag.emailTagTypeID
			and metagt.emailTagType = 'Primary'
		where me.email <> '';
	END

	SELECT @numRecipients = COUNT(*) FROM #tmpRecipientsMID;

	IF @numRecipients = 0 
		RAISERROR('No recipients for message.',16,1);

	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'EMAILLSTDGST';
	SELECT @messageStatusIDInserting = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'I';
	SELECT @messageStatusIDQueued = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'Q';

	SELECT TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail
	FROM dbo.networks AS net
	INNER JOIN dbo.networkSites AS ns ON net.networkID = ns.networkID
	INNER JOIN dbo.sites AS s ON s.siteID = ns.siteID
	WHERE s.siteID = @siteID 
	AND ns.isLoginNetwork = 1;
		
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsMID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@rawcontent,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

    IF OBJECT_ID('tempdb..##tmpEmailDigest') IS NOT NULL 
        DROP TABLE ##tmpEmailDigest;

	IF @colList IS NULL
		SELECT DISTINCT memberID
		INTO ##tmpEmailDigest
		FROM #tmpRecipientsMID;
	ELSE BEGIN
		SET @vwSQL = 'SELECT DISTINCT m.memberid, ' + @colList + ' 
			INTO ##tmpEmailDigest 
			FROM #tmpRecipientsMID AS m 
			INNER JOIN #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
		EXEC(@vwSQL);
	END

	SELECT NEWID() as MCItemUID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, m.membernumber, m.firstname + ' ' + m.lastname AS fullname, 
		m.firstname + ISNULL(' ' + NULLIF(m.middlename,''),'') + ' ' + m.lastname + ISNULL(' ' + NULLIF(m.suffix,''),'') AS extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, tmp.mc_emailBlast_email, tmp.mc_emailBlast_emailTypeID, o.orgcode, vw.*
	INTO #tmpRecipients
	FROM #tmpRecipientsMID AS tmp
	INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
	INNER JOIN dbo.organizations as o WITH(NOLOCK) on o.orgID = m.orgID
	INNER JOIN ##tmpEmailDigest AS vw ON vw.memberID = m.memberID
	INNER JOIN dbo.orgIdentities as i on i.orgIdentityID = @defaultOrgIdentityID
	INNER JOIN dbo.ams_states as s on s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID;

	IF OBJECT_ID('tempdb..##tmpEmailDigest') IS NOT NULL 
		DROP TABLE ##tmpEmailDigest;

	-- get tmpRecipients columns
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION INT, COLUMN_NAME SYSNAME, datatype VARCHAR(40));

	INSERT INTO #tmpRecipientsCols
	SELECT c.column_id, c.name, t.name
	FROM tempdb.sys.columns AS c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	WHERE c.OBJECT_ID = OBJECT_ID('tempdb..#tmpRecipients');

	-- insert merge code fields
	EXEC platformMail.dbo.email_massInsertMetaDataFields @siteID=@siteID, @referenceID=@listID, @referenceType='EmailListDigest', 
		@messageToParse=@rawContent, @extraMergeCodeList='';
	
	BEGIN TRAN
		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=@isTestMode, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
			@fromName=@orgName, @fromEmail=@fromaddress, @replyToEmail=@replytoaddress, @senderEmail='', 
			@subject=@emailSubject, @contentVersionID=@contentVersionID, @messageWrapper='', 
			@referenceType='EmailListDigest', @referenceID=@listID, @consentListIDs=null, @messageID=@messageID OUTPUT;

		IF @isTestMode = 0
			INSERT INTO dbo.lists_digestContent (listID, messageID, digestType, digestDate)
			VALUES (@listID, @messageID, @digestType, @digestDate);
	COMMIT TRAN;

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add recipients as I (not ready to be queued yet)
	-- we do it this way because insert with OUTPUT INTO can only refer to columns of the inserted table
	MERGE INTO platformMail.dbo.email_messageRecipientHistory AS t USING #tmpRecipients AS tmp ON 1 = 0
	WHEN NOT MATCHED THEN
		INSERT (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
		VALUES (@messageID, memberID, GETDATE(), fullname, mc_emailBlast_email, @messageStatusIDInserting, NULL, NULL, mc_emailBlast_emailTypeID, @siteID,@initialQueuePriority)
		OUTPUT INSERTED.recipientID, INSERTED.memberID, tmp.membernumber ,@listID
		INTO #tmpRecipientDetails (recipientID, recipientMemberID, memberNumber, listID);

	-- add recipient metadata
	SET @ParamDefinition = N'@messageID int, @fieldID int';
	SELECT @fieldID = MIN(fieldID) FROM #tmpEmailMetaDataFields WHERE isExtMergeCode = 0;
	WHILE @fieldID IS NOT NULL BEGIN
		SELECT @fieldName = fieldName FROM #tmpEmailMetaDataFields WHERE fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (SELECT ORDINAL_POSITION FROM #tmpRecipientsCols WHERE column_name = @fieldName) BEGIN
			SET @fieldValueString = 'isnull(cast([' + @fieldName + '] as varchar(max)),'''')';

			SET @mcSQL = 'INSERT INTO platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				SELECT @messageID, @fieldID, rcp.recipientMemberID, fieldValue = ' + @fieldValueString + ', rcp.recipientID
				FROM #tmpRecipientDetails AS rcp
				INNER JOIN #tmpRecipients AS tmp ON rcp.recipientMemberID = tmp.memberID;';
			EXEC sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		SELECT @fieldID = MIN(fieldID) FROM #tmpEmailMetaDataFields WHERE fieldID > @fieldID and isExtMergeCode = 0;
	END
	
	-- has extended merge codes
	IF EXISTS (SELECT TOP 1 fieldID FROM #tmpEmailMetaDataFields WHERE isExtMergeCode = 1) BEGIN
		-- queue recipient details with extended merge codes
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		select MCItemUID, @insertingQueueStatusID
		from #tmpRecipients;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		select distinct @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, rcp.recipientID
		from #tmpRecipientDetails as rcp
		inner join #tmpRecipients as tmp on tmp.memberID = rcp.recipientMemberID
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCRecipientID'
			union
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, @messageID
		from #tmpRecipients as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCMessageID';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger, columnValueText)
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID, mdf.fieldTextToReplace
		from #tmpEmailMetaDataFields as mdf
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCExtMergeCodeFieldID'
		cross join #tmpRecipients as tmp
		where mdf.isExtMergeCode = 1;

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = @sendOnDate
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpRecipients as tmp on tmp.MCItemUID = qi.itemUID;
	END
	-- mark recipients as queued
	ELSE BEGIN
		UPDATE mrh 
		SET emailStatusID = @messageStatusIDQueued
		FROM platformMail.dbo.email_messages as m
		INNER JOIN platformMail.dbo.email_messageRecipientHistory as mrh ON m.messageID = mrh.messageID
		WHERE m.messageID = @messageID;
	END
	

	-- return recipients
	SELECT recipientID, @messageID AS messageID, recipientMemberID AS memberID, memberNumber, listID
	FROM #tmpRecipientDetails;

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpMemberData') IS NOT NULL 
		DROP TABLE #tmpMemberData;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
