ALTER PROC dbo.queue_subscriptionRenew_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpqueue_subscriptionRenew') IS NOT NULL 
		DROP TABLE #tmpqueue_subscriptionRenew;
	CREATE TABLE #tmpqueue_subscriptionRenew (itemID int, recordedByMemberID int, orgID int, siteID int, 
		subscriberID int, rescindDate datetime, overrideStartDate datetime);

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize int, @batchUID uniqueidentifier, @itemsToProcess int = 0;
	DECLARE @taskParams TABLE (batchSize int, batchUID uniqueidentifier, requestedBatchSize int);
	
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenew', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- CAUTION:  Raising the batch size may cause maximum recursion issues.
	SET @batchSize = 100;
	SET @batchUID = NEWID();

	INSERT INTO @taskParams (batchSize, batchUID, requestedBatchSize) 
	VALUES (0, @batchUID, @batchSize);

	SELECT @itemsToProcess = COUNT(*)
	FROM dbo.queue_subscriptionRenew
	WHERE statusID = @statusReady

	IF @itemsToProcess > 0 BEGIN
		-- dequeue
		;WITH subsRenewBatch AS (
			select top 1 itemID, itemGroupUID, treeSize as totalSubs
			from dbo.queue_subscriptionRenew
			where statusID = @statusReady
			order by queuePriority
				union all
			select tmp.itemID, tmp.itemGroupUID, tmp.totalSubs
			from (
				select qi.itemID, qi.itemGroupUID, qi.treeSize + rb.totalSubs AS totalSubs,
					ROW_NUMBER() OVER (ORDER BY qi.queuePriority, qi.itemID) as rowNum
				from dbo.queue_subscriptionRenew as qi
				inner join subsRenewBatch as rb on rb.itemGroupUID = qi.itemGroupUID
				where qi.statusID = @statusReady
				and qi.itemID > rb.itemID
			) tmp
			where tmp.rowNum = 1
			and tmp.totalSubs <= @batchSize
		)
		UPDATE qid WITH (UPDLOCK, READPAST)
		SET qid.statusID = @statusGrabbed,
			qid.batchUID = @batchUID,
			qid.dateUpdated = GETDATE()
			OUTPUT inserted.itemID, inserted.recordedByMemberID, inserted.orgID, inserted.siteID, 
				inserted.subscriberID, inserted.rescindDate, inserted.overrideStartDate
			INTO #tmpqueue_subscriptionRenew
		FROM dbo.queue_subscriptionRenew as qid
		INNER JOIN subsRenewBatch as tmp on tmp.itemID = qid.itemID
		WHERE qid.statusID = @statusReady;

		UPDATE @taskParams SET batchSize = @@ROWCOUNT;
	END

	-- final data
	select qid.itemID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.subscriberID, qid.rescindDate, qid.overrideStartDate
	from #tmpqueue_subscriptionRenew as qid;

	-- qryTaskParams
	select batchSize, batchUID, requestedBatchSize
	from @taskParams;

	IF OBJECT_ID('tempdb..#tmpqueue_subscriptionRenew') IS NOT NULL 
		DROP TABLE #tmpqueue_subscriptionRenew;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
