ALTER PROC dbo.cms_queueFeaturedImages
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#tmpQueueFeaturedImages') IS NULL
		RAISERROR('Unable to locate the temp table for queuing.',16,1);

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='featuredImages', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	INSERT INTO platformQueue.dbo.queue_featuredImages (featureImageID, featureImageSizeID, enteredByMemberID, statusID, dateAdded, dateUpdated, nextAttemptDate)
	SELECT tmp.featureImageID, tmp.featureImageSizeID, @enteredByMemberID, @statusReady, @nowDate, @nowDate, @nowDate
	FROM #tmpQueueFeaturedImages as tmp
	LEFT OUTER JOIN platformQueue.dbo.queue_featuredImages as fi on fi.featureImageID = tmp.featureImageID and fi.featureImageSizeID = tmp.featureImageSizeID
	WHERE fi.itemID is null;

	IF @@ROWCOUNT > 0
		EXEC dbo.sched_resumeTask @name='Process Featured Images Queue', @engine='MCLuceeLinux';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
