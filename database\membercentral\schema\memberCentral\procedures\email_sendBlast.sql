ALTER PROC dbo.email_sendBlast
@recordedByMemberID int,
@siteID int,
@blastID int,
@messageHTML varchar(max),
@markRecipientAsReady bit,
@messageID int OUTPUT,
@hasExtendedMergeCodes bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	SELECT @messageID = NULL, @hasExtendedMergeCodes = 0;

	DECLARE @numRecipients int, @messageTypeID int, @messageStatusIDInserting int, @messageStatusIDQueued int, 
		@sendingSiteResourceID int, @rawcontent varchar(max), @messageToParse varchar(MAX), @fieldID int,
		@fieldName varchar(300), @contentID int, @languageID int, @ruleID int, @fromName varchar(200), 
		@fromEmail varchar(200), @replyToEmail varchar(200), @subject varchar(400), @contentVersionID int, 
		@deliveryReportEmail varchar(200), @vwSQL varchar(max), @ParamDefinition nvarchar(100), @mcSQL nvarchar(max), 
		@orgID int, @afID int, @newEmailDateScheduled datetime, @fieldValueString varchar(200), @sendOnDate datetime = getdate(),
		@colList varchar(max), @contentLanguageID int, @orgSystemMemberID int,
		@itemGroupUID uniqueidentifier = NEWID(), @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int,
		@currentDateLastSent datetime, @currentEmailDateScheduled datetime, @globalOptOutListID INT, @orgIdentityID int, @consentListIDs varchar(MAX);

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;

	CREATE TABLE #tmpRecipientsMID (memberID int, mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int);
	CREATE TABLE #tmpEmailMetaDataFields (siteID int, referenceID int, referenceType varchar(20), fieldName varchar(300), fieldID int, isExtMergeCode bit, fieldTextToReplace varchar(max));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';
	set @languageID = dbo.fn_getLanguageID('en');

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

	select @sendingSiteResourceID=st.siteResourceID, @orgID=s.orgID
	from dbo.sites as s
	inner join dbo.admin_siteTools as st on st.siteID = s.siteID
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	where s.siteID = @siteID;

	select @ruleID=ruleID, @fromName=fromName, @fromEmail=fromEmail, @replyToEmail=replyTo, @contentID=contentID, 
		@deliveryReportEmail=deliveryReportEmail, @afID=isnull(afID,0),
		@currentDateLastSent = emailDateSent,
		@currentEmailDateScheduled = emailDateScheduled,
		@orgIdentityID=orgIdentityID
	from dbo.email_EmailBlasts
	where blastID = @blastID;

	select @subject=contentTitle, @contentVersionID=contentVersionID, @rawContent=rawContent, @contentLanguageID=contentLanguageID
	from dbo.fn_getContent(@contentID,@languageID) as messageContent;
	
	-- run rule to get members
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	CREATE TABLE #tmpVGRMembers (memberID int PRIMARY KEY);

	EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;

	-- get recipients
	insert into #tmpRecipientsMID (memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
	select m.memberID, me.email, me.emailTypeID
	FROM #tmpVGRMembers as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberID
	INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
		and me.memberid = m.memberid
		and len(me.email) > 0
	INNER JOIN dbo.email_emailBlastEmailTypes as ebet on ebet.emailTypeID = me.emailTypeID
		and ebet.blastID = @blastID
	UNION
	SELECT m.memberID, me.email, me.emailTypeID
	FROM #tmpVGRMembers as tmp
	INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberid = tmp.memberID
	INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
		AND me.memberid = m.memberid
		AND LEN(me.email) > 0
	INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
		AND metag.memberID = me.memberID
		AND metag.emailTypeID = me.emailTypeID
	INNER JOIN dbo.email_emailBlastEmailTagTypes AS ebett ON ebett.emailTagTypeID=metag.emailTagTypeID
		AND ebett.blastID = @blastID;

	SELECT @numRecipients = COUNT(*) FROM #tmpRecipientsMID;

	IF @numRecipients = 0 BEGIN
		-- update emailDateScheduled to reschedule if this was a recurring blast.
		IF @afID > 0 BEGIN
			EXEC dbo.email_sendBlastDateAdvance @siteID=@siteID, @blastID=@blastID;
		END

		RAISERROR('No recipients for message.',16,1);
	END

	-- add any necessary metadata fields for message
	SET @messageToParse = @messageHTML + isnull(@subject,'');
		
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsMID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@messageToParse,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

	IF OBJECT_ID('tempdb..#tmpEmailBlastMemberData') IS NOT NULL 
		DROP TABLE #tmpEmailBlastMemberData;
	CREATE TABLE #tmpEmailBlastMemberData (memberID int);

	if @colList is null
		insert into #tmpEmailBlastMemberData (memberID)
		select distinct memberID 
		from #tmpRecipientsMID;
	ELSE BEGIN
		set @vwSQL = 'select m.memberID, ' + @colList + ' 
			from (select distinct memberID from #tmpRecipientsMID) as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';

		declare @tblColumns TABLE (is_hidden bit, column_ordinal int, name sysname NULL, is_nullable bit, system_type_id int, system_type_name nvarchar(256) NULL,
			max_length smallint NULL, precision tinyint, scale tinyint, collation_name sysname NULL, user_type_id int NULL, user_type_database sysname NULL, 
			user_type_schema sysname NULL, user_type_name sysname NULL, assembly_qualified_type_name nvarchar(4000) NULL, xml_collection_id int NULL, 
			xml_collection_database sysname NULL, xml_collection_schema sysname NULL, xml_collection_name sysname NULL, is_xml_document bit, is_case_sensitive bit, 
			is_fixed_length_clr_type bit, source_server sysname NULL, source_database sysname NULL, source_schema sysname NULL, source_table sysname NULL, 
			source_column sysname NULL, is_identity_column bit NULL, is_part_of_unique_key bit NULL, is_updateable bit NULL, is_computed_column bit NULL, 
			is_sparse_column_set bit NULL, ordinal_in_order_by_list smallint NULL, order_by_list_length smallint NULL, order_by_is_descending smallint NULL, 
			tds_type_id int, tds_length int, tds_collation_id int NULL, tds_collation_sort_id tinyint NULL);
		declare @sqln nvarchar(max) = @vwSQL, @column_list NVARCHAR(MAX);
		INSERT INTO @tblColumns
		EXEC dbo.sp_describe_first_result_set @tsql=@sqln;

		SELECT @column_list = STUFF((SELECT N', ' + QUOTENAME(name) + N' ' + system_type_name + N' NULL'
		FROM @tblColumns
		WHERE [name] <> 'memberid'
		ORDER BY column_ordinal
		FOR XML PATH(N''), TYPE).value(N'.[1]', N'NVARCHAR(MAX)'), 1, 2, N'');

		IF @column_list IS NOT NULL
			EXEC (N'ALTER TABLE #tmpEmailBlastMemberData ADD ' + @column_list);

		INSERT INTO #tmpEmailBlastMemberData
        EXEC(@vwSQL);
    END

	select newid() as MCItemUID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
		m.membernumber, m.firstname + ' ' + m.lastname as fullname, 
		m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, tmp.mc_emailBlast_email, tmp.emailTypeID as mc_emailBlast_emailTypeID, vw.*
	into #tmpRecipients
	from (
		select tmpMID.memberID, tmpMID.mc_emailBlast_email, min(mc_emailBlast_emailTypeID) as emailTypeID
		from #tmpRecipientsMID as tmpMID
		group by tmpMID.memberid, tmpMID.mc_emailBlast_email
	) as tmp
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberID = tmp.memberID
	inner join #tmpEmailBlastMemberData as vw on vw.memberID = m.memberID
	inner join dbo.orgIdentities as i on i.orgIdentityID = @orgIdentityID
	inner join dbo.ams_states as s on s.stateID = i.stateID
	inner join dbo.ams_countries c on c.countryID = s.countryID;

	alter table #tmpRecipients add recipientID int;

	IF OBJECT_ID('tempdb..#tmpEmailBlastMemberData') IS NOT NULL 
		DROP TABLE #tmpEmailBlastMemberData;
	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;

	-- get tmpRecipients columns
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));

	insert into #tmpRecipientsCols
	select c.column_id, c.name, t.name
	from tempdb.sys.columns as c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	where c.object_id = object_id('tempdb..#tmpRecipients');


	-- get consentListIDS
	SELECT @consentListIDs = STUFF((SELECT ',' + cast(bcl.consentListID AS VARCHAR(10))
	FROM platformMail.dbo.email_consentListTypes clt 
	INNER JOIN platformMail.dbo.email_consentLists cl ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID 
		AND modeName = 'Opt-Out'
	INNER JOIN membercentral.dbo.email_emailBlastConsentLists AS bcl ON cl.consentListID = bcl.consentListID
		AND  bcl.blastID = @blastID
	order by isPrimary desc, emailBlastConsentListID
	FOR XML PATH('')),1,1,'');

	IF NULLIF(@consentListIDs,'') is null 
		-- get default consent list for site 	
		SELECT @consentListIDs=cast(defaultConsentListID as varchar(max))
		from sites s 
		where siteID = @siteID


	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


	-- if changes were made to the content, create a new inactive contentversion and use it for the email.
	if @messageHTML <> @rawContent BEGIN
		set @orgSystemMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID)

		exec membercentral.dbo.cms_createContentVersion
			@contentLanguageID = @contentLanguageID,
			@rawContent = @messageHTML,
			@isActive = 0,
			@memberID = @orgSystemMemberID,
			@contentVersionID = @contentVersionID OUTPUT
	END

	-- insert merge code fields
	EXEC platformMail.dbo.email_massInsertMetaDataFields @siteID=@siteID, @referenceID=@blastID, @referenceType='emailblast', @messageToParse=@messageToParse, @extraMergeCodeList='';


	BEGIN TRAN
		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, @orgIdentityID=@orgIdentityID,
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
			@fromName=@fromName, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='', 
			@subject=@subject, @contentVersionID=@contentVersionID, @messageWrapper='',
			@referenceType='EmailBlast', @referenceID=@blastID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;
		
		-- update deliveryReportEmail
		IF nullIf(@deliveryReportEmail,'') is not null
			update platformMail.dbo.email_messages
			set deliveryReportEmail = @deliveryReportEmail
			where messageID = @messageID;

		-- update emailDateScheduled to reschedule or clear emailDateScheduled so scheduled task will not attempt to send again.
		set @newEmailDateScheduled = null;
		IF @afID > 0
			select @newEmailDateScheduled = dbo.fn_getAdvanceEmailDateScheduled(@siteID,@blastID);

		update dbo.email_EmailBlasts 
		set emailDateScheduled = @newEmailDateScheduled,
			emailDateSent = @sendOnDate
		where blastID =  @blastID;
	COMMIT TRAN;

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);


	-- add recipients as I (not ready to be queued yet)
	INSERT INTO platformMail.dbo.email_messageRecipientHistory (messageID, memberID, dateLastUpdated, 
		toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select @messageID, memberID, @sendOnDate, fullname, mc_emailBlast_email, @messageStatusIDInserting, 
		null, null, mc_emailBlast_emailTypeID, @siteID,@initialQueuePriority
	from #tmpRecipients;

	update tmp
	set tmp.recipientID = r.recipientID
	from #tmpRecipients as tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.memberID = tmp.memberiD
		and r.emailTypeID = tmp.mc_emailBlast_emailTypeID
		and r.messageID = @messageID;	

	-- add recipient metadata
	set @ParamDefinition = N'@messageID int, @fieldID int';		
	select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where isExtMergeCode = 0;
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from #tmpEmailMetaDataFields where fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName) BEGIN
			set @fieldValueString = 'isnull(cast([' + @fieldName + '] as varchar(max)),'''')';

			set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				select @messageID, @fieldID, memberID, fieldValue = ' + @fieldValueString + ', recipientID
				from #tmpRecipients;';
			exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where fieldID > @fieldID and isExtMergeCode = 0;
	END

	-- has extended merge codes
	IF EXISTS (select top 1 fieldID from #tmpEmailMetaDataFields where isExtMergeCode = 1) BEGIN
		SET @hasExtendedMergeCodes = 1;

		-- queue recipient details with extended merge codes
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		select MCItemUID, @insertingQueueStatusID
		from #tmpRecipients;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.recipientID
		from #tmpRecipients as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCRecipientID'
			union
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, @messageID
		from #tmpRecipients as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCMessageID';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger, columnValueText)
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID, mdf.fieldTextToReplace
		from #tmpEmailMetaDataFields as mdf
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCExtMergeCodeFieldID'
		cross join #tmpRecipients as tmp
		where mdf.isExtMergeCode = 1;

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = @sendOnDate
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpRecipients as tmp on tmp.MCItemUID = qi.itemUID;
	END
	-- mark recipients as queued
	ELSE IF @markRecipientAsReady = 1 BEGIN
		UPDATE mrh 
		SET emailStatusID = @messageStatusIDQueued
		FROM platformMail.dbo.email_messages as m
		INNER JOIN platformMail.dbo.email_messageRecipientHistory as mrh ON m.messageID = mrh.messageID
		WHERE m.messageID = @messageID;
	END
	

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	IF @messageID is not null BEGIN
		--revert email blast dates
		update dbo.email_EmailBlasts 
		set emailDateScheduled = @currentEmailDateScheduled,
			emailDateSent = @currentDateLastSent
		where blastID =  @blastID;

		--mark sending queue message as deleted
		update platformMail.dbo.email_messages
		set status='D'
		where messageID = @messageID
	END
	RETURN -1;
END CATCH
GO
