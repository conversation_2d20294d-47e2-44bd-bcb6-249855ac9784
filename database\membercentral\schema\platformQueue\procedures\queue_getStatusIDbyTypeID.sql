CREATE PROC dbo.queue_getStatusIDbyTypeID
@queueTypeID int,
@queueStatus varchar(30),
@queueStatusID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT @queueStatusID = queueStatusID
	FROM dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = @queueStatus;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
