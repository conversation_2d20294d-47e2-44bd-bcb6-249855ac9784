<cfcomponent output="false" cache="true">

	<cfset variables.sendgridAPIKey = "*********************************************************************">
	<cfset variables.limit = 500>

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSizeSingle", type="INTEGER", desc="Batch Size Single Email", value="30" },
				{ name="BatchSizeSubUser", type="INTEGER", desc="Batch Size SubUser", value="5" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfsetting requesttimeout="400">
		<!--- Comment out for now so we don't remove suppressions until we are ready.
		<cfset removeEmailsFromSuppressionList()>
		--->
		<cfset local.processQueueResult = processQueue(BatchSizeSingle=local.strTaskFields.BatchSizeSingle, BatchSizeSubUser=local.strTaskFields.BatchSizeSubUser)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="removeEmailsFromSuppressionList" access="private" output="false" returntype="void">
		<cfset var local = structNew()>

		<cfquery name="local.qryGetSuppression" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @dateLimit datetime =DATEADD(DD, -30, GETDATE()), @bouncesTypeID int, @invalid_emailsTypeID int, @spam_reportsTypeID int;
			SELECT @bouncesTypeID = typeID FROM dbo.sendgrid_suppressionListTypes WHERE typeCode = 'bounces';	
			SELECT @invalid_emailsTypeID = typeID FROM dbo.sendgrid_suppressionListTypes WHERE typeCode = 'invalid_emails';

			SELECT top 100 ss.entryID, ss.emailAddress, ss.siteID, ss.subuserID, s.sitecode, s.orgID, sub.username as subuserName,	slt.typeCode
			FROM sendgrid_suppressions ss
			inner join dbo.sendgrid_suppressionListTypes slt on slt.typeID = ss.typeID
			inner join dbo.sendgrid_subusers sub on sub.subuserID =ss.subuserID
			inner join membercentral.dbo.sites s on s.siteID = ss.siteID
			inner join membercentral.dbo.cms_siteResources sr on sr.siteID = s.siteID 
				and sr.siteResourceID = s.siteResourceID
				and sr.siteResourceStatusID=1
			WHERE ss.typeID in (@bouncesTypeID,@invalid_emailsTypeID)
			AND dateCreated < @dateLimit;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryGetSuppression.recordCount>
			<cfscript>
				var sendgrid = createObject("modules.sendgridcfc.sendgrid").init(apiKey=variables.sendgridAPIKey, includeRaw=true);
				local.qryGetSuppression.each((row) => removeEmailsFromSuppressionList_each(row,sendgrid), true, 4);
			</cfscript>
		</cfif>	
	</cffunction>

	<cffunction name="removeEmailsFromSuppressionList_each" access="private" output="false" returntype="void">
		<cfargument name="thisrow" type="struct" required="true">
		<cfargument name="sendgridapi" type="any" required="true">

		<cfset var local = structNew()>

		<cfset local.strArgs = { email=thisrow.emailAddress, on_behalf_of=thisrow.subUsername }>		
		<cfswitch expression="#thisrow.typeCode#">
			<cfcase value="Bounces">
				<cfset local.strResult = arguments.sendgridapi.deleteBounce(argumentcollection=local.strArgs)>
			</cfcase>
			<cfcase value="invalid_emails">
				<cfset local.strResult = arguments.sendgridapi.deleteInvalidEmail(argumentcollection=local.strArgs)>	
			</cfcase>
		</cfswitch>

		<cfquery name="local.qryDeleteSuppression" datasource="#application.dsn.platformMail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
				IF OBJECT_ID('tempdb..##tmpAuditLogDELSUP') IS NOT NULL 
					DROP TABLE ##tmpAuditLogDELSUP;
				CREATE TABLE ##tmpAuditLogDELSUP (msg varchar(max));
				

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#thisrow.siteID#">, @orgID int, @nowDate datetime = GETDATE(),
					@emailAddress varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#thisrow.emailAddress#">, @recordedByMemberID int;
					
				<cfif structKeyExists(application.objSiteInfo.mc_siteInfo,thisrow.siteCode)>
					set @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.mc_siteInfo[thisrow.siteCode].sysMemberID#">;
				<cfelse>					
					set @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();
				</cfif>
		
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#thisrow.orgID#">;
				
				INSERT INTO ##tmpAuditLogDELSUP (msg)
				SELECT 'Remove Suppression Email(' + s.emailAddress + ') Of Type ' + slt.typeName
				FROM dbo.sendgrid_suppressions AS s
				INNER JOIN dbo.sendgrid_suppressionListTypes AS slt ON slt.typeID = s.typeID
				WHERE s.entryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#thisrow.entryID#">;
				
				DELETE FROM dbo.sendgrid_suppressions
				WHERE entryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#thisrow.entryID#">;
				
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				SELECT ('{ "c":"auditLog", "d": {
					"AUDITCODE":"SUPENTRY",
					"ORGID":' + cast(@orgID as varchar(10)) + ',
					"SITEID":' + cast(@siteID as varchar(10)) + ',
					"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
					"ACTIONDATE":"' + convert(varchar(20),@nowDate,120) + '",
					"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }')
				FROM ##tmpAuditLogDELSUP;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>	
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="BatchSizeSingle" type="numeric" required="true">
		<cfargument name="BatchSizeSubUser" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc procedure="queue_sendgridSuppressions_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.BatchSizeSingle#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.BatchSizeSubUser#">
				<cfprocresult name="local.qryItems" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryItems.recordCount>

			<cfif local.qryItems.recordCount>
				<cfset local.sendgrid = createObject("modules.sendgridcfc.sendgrid").init(apiKey=variables.sendgridAPIKey, includeRaw=true)>

				<cfloop query="local.qryItems">
					<cfif queueItemHasStatus(queueStatus='grabbedForProcessing', itemID=local.qryItems.itemID)>
						<cfset updateQueueItemStatus(itemID=local.qryItems.itemID, queueStatus='processingItem')>

						<cfset local.strSuppressionList = {
							"bounces": [],
							"invalidemails": [],
							"spamreports": []
						}>
						
						<cfif len(local.qryItems.emailAddress)>
							<cfset local.updateMode = "single">

							<cfset local.strResponse = local.sendgrid.getBounce(email=local.qryItems.emailAddress, on_behalf_of=local.qryItems.subuserName)>
							<cfif local.strResponse.statusCode EQ 200 AND arrayLen(local.strResponse.data)>
								<cfset local.strSuppressionList['bounces'] = local.strSuppressionList['bounces'].merge(local.strResponse.data)>
							</cfif>

							<cfset local.strResponse = local.sendgrid.getInvalidEmail(email=local.qryItems.emailAddress, on_behalf_of=local.qryItems.subuserName)>
							<cfif local.strResponse.statusCode EQ 200 AND arrayLen(local.strResponse.data)>
								<cfset local.strSuppressionList['invalidemails'] = local.strSuppressionList['invalidemails'].merge(local.strResponse.data)>
							</cfif>

							<cfset local.strResponse = local.sendgrid.getSpamReport(email=local.qryItems.emailAddress, on_behalf_of=local.qryItems.subuserName)>
							<cfif local.strResponse.statusCode EQ 200 AND arrayLen(local.strResponse.data)>
								<cfset local.strSuppressionList['spamreports'] = local.strSuppressionList['spamreports'].merge(local.strResponse.data)>
							</cfif>
						<cfelse>
							<cfset local.updateMode = "subuser">

							<cfset local.offset = 0>
							<cfset local.finalOffset = variables.limit>
							<cfloop condition="#local.offset LTE local.finalOffset#">
								<cfset local.strResponse = local.sendgrid.listBounces(limit=variables.limit, offset=local.offset, on_behalf_of=local.qryItems.subuserName)>
								<cfif local.strResponse.statusCode EQ 200 AND arrayLen(local.strResponse.data)>
									<cfif local.offset EQ 0>
										<cfset local.finalOffset = getFinalOffsetFromResponseHeaders(link=local.strResponse.raw.responseHeaders.link)>
									</cfif>
									<cfset local.strSuppressionList['bounces'] = local.strSuppressionList['bounces'].merge(local.strResponse.data)>
									<cfset local.offset = local.offset + variables.limit>
								<cfelse>
									<cfbreak>
								</cfif>
							</cfloop>

							<cfset local.offset = 0>
							<cfset local.finalOffset = variables.limit>
							<cfloop condition="#local.offset LTE local.finalOffset#">
								<cfset local.strResponse = local.sendgrid.listInvalidEmails(limit=variables.limit, offset=local.offset, on_behalf_of=local.qryItems.subuserName)>
								<cfif local.strResponse.statusCode EQ 200 AND arrayLen(local.strResponse.data)>
									<cfif local.offset EQ 0>
										<cfset local.finalOffset = getFinalOffsetFromResponseHeaders(link=local.strResponse.raw.responseHeaders.link)>
									</cfif>
									<cfset local.strSuppressionList['invalidemails'] = local.strSuppressionList['invalidemails'].merge(local.strResponse.data)>
									<cfset local.offset = local.offset + variables.limit>
								<cfelse>
									<cfbreak>
								</cfif>
							</cfloop>

							<cfset local.offset = 0>
							<cfset local.finalOffset = variables.limit>
							<cfloop condition="#local.offset LTE local.finalOffset#">
								<cfset local.strResponse = local.sendgrid.listSpamReports(limit=variables.limit, offset=local.offset, on_behalf_of=local.qryItems.subuserName)>
								<cfif local.strResponse.statusCode EQ 200 AND arrayLen(local.strResponse.data)>
									<cfif local.offset EQ 0>
										<cfset local.finalOffset = getFinalOffsetFromResponseHeaders(link=local.strResponse.raw.responseHeaders.link)>
									</cfif>
									<cfset local.strSuppressionList['spamreports'] = local.strSuppressionList['spamreports'].merge(local.strResponse.data)>
									<cfset local.offset = local.offset + variables.limit>
								<cfelse>
									<cfbreak>
								</cfif>
							</cfloop>
						</cfif>

						<!--- updateSuppressionsList --->
						<cfif arrayLen(local.strSuppressionList.bounces)
								OR arrayLen(local.strSuppressionList.invalidemails)
								OR arrayLen(local.strSuppressionList.spamreports)>
							
							<cfset updateSuppressionsList(siteID=local.qryItems.siteID, subuserID=local.qryItems.subuserID, 
									referencedRecipientID=val(local.qryItems.referencedRecipientID),
									strSuppressionList=local.strSuppressionList, updateMode=local.updateMode)>

						<!--- delete existing suppression list entries for matching email address, siteID, subuserID --->
						<cfelse>
							<!---
							<cfset deleteSuppressionsList(siteID=local.qryItems.siteID, subuserID=local.qryItems.subuserID, 
									emailAddress=local.qryItems.emailAddress, updateMode=local.updateMode)>
							--->
						</cfif>

						<!--- mark as done --->
						<cfset updateQueueItemStatus(itemID=local.qryItems.itemID, queueStatus='Done')>
					</cfif>
				</cfloop>
			</cfif>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_sendgridSuppressions_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>	

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
					
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFinalOffsetFromResponseHeaders" access="private" output="false" returntype="numeric">
		<cfargument name="link" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.finalOffset = 0>

		<!--- pagination details are provided in the link header which conforms to https://datatracker.ietf.org/doc/html/rfc2068#section-19.6.2.4 --->
		<cfset local.arrLastPageLink = listToArray(arguments.link).filter(
										function(thisLink){ 
											return arguments.thisLink.findNoCase('rel="last";'); 
										})>
		<cfif arrayLen(local.arrLastPageLink)>
			<!--- java needed because lucee doesnt support look ahead/behinds --->
			<cfset local.objPattern = CreateObject("java","java.util.regex.Pattern").Compile("(?<=<).*?(?=>)")>
			<cfset local.objMatcher = local.objPattern.Matcher(local.arrLastPageLink[1])>
			<cfif local.objMatcher.Find()>
				<cfset local.finalOffsetLink = local.objMatcher.Group()>
				<cfset local.finalOffsetValue = reMatch('[?&]offset=[^&##]+', local.finalOffsetLink)>
				<cfif arrayLen(local.finalOffsetValue)>
					<cfset local.finalOffset = urlDecode(listRest(local.finalOffsetValue[1],'=',true))>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.finalOffset>
	</cffunction>

	<cffunction name="updateQueueItemStatus" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfset var qryUpdate = "">

		<cfquery name="qryUpdate" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @newStatus int;
			EXEC dbo.queue_getStatusIDbyType @queueType='SendgridSuppressions', @queueStatus=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">, @queueStatusID=@newStatus OUTPUT;

			UPDATE dbo.queue_sendgridSuppressions
			SET statusID = @newStatus,
				dateUpdated = GETDATE()
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="queueItemHasStatus" access="private" output="false" returntype="boolean">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var qryCheck = "">

		<cfquery name="qryCheck" datasource="#application.dsn.platformQueue.dsn#">
			SELECT COUNT(qi.itemID) AS itemCount
			FROM dbo.queue_sendgridSuppressions AS qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
			WHERE qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
		</cfquery>

		<cfreturn qryCheck.itemCount GT 0>
	</cffunction>

	<cffunction name="updateSuppressionsList" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="subuserID" type="numeric" required="true">
		<cfargument name="referencedRecipientID" type="numeric" required="true">
		<cfargument name="strSuppressionList" type="struct" required="true">
		<cfargument name="updateMode" type="string" required="true">

		<cfset var qryUpdateSuppressionsList = "">
				 
		<cfquery name="qryUpdateSuppressionsList" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpSubUserSuppressionLists') IS NOT NULL 
				DROP TABLE ##tmpSubUserSuppressionLists;
			CREATE TABLE ##tmpSubUserSuppressionLists (typeID int, emailAddress varchar(200), domain varchar(200), 
				dateCreated datetime, reason varchar(200), status varchar(200));

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@subuserID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subuserID#">,
				@referencedRecipientID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referencedRecipientID#">,0),
				@updateMode varchar(10) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.updateMode#">,
				@bouncesTypeID int, @invalid_emailsTypeID int, @spam_reportsTypeID int;

			<cfif arrayLen(arguments.strSuppressionList.bounces)>
				SELECT @bouncesTypeID = typeID
				FROM dbo.sendgrid_suppressionListTypes
				WHERE typeCode = 'bounces';

				<cfloop array="#arguments.strSuppressionList.bounces#" index="local.tmpStr">
					INSERT INTO ##tmpSubUserSuppressionLists (typeID, emailAddress, domain, dateCreated, reason, status)
					VALUES (@bouncesTypeID, '#local.tmpStr.email#', '#listLast(local.tmpStr.email,"@")#', 
						'#DateTimeFormat(getCentralTimeFromUnixTime(timestamp=local.tmpStr.created),"m/d/yyyy h:nn:ss tt")#',
						'#left(local.tmpStr.reason,200)#', '#left(local.tmpStr.status,200)#');
				</cfloop>
			</cfif>

			<cfif arrayLen(arguments.strSuppressionList.invalidemails)>
				SELECT @invalid_emailsTypeID = typeID
				FROM dbo.sendgrid_suppressionListTypes
				WHERE typeCode = 'invalid_emails';
				
				<cfloop array="#arguments.strSuppressionList.invalidemails#" index="local.tmpStr">
					INSERT INTO ##tmpSubUserSuppressionLists (typeID, emailAddress, domain, dateCreated, reason, status)
					VALUES (@invalid_emailsTypeID, '#local.tmpStr.email#', '#listLast(local.tmpStr.email,"@")#', 
						'#DateTimeFormat(getCentralTimeFromUnixTime(timestamp=local.tmpStr.created),"m/d/yyyy h:nn:ss tt")#',
						'#left(local.tmpStr.reason,200)#', NULL);
				</cfloop>
			</cfif>

			<cfif arrayLen(arguments.strSuppressionList.spamreports)>
				SELECT @spam_reportsTypeID = typeID
				FROM dbo.sendgrid_suppressionListTypes
				WHERE typeCode = 'spam_reports';
				
				<cfloop array="#arguments.strSuppressionList.spamreports#" index="local.tmpStr">
					INSERT INTO ##tmpSubUserSuppressionLists (typeID, emailAddress, domain, dateCreated, reason, status)
					VALUES (@spam_reportsTypeID, '#local.tmpStr.email#', '#listLast(local.tmpStr.email,"@")#', 
						'#DateTimeFormat(getCentralTimeFromUnixTime(timestamp=local.tmpStr.created),"m/d/yyyy h:nn:ss tt")#', NULL, NULL);
				</cfloop>
			</cfif>

			EXEC dbo.email_massUpdateSuppressionList @siteID=@siteID, @subuserID=@subuserID, 
				@referencedRecipientID=@referencedRecipientID, @updateMode=@updateMode;

			IF OBJECT_ID('tempdb..##tmpSubUserSuppressionLists') IS NOT NULL 
				DROP TABLE ##tmpSubUserSuppressionLists;
		</cfquery>
	</cffunction>

	<cffunction name="deleteSuppressionsList" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="subuserID" type="numeric" required="true">
		<cfargument name="emailAddress" type="string" required="true">
		<cfargument name="updateMode" type="string" required="true">

		<cfset var qryDeleteSuppressionsList = "">
				 
		<cfquery name="qryDeleteSuppressionsList" datasource="#application.dsn.platformMail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
			
				IF OBJECT_ID('tempdb..##tmpAuditLogDELSUP') IS NOT NULL 
					DROP TABLE ##tmpAuditLogDELSUP;
				CREATE TABLE ##tmpAuditLogDELSUP (msg varchar(max));

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">, @orgID int, @nowDate datetime = GETDATE(),
					@subuserID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subuserID#">,
					@emailAddress varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.emailAddress#">,
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.mc_siteInfo['MC'].sysMemberID#">;

				SET @orgID = membercentral.dbo.fn_getOrgIDFromSiteID(@siteID);

				BEGIN TRAN;
					
					INSERT INTO ##tmpAuditLogDELSUP (msg)
					SELECT 'Remove Suppression Email (' + s.emailAddress + ') Of Type ' + slt.typeName
					FROM dbo.sendgrid_suppressions AS s
					INNER JOIN dbo.sendgrid_suppressionListTypes AS slt ON slt.typeID = s.typeID
					WHERE s.siteID = @siteID
					AND s.subuserID = @subuserID
					<cfif arguments.updateMode EQ 'single'>
						AND s.emailAddress = @emailAddress
					</cfif>;
					
					DELETE FROM dbo.sendgrid_suppressions
					WHERE siteID = @siteID
					AND subuserID = @subuserID
					<cfif arguments.updateMode EQ 'single'>
						AND emailAddress = @emailAddress
					</cfif>;

					<cfif arguments.updateMode EQ 'subuser'>
						UPDATE dbo.sendgrid_subusers
						SET suppressionLists_dateUpdated = GETDATE()
						WHERE subuserID = @subuserID;
					</cfif>
					
					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					SELECT ('{ "c":"auditLog", "d": {
						"AUDITCODE":"SUPENTRY",
						"ORGID":' + cast(@orgID as varchar(10)) + ',
						"SITEID":' + cast(@siteID as varchar(10)) + ',
						"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
						"ACTIONDATE":"' + convert(varchar(20),@nowDate,120) + '",
						"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }')
					FROM ##tmpAuditLogDELSUP;
				COMMIT TRAN;

				-- process conditions
				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
					DROP TABLE ##tblMCQRun;
				CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				<cfif arguments.updateMode EQ 'subuser'>
					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select @orgID, null, conditionID
					from membercentral.dbo.ams_virtualGroupConditions 
					where orgID = @orgID
					and fieldCode = 'sup_entry';
				<cfelseif arguments.updateMode EQ 'single'>
					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select distinct @orgID, m.activeMemberID, vgc.conditionID
					from membercentral.dbo.ams_virtualGroupConditions as vgc
					inner join membercentral.dbo.ams_memberEmailTypes as met on met.orgID = @orgID
					inner join membercentral.dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.emailTypeID = met.emailTypeID
						and isnull(me.email,'') = @emailAddress
					inner join membercentral.dbo.ams_members as m on m.orgID = @orgID 
						and m.memberID = me.memberID
					where vgc.orgID = @orgID
					and vgc.fieldCode = 'sup_entry';
				</cfif>

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
					DROP TABLE ##tblMCQRun;
				IF OBJECT_ID('tempdb..##tmpAuditLogDELSUP') IS NOT NULL 
					DROP TABLE ##tmpAuditLogDELSUP;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getCentralTimeFromUnixTime" access="private" output="false" returntype="date">
		<cfargument name="timestamp" type="numeric" required="true">
		<!--- Calculate adjustments for Central timezone and daylightsavingtime --->
		<cfset var offset = ((GetTimeZoneInfo().utcHourOffset)+1)*-3600>
		<cfreturn DateAdd('s', arguments.timestamp + offset, CreateDateTime(1970, 1, 1, 0, 0, 0))>
	</cffunction>

</cfcomponent>