﻿<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.itemCount = 0>

		<cfset local.getDocResult = processDocuments()>
		<cfif NOT local.getDocResult.success>
			<cfthrow message="Error running processDocuments()">
		<cfelse>
			<cfset local.itemCount = local.itemCount + local.getDocResult.itemCount>
		</cfif>

		<cfset local.getProfResult = processProfessionals()>
		<cfif NOT local.getProfResult.success>
			<cfthrow message="Error running processProfessionals()">
		<cfelse>
			<cfset local.itemCount = local.itemCount + local.getProfResult.itemCount>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.itemCount)>
	</cffunction>

	<cffunction name="processDocuments" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_dispActionsDoc_grabForProcessing">
				<cfprocresult name="local.qryItems">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryItems.recordCount>

			<cfif local.qryItems.recordCount gt 0>
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					set nocount on;

					declare @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='dispActionsDoc', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_dispActionsDoc
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID in (#valuelist(local.qryItems.itemID)#);
				</cfquery>

				<cfquery name="local.qryDocs" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tblDocuments') IS NOT NULL 
							DROP TABLE ##tblDocuments;
						CREATE TABLE ##tblDocuments (counter int IDENTITY(1,1), documentID int NOT NULL, documentUID char(35) NULL, 
							professionalID int NOT NULL, title varchar(255) NULL, description varchar(max) NULL, active bit NOT NULL, 
							dateCreated datetime NULL, dateModified datetime NULL, documentURL varchar(500) NULL);

						<cfloop query="#local.qryItems#">
							<cfset local.thisDoc = DeSerializeJSON(local.qryItems.jsonDoc)>

							insert into ##tblDocuments (documentID, documentUID, professionalID, title, description, active, dateCreated, dateModified, documentURL)
							values (
								<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisDoc.DOCUMENTID#">,
								nullIf(<cfqueryparam cfsqltype="cf_sql_char" value="#local.thisDoc.DOCUMENTUID?:''#">,''),
								<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisDoc.PROFESSIONALID#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisDoc.TITLE?:'',255)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.thisDoc.DESCRIPTION?:''#">,
								<cfqueryparam cfsqltype="cf_sql_bit" value="#local.thisDoc.ACTIVE#">,
								nullIf(<cfqueryparam cfsqltype="cf_sql_timestamp" value="#DateTimeFormat(local.thisDoc.DATECREATED?:'','m/d/yyyy HH:nn')#">,''), 
								nullIf(<cfqueryparam cfsqltype="cf_sql_timestamp" value="#DateTimeFormat(local.thisDoc.DATEMODIFIED?:'','m/d/yyyy HH:nn')#">,''), 
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.thisDoc.URL?:''#">
							);
						</cfloop>

						MERGE disiplinaryActions.dbo.document as d USING ##tblDocuments AS tmp on d.documentID = tmp.documentID
						WHEN MATCHED THEN
							UPDATE 
							SET documentUID = tmp.documentUID, professionalID = tmp.professionalID, title = tmp.title, 
								description = tmp.description, active = tmp.active, dateCreated = tmp.dateCreated, 
								dateModified = tmp.dateModified, documentURL = tmp.documentURL
						WHEN NOT MATCHED THEN
							INSERT (documentID, documentUID, professionalID, title, description, active, dateCreated, 
								dateModified, documentURL)
							VALUES (tmp.documentID, tmp.documentUID, tmp.professionalID, tmp.title, tmp.description, 
								tmp.active, tmp.dateCreated, tmp.dateModified, tmp.documentURL);

						IF OBJECT_ID('tempdb..##tblDocuments') IS NOT NULL 
							DROP TABLE ##tblDocuments;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfquery name="local.clearDone" datasource="#application.dsn.platformQueue.dsn#">
					DELETE FROM dbo.queue_dispActionsDoc
					WHERE itemID in (#valuelist(local.qryItems.itemID)#);
				</cfquery>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>	
	</cffunction>

	<cffunction name="processProfessionals" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_dispActionsProf_grabForProcessing">
				<cfprocresult name="local.qryItems">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryItems.recordCount>

			<cfif local.qryItems.recordCount gt 0>
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					set nocount on;

					declare @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='dispActionsProf', @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_dispActionsProf
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID in (#valuelist(local.qryItems.itemID)#);
				</cfquery>

				<cfquery name="local.qryProfs" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tblProfs') IS NOT NULL 
							DROP TABLE ##tblProfs;
						IF OBJECT_ID('tempdb..##profUpdated') IS NOT NULL 
							DROP TABLE ##profUpdated;
						CREATE TABLE ##tblProfs (counter int IDENTITY(1,1), professionalId int NOT NULL, professionalUID char(35) NULL, 
							companyName varchar(255) NULL, salutation varchar(10) NULL, firstName varchar(50) NULL, lastName varchar(50) NULL, 
							middleName varchar(50) NULL, suffix varchar(10) NULL, professionalSuffix varchar(10) NULL, alias varchar(150) NULL, 
							birthYear char(4) NULL, licenseNumber varchar(50) NULL, address1 varchar(50) NULL, address2 varchar(50) NULL, 
							city varchar(50) NULL, state varchar(10) NULL, zip varchar(15) NULL, phone varchar(15) NULL, disciplineId int NULL,
							subSpecialties varchar(255) NULL, authorityId int NULL, actionDate datetime NULL, lastActionDate datetime NULL,
							misconductDescr varchar(max) NULL, actionsTaken varchar(max) NULL);
						CREATE TABLE ##profUpdated (professionalId int NOT NULL);

						<cfloop query="#local.qryItems#">
							<cfset local.thisProf = DeSerializeJSON(local.qryItems.jsonDoc)>

							insert into ##tblProfs (professionalID, professionalUID, companyName, salutation, firstName, lastName, middleName, 
								suffix, professionalSuffix, alias, birthYear, licenseNumber, address1, address2, city, state, zip, 
								phone, disciplineId, subSpecialties, authorityId, actionDate, lastActionDate, misconductDescr, 
								actionsTaken)
							values (
								<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisProf.PROFESSIONALID#">,
								nullIf(<cfqueryparam cfsqltype="cf_sql_char" value="#local.thisProf.PROFESSIONALUID?:''#">,''),
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.COMPANYNAME?:'',255)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.SALUTATION?:'',10)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.FIRSTNAME?:'',50)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.LASTNAME?:'',50)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.MIDDLENAME?:'',50)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.SUFFIX?:'',10)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.PROFESSIONALSUFFIX?:'',10)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.ALIAS?:'',150)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.BIRTHYEAR?:'',4)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.LICENSENUMBER?:'',50)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.ADDRESS1?:'',50)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.ADDRESS2?:'',50)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.CITY?:'',50)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.STATE?:'',10)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.ZIP?:'',15)#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.PHONE?:'',15)#">,
								<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisProf.DISCIPLINEID#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.thisProf.SUBSPECIALTIES?:'',255)#">,
								<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisProf.AUTHORITYID#">,
								nullIf(<cfqueryparam cfsqltype="cf_sql_timestamp" value="#DateTimeFormat(local.thisProf.ACTIONDATE?:'','m/d/yyyy HH:nn')#">,''), 
								nullIf(<cfqueryparam cfsqltype="cf_sql_timestamp" value="#DateTimeFormat(local.thisProf.LASTACTIONDATE?:'','m/d/yyyy HH:nn')#">,''), 
								<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.thisProf.MISCONDUCTDESCR?:''#">,
								<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.thisProf.ACTIONSTAKEN?:''#">
							);
						</cfloop>

						BEGIN TRAN;
							MERGE disiplinaryActions.dbo.professional as p USING ##tblProfs AS tmp on p.professionalID = tmp.professionalID
							WHEN MATCHED THEN
								UPDATE 
								SET companyName = tmp.companyName, salutation = tmp.salutation, firstName = tmp.firstName, lastName = tmp.lastName, 
									middleName = tmp.middleName, suffix = tmp.suffix, professionalSuffix = tmp.professionalSuffix, alias = tmp.alias, 
									birthYear = tmp.birthYear, licenseNumber = tmp.licenseNumber, address1 = tmp.address1, address2 = tmp.address2, 
									city = tmp.city, state = tmp.state, zip = tmp.zip, phone = tmp.phone, disciplineId = tmp.disciplineId, 
									subSpecialties = tmp.subSpecialties, authorityId = tmp.authorityId, actionDate = tmp.actionDate, 
									lastActionDate = tmp.lastActionDate, misconductDescr = tmp.misconductDescr, actionsTaken = tmp.actionsTaken 
							WHEN NOT MATCHED THEN
								INSERT (professionalID, companyName, salutation, firstName, lastName, middleName, suffix, professionalSuffix, alias, 
									birthYear, licenseNumber, address1, address2, city, state, zip, phone, disciplineId, subSpecialties, authorityId, 
									actionDate, lastActionDate, misconductDescr, actionsTaken)
								VALUES (tmp.professionalID, tmp.companyName, tmp.salutation, tmp.firstName, tmp.lastName, tmp.middleName, tmp.suffix, 
									tmp.professionalSuffix, tmp.alias, tmp.birthYear, tmp.licenseNumber, tmp.address1, tmp.address2, tmp.city, 
									tmp.state, tmp.zip, tmp.phone, tmp.disciplineId, tmp.subSpecialties, tmp.authorityId, tmp.actionDate, 
									tmp.lastActionDate, tmp.misconductDescr, tmp.actionsTaken)
							OUTPUT inserted.professionalID
							INTO ##profUpdated;

							MERGE disiplinaryActions.dbo.uniqueNames as un USING disiplinaryActions.dbo.professional AS p on 
								isnull(p.companyName,'') = un.companyName AND
								isnull(p.salutation,'') = un.salutation AND
								isnull(p.firstName,'') = un.firstName AND
								isnull(p.lastName,'') = un.lastName AND
								isnull(p.middleName,'') = un.middleName AND
								isnull(p.suffix,'') = un.suffix AND
								isnull(p.professionalSuffix,'') = un.professionalSuffix
							WHEN NOT MATCHED THEN
								INSERT (companyName, salutation, firstName, lastName, middleName, suffix, professionalSuffix)
								VALUES (isnull(companyName,''), isnull(salutation,''), isnull(firstName,''), isnull(lastName,''), 
									isnull(middleName,''), isnull(suffix,''), isnull(professionalSuffix,''));

							-- Add new records to professionalUniqueNames
							insert into disiplinaryActions.dbo.professionalUniqueNames (professionalID, uniqueNameID)
							select distinct p.professionalID, un.uniqueNameID
							from ##profUpdated as pu
							inner join disiplinaryActions.dbo.professional as p on p.professionalID = pu.professionalID
							inner join disiplinaryActions.dbo.uniqueNames un on 
								isnull(p.companyName,'') = un.companyName
								AND isnull(p.salutation,'') = un.salutation
								AND isnull(p.firstName,'') = un.firstName
								AND isnull(p.lastName,'') = un.lastName
								AND isnull(p.middleName,'') = un.middlename
								AND isnull(p.suffix,'') = un.suffix
								AND isnull(p.professionalSuffix,'') = un.professionalSuffix
								except 
							select professionalId, uniqueNameID
							from disiplinaryActions.dbo.professionalUniqueNames;

							-- Build search text for professional
							update p
							set p.searchText = 
								isNull(companyName,'') + ' ' + isNull(salutation,'') + ' ' + isNull(firstName,'') + ' ' + 
								isNull(lastName,'') + ' ' + isNull(middleName,'') + ' ' + isNull(suffix,'') + ' ' + 
								isNull(professionalSuffix,'') + ' ' + isNull(alias,'') + ' ' + isNull(licenseNumber,'') +
								isNull(address1,'') + ' ' + isNull(city,'') + ' ' + isNull(state,'') +
								isNull(misconductDescr,'') + ' ' + isNull(actionsTaken,'')
							from ##profUpdated as pu
							inner join disiplinaryActions.dbo.professional as p on p.professionalID = pu.professionalID;

							-- Build search text for uniqueNames
							update disiplinaryActions.dbo.uniqueNames
							set searchText = companyName + ' ' + salutation + ' ' + firstName + ' ' + lastName + ' ' + middleName + ' ' + suffix + ' ' + professionalSuffix
							where searchText is null;
						COMMIT TRAN;

						IF OBJECT_ID('tempdb..##tblProfs') IS NOT NULL 
							DROP TABLE ##tblProfs;
						IF OBJECT_ID('tempdb..##profUpdated') IS NOT NULL 
							DROP TABLE ##profUpdated;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfquery name="local.clearDone" datasource="#application.dsn.platformQueue.dsn#">
					DELETE FROM dbo.queue_dispActionsProf
					WHERE itemID in (#valuelist(local.qryItems.itemID)#);
				</cfquery>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>	
	</cffunction>
		
</cfcomponent>