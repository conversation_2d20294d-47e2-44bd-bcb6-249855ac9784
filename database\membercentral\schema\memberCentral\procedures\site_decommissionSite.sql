ALTER PROC dbo.site_decommissionSite
@siteCode varchar(10),
@runByMemberID int,
@doit char(9)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @siteCode is null or @doit <> 'yesreally'
		RAISERROR('Blocked from running.',16,1);

	declare @siteID int, @sectionID int, @siteResourceID int, @orgID int, @memberID int, @groupIDList varchar(max),
		@itemGroupUIDMemberDelete UNIQUEIDENTIFIER = NEWID(), @statusReadyMemberDelete int, 
		@nowDate datetime = getdate(), @xmlMessage xml, @reportID int, @queryID int, @blastID int, @ruleID int,
		@conditionIDList varchar(max), @apiUserID int, @numOtherActiveSites int;
	select @siteID = siteID, @orgID = orgID from dbo.sites where sitecode = @siteCode;
	
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberDelete', @queueStatus='readyToProcess', @queueStatusID=@statusReadyMemberDelete OUTPUT;

	-- are there any other active sites other than this one in the org? If not, we can delete some org data
	select @numOtherActiveSites = count(s.siteID)
	FROM dbo.sites as s
	INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID 
		and sr.siteResourceID = s.siteResourceID 
		and sr.siteResourceStatusID = 1
	WHERE s.orgID = @orgID
	AND s.siteID <> @siteID;

	-- delete all site resources including the site's
	select @siteResourceID = min(siteResourceID) from dbo.cms_siteResources where siteID = @siteID and siteResourceStatusID = 1 and parentSiteResourceID is null;
	while @siteResourceID is not null begin
		EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
		select @siteResourceID = min(siteResourceID) from dbo.cms_siteResources where siteID = @siteID and siteResourceStatusID = 1 and parentSiteResourceID is null and siteResourceID > @siteResourceID;
	end

	-- delete api users
	SELECT @apiUserID = min(userID) FROM dbo.api_users WHERE siteID = @siteID and tokenStatus <> 'D' and isSystemToken = 0;
	WHILE @apiUserID IS NOT NULL BEGIN
		EXEC dbo.api_deleteLogin @userID=@apiUserID;
		SELECT @apiUserID = min(userID) FROM dbo.api_users WHERE siteID = @siteID AND tokenStatus <> 'D' and isSystemToken = 0 and userID > @apiUserID;
	END

	-- delete all members
	IF @numOtherActiveSites = 0 BEGIN
		INSERT INTO platformQueue.dbo.queue_memberDelete (itemGroupUID, orgID, memberID, addedByMemberID, statusID, dateAdded, dateUpdated)
		SELECT @itemGroupUIDMemberDelete, @orgID, memberID, @runByMemberID, @statusReadyMemberDelete, @nowDate, @nowDate
		FROM dbo.ams_members
		WHERE orgID = @orgID 
		AND [status] <> 'D'
		AND isProtected = 0
			EXCEPT
		SELECT @itemGroupUIDMemberDelete, @orgID, memberID, @runByMemberID, @statusReadyMemberDelete, @nowDate, @nowDate
		FROM platformQueue.dbo.queue_memberDelete
		WHERE orgID = @orgID;

		SELECT @xmlMessage = ISNULL((
			SELECT 'memberDeleteLoad' AS t, cast(@itemGroupUIDMemberDelete as varchar(60)) AS u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');

		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	END

	-- delete viewed member times
	delete from dbo.ams_viewedMemberTimes where viewedSiteID = @siteID;

	-- delete reports
	SELECT @reportID = MIN(reportID) FROM dbo.rpt_savedReports WHERE siteID = @siteID;
	WHILE @reportID IS NOT NULL BEGIN
		EXEC dbo.rpt_deleteSavedReport @reportID=@reportID, @recordedByMemberID=@runByMemberID;
		SELECT @reportID = MIN(reportID) FROM dbo.rpt_savedReports WHERE siteID = @siteID AND reportID > @reportID;
	END

	-- delete saved queries
	SELECT @queryID = MIN(queryID) FROM dbo.ams_savedQueries WHERE siteID = @siteID;
	WHILE @queryID IS NOT NULL BEGIN
		EXEC dbo.ams_deleteSavedQuery @queryID=@queryID, @recordedByMemberID=@runByMemberID;
		SELECT @queryID = MIN(queryID) FROM dbo.ams_savedQueries WHERE siteID = @siteID AND queryID > @queryID;
	END
	SELECT @queryID = MIN(queryID) FROM dbo.ams_savedLinkedRecordsQueries WHERE siteID = @siteID;
	WHILE @queryID IS NOT NULL BEGIN
		EXEC dbo.ams_deleteSavedLinkedRecordsQuery @queryID=@queryID, @recordedByMemberID=@runByMemberID;
		SELECT @queryID = MIN(queryID) FROM dbo.ams_savedLinkedRecordsQueries WHERE siteID = @siteID AND queryID > @queryID;
	END

	-- delete email blasts
	SELECT @blastID = min(blastID) FROM dbo.email_emailBlasts WHERE siteID = @siteID;
	WHILE @blastID IS NOT NULL BEGIN
		EXEC dbo.email_deleteEmailBlast @blastID=@blastID, @recordedByMemberID=@runByMemberID;
		SELECT @blastID = min(blastID) FROM dbo.email_emailBlasts WHERE siteID = @siteID AND blastID > @blastID;
	END

	-- delete virtual group rules (backwards)
	IF @numOtherActiveSites = 0 BEGIN
		SELECT @ruleID = max(ruleID) FROM dbo.ams_virtualGroupRules WHERE orgID = @orgID;
		WHILE @ruleID IS NOT NULL BEGIN
			EXEC dbo.ams_deleteVirtualGroupRule @orgID=@orgID, @ruleIDList=@ruleID, @recordedByMemberID=@runByMemberID, @bypassQueue=1;
			SELECT @ruleID = max(ruleID) FROM dbo.ams_virtualGroupRules WHERE orgID = @orgID AND ruleID < @ruleID;
		END
	END

	-- delete virtual group conditions
	IF @numOtherActiveSites = 0 BEGIN
		select @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(conditionID as varchar(10)) 
		FROM dbo.ams_virtualGroupConditions 
		WHERE orgID = @orgID;

		EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@conditionIDList, @recordedByMemberID=@runByMemberID, @bypassQueue=1;
	END

	-- delete groups
	IF @numOtherActiveSites = 0 BEGIN
		select @groupIDList = COALESCE(@groupIDList + ',', '') + cast(groupID as varchar(10)) 
		from dbo.ams_groups 
		where orgID = @orgID 
		and status <> 'D' 
		and isSystemGroup = 0;

		EXEC dbo.ams_deleteGroup @orgID=@orgID, @groupIDList=@groupIDList, @recordedByMemberID=null;
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
