ALTER PROC dbo.ams_createMemberSiteDefaultsByOrgID
@orgID int,
@loginFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpMembersForSiteDefaults') IS NOT NULL
		DROP TABLE #tmpMembersForSiteDefaults;
	IF OBJECT_ID('tempdb..#tblUn') IS NOT NULL
		DROP TABLE #tblUn;
	IF OBJECT_ID('tempdb..#tblPW') IS NOT NULL
		DROP TABLE #tblPW;
	CREATE TABLE #tmpMembersForSiteDefaults (autoid int IDENTITY(1,1), siteID int, [status] char(1), memberID int);
	CREATE TABLE #tblUn (autoid int IDENTITY(1,1), un varchar(40));
	CREATE TABLE #tblPW (autoid int IDENTITY(1,1), pw varchar(40));

	DECLARE @queueTypeID int, @readyToProcessQueueStatusID int, @processingQueueStatusID int, @memCount int = 0, 
		@orgcode varchar(10), @siteID int, @loginNetworkID int;
	
	SELECT @orgcode = orgcode from dbo.organizations where orgID = @orgID;
	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='memberSiteDefByOrg', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingQueueStatusID OUTPUT;

	IF NOT EXISTS (select 1 from platformQueue.dbo.queue_memberSiteDefByOrg where orgID = @orgID and statusID = @readyToProcessQueueStatusID)
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_memberSiteDefByOrg
	SET statusID = @processingQueueStatusID,
		dateUpdated = GETDATE()
	WHERE orgID = @orgID
	AND statusID = @readyToProcessQueueStatusID;

	-- members in groups that are not linked to a profile and do not have def u/p
	-- guest accounts that are not linked to a profile and do not have def u/p
	SELECT @siteID = min(siteID) from dbo.sites where orgID = @orgID;
	WHILE @siteID is not null BEGIN
		INSERT INTO #tmpMembersForSiteDefaults (siteID, [status], memberID)
		select distinct s.siteid, m.status, m.memberid
		from dbo.sites s
		inner join dbo.ams_members m on m.orgID = @orgID 
			and m.status in ('A','I')
		inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
			and gprp.groupPrintID = m.groupPrintID
		inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
			and srfrp.siteResourceID = s.siteResourceID
			and srfrp.functionID = @loginFID
			and srfrp.rightPrintID = gprp.rightPrintID
		left outer join dbo.ams_memberNetworkProfiles as mnp
			inner join dbo.ams_networkProfiles as np on np.profileid = mnp.profileid 
				and np.status <> 'D'
			on mnp.memberid = m.memberid 
			and mnp.siteID = @siteID 
			and mnp.status <> 'D'
		left outer join dbo.ams_memberSiteDefaults as msd on msd.siteID = @siteID 
			and msd.memberid = m.memberid 
			and msd.status <> 'D'
		where s.siteID = @siteID
		and mnp.mnpID is null
		and msd.defaultID is null
			union
		select distinct s.siteid, m.status, m.memberid
		from dbo.sites s
		inner join dbo.ams_members m on m.orgID = @orgID 
			and m.status in ('A','I')
			and m.memberTypeID = 1
		left outer join dbo.ams_memberNetworkProfiles as mnp
			inner join dbo.ams_networkProfiles as np on np.profileid = mnp.profileid 
				and np.status <> 'D'
			on mnp.memberid = m.memberid 
			and mnp.siteID = @siteID 
			and mnp.status <> 'D'
		left outer join dbo.ams_memberSiteDefaults as msd on msd.siteid = @siteID 
			and msd.memberid = m.memberid 
			and msd.status <> 'D'
		where s.siteID = @siteID
		and mnp.mnpID is null
		and msd.defaultID is null;

		SET @memCount = @memCount + @@ROWCOUNT;

		SELECT @siteID = min(siteID) from dbo.sites where orgID = @orgID and siteID > @siteID;
	END

	-- create a pool of default usernames
	select @loginNetworkID = dbo.fn_getLoginNetworkFromSiteID(@siteID);

	INSERT INTO #tblUN (un)
	SELECT TOP (@memCount) rntbl2.un
	FROM (
		select randomNumber, @orgcode + randomNumber as un
		from (
			SELECT cast(abs(cast(newid() as binary(6)) %999999) + 1 as varchar(6)) as randomNumber
			FROM dbo.F_TABLE_NUMBER_RANGE(1,@memCount*6)
		) as rntbl
	) as rntbl2
	WHERE NOT EXISTS (
		select defaultusername
		from dbo.ams_memberSiteDefaults
		where siteID = @siteID
		and [status] in ('A','I')
		and defaultusername = rntbl2.un
		)
	AND NOT EXISTS (
		select username
		from dbo.ams_networkprofiles
		where networkID IN (@loginNetworkID,1)
		and [status] in ('A','I')
		and username = rntbl2.un
		)
	GROUP BY rntbl2.randomNumber, rntbl2.un
	HAVING rntbl2.randomNumber > 100000
	AND Count(rntbl2.un) = 1;

	-- create a pool of default passwords
	INSERT INTO #tblPW (pw)
	SELECT TOP (@memCount) sample.randomNumber
	FROM (
		SELECT cast(abs(cast(newid() as binary(6)) %999999) + 1 as varchar(6)) as randomNumber
		FROM dbo.F_TABLE_NUMBER_RANGE(1,@memCount*6)) as sample
	GROUP BY sample.randomNumber
	HAVING sample.randomNumber > 100000
	AND Count(sample.randomNumber) = 1;

	-- add defaults
	INSERT INTO dbo.ams_memberSiteDefaults (memberID, siteID, defaultUsername, defaultPassword, [status], dateCreated)
	SELECT m.memberID, m.siteID, u.un, p.pw, m.status, getdate()
	FROM #tmpMembersForSiteDefaults as m
	inner join #tblUN as u on u.autoID = m.autoID
	inner join #tblPW as p on p.autoID = m.autoID;

	-- delete from queue table
	DELETE FROM platformQueue.dbo.queue_memberSiteDefByOrg
	WHERE orgID = @orgID
	AND statusID = @processingQueueStatusID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpMembersForSiteDefaults') IS NOT NULL
		DROP TABLE #tmpMembersForSiteDefaults;
	IF OBJECT_ID('tempdb..#tblUn') IS NOT NULL
		DROP TABLE #tblUn;
	IF OBJECT_ID('tempdb..#tblPW') IS NOT NULL
		DROP TABLE #tblPW;
		
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
