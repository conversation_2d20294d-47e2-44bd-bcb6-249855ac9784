ALTER PROC dbo.sub_addSubscriberFromQueue
@itemUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
	declare @queueTypeID int, @statusReady int, @statusProcessing int, @statusNotify int, @itemStatus int, @treeRowID int, 
		@parentSubscriptionID int, @parentSubscriberID int, @orgID int, @memberID int, @subscriptionID int, @RFID int, 
		@GLAccountID int, @status char(1), @subStartDate datetime, @subEndDate datetime, @graceEndDate datetime, 
		@recogStartDate datetime, @recogEndDate datetime, @activationOptionCode char(1), @recordRevenueTransaction bit,
		@recordedByMemberID int, @lastPrice decimal(18,2), @storeModifiedRate varchar(3), @skipEmailTemplateNotifications bit, 
		@siteID int, @subscriberID int, @treeOK bit, @subscriberIDList varchar(max), @APWithTransactions bit, @suppressEmails bit,
		@statusUpdated bit;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='addSubscribers', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusNotify OUTPUT;
	
	-- if itemUID is not readyToProcess, kick out now
	select @itemStatus = statusID from platformQueue.dbo.queue_subscriptionAdd where itemUID = @itemUID;
	IF @itemStatus <> @statusReady
		GOTO on_done;

	IF OBJECT_ID('tempdb..#tmpAddSubscribers') IS NOT NULL 
		DROP TABLE #tmpAddSubscribers;
	CREATE TABLE #tmpAddSubscribers (recordedByMemberID int, orgID int, siteID int, memberID int, skipEmailTemplateNotifications bit,
		rowID int, subscriptionID int, parentSubscriptionID int, RFID int, subStartDate datetime, subscriberID int, subEndDate datetime, 
		graceEndDate datetime, recogStartDate datetime, recogEndDate datetime, [status] char(1), lastPrice decimal(18,2), storeModifiedRate varchar(3), 
		GLAccountID int, ActivationOptionCode char(1), recordRevenueTransaction bit);

	-- get subscription tree
	insert into #tmpAddSubscribers
	select qid.recordedByMemberID, qid.orgID, qid.siteID, qid.memberID, qid.skipEmailTemplateNotifications,
		qidd.rowID, qidd.subscriptionID, qidd.parentSubscriptionID, qidd.RFID, qidd.subStartDate, qidd.subscriberID, 
		qidd.subEndDate, qidd.graceEndDate, qidd.recogStartDate, qidd.recogEndDate, qidd.status, qidd.lastPrice, qidd.storeModifiedRate, 
		GLAccountID = case when subs.allowRateGLAccountOverride = 1 and r.GLAccountID is not null then r.GLAccountID else subs.GLAccountID end,
		ActivationOptionCode = case when qidd.status in ('R','O') then ao.subActivationCode else 'N' end, qid.recordRevenueTransaction
	from platformQueue.dbo.queue_subscriptionAdd as qid
	inner join platformQueue.dbo.queue_subscriptionAddDetail as qidd on qidd.itemUID = qid.itemUID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = qidd.subscriptionID
	inner join membercentral.dbo.sub_activationOptions as ao on ao.subActivationID = subs.subAlternateActivationID
	inner join membercentral.dbo.sub_rates as r on r.uid = qidd.rateUID
	where qid.itemUID = @itemUID
	order by qidd.rowID;

	IF @@ROWCOUNT = 0
		GOTO on_done;

	IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	
	update platformQueue.dbo.queue_subscriptionAdd
	set statusID = @statusProcessing, dateUpdated = GETDATE()
	where itemUID = @itemUID;
	
	BEGIN TRY
		BEGIN TRAN;
			-- loop for each subscription in the tree
			select @treeRowID = min(rowID) from #tmpAddSubscribers;
			while @treeRowID is not null BEGIN
				select @parentSubscriptionID=null, @parentSubscriberID=null, @subscriberID=null, @orgID=null, @memberid=null,
					@subscriptionID=null, @RFID=null, @GLAccountID=null, @status=null, @subStartDate=null, @subEndDate=null,
					@graceEndDate=null, @recogStartDate=null, @recogEndDate=null, @activationOptionCode=null, @siteID=null, 
					@recordedByMemberID=null, @lastPrice=null, @storeModifiedRate=null, @skipEmailTemplateNotifications=null,
					@recordRevenueTransaction=null, @statusUpdated=null;

				select @parentSubscriptionID=parentSubscriptionID, @orgID=orgID, @memberid=memberid, @subscriptionID=subscriptionID,
					@RFID=RFID, @GLAccountID=GLAccountID, @status=[status], @subStartDate=subStartDate, @subEndDate=subEndDate,
					@graceEndDate=graceEndDate, @recogStartDate=recogStartDate, @recogEndDate=recogEndDate, @lastPrice=lastPrice, 
					@activationOptionCode=activationOptionCode, @recordedByMemberID=recordedByMemberID, @storeModifiedRate=storeModifiedRate,
					@skipEmailTemplateNotifications=skipEmailTemplateNotifications, @recordRevenueTransaction=recordRevenueTransaction, @siteID=siteID
				from #tmpAddSubscribers
				where rowID = @treeRowID;

				IF @parentSubscriptionID is not null
					select top 1 @parentSubscriberID = subscriberID 
					from #tmpAddSubscribers
					where subscriptionID = @parentSubscriptionID;

				IF @recordRevenueTransaction = 1 AND @status in ('A','P') BEGIN
					set @status = 'O';
					set @APWithTransactions = 1;
				END ELSE BEGIN
					set @APWithTransactions = 0;
				END

				EXEC dbo.sub_addSubscriber @orgID=@orgID, @memberid=@memberid, @subscriptionID=@subscriptionID, 
					@parentSubscriberID=@parentSubscriberID, @RFID=@RFID, @GLAccountID=@GLAccountID, @status=@status, 
					@subStartDate=@subStartDate, @subEndDate=@subEndDate, @graceEndDate=@graceEndDate, 
					@recogStartDate=@recogStartDate, @recogEndDate=@recogEndDate, @pcfree=0, 
					@activationOptionCode=@activationOptionCode, @recordedByMemberID=@recordedByMemberID, 
					@bypassQueue=1, @subscriberID=@subscriberID OUTPUT;

				UPDATE #tmpAddSubscribers set subscriberID = @subscriberID where rowID = @treeRowID;
				UPDATE platformQueue.dbo.queue_subscriptionAddDetail set subscriberID = @subscriberID where itemUID = @itemUID and rowID = @treeRowID;

				update dbo.sub_subscribers set lastPrice = @lastPrice where subscriberID = @subscriberID;

				IF @storeModifiedRate in ('Y','YES')			
					update dbo.sub_subscribers set modifiedRate = @lastPrice where subscriberID = @subscriberID;

				-- mark as activation met if sub being imported as expired, deleted, or active/accepted without transactions
				IF @status in ('X','D','E','A','P')
					EXEC dbo.sub_overrideActivationMemberSubscription @subscriberID=@subscriberID, @siteID=@siteID, @actorMemberID=@recordedByMemberID, @byPassQueue=1, @statusUpdated=@statusUpdated OUTPUT;

				IF @skipEmailTemplateNotifications = 1 AND (@status in ('A','P') OR @APWithTransactions = 1)
					EXEC dbo.sub_suppressSubscriberAutomatedEmail @subscriberID=@subscriberID, @enteredByMemberID=@recordedByMemberID, @limitToEmailTemplateID=NULL;

				EXEC dbo.sub_createStatusBackfill @siteID=@siteID, @subscriberID=@subscriberID, @isRenewal=0, @enteredByMemberID=@recordedByMemberID, @bypassQueue=1;

				select @treeRowID = min(rowID) from #tmpAddSubscribers where rowID > @treeRowID;
			END

			-- when recording as Active or Accepted with revenue transactions
			IF EXISTS (select 1 from #tmpAddSubscribers where recordRevenueTransaction = 1 and status in ('A','P')) BEGIN
				DECLARE @importResult xml;

				select @subscriberIDList = COALESCE(@subscriberIDList + ',', '') + cast(subscriberID as varchar(10)) 
				from #tmpAddSubscribers 
				where recordRevenueTransaction = 1
				and status in ('A','P');

				IF @skipEmailTemplateNotifications = 1
					SET @suppressEmails = 1;
				ELSE
					SET @suppressEmails = 0;

				EXEC dbo.sub_queueMarkAccepted @recordedByMemberID=@recordedByMemberID, @subscriberIDList=@subscriberIDList, 
					@suppressEmails=@suppressEmails, @markQueueAsReady=1, @importResult=@importResult OUTPUT;
			END
		COMMIT TRAN;

		SET @treeOK = 1;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;
		UPDATE platformQueue.dbo.queue_subscriptionAddDetail set errorMessage = ERROR_MESSAGE() where itemUID = @itemUID and rowID = @treeRowID;
		SET @treeOK = 0;
	END CATCH

	update platformQueue.dbo.queue_subscriptionAdd
	set statusID = @statusNotify, dateUpdated = GETDATE()
	where itemUID = @itemUID;

	IF OBJECT_ID('tempdb..#tmpAddSubscribers') IS NOT NULL 
		DROP TABLE #tmpAddSubscribers;

	on_done:
	IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
