ALTER PROC dbo.cp_createContributionInstallmentsFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID ('tempdb..#tmpInstallmentsFromQueue') is not null
		DROP TABLE #tmpInstallmentsFromQueue;
	IF OBJECT_ID('tempdb..#tmpContribMoneyFieldsFromQueue') is not null
		DROP TABLE #tmpContribMoneyFieldsFromQueue;
	CREATE TABLE #tmpInstallmentsFromQueue (rowID int PRIMARY KEY, scheduleID int, dueDate date, GLAccountID int, installAmount decimal(18,2), itemType varchar(50), itemID int, detail varchar(500));
	CREATE TABLE #tmpContribMoneyFieldsFromQueue (contributionID int, dataID int, GLAccountID int, detail varchar(500), amount decimal(18,2), withInstallment bit);

	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @runByMemberID int, @siteID int, @contributionID int, @memberID int, 
		@startDate date, @endDate date, @orgID int, @firstScheduleID int, @firstDueDate date, @contribdetail varchar(500), @rowID int,
		@dueDate date, @distAmount decimal(18,2), @GLAccountID int, @scheduleID int, @itemType varchar(50), @trItemID int, @detail varchar(500),
		@programID int, @transactionID int, @doneStatusID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='contributionsInstall', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;

	set @runByMemberID = dbo.fn_ams_getMCSystemMemberID();

	-- get contribution info from queue item
	select @siteID=siteID, @contributionID=contributionID, @memberID=memberID, @startDate=startDate, @endDate=endDate
	from platformQueue.dbo.queue_contributionsInstallments
	where itemID = @itemID
	and statusID = @readyToProcessStatusID;
	
	select top 1 @firstScheduleID=scheduleID, @firstDueDate=dueDate
	from dbo.cp_contributionSchedule
	where contributionID = @contributionID
	order by dueDate, scheduleID;

	IF @contributionID IS NULL OR @firstScheduleID IS NULL
		goto on_end;

	update platformQueue.dbo.queue_contributionsInstallments
	set statusID = @processingStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	select @programID = cp.programID, @contribdetail = cp.programName + CASE WHEN cpc.campaignID IS NOT NULL THEN ' - ' + cpc.campaignName + ' - ' ELSE ' - ' END + cf.frequency
	from dbo.cp_contributions as c
	inner join dbo.cp_programs as cp on cp.programID = c.programID
	inner join dbo.cp_frequencies as cf on cf.frequencyID = c.frequencyID
	left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
	where c.contributionID = @contributionID;

	-- get possible money custom fields
	INSERT INTO #tmpContribMoneyFieldsFromQueue (contributionID, dataID, GLAccountID, detail, amount, withInstallment)
	select c.contributionID, fd.dataID, isnull(f.GLAccountID,cp.defaultGLAccountID), f.fieldReference as detail,
		isnull(fv.valueDecimal2,0), case when fu.areaName = 'EachInstallment' then 1 else 0 end as withInstallment
	from dbo.cp_contributions as c 
	inner join dbo.cp_programs as cp on cp.programID = c.programID
		and c.contributionID = @contributionID 
	inner join dbo.cf_fields as f on f.controllingSiteResourceID = cp.siteResourceID
		and f.detailID = cp.programID
	inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
		and ft.displayTypeCode = 'TEXTBOX'
		and ft.supportAmt = 1
		and ft.supportQty = 0
	inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
	inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
	inner join dbo.cf_fieldData as fd on fd.fieldID = f.fieldID
		and fd.valueID = fv.valueID
	where fd.itemID = c.contributionID
	and fd.itemType = 'ContributionProgram'
	and isnull(fv.valueDecimal2,0) > 0
	and fu.areaName in ('FirstInstallment','EachInstallment')
		union all 
	select c.contributionID, fd.dataID, isnull(f.GLAccountID,cp.defaultGLAccountID), f.fieldReference as detail,
		isnull(fv.valueInteger,0) * fd.amount, case when fu.areaName = 'EachInstallment' then 1 else 0 end as  withInstallment
	from dbo.cp_contributions as c 
	inner join dbo.cp_programs as cp on cp.programID = c.programID
		and c.contributionID = @contributionID 
	inner join dbo.cf_fields as f on f.controllingSiteResourceID = cp.siteResourceID
		and f.detailID = cp.programID
	inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
		and ft.displayTypeCode = 'TEXTBOX'
		and ft.supportAmt = 1
		and ft.supportQty = 1
	inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
	inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
	inner join dbo.cf_fieldData as fd on fd.fieldID = f.fieldID
		and fd.valueID = fv.valueID
	where fd.itemID = c.contributionID
	and fd.itemType = 'ContributionProgram'
	and isnull(fv.valueInteger,0) > 0
	and fd.amount > 0
	and fu.areaName in ('FirstInstallment','EachInstallment')
		union all 
	select c.contributionID, fd.dataID, isnull(f.GLAccountID,cp.defaultGLAccountID), 
		case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(500))
			when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
			when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(15))
			when ft.dataTypeCode = 'BIT' then case when fv.valueBit = 1 then 'Yes' else 'No' end
			when ft.dataTypeCode = 'DATE' then convert(varchar(12), fv.valueDate, 101) 
			else 'QTY' end as detail,
		fd.amount, case when fu.areaName = 'EachInstallment' then 1 else 0 end as withInstallment
	from dbo.cp_contributions as c 
	inner join dbo.cp_programs as cp on cp.programID = c.programID
		and c.contributionID = @contributionID 
	inner join dbo.cf_fields as f on f.controllingSiteResourceID = cp.siteResourceID
		and f.detailID = cp.programID
	inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
		and ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
		and ft.supportAmt = 1
	inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
	inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
	inner join dbo.cf_fieldData as fd on fd.fieldID = f.fieldID
		and fd.valueID = fv.valueID
	where fd.itemID = c.contributionID
	and fd.itemType = 'ContributionProgram'
	and fd.amount > 0
	and fu.areaName in ('FirstInstallment','EachInstallment');

	-- installments to create
	INSERT INTO #tmpInstallmentsFromQueue (rowID, scheduleID, dueDate, GLAccountID, installAmount, itemType, itemID, detail)
	select ROW_NUMBER() OVER(ORDER BY dueDate, scheduleID, orderPref, installAmount desc) as rowID, 
		scheduleID, dueDate, GLAccountID, installAmount, itemType, itemID, detail
	FROM (
		-- get schedule entries in range that have not been converted to installments yet
		select 1 as orderPref, cs.scheduleID, cs.dueDate, d.GLAccountID, cd.distAmount as installAmount, 
			'contribInstallment' as itemType, cd.contribDistributionID as itemID, 
			cp.programName + 
				CASE WHEN cpc.campaignID IS NOT NULL THEN ' - ' + cpc.campaignName ELSE + '' END + 
				CASE WHEN cpd.settingCode <> 'NODISPLAY' THEN ' - ' + d.distDesc ELSE + ''  END + 
				' - ' + cf.frequency as detail
		from dbo.cp_contributionSchedule as cs
		inner join dbo.cp_contributionDistributions as cd on cd.contributionID = cs.contributionID
		inner join dbo.cp_distributions as d on d.distribID = cd.distribID
		inner join dbo.cp_contributions as c on c.contributionID = cs.contributionID
		inner join dbo.cp_programs as cp on cp.programID = c.programID
		inner join dbo.cp_distribSettings as cpd on cpd.settingID  = cp.distribSettingID
		inner join dbo.cp_frequencies as cf on cf.frequencyID = c.frequencyID
		left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
		where cs.contributionID = @contributionID
		and cs.dueDate between @startDate and @endDate
		and not exists (select 1 from dbo.tr_transactionInstallments where orgID = @orgID and scheduleID = cs.scheduleID)
			union all
		-- any custom fields setup as one time that have not been converted to installments yet
		select 2 as orderPref, @firstScheduleID as scheduleID, @firstDueDate as dueDate, tmp.GLAccountID, tmp.amount as installAmount,
			'contribFieldInstallment' as itemType, tmp.dataID as itemID, tmp.detail
		from #tmpContribMoneyFieldsFromQueue as tmp
		where tmp.withInstallment = 0
		and tmp.amount > 0
		and not exists (select 1 from dbo.tr_applications where orgID = @orgID and itemID = tmp.dataID and itemType = 'contribFieldInstallment')
			union all
		-- any custom fields setup as with installment that have not been converted to installments yet
		select 3 as orderPref, cs.scheduleID, cs.dueDate, tmp.GLAccountID, tmp.amount as installAmount,
			'contribFieldInstallment' as itemType, tmp.dataID as itemID, tmp.detail
		from #tmpContribMoneyFieldsFromQueue as tmp
		inner join dbo.cp_contributionSchedule as cs on cs.contributionID = tmp.contributionID
		where tmp.withInstallment = 1
		and cs.dueDate between @startDate and @endDate
		and tmp.amount > 0
		and not exists (
			select 1 
			from dbo.tr_transactionInstallments as ti
			inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.transactionID = ti.transactionID
			where ti.orgID = @orgID 
			and ti.scheduleID = cs.scheduleID
			and tra.itemID = tmp.dataID 
			and tra.itemType = 'contribFieldInstallment')
	) as allInstallments;

	BEGIN TRAN;
		select @rowID = min(rowID) from #tmpInstallmentsFromQueue;
		WHILE @rowID is not null BEGIN
			select @dueDate = null, @distAmount = null, @GLAccountID = null, @scheduleID = null, 
				@itemType = null, @trItemID = null, @detail = null, @transactionID = null;

			select @dueDate=dueDate, @distAmount=installAmount, @GLAccountID=GLAccountID, @scheduleID=scheduleID,
				@itemType=itemType, @trItemID=itemID, @detail=detail
			from #tmpInstallmentsFromQueue
			where rowID = @rowID;

			EXEC dbo.tr_createTransaction_installment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
				@assignedToMemberID=@memberID, @recordedByMemberID=@runByMemberID, @statsSessionID=null, 
				@detail=@detail, @amount=@distAmount, @transactionDate=@dueDate, @revenueGLAccountID=@GLAccountID, 
				@scheduleID=@scheduleID, @offsetInstallmentTID=null, @transactionID=@transactionID OUTPUT;

			EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Contributions', 
				@transactionID=@transactionID, @itemType=@itemType, @itemID=@trItemID, @subItemID=null;

			select @rowID = min(rowID) from #tmpInstallmentsFromQueue where rowID > @rowID;
		END
	COMMIT TRAN;
	
	update platformQueue.dbo.queue_contributionsInstallments
	set statusID = @doneStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	delete from platformQueue.dbo.queue_contributionsInstallments
	where itemID = @itemID;

	-- process conditions
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
	select distinct @orgID, @memberID, c.conditionID
	from dbo.ams_virtualGroupConditions as c
	cross apply (
		select cv.conditionValue
		from dbo.ams_virtualGroupConditionValues as cv
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'programList'
		where cv.conditionID = c.conditionID
	) as programList(val)
	where c.orgID = @orgID
	and c.fieldCode in ('cp_entry','cp_valuesum')
	and programList.val = cast(@programID as varchar(10));

	EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;

	on_end:
	IF OBJECT_ID ('tempdb..#tmpInstallmentsFromQueue') is not null
		DROP TABLE #tmpInstallmentsFromQueue;
	IF OBJECT_ID('tempdb..#tmpContribMoneyFieldsFromQueue') is not null
		DROP TABLE #tmpContribMoneyFieldsFromQueue;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
