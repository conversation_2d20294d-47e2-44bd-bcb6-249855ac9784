ALTER PROC dbo.queue_importSWODPrograms_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @statusGrabbed int, @queueTypeID int;
	EXEC dbo.queue_getQueueTypeID @queueType='importSWODPrograms', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyPrograms') IS NOT NULL
		DROP TABLE #tmpNotifyPrograms;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotifyPrograms (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, siteID int,
		recordedByMemberID int, ProgramCode varchar(15), SeminarTitle varchar(200));

	-- dequeue
	; WITH itemGroupUIDs AS (
		SELECT DISTINCT qid.itemGroupUID
		FROM dbo.tblQueueItems as qi
		INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		WHERE qi.queueStatusID = @statusReady
			EXCEPT
		SELECT DISTINCT qid.itemGroupUID
		FROM dbo.tblQueueItems as qi
		INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		WHERE qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	WHERE qi.queueStatusID = @statusReady;

	-- if none found, no need to continue. return empty query.
	IF @@ROWCOUNT = 0 BEGIN
		SELECT * FROM #tmpNotifyPrograms;
		GOTO on_done;
	END

	-- programs in groupUID
	INSERT INTO #tmpNotifyPrograms (itemGroupUID, itemUID, siteID, recordedByMemberID, ProgramCode, SeminarTitle)
	SELECT itemGroupUID, itemUID, siteID, recordedByMemberID, ProgramCode, SeminarTitle
	FROM (
		SELECT qid.itemGroupUID, qid.itemUID, qid.siteID, qid.recordedByMemberID, dc.columnname, qid.columnValueString
		FROM (SELECT DISTINCT itemGroupUID FROM #tmpNotify) as tmpN
		INNER JOIN dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
		INNER JOIN dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			AND dc.columnname in ('SeminarTitle','ProgramCode')
	) AS tmp
	PIVOT (MIN(columnValueString) for columnname in (ProgramCode,SeminarTitle)) as pvt;

	SELECT DISTINCT tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode,
		mActive.firstName, mActive.lastName, mActive.memberNumber, mActive.memberID,
		tmpN.ProgramCode, tmpN.SeminarTitle, seminar.seminarID as MCSeminarID
	FROM #tmpNotifyPrograms as tmpN
	INNER JOIN seminarweb.dbo.tblSeminars as seminar ON seminar.programCode = tmpN.ProgramCode
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID
		AND metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		AND me.memberID = metag.memberID
		AND me.emailTypeID = metag.emailTypeID
	ORDER BY tmpN.itemGroupUID, tmpN.SeminarTitle, tmpN.ProgramCode;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyPrograms') IS NOT NULL
		DROP TABLE #tmpNotifyPrograms;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
