<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.processQueueResult = processQueue(strTask=arguments.strTask, batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.totalItemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "totalItemCount":0 }>

		<cftry>
			<cfstoredproc procedure="queue_exportPagesJSON_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryPageJSONEntries" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.totalItemCount = val(local.qryPageJSONEntries.totalItemCount)>

			<cfset updateQueueStatus(itemIDList=valueList(local.qryPageJSONEntries.itemID), queueStatus="Processing")>
			<cfset processPageJSON(itemIDList=valueList(local.qryPageJSONEntries.itemID))>

			<cfset sendEmailReportsForProcessedEntries(strTask=arguments.strTask)>

			<cfset deleteCompletedQueueEntries()>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processPageJSON" access="private" output="false" returntype="void">
		<cfargument name="itemIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryPage" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpSitesBaseURL') IS NOT NULL
				DROP TABLE ##tmpSitesBaseURL;

			DECLARE @environmentID INT, @environmentName varchar(50);

			CREATE TABLE ##tmpSitesBaseURL (siteID int, baseURL varchar(500) )

			SELECT @environmentName = tier FROM dbo.fn_getServerSettings();
			SELECT @environmentID = environmentID FROM dbo.platform_environments WHERE environmentName = @environmentName;

			INSERT INTO ##tmpSitesBaseURL (siteID, baseURL)
			SELECT DISTINCT sh.siteID, CONCAT(CASE WHEN sh.hasssl = 1 THEN 'https' ELSE 'http' END, '://', sh.hostname, '/')
			FROM dbo.siteHostnames AS sh 
			INNER JOIN dbo.siteEnvironments AS se ON se.siteID = sh.siteID
			AND se.environmentID = @environmentID
			AND se.mainHostnameID = sh.hostNameID
			AND sh.siteID IN (SELECT DISTINCT siteID FROM platformQueue.dbo.queue_exportPagesJSON qep WHERE qep.itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">));

			SELECT p.pageID, p.pageName, ps.thePathExpanded, srt.resourceType AS siteResourceType, srs.siteResourceStatusDesc AS status, tmpSB.baseURL+srd.redirectName AS quickLink,
				ps.inheritPlacements, pm.modeName AS modeOverride, pt.templateName AS templateOverride,	ptm.templateName AS templateOverrideMobile,
				CASE WHEN CHARINDEX('noindex',p.pageDirectives) > 0 THEN 1 ELSE 0 END AS applyNoIndex,
				CASE WHEN CHARINDEX('nofollow',p.pageDirectives) > 0 THEN 1 ELSE 0 END AS applyNoFollow,
				CASE WHEN CHARINDEX('noarchive',p.pageDirectives) > 0 THEN 1 ELSE 0 END AS applyNoArchive,
				qep.itemID,	qep.itemGroupUID
			FROM platformQueue.dbo.queue_exportPagesJSON qep
			INNER JOIN dbo.cms_pages AS p ON p.pageID = qep.pageID 
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = qep.siteID AND sr.siteResourceID = p.siteResourceID
			INNER JOIN dbo.cms_siteResourceTypes srt ON srt.resourceTypeID = sr.resourceTypeID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
			INNER JOIN dbo.cms_pageSections AS ps ON ps.sectionID = p.sectionID
			LEFT OUTER JOIN dbo.cms_pageModes AS pm ON pm.modeID = p.ovModeID
			INNER JOIN ##tmpSitesBaseURL AS tmpSB ON tmpSB.siteID = qep.siteID
			LEFT JOIN dbo.cms_pageTemplates AS pt ON pt.templateID = p.ovTemplateID
			LEFT JOIN dbo.cms_pageTemplates AS ptm ON ptm.templateID = p.ovTemplateIDMobile
			LEFT JOIN dbo.siteRedirects AS srd ON srd.siteID = qep.siteID AND srd.redirectID = p.redirectID
			WHERE qep.itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">)
			ORDER BY qep.itemID, p.pageID;

			IF OBJECT_ID('tempdb..##tmpSitesBaseURL') IS NOT NULL
				DROP TABLE ##tmpSitesBaseURL;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryPageLanguages" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT p.pageID, l.languageCode, pl.pageTitle, pl.keywords, pl.pageDesc AS description, qep.itemID
			FROM platformQueue.dbo.queue_exportPagesJSON qep
			INNER JOIN dbo.cms_pages AS p ON p.pageID = qep.pageID AND p.siteID = qep.siteID
			INNER JOIN dbo.cms_pageLanguages AS pl ON pl.pageID = p.pageID
			INNER JOIN dbo.cms_languages AS l ON l.languageID = pl.languageID
			WHERE qep.itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">);
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryPageZones" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT p.pageID, pz.zoneID, pz.zoneName, c.contentID, c.isHTML, c.uid, qep.itemID
			FROM platformQueue.dbo.queue_exportPagesJSON qep
			INNER JOIN dbo.cms_pages AS p ON p.pageID = qep.pageID AND p.siteID = qep.siteID
			INNER JOIN dbo.cms_pageZonesResources AS pzr ON pzr.pageID = p.pageID
			INNER JOIN dbo.cms_pageZones AS pz ON pz.zoneID = pzr.zoneID
			INNER JOIN dbo.cms_content AS c ON c.siteResourceID = pzr.siteResourceID AND c.siteID = qep.siteID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = c.siteResourceID and sr.siteResourceStatusID = 1
			WHERE qep.itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">)
			ORDER BY qep.itemID, qep.pageID, pz.zoneID, pzr.sortOrder;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryPageZoneLanguages" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT qep.pageID, pz.zoneID, pz.zoneName, c.contentID, l.languageID, l.languageCode,  cl.contentTitle, cv.rawContent, cl.contentDesc AS description, qep.itemID
			FROM platformQueue.dbo.queue_exportPagesJSON qep
			INNER JOIN dbo.cms_pages AS p ON p.pageID = qep.pageID AND p.siteID = qep.siteID
			INNER JOIN dbo.cms_pageZonesResources AS pzr ON pzr.pageID = p.pageID
			INNER JOIN dbo.cms_pageZones AS pz ON pz.zoneID = pzr.zoneID
			INNER JOIN dbo.cms_content AS c ON c.siteResourceID = pzr.siteResourceID AND c.siteID = qep.siteID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = c.siteResourceID and sr.siteResourceStatusID = 1
			INNER JOIN dbo.cms_contentVersions cv ON cv.contentID = c.contentID AND cv.isActive =1
			INNER JOIN dbo.cms_contentLanguages cl ON cl.contentLanguageID = cv.contentLanguageID
			INNER JOIN dbo.cms_languages l ON cl.languageID = l.languageID
			WHERE qep.itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">)
			ORDER BY qep.itemID, qep.pageID, pz.zoneID, l.languageID;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryPageZonePermissions" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT qep.pageID, pz.zoneID,c.contentID, pz.zoneName, srr.[include] AS allowed, srr.functionID, srtf.displayName AS functionName, srr.groupID, g.uid AS groupUID, qep.itemID
			FROM platformQueue.dbo.queue_exportPagesJSON AS qep 
			INNER JOIN dbo.cms_pageZonesResources AS pzr ON pzr.pageID = qep.pageID
			INNER JOIN dbo.cms_siteResourceRights AS srr ON srr.resourceID = pzr.siteResourceID
			INNER JOIN dbo.cms_pageZones AS pz ON pz.zoneID = pzr.zoneID
			INNER JOIN dbo.cms_siteResourceFunctions f ON srr.functionID = f.functionID
			INNER JOIN dbo.cms_siteResourceTypeFunctions srtf ON srtf.functionID = f.functionID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = pzr.siteResourceID  AND sr.resourceTypeID = srtf.resourceTypeID 
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
			INNER JOIN dbo.cms_siteResourceTypes srt ON srt.resourceTypeID = sr.resourceTypeID
			INNER JOIN dbo.cms_content AS c ON c.siteResourceID = pzr.siteResourceID AND c.siteID = qep.siteID
			LEFT OUTER JOIN dbo.ams_groups AS g ON g.groupID = srr.groupID
			WHERE qep.itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">)
			ORDER BY qep.itemID, qep.pageID, pz.zoneID, srr.functionID;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cftry>
			<cfloop query="local.qryPage">
				<cfquery dbtype="query" name="local.pageLanguages">
					SELECT *
					FROM [local].qryPageLanguages
					WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPage.itemID#">;
				</cfquery>

				<cfset local.languages = structNew("Ordered")>
				<cfloop query="local.pageLanguages">
					<cfset local.languages['#local.pageLanguages.languageCode#'] = structNew("Ordered")>
					<cfset local.languages['#local.pageLanguages.languageCode#'].languageCode = local.pageLanguages.languageCode>
					<cfset local.languages['#local.pageLanguages.languageCode#'].pageTitle = local.pageLanguages.pageTitle>
					<cfset local.languages['#local.pageLanguages.languageCode#'].keywords = local.pageLanguages.keywords>
					<cfset local.languages['#local.pageLanguages.languageCode#'].description = local.pageLanguages.description>
				</cfloop>

				<cfquery dbtype="query" name="local.pageZones">
					SELECT *
					FROM [local].qryPageZones
					WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPage.itemID#">;
				</cfquery>

				<cfset local.zones = structNew("Ordered")>
				<cfoutput query="local.pageZones" group="zoneID">
					<cfset local.zones['#local.pageZones.zoneName#'] = structNew("Ordered")>
					<cfset local.zones['#local.pageZones.zoneName#'].zoneName = local.pageZones.zoneName>
					<cfset local.zones['#local.pageZones.zoneName#'].content = arrayNew(1)>
					
					<cfoutput>
						<cfquery dbtype="query" name="local.pageZoneLanguages">
							SELECT *
							FROM [local].qryPageZoneLanguages
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPage.itemID#">
							AND zoneID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.pageZones.zoneID#">
							AND contentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.pageZones.contentID#">;
						</cfquery>
						
						<cfquery dbtype="query" name="local.tmpPageZonePermissions">
							SELECT *
							FROM [local].qryPageZonePermissions
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPage.itemID#">
							AND zoneID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.pageZones.zoneID#">
							AND contentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.pageZones.contentID#">;
						</cfquery>

						<cfquery dbtype="query" name="local.pageZonePermissions">
							SELECT functionID, functionName, allowed
							FROM [local].tmpPageZonePermissions
							GROUP BY functionID, functionName, allowed
							ORDER BY functionID
						</cfquery>

						<cfset local.content = structNew("Ordered")>
						<cfset local.content.isHTML = local.pageZones.isHTML>
						<cfset local.content.uid = local.pageZones.uid>
						<cfset local.content.permissions = arrayNew()>
						<cfloop query="local.pageZonePermissions">
							<cfset local.permission = structNew("Ordered")>
							<cfset local.permission.functionName = local.pageZonePermissions.functionName>			
							<cfset local.permission.allowed = (local.pageZonePermissions.allowed) ? 1:0>
							<cfquery dbtype="query" name="local.pageZonePermissionsGroups">
								SELECT groupUID
								FROM [local].tmpPageZonePermissions
								WHERE functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.pageZonePermissions.functionID#">;
							</cfquery>
							
							<cfset local.permission.groupUIDs = arrayNew(1)>
							<cfloop query="local.pageZonePermissionsGroups">
								<cfset ArrayAppend(local.permission.groupUIDs, local.pageZonePermissionsGroups.groupUID )>
							</cfloop>
							<cfset ArrayAppend(local.content.permissions, local.permission)>
						</cfloop>
						<cfset local.content.languages = structNew("Ordered")>
						<cfloop query="local.pageZoneLanguages">
							<cfset local.content.languages['#local.pageZoneLanguages.languageCode#'] = structNew("Ordered")>
							<cfset local.content.languages['#local.pageZoneLanguages.languageCode#'].languageCode = local.pageZoneLanguages.languageCode>
							<cfset local.content.languages['#local.pageZoneLanguages.languageCode#'].contentTitle = local.pageZoneLanguages.contentTitle>			
							<cfset local.content.languages['#local.pageZoneLanguages.languageCode#'].description = local.pageZoneLanguages.description>
							<cfset local.content.languages['#local.pageZoneLanguages.languageCode#'].rawContent = local.pageZoneLanguages.rawContent>
						</cfloop>
						<cfset ArrayAppend(local.zones['#local.pageZones.zoneName#'].content, local.content)>
					</cfoutput>
				</cfoutput>

				<cfset local.pageStruct = structNew("Ordered")>
				<cfset local.pageStruct.pageName = local.qryPage.pageName>
				<cfset local.pageStruct.sectionPathExpanded = local.qryPage.thePathExpanded>
				<cfset local.pageStruct.siteResourceType = local.qryPage.siteResourceType>
				<cfset local.pageStruct.status = local.qryPage.status>
				<cfset local.pageStruct.quickLink = local.qryPage.quickLink>
				<cfset local.pageStruct.modeOverride = local.qryPage.modeOverride>
				<cfset local.pageStruct.inheritPlacements = local.qryPage.inheritPlacements>
				<cfset local.pageStruct.templateOverride = local.qryPage.templateOverride>
				<cfset local.pageStruct.templateOverrideMobile = local.qryPage.templateOverrideMobile>
				<cfset local.pageStruct.applyNoIndex = local.qryPage.applyNoIndex>
				<cfset local.pageStruct.applyNoFollow = local.qryPage.applyNoFollow>
				<cfset local.pageStruct.applyNoArchive = local.qryPage.applyNoArchive>
				<cfset local.pageStruct.languages = local.languages>
				<cfset local.pageStruct.pageZones = local.zones>

				<cfset local.pageJSON = serializejson(local.pageStruct)>
				<cfset local.folderPath = "#application.paths.SharedTemp.path#exportPageJSON_#local.qryPage.itemGroupUID#">
				<cfset local.fileName = replace(replace(rereplaceNoCase(local.qryPage.pageName,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL') & '_#CreateUUID()#'>
				<cfif NOT directoryExists(local.folderPath)>
					<cfdirectory action="create" directory="#local.folderPath#">
				</cfif>
				<cffile action="write" output="#local.pageJSON#" file="#local.folderPath#/#local.fileName#.json">

				<cfquery name="local.updatePageJSON" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						UPDATE dbo.queue_exportPagesJSON
						SET pageJSON = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.pageJSON#">
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPage.itemID#">;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
					END CATCH
				</cfquery>

				<cfset updateQueueStatus(itemIDList=local.qryPage.itemID, queueStatus="ReadyToNotify")>
			</cfloop>
		
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="sendEmailReportsForProcessedEntries" access="private" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">

		<cfset var local = structNew()>

		<cfstoredproc procedure="queue_exportPagesJSON_grabForNotifying" datasource="#application.dsn.platformQueue.dsn#">
			<cfprocresult name="local.qryNotifyEntries" resultset="1">
		</cfstoredproc>

		<cfset local.itemIDList = valueList(local.qryNotifyEntries.itemID)>

		<cfoutput query="local.qryNotifyEntries" group="itemGroupUID">
			<cftry>
				<!--- compile JSONs into one zip --->
				<cfset local.folderPath = "#application.paths.SharedTemp.path#exportPageJSON_#local.qryNotifyEntries.itemGroupUID#">
				<cfif directoryExists("#local.folderPath#")>
					<cfzip action="zip" source="#local.folderPath#" file="#local.folderPath#/exportPageJSON.zip" filter="*.json" recurse="false" storepath="false"></cfzip>
				<cfelse>
					<cfthrow message="Folder of JSONs was not found">
				</cfif>

				<cfif len(local.qryNotifyEntries.memberEmail)>
					<cfset local.mailAttach = [{ file:"exportPageJSON.zip", folderpath:"#local.folderPath#" }]>
					<cfset local.sitecode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.qryNotifyEntries.siteID)>
					<cfsavecontent variable="local.notificationMessage">
						<div>
							<p>#local.qryNotifyEntries.memberName#:</p>
							<p>Find the attached exported JSON Pages for #application.objSiteInfo.mc_siteInfo[local.sitecode].siteName# (#local.sitecode#).</p>
						</div>
					</cfsavecontent>
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo[local.sitecode].networkEmailFrom},
						emailto=[{ name=local.qryNotifyEntries.memberName, email=local.qryNotifyEntries.memberEmail}],
						emailreplyto=application.objSiteInfo.mc_siteInfo[local.sitecode].supportProviderEmail,
						emailsubject="#application.objSiteInfo.mc_siteInfo[local.sitecode].siteName# JSON Pages Export",
						emailtitle="JSON Pages Export",
						emailhtmlcontent=local.notificationMessage,
						emailAttachments=local.mailAttach,
						siteID=application.objSiteInfo.mc_siteInfo[local.sitecode].siteID,
						memberID=val(local.qryNotifyEntries.submittedMemberID),
						messageTypeID=arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="SCHEDTASK"),
						sendingSiteResourceID=application.objSiteInfo.mc_siteInfo[local.sitecode].siteSiteResourceID
						)>
				</cfif>		
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStruct.success = false>
			</cfcatch>
			</cftry>
		</cfoutput>

		<cfset updateQueueStatus(itemIDList=local.itemIDList, queueStatus="Done")>
		
	</cffunction>

	<cffunction name="updateQueueStatus" access="private" output="false" returntype="void">
		<cfargument name="itemIDList" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @queueStatusID INT;
				EXEC dbo.queue_getStatusIDbyType @queueType='exportPagesJSON', @queueStatus=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueStatus#">, @queueStatusID=@queueStatusID OUTPUT;

				UPDATE dbo.queue_exportPagesJSON
				SET statusID = @queueStatusID,
					dateUpdated = getdate()
				WHERE itemID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.itemIDList#" list="true">);

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="deleteCompletedQueueEntries" access="private" output="false" returntype="void">
		<cfset var local = structNew()>

		<cfquery name="local.updateQueueStatuses" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @statusDone INT;
				EXEC dbo.queue_getStatusIDbyType @queueType='exportPagesJSON', @queueStatus='Done', @queueStatusID=@statusDone OUTPUT;

				WITH completedJobs AS (
					SELECT qi.itemID 
					FROM dbo.queue_exportPagesJSON AS qi
					WHERE qi.statusID = @statusDone
					AND NOT EXISTS (
						SELECT 1
						FROM dbo.queue_exportPagesJSON AS tmp
						WHERE tmp.itemGroupUID = qi.itemGroupUID
						AND tmp.statusID <> @statusDone
					)
				)
				DELETE qid
				FROM dbo.queue_exportPagesJSON AS qid
				INNER JOIN completedJobs AS batch ON batch.itemID = qid.itemID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		</cfquery>
	</cffunction>

</cfcomponent>