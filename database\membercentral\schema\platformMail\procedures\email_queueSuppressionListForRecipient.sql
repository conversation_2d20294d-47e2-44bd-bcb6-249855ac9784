ALTER PROC dbo.email_queueSuppressionListForRecipient
@recipientID int,
@subUserName varchar(100),
@runAfter datetime

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @itemID int, @siteID int, @subuserID int, @emailAddress varchar(200), @emailStatusCode varchar(10),
		@queueTypeID int, @readyToProcessStatusID int, @entryID int;
			
	declare @tier varchar(20);
	select @tier=tier from membercentral.dbo.fn_getServerSettings();
	IF @tier <> 'production'
		GOTO on_done;

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='SendgridSuppressions', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	
	SELECT @siteID = mrh.siteID, @emailAddress = mrh.toEmail, @emailStatusCode = s.statusCode
	FROM dbo.email_messageRecipientHistory AS mrh
	INNER JOIN dbo.email_statuses AS s ON s.statusID = mrh.emailStatusID
	WHERE mrh.recipientID = @recipientID;

	SELECT @subuserID = u.subuserID
	FROM dbo.sendgrid_subusers AS u
	INNER JOIN dbo.sendgrid_subuserStatuses AS s ON s.subuserStatusID = u.statusID
	WHERE u.siteID = @siteID
	AND u.username = @subUserName
	AND s.status = 'Active';

	IF @subuserID IS NULL
		GOTO on_done;

	IF @emailStatusCode = 'sg_drop' BEGIN
		SELECT TOP 1 @entryID = s.entryID
		FROM dbo.sendgrid_suppressions AS s
		INNER JOIN dbo.sendgrid_suppressionListTypes AS slt ON slt.typeID = s.typeID
		WHERE s.subuserID = @subuserID
		AND slt.typeCode IN ('bounces', 'invalid_emails', 'spam_reports')
		AND s.emailAddress = @emailAddress;

		IF @entryID IS NOT NULL
			GOTO on_done;
	END

	SELECT @itemID = itemID
	FROM platformQueue.dbo.queue_sendgridSuppressions
	WHERE siteID = @siteID
	AND subuserID = @subuserID
	AND emailAddress = @emailAddress
	AND referencedRecipientID = @recipientID
	AND statusID = @readyToProcessStatusID;

	IF @itemID IS NULL 
		INSERT INTO platformQueue.dbo.queue_sendgridSuppressions (itemGroupUID, siteID, subuserID, emailAddress, referencedRecipientID, statusID, 
			dateAdded, dateUpdated, runAfter)
		VALUES (NEWID(), @siteID, @subuserID, @emailAddress, @recipientID, @readyToProcessStatusID, GETDATE(), GETDATE(), @runAfter);

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
