ALTER PROC dbo.queue_monthBillEmailOrg_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int = 60, @queueTypeID int, @statusReady int, @statusGrabbed int,
		@applicationTypeID int = membercentral.dbo.fn_getApplicationTypeIDFromName('BuyNow'),
		@siteID int = membercentral.dbo.fn_getSiteIDFromSiteCode('MC'), @settingsXML xml;
	EXEC dbo.queue_getQueueTypeID @queueType='monthBillEmailOrg', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	SELECT @settingsXML = settingsXML
	FROM membercentral.dbo.cms_applicationTypeSettings
	WHERE siteID = @siteID
	AND applicationTypeID = @applicationTypeID;

	SELECT @settingsXML = ISNULL(@settingsXML,'<settings />');

	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	CREATE TABLE #tmpOrgs (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpOrgs
	FROM dbo.queue_monthBillEmailOrg as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillEmailOrg as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qieo.itemID, bp.EOMPeriod, qieo.siteCode, oi.organizationName as orgName, mcb.depoMemberDataID, mcb.billingEmail, 
		qisw.folderPath as swFolderPath, qisw.[filename] as swFileName, qisw.isPayable as swIsPayable, 
		qisw.payableAmount as swPayableAmount, qitsr.folderPath as tsrFolderPath, qitsr.[filename] as tsrFileName,
		qitsr.TotalRoyalty as tsrPayableAmount, qitsa.folderPath as tsFolderPath, qitsa.[filename] as tsFileName, 
		qitsa.balanceForward, qitsa.charges, qitsa.credits, qitsa.balanceEnd, qitsa.payLinkDirect, qitsa.payLinkCode, 
		d.sourceID,
		case 
		when exists (
			select dd.depoMemberDataID
			from trialsmith.dbo.depoMemberData as dd
			INNER JOIN trialsmith.dbo.ccMemberPaymentProfiles as TSCC on TSCC.depomemberdataID = dd.depomemberdataID 
				and TSCC.orgcode = 'TS'
				and TSCC.declined = 0
			where dd.depoMemberDataID = mcb.depoMemberDataID
			and dd.paymenttype = 'C'
			) then 1 
		else 0 
		end as willChargeCC,
		(select top 1 m.memberID 
			from membercentral.dbo.ams_members as m 
			where m.orgID = 1 
			and m.status = 'A' 
			and m.memberNumber = 'TSDEPOID_' + CAST(mcb.depoMemberDataID as varchar(10))) as MCMemberID
	from #tmpOrgs as tmp
	inner join dbo.queue_monthBillEmailOrg as qieo on qieo.itemID = tmp.itemID
	inner join trialsmith.dbo.memberCentralBilling as mcb on mcb.orgCode = qieo.siteCode
	inner join trialsmith.dbo.billingPeriods as bp on bp.periodID = qieo.billingPeriodID
	inner join membercentral.dbo.sites as s on s.siteCode = qieo.siteCode
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	left outer join dbo.queue_monthBillSW as qisw on qisw.billingPeriodID = qieo.billingPeriodID and qisw.siteCode = qieo.siteCode
	left outer join dbo.queue_monthBillTSRoyalty as qitsr on qitsr.billingPeriodID = qieo.billingPeriodID and qitsr.orgCode = qieo.siteCode
	left outer join dbo.queue_monthBillTSA as qitsa on qitsa.billingPeriodID = qieo.billingPeriodID and qitsa.depoMemberDataID = mcb.depoMemberDataID and qitsa.isOrg = 1
	left outer join trialsmith.dbo.depoMemberData as d on d.depomemberdataID = mcb.depoMemberDataID
	order by tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
