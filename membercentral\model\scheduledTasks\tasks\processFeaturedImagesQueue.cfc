<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="100" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_featuredImages_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryFeaturedImages" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryFeaturedImages.recordCount>

			<!--- loop per image --->
			<cfloop query="local.qryFeaturedImages">
			
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='featuredImages', @queueStatus='processing', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_featuredImages
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryFeaturedImages.itemID#">;
				</cfquery>

				<cfset local.featuredImageRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qryFeaturedImages.orgcode)#/#LCASE(local.qryFeaturedImages.sitecode)#/featuredimages/">
				<cfset local.originalImage = "#local.featuredImageRootPath#originals/#local.qryFeaturedImages.featureImageID#.#local.qryFeaturedImages.origFileExtension#">

				<cfif fileExists(local.originalImage)>
					<cfif val(local.qryFeaturedImages.originalWidth) eq 0 OR val(local.qryFeaturedImages.originalHeight) eq 0>
						<cfset local.origPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.originalImage)>
					</cfif>

					<cfset local.objFeaturedImages.generatePreviewThumbnailsOfOriginalFeatureImage(orgCode=local.qryFeaturedImages.orgcode, siteCode=local.qryFeaturedImages.sitecode, featureImageID=local.qryFeaturedImages.featureImageID, fileExt=local.qryFeaturedImages.origFileExtension)>

					<cfset local.outputfilePath = "#local.featuredImageRootPath#thumbnails/#local.qryFeaturedImages.featureImageID#-#local.qryFeaturedImages.featureImageSizeID#.#local.qryFeaturedImages.fileExtension#">

					<cfset local.processPhoto = local.objFeaturedImages.thumborImageTransformByMethod(originalImagePath=local.originalImage, outputfilePath=local.outputfilePath,
						width=local.qryFeaturedImages.width, height=local.qryFeaturedImages.height, fileExtension=local.qryFeaturedImages.fileExtension,
						methodType=local.qryFeaturedImages.methodType, horizontalAlign=local.qryFeaturedImages.horizontalAlign, verticalAlign=local.qryFeaturedImages.verticalAlign)>

					<cfif local.processPhoto.success>
						<!--- generate preview thumbnail --->
						<cfset local.outputPreviewFilePath = "#local.featuredImageRootPath#thumbnails/#local.qryFeaturedImages.featureImageID#-#local.qryFeaturedImages.featureImageSizeID#-preview-small.#local.qryFeaturedImages.fileExtension#">
						<cfset local.processPhotoPreviewThumb = application.objCommon.thumborImageTranform(command="fit-in/80x60", filePath=local.outputfilePath, outputfilePath=local.outputPreviewFilePath)>

						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInsertFeaturedImageThumbnail">
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								DECLARE @featureImageID int, @featureImageSizeID int, @width int, @height int, @featureImageThumbnailID int, 
									@itemID int, @enteredByMemberID int;

								SET @featureImageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFeaturedImages.featureImageID#">;
								SET @featureImageSizeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFeaturedImages.featureImageSizeID#">;
								SET @width = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.processPhoto.width#">;
								SET @height = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.processPhoto.height#">;
								SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFeaturedImages.enteredByMemberID#">;
								SET @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFeaturedImages.itemID#">;
								
								BEGIN TRAN;
									<cfif val(local.qryFeaturedImages.originalWidth) eq 0 OR val(local.qryFeaturedImages.originalHeight) eq 0>
										UPDATE dbo.cms_featuredImages
										SET originalWidth = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.origPhotoInfo.imageInfo.source.width#">,
											originalHeight = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.origPhotoInfo.imageInfo.source.height#">
										WHERE featureImageID = @featureImageID;
									</cfif>

									-- inactivate existing thumbnail entry if any
									UPDATE dbo.cms_featuredImageThumbnails
									SET isActive = 0
									WHERE featureImageSizeID = @featureImageSizeID
									AND featureImageID = @featureImageID;

									-- create entry for thumbnail
									EXEC dbo.cms_createFeaturedImageThumbnail @featureImageID=@featureImageID, @featureImageSizeID=@featureImageSizeID, @width=@width, @height=@height,
										@cropX1=0, @cropX2=@width, @cropY1=0, @cropY2=@height, @addedByMemberID=@enteredByMemberID, @featureImageThumbnailID=@featureImageThumbnailID OUTPUT;
								COMMIT TRAN;

								DELETE FROM platformQueue.dbo.queue_featuredImages
								WHERE itemID = @itemID;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
							END CATCH
						</cfquery>
					<cfelse>
						<cfquery name="local.requeue" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @newStatus int;
							EXEC dbo.queue_getStatusIDbyType @queueType='featuredImages', @queueStatus='readyToProcess', @queueStatusID=@newStatus OUTPUT;

							UPDATE dbo.queue_featuredImages
							SET statusID = @newStatus,
								dateUpdated = getdate(),
								nextAttemptDate = dateadd(minute,30,getdate())
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFeaturedImages.itemID#">;
						</cfquery>
					</cfif>

				<!--- if original image not found; original image replaced by another one after queuing --->
				<cfelse>
					<cfquery name="local.delFromQueue" datasource="#application.dsn.platformQueue.dsn#">
						DELETE FROM dbo.queue_featuredImages
						WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryFeaturedImages.itemID#">
					</cfquery>
				</cfif>
		
			</cfloop>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>