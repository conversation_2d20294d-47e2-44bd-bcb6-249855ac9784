<cfcomponent output="no">

	<cffunction name="getAlbumData" access="public" output="false" returntype="struct">
		<cfargument name="albumID" type="numeric" required="TRUE">

		<cfset var local = structNew()>

		<cfquery name="local.Info" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT top 1 a.albumID, a.siteResourceID, a.galleryID, a.albumName, a.albumDesc, 
				a.columnsPerPage, a.ItemsPerPage, a.featuredvideoID, a.dateCreated, a.creatorMemberID,
				mActive.memberID as activeMemberID, mActive.firstName, mActive.lastName, sr.parentSiteResourceID,
				(select rootAlbumID from vg_gallery where galleryID = a.galleryID) as galleryRootAlbumID,
				(select albumID from vg_albums where siteResourceID = sr.parentSiteResourceID) as parentAlbumID
				, s.orgID, s.siteID, s.siteCode
			FROM dbo.vg_albums as a
			INNER JOIN dbo.ams_members as m on m.memberID = a.creatorMemberID
			INNER JOIN dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			INNER JOIN dbo.cms_siteResources as sr ON sr.siteResourceID = a.siteResourceID
			inner join dbo.sites s on sr.siteID = s.siteID
			WHERE a.albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfquery name="local.albumTree" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT albumID, albumName
			FROM dbo.fn_getVideoAlbumTreeUp(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">)
			ORDER BY Depth;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>		
		
		<cfif not local.info.recordcount>
			<cfset local.orgID = 0>
			<cfset local.siteID = 0>
		<cfelse>
			<cfset local.orgID = #local.Info.orgID#>
			<cfset local.siteID = #local.Info.siteID#>
		</cfif>

		<cfset local.viewVideoAlbumRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="VideoAlbum", functionName="View")>

		<cfquery name="local.Items" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @groupPrintID INT, @siteID INT, @albumID INT, @viewFunctionID int;
			SET @viewFunctionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.viewVideoAlbumRFID#">;
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">;
			SET @albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">;

			SELECT @groupPrintID = groupPrintID 
			FROM dbo.ams_members 
			WHERE memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=local.orgID)#">;

			IF @groupPrintID IS NULL
				select @groupPrintID = o.publicGroupPrintID
				from dbo.organizations as o
				inner join dbo.sites as s on s.orgID = o.orgID 
					and s.siteID = @siteID;

			SELECT 
				1 as isAlbum, 
				aChild.albumID, 
				aChild.siteResourceID, 
				aChild.albumName, 
				m3.activeMemberID as albumCreatorMemberID,
				p.videoID as featuredvideoID, 
				p.videoExt as featuredvideoExt,
				(SELECT COUNT(*) FROM dbo.vg_videos as pc WHERE pc.albumID IN (select albumID FROM dbo.fn_getVideoChildAlbums(aChild.siteResourceID))) as videoCount,
				0 as isVideo, 
				NULL as videoID, 
				NULL as videoName, 
				NULL as videoDesc, 
				NULL as videoExt, 
				NULL as videoCreatorMemberID,
				aChild.dateCreated as DateCreated,
				NULL as videoRatioID,
				NULL as status,
				NULL as limeLightUrl,
				NULL as memberFirstName,
				NULL as memberLastName,
				aChild.albumOrder,
				p.videoOrder
			FROM dbo.vg_albums AS aChild 
			INNER JOIN dbo.ams_members m3 on m3.memberID = aChild.creatorMemberID
			INNER JOIN dbo.cms_siteResources AS sr ON aChild.SiteResourceID = sr.siteResourceID
				AND sr.siteID = @siteID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.vg_albums AS aParent ON sr.parentSiteResourceID = aParent.SiteResourceID
			INNER JOIN dbo.vg_gallery AS g ON g.galleryID = aChild.galleryID
			LEFT OUTER JOIN dbo.vg_videos AS p ON p.videoID = aChild.featuredvideoID
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID
				AND srfrp.siteResourceID = aChild.siteResourceID
				AND srfrp.functionID = @viewFunctionID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
				AND gprp.rightPrintID = srfrp.rightPrintID
				AND gprp.groupPrintID = @groupPrintID
			WHERE aParent.albumID = @albumID
				union
			SELECT 0, null, null, null, null, null, null, 0,
				1, p.videoID, p.videoName, p.videoDesc, p.videoExt, m3.activeMemberID as videoCreatorMemberID, p.dateCreated, p.videoRatioID as videoRatioID
				, p.status, p.limeLightUrl
				, m3.firstName as memberFirstName
				, m3.lastName as memberLastName
				, NULL, p.videoOrder
			FROM dbo.vg_videos as p
			INNER JOIN dbo.ams_members m3 on m3.memberID = p.creatorMemberID
			INNER JOIN dbo.vg_albums as a ON p.albumID = a.albumID
			INNER JOIN dbo.cms_siteResources AS sr ON p.siteResourceID = sr.siteResourceID
				AND sr.siteID = @siteID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			WHERE a.albumID = @albumID
			order by isAlbum DESC, albumOrder ASC, isVideo DESC, videoOrder ASC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local>
	</cffunction>
	
	<cffunction name="getUserCreatedAlbums" access="public" output="false" returntype="query">
		<cfargument name="galleryID" type="numeric" required="TRUE">
		<cfargument name="memberID" type="numeric" required="TRUE">

		<cfset var local = structNew()>
		<cfquery name="local.getUserCreatedAlbums" datasource="#application.dsn.membercentral.dsn#">
			SELECT a.albumID
			FROM dbo.vg_albums a
			inner join dbo.vg_gallery g on a.galleryID = g.galleryID
			inner join ams_members m on m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			inner join ams_members m2 on m.activeMemberID = m2.activeMemberID
			where a.creatorMemberID = m2.memberID
				AND a.galleryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.galleryID#">
		</cfquery>		
		
		<cfreturn local.getUserCreatedAlbums>
	</cffunction>
	
	<cffunction name="getAllParentAlbumData" access="public" output="false" returntype="struct">
		<cfargument name="galleryID" type="numeric" required="TRUE">
		<cfargument name="memberID" type="numeric" required="TRUE">
		<cfset var local = structNew()>
		<cfquery name="local.All" datasource="#application.dsn.membercentral.dsn#">
			SELECT a.albumID, a.albumName
			FROM dbo.vg_albums as a
			INNER JOIN dbo.cms_siteResources as sr ON sr.siteResourceID = a.siteResourceID 
			inner join ams_members m on m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			inner join ams_members m2 on m.activeMemberID = m2.activeMemberID
			WHERE a.galleryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.galleryID#">
			AND a.creatorMemberID = m2.memberID
		</cfquery>
		<cfreturn local>
	</cffunction>
	
	<cffunction name="getParentAlbumData" access="public" output="false" returntype="struct">
		<cfargument name="galleryID" type="numeric" required="TRUE">
		<cfargument name="albumID" type="numeric" required="TRUE">
		<cfset var local = structNew()>
		<cfquery name="local.All" datasource="#application.dsn.membercentral.dsn#">
			WITH CTE (albumID, albumName, SiteResourceID, parentSiteResourceID, Depth) AS	
			(
				SELECT a.albumID, a.albumName, a.SiteResourceID, sr.parentSiteResourceID, 0 AS Depth
				FROM dbo.vg_albums as a
				INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = a.siteResourceID
				where albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">
				UNION ALL
				SELECT a.albumID, a.albumName, a.SiteResourceID, sr.parentSiteResourceID, CTE.Depth + 1 AS Depth
				FROM dbo.vg_albums as a
				INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = a.siteResourceID
				JOIN CTE ON sr.parentsiteResourceID = CTE.SiteResourceID
			)
			SELECT a.albumid,  
				(select albumID from vg_albums where siteResourceID = sr.parentSiteResourceID) as parentAlbumID,
				a.albumName
			FROM dbo.vg_albums as a
			INNER JOIN dbo.cms_siteResources as sr ON sr.siteResourceID = a.siteResourceID 
			WHERE a.galleryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.galleryID#">

			EXCEPT
			SELECT albumid,  
				(select albumID from vg_albums where siteResourceID = parentSiteResourceID) as parentAlbumID,
				albumName
			FROM CTE 
		</cfquery>
		<cfreturn local>
	</cffunction>	
	
	<cffunction name="updateAlbumSettings" access="public" output="false" returntype="void" hint="Update">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfset local.albumData = getAlbumData(arguments.event.getValue('vgAID'))>
		<!--- Disable the moving of albums
		<cfif local.albumData.Info.parentAlbumID NEQ arguments.event.getValue('parentAlbumID')>
			<cfset local.newAlbumData = getAlbumData(arguments.event.getValue('parentAlbumID'))>
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				update cms_siteResources
					set parentSiteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.newAlbumData.Info.siteResourceID#">
				where siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.albumData.Info.siteResourceID#">
			</cfquery>		
		</cfif>
		--->
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.vg_albums
			SET
				albumName		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('albumName')#">,
				albumDesc		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('albumDesc')#">
			WHERE
				albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('vgAID')#">
		</cfquery>		
	</cffunction>	
				
	<cffunction name="getVideoData" access="public" output="false" returntype="query">
		<cfargument name="videoData" type="query" required="TRUE">
		<cfargument name="videoID" type="numeric" required="TRUE">

		<cfset var local = structNew()>
		
		<cfquery name="local.data" dbtype="query">
			SELECT *
			FROM arguments.videoData
			WHERE isVideo = 1
			<cfif arguments.videoID gt 0>
				AND videoID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.videoID#">
			</cfif>
			ORDER BY videoOrder ASC
		</cfquery>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getVideoDetails" access="public" output="false" returntype="query">
		<cfargument name="albumID" type="numeric" required="TRUE">
		<cfargument name="videoID" type="numeric" required="TRUE">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			SELECT *
			FROM dbo.vg_videos 
			WHERE albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">
			AND videoID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.videoID#">
		</cfquery>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertVideo" access="public" output="false" returntype="struct" hint="Update">
		<cfargument name="albumID" type="numeric" required="TRUE">
		<cfargument name="fileName" type="string" required="TRUE">
		<cfargument name="fileExt" type="string" required="TRUE">
		<cfargument name="memberID" type="string" required="TRUE">
		
		<cfscript>
			var local = structNew();
			local.data.success = true;
			local.data.vidID = "0";
			local.albumInfo = getAlbumData(arguments.albumID);
			local.memberID = arguments.memberID;
		</cfscript>
		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		
			<cfif arguments.memberID EQ 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMember">
					SELECT m.activeMemberID as memberID
					FROM dbo.vg_albums a
					INNER JOIN ams_members m on m.memberID = a.creatorMemberID
					WHERE albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">
				</cfquery>
				<cfset local.memberID = local.getMember.memberID />
			</cfif>
			
			<!--- Update the activity log and create the site resource --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="vg_createVideo">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.albumID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileExt#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=local.albumInfo.info.orgID)#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.videoID">
			</cfstoredproc>			

			<cfset local.data.vidID = local.videoID>
			<cfset local.arguments = arguments>
			<cfset local.httprequest = GetHttpRequestData()>
			<cfset local.cgi = cgi>
			<cfset local.session = session>
		</cfif>

		<cfreturn local.data>
	</cffunction>	
	
	<cffunction name="updateVideoStatus" access="public" output="false" returntype="void" hint="Update">
		<cfargument name="albumID" type="numeric" required="TRUE">
		<cfargument name="videoID" type="numeric" required="TRUE">
		<cfargument name="status" type="string" required="TRUE">
		<cfscript>
			var local = structNew();
			local.insert.videoID = "";
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insert">
			UPDATE dbo.vg_videos 
				SET status = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#">
			WHERE albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">
			AND videoID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.videoID#">
		</cfquery>	
	</cffunction>		


	<cffunction name="updateVideoS3Url" access="public" output="false" returntype="void" hint="Update">
		<cfargument name="albumID" type="numeric" required="TRUE">
		<cfargument name="videoID" type="numeric" required="TRUE">
		<cfargument name="url" type="string" required="TRUE">
		<cfscript>
			var local = structNew();
			local.insert.videoID = "";
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insert">
			UPDATE dbo.vg_videos 
				SET s3Url = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.url#">
			WHERE albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">
			AND videoID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.videoID#">
		</cfquery>	
	</cffunction>		
	
	<cffunction name="updateVideoLimelightUrl" access="public" output="false" returntype="void" hint="Update">
		<cfargument name="albumID" type="numeric" required="TRUE">
		<cfargument name="videoID" type="numeric" required="TRUE">
		<cfargument name="url" type="string" required="TRUE">
		<cfscript>
			var local = structNew();
			local.insert.videoID = "";
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insert">
			UPDATE dbo.vg_videos 
				SET limelightUrl = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.url#">
			WHERE albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">
			AND videoID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.videoID#">
		</cfquery>	
	</cffunction>			
	
	<cffunction name="getVideoDataFor" access="public" output="false" returntype="query">
		<cfargument name="albumID" type="numeric" required="TRUE">
		<cfargument name="videoID" type="numeric" required="TRUE">
		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			SELECT *
			FROM vg_videos
			WHERE albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.albumID#">
			AND videoID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.videoID#">
		</cfquery>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateVideo" access="public" output="false" returntype="void" hint="Update">
		<cfargument name="Event" type="any">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.vg_videos
			SET
				videoName		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('videoName')#">,
				videoDesc		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('videoDesc')#">
			WHERE
				videoID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('vID')#">
			AND
				albumID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('aID')#">
		</cfquery>		
		<!--- Update the activity log --->
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="vg_updateVideo">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('vID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('memberid')#">
		</cfstoredproc>					
	</cffunction>	

	<cffunction name="getVideoDetail" access="public" output="false" returntype="query">
		<cfargument name="videoRatioID" type="numeric" required="TRUE">
		
		<cfset var qGetVideoDetail = "">

		<cfquery name="qGetVideoDetail" datasource="#application.dsn.membercentral.dsn#">
			SELECT videoRatio,width,height
			FROM dbo.vg_videoRatios			
			WHERE videoRatioID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.videoRatioID#">
		</cfquery>
		
		<cfreturn qGetVideoDetail>
	</cffunction>

	<cffunction name="addVideoToS3DeleteQueue" access="public" output="false" returntype="void">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="albumID" type="numeric" required="true">
		<cfargument name="videoID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryAppInstance" datasource="#application.dsn.membercentral.dsn#">
				select vg.applicationInstanceID
				from dbo.vg_albums as va
				inner join dbo.vg_gallery as vg on vg.galleryID = va.galleryID
				where va.albumID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.albumID#">
			</cfquery>

			<cfset local.qryS3Result = application.objS3.listObjects(bucket="membercentralcdn", prefix="mcapps/#LCase(arguments.sitecode)#/#local.qryAppInstance.applicationInstanceID#/#arguments.videoID#.", delimiter="", maxResults=0)>
			<cfif local.qryS3Result.recordCount>
				<cfquery name="local.insertQueueS3Delete" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @queueStatusID int, @nowDate datetime = GETDATE();
					EXEC dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;
	
					<cfloop query="local.qryS3Result">
						INSERT INTO dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
						VALUES (
							@queueStatusID, 
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryS3Result.bucket#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryS3Result.objectkey#">, 
							@nowDate, 
							@nowDate
						);
					</cfloop>
				</cfquery>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>
	</cffunction>
</cfcomponent>