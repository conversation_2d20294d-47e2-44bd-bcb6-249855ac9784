<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">

		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSize", type="INTEGER", desc="Batch Size", value="500" },
				{ name="Threads", type="INTEGER", desc="Number of Threads", value="4" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize, threads=local.strTaskFields.threads)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="threads" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc procedure="queue_memberPhotoThumb_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryMemberThumbQueue" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryMemberThumbQueue.recordCount>

			<cfscript>
			QueryEach(local.qryMemberThumbQueue, function(struct thisPhoto) {

				// item must still be in the grabbedForProcessing state for this job. else skip it. 
				// this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. 
				if (queueItemHasStatus(queueStatus='grabbedForProcessing', itemID=arguments.thisPhoto.itemID)) {
					try {
						
						queryExecute("
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								declare @statusProcessing int;
								EXEC dbo.queue_getStatusIDbyType @queueType='memberPhotoThumb', @queueStatus='processingPhoto', @queueStatusID=@statusProcessing OUTPUT;

								UPDATE dbo.queue_memberPhotoThumb
								SET statusID = @statusProcessing,
									dateUpdated = GETDATE()
								WHERE itemID = :itemID;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
							END CATCH
						", 
						{ itemID = { value=arguments.thisPhoto.itemID, cfsqltype="cf_sql_integer" } }, 
						{ datasource=application.dsn.platformQueue.dsn } );

						var memberPhotoDir = "#application.paths.RAIDUserAssetRoot.path##LCASE(arguments.thisPhoto.orgcode)#/memberphotos";
						var memberPhotoThDir = "#application.paths.RAIDUserAssetRoot.path##LCASE(arguments.thisPhoto.orgcode)#/memberphotosth";
						var memberPhotoPath = "#local.memberPhotoDir#/#LCASE(arguments.thisPhoto.membernumber)#.jpg";
						var memberPhotoThPath = "#local.memberPhotoThDir#/#LCASE(arguments.thisPhoto.membernumber)#.jpg";
						var command = "fit-in/#arguments.thisPhoto.width#x#arguments.thisPhoto.height#";
						
						var resizeRequest = application.objCommon.thumborImageTranform(command=command, filePath=memberPhotoPath, outputfilePath=memberPhotoThPath);

						if (resizeRequest.success)
							queryExecute("
								UPDATE dbo.ams_members
								SET hasMemberPhotoThumb = 1
								WHERE memberID = :memberID;
							", 
							{ memberID = { value=arguments.thisPhoto.memberID, cfsqltype="cf_sql_integer" } }, 
							{ datasource=application.dsn.memberCentral.dsn } );

						queryExecute("
							DELETE FROM dbo.queue_memberPhotoThumb
							WHERE itemID = :itemID;
						", 
						{ itemID = { value=arguments.thisPhoto.itemID, cfsqltype="cf_sql_integer" } }, 
						{ datasource=application.dsn.platformQueue.dsn } );

					} catch (e) {
						application.objError.sendError(cfcatch=e, objectToDump=local);
						rethrow;
					}
				}
			}, true, arguments.threads);
			</cfscript>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>			

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="queueItemHasStatus" access="private" returntype="boolean" output="false">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
			select count(qi.itemID) as itemCount
			from dbo.queue_memberPhotoThumb as qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
			where qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
		</cfquery>

		<cfreturn (local.checkItemID.itemCount gt 0)>
	</cffunction>	

</cfcomponent>