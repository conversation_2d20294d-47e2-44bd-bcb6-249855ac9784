<cfcomponent>
	<cffunction name="insertDocument" access="public" output="false" returntype="struct" hint="Insert Document">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="sectionID" type="numeric" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="docTitle" type="string" required="true">
		<cfargument name="docDesc" type="string" required="true">
		<cfargument name="fileName" type="string" required="true">
		<cfargument name="fileExt" type="string" required="true">
		<cfargument name="contributorMemberID" type="numeric" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">
		
		<cfset var local = structNew()>
											
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.Resource">
			SELECT dbo.fn_getResourceTypeId('#arguments.ResourceType#') as ResourceTypeID, dbo.fn_getResourceStatusId('Active') as siteResourceStatusID
		</cfquery>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#"  procedure="cms_createDocument">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.Resource.ResourceTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.Resource.siteResourceStatusID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionID#">	
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contributorMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docTitle#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.docDesc#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="Yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileExt#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.insertDocument.documentID">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.insertDocument.documentVersionID">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.insertDocument.documentSiteResourceID">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.insertDocument = { documentID=0, documentVersionID=0, documentSiteResourceID=0 }>
		</cfcatch>
		</cftry>
			
		<cfreturn local.insertDocument>
	</cffunction>

	<cffunction name="insertReportDocument" access="public" output="false" returntype="struct" hint="Insert Report Document">
		<cfargument name="siteID" type="numeric" required="true"/>
		<cfargument name="documentID" type="numeric" required="true"/>
		<cfargument name="dateExpire" type="string" required="false" default=""/>	
		
		<cfset var local = structNew()>
		<cfset local.result = {success=false, documentUID=''}>
			
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#"  procedure="rpt_createReportDocument">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
				<cfif len(arguments.dateExpire)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateExpire#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
				</cfif>
				<cfprocparam type="Out" cfsqltype="CF_SQL_IDSTAMP" variable="local.result.documentUID">
			</cfstoredproc>
			<cfset local.result.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
			
		<cfreturn local.result>
	</cffunction>

	<cffunction name="addToS3UploadQueue" access="public" output="false" returntype="void">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="documentVersionID" type="numeric" required="true">
		<cfargument name="fileExt" type="string" required="true">

		<cfset var qryAddDocToS3UploadQueue = "">
		
		<cfquery name="qryAddDocToS3UploadQueue" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			
			DECLARE @siteCode varchar(10), @orgCode varchar(10), @documentVersionID int, @fileExt varchar(20), @s3keyMod varchar(4), 
				@objectKey varchar(400), @s3bucketName varchar(100), @siteDocumentsPath varchar(100), @filePath varchar(400), 
				@s3UploadReadyStatusID int, @nowDate datetime = getdate();

			SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;
			SET @orgCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgCode#">;
			SET @documentVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentVersionID#">;
			SET @fileExt = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileExt#">;
			SET @s3bucketName = 'membercentralcdn';

			select @siteDocumentsPath = siteDocumentsPath from memberCentral.dbo.fn_getServerSettings();
			SET @filePath = lower(@siteDocumentsPath + @orgCode + '\' + @siteCode + '\' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);

			SET @s3keyMod = FORMAT(@documentVersionID % 1000, '0000');
			SET @objectKey = LOWER('sitedocuments/' + @orgCode + '/' + @siteCode + '/' + @s3keyMod + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);

			EXEC dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

			IF NOT EXISTS (select 1 from dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
				INSERT INTO dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
				VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePath, 1, @nowDate, @nowDate);
		</cfquery>
	</cffunction>
</cfcomponent>