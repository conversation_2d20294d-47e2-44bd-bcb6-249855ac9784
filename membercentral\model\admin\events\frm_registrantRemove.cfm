<!--- Setup GL Account Widget for Revenue GL --->
<cfset local.strRevenueGLAcctWidgetData = {
	"label": "GL Account for Cancellation Fee",
	"btnTxt": "Choose GL Account",
	"glatid": 3,
	"widgetMode": "GLSelector",
	"idFldName": "GLAccountID",
	"idFldValue": val(arguments.event.getValue('GLAccountID',0)),
	"pathFldValue": arguments.event.getValue('GLAccountPath',''),
	"pathNoneTxt": "(no account selected)"
}>
<cfset local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>

<cfsavecontent variable="local.remreghead">
	<cfoutput>
	#local.strRevenueGLAcctWidget.js#
	<script language="javascript">
	<cfif local.removeMode EQ 'MassRegistrants'>
		var #ToScript(local.massRemoveRegistrants,'link_massRemoveRegistrants')#
	</cfif>
	
		function checkAROptChanges() {
			if ($('input[name="rdoAR"]:checked').val() == 'A' && $('##deallocUsingPaidAmt').is(':disabled')) {
				$('##deallocUsingPaidAmt').prop('disabled',false);
			} else if ($('input[name="rdoAR"]:checked').val() != 'A' && !$('##deallocUsingPaidAmt').is(':disabled')) {
				$('##deallocUsingPaidAmt').prop('disabled',true).prop('checked',false);
			}
		}
		<cfif local.removeMode eq "MassRegistrants">
			function doCallRemoveFilteredReg() {
				if ($('input[name="rdoAR"]:checked').val() == null){
					$('##ev_remreg_err_div').html('Select an accounts receivable option.').show();
					return false;
				} else {
					$('##ev_remreg_err_div').html('').hide();
					top.$('##btnMCModalSave').text("Removing...").prop('disabled',true);
					$('##divRemoveRegLoading').show();
					$('##divRemoveRegForm').hide();

					var urlParams = 'eventSRID=#val(local.qryRegLookup.eventSRID)#' +
						'&calendarSRID=#val(local.qryRegLookup.calendarSRID)#' +
						'&AROption=' + $('input[name="rdoAR"]:checked').val() +
						'&cancellationFee=' + mca_stripCurrency(formatCurrency($('##cancellationFee').val())) +
						'&GLAccountID=' + $('##GLAccountID').val() +
						'&deallocUsingPaidAmt=' + $('##deallocUsingPaidAmt').is(':checked') +
						'&' + top.$('##frmFilter').serialize();
					$('##divRemoveRegLoading').load(link_massRemoveRegistrants +'&eid=#arguments.event.getValue('eID')#&' + urlParams);
				}
			}
		<cfelse>
			function doCallRemoveReg() {
				if ($('input[name="rdoAR"]:checked').val() == null){
					$('##ev_remreg_err_div').html('Select an accounts receivable option.').show();
					return false;
				} else {
					$('##ev_remreg_err_div').html('').hide();

					top.$('##btnMCModalSave').text("Removing...").prop('disabled',true);
					$('##divRemoveRegLoading').show();
					$('##divRemoveRegForm').hide();

					var objParams = { registrantID:#val(local.qryRegLookup.registrantID)#, eventSRID:#val(local.qryRegLookup.eventSRID)#,
						calendarSRID:#val(local.qryRegLookup.calendarSRID)#, AROption:$('input[name="rdoAR"]:checked').val(),
						cancellationFee:mca_stripCurrency(formatCurrency($('##cancellationFee').val())),
						GLAccountID:$('##GLAccountID').val(), deallocUsingPaidAmt:$('##deallocUsingPaidAmt').is(':checked'), 
						registrantMemberID:#val(local.qryRegLookup.memberID)# };
					top.doRemoveReg(objParams);
				}
			}
		</cfif>
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.remreghead#">

<cfoutput>
<div id="divRemoveRegForm" class="px-3 mt-2">
	<cfif local.removeMode eq "MassRegistrants">
		<div class="alert alert-warning" id="warningMsg">
			WARNING: You are about to remove <b>#arguments.event.getValue('frc')# registrants</b> from this event.
			<b>This action is not reversable.</b>
		</div>
		<div class="alert alert-success d-none" id="successMsg">
			<b>#arguments.event.getValue('frc')# registrants</b> have been queued for removal from this event and will be processed shortly.
		</div>
	</cfif>

	<div id="ev_remreg_err_div" class="alert alert-danger mb-3" style="display:none;"></div>
	<form name="frmRemoveReg" id="frmRemoveReg">
		
		<cfinclude template="dsp_AROptions.cfm">

		<h5 class="mt-3">Cancellation Fee</h5>
		<div>Charge a cancellation fee while removing this registration by entering the fee amount below:</div>
		<div class="d-flex align-items-center mt-2">
			<div>
				<div class="input-group input-group-sm">
					<div class="input-group-prepend">
						<span class="input-group-text">$</span>
					</div>
					<input type="text" name="cancellationFee" id="cancellationFee" value="" onblur="this.value=formatCurrency(this.value);" class="form-control form-control-sm">									
				</div>
			</div>
			<span class="mx-2">#local.defaultCurrencyType# to</span>
			<div>
				#local.strRevenueGLAcctWidget.html#
			</div>
		</div>
		<div class="d-flex align-items-center mt-2">
			<input type="checkbox" name="deallocUsingPaidAmt" id="deallocUsingPaidAmt" value="1" class="align-self-center" disabled="disabled">
			<label for="deallocUsingPaidAmt" class="align-self-center mb-0 ml-1">Apply deallocated payments to this cancellation fee, if applicable</label>
		</div>
	</form>
</div>
<div id="divRemoveRegLoading" class="mt-4" style="display:none;">
	<div class="mt-4">
		<div class="text-center">
			<div class="spinner-border" role="status"></div>
			<h4 class="mt-2">Removing Registrant<cfif local.removeMode eq "MassRegistrants">s</cfif>...</h4>
		</div>
	</div>
</div>

</cfoutput>