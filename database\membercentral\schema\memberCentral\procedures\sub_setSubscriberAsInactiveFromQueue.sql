ALTER PROC dbo.sub_setSubscriberAsInactiveFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusProcessing int, @statusNotify int, @itemStatus int, 
		@siteID int, @subscriberID int, @recordedByMemberID int, @trashID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='subscriptionInactivate', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusNotify OUTPUT;

	-- if itemID is not readyToProcess, kick out now
	select @itemStatus = statusID, @siteID = siteID, @subscriberID = subscriberID, @recordedByMemberID = recordedByMemberID
	from platformQueue.dbo.queue_subscriptionInactivate
	where itemID = @itemID;
	
	IF @itemStatus <> @statusReady OR @subscriberID is null
		GOTO on_done;

	update platformQueue.dbo.queue_subscriptionInactivate
	set statusID = @statusProcessing,
		dateUpdated = getdate()
	where itemID = @itemID;
	
	BEGIN TRY
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode='I',
			@siteID=@siteID, @enteredByMemberID=@recordedByMemberID, @updateDate=null, 
			@byPassQueue=0, @result=@trashID OUTPUT;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;
		UPDATE platformQueue.dbo.queue_subscriptionInactivate set errorMessage = ERROR_MESSAGE(), dateUpdated = getdate() where itemID = @itemID;
	END CATCH

	update platformQueue.dbo.queue_subscriptionInactivate
	set statusID = @statusNotify,
		dateUpdated = getdate()
	where itemID = @itemID;	

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
