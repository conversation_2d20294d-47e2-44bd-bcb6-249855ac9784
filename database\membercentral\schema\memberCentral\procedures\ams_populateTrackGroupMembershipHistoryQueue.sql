ALTER PROC dbo.ams_populateTrackGroupMembershipHistoryQueue

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @itemGroupUID uniqueidentifier = NEWID(), @xmlMessage xml;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='trackGrpMembershipHistory', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	-- active orgs
	INSERT INTO platformQueue.dbo.queue_trackGrpMembershipHistory (itemGroupUID, orgID, statusID, dateAdded, dateupdated)
	SELECT @itemGroupUID, s.orgID, @statusReady, GETDATE(), GETDATE()
	FROM dbo.sites as s
	INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID AND sr.siteResourceStatusID = 1
	WHERE NOT EXISTS (SELECT orgID FROM platformQueue.dbo.queue_trackGrpMembershipHistory WHERE orgID = s.orgID)
	GROUP BY s.orgID;

	-- send message to service broker to create all the individual messages
	SELECT @xmlMessage = isnull((
		SELECT 'trackGrpMembershipHistoryLoad' as t, cast(@itemGroupUID as varchar(60)) as u
		FOR XML RAW('mc'), TYPE
	),'<mc/>');
	EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
