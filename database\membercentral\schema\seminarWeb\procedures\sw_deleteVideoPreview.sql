ALTER PROC dbo.sw_deleteVideoPreview
@previewID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @s3DeleteReadyStatusID int, @siteResourceID int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

	-- video previews
	INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
	select @s3DeleteReadyStatusID, 'seminarweb', LOWER('swod/' + sP.orgcode + '/' + cast(sP.participantID as varchar(10)) + '/' 
								+ cast(p.baseFileID as varchar(10)) + '_s_'
								+ cast(p.seminarID as varchar(10)) + '_preview.mp4'),
			GETDATE(), GETDATE()
	from dbo.tblVideoPreviews as p
	inner join dbo.tblSeminars as s on s.seminarID = p.seminarID
	inner join dbo.tblParticipants as sP on sP.participantID = s.participantID
	where p.previewID = @previewID
	and p.isOnline = 1;

	BEGIN TRAN;
		DELETE FROM dbo.tblVideoPreviews
		WHERE previewID = @previewID;

		DELETE FROM platformQueue.dbo.queue_SWVideoPreview
		WHERE previewID = @previewID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
